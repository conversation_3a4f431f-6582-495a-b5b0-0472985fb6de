update bank_accounts.bank_account
set name = trim(both ' ' || E'\t' || E'\t' || E'\n' from name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from name) != name;

update business.business
set name = trim(both ' ' || E'\t' || E'\t' || E'\n' from name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from name) != name;

update business.user_invitation
set email_address = trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address) != email_address;

update business.user_invitation
set first_name = trim(both ' ' || E'\t' || E'\t' || E'\n' from first_name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from first_name) != first_name;

update business.user_invitation
set last_name = trim(both ' ' || E'\t' || E'\t' || E'\n' from last_name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from last_name) != last_name;

update business.user_invitation
set sender_name = trim(both ' ' || E'\t' || E'\t' || E'\n' from sender_name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from sender_name) != sender_name;

update business.user_invitation
set phone_number = trim(both ' ' || E'\t' || E'\t' || E'\n' from phone_number)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from phone_number) != phone_number;

-- This recipient was duplicated but with a leading space.
-- Its duplicate (580f4681-f156-4aeb-af28-8f953ca220be) has more recent transfers.
delete
from transfer.recipient
where guid = 'c7676b56-2dec-4c1e-9fa1-bf0de411d2be';

update transfer.recipient
set name = trim(both ' ' || E'\t' || E'\t' || E'\n' from name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from name) != name;

update transfer.recipient
set email_address = trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address) != email_address;

update transfer.recipient
set phone = trim(both ' ' || E'\t' || E'\t' || E'\n' from phone)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from phone) != phone;

update transfer.recipient
set address_line_1 = trim(both ' ' || E'\t' || E'\t' || E'\n' from address_line_1)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from address_line_1) != address_line_1;

update transfer.recipient
set address_line_2 = trim(both ' ' || E'\t' || E'\t' || E'\n' from address_line_2)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from address_line_2) != address_line_2;

update transfer.recipient
set city = trim(both ' ' || E'\t' || E'\t' || E'\n' from city)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from city) != city;

update transfer.recipient
set state = trim(both ' ' || E'\t' || E'\t' || E'\n' from state)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from state) != state;

update transfer.recipient
set zip_code = trim(both ' ' || E'\t' || E'\t' || E'\n' from zip_code)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from zip_code) != zip_code;

update transfer.recipient
set country = trim(both ' ' || E'\t' || E'\t' || E'\n' from country)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from country) != country;

update transfer.recipient
set ach_routing_number = trim(both ' ' || E'\t' || E'\t' || E'\n' from ach_routing_number)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from ach_routing_number) != ach_routing_number;

update transfer.recipient
set ach_account_number = trim(both ' ' || E'\t' || E'\t' || E'\n' from ach_account_number)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from ach_account_number) != ach_account_number;

update transfer.recipient
set wire_routing_number = trim(both ' ' || E'\t' || E'\t' || E'\n' from wire_routing_number)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from wire_routing_number) != wire_routing_number;

update transfer.recipient
set wire_account_number = trim(both ' ' || E'\t' || E'\t' || E'\n' from wire_account_number)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from wire_account_number) != wire_account_number;

update transfer.recipient
set international_wire_type = trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_type)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_type) !=
      international_wire_type;

update transfer.recipient
set international_wire_swift = trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_swift)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_swift) != international_wire_swift;

update transfer.recipient
set international_wire_iban = trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_iban)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from international_wire_iban) != international_wire_iban;

update users.user
set email_address = trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from email_address) != email_address;

update users.user
set first_name = trim(both ' ' || E'\t' || E'\t' || E'\n' from first_name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from first_name) != first_name;

update users.user
set last_name = trim(both ' ' || E'\t' || E'\t' || E'\n' from last_name)
where trim(both ' ' || E'\t' || E'\t' || E'\n' from last_name) != last_name;
