-- The Accounts Payable (AP) role and the Bookkeeper role should have identical permissions.
-- The Accounts Payable (AP) role got missed in V345__give_cash_flow_page_permissions_to_bookkeeper.sql.
update auth.user_role
set permissions = jsonb_set(permissions, '{ permissions }',
                            permissions -> 'permissions' ||
                            '["insightsAlias:write", "insightsAuditReport:read", "insightsCategorizer:write", "insightsCounterpartyReport:read", "insightsTransactionReport:read"]')
where name in ('Accounts Payable (AP)');

-- Now add the new permissions
update auth.user_role
set permissions = jsonb_set(permissions, '{ permissions }',
                            permissions -> 'permissions' ||
                            '["insightsSheetSync:run"]')
where name in ('Accounts Payable (AP)', 'Bookkeeper');
