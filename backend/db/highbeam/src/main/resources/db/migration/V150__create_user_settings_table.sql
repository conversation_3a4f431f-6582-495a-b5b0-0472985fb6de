create table users.user_notification_settings
(
  guid              uuid
    constraint pkey__user_notification_settings primary key,
  created_at        timestamptz not null default now(),
  updated_at        timestamptz not null default now(),

  user_guid         uuid        not null,
  transaction_email boolean     not null,
  transaction_sms   boolean     not null,

  constraint uniq__user_notification_settings__user_guid
    unique (user_guid)
);

