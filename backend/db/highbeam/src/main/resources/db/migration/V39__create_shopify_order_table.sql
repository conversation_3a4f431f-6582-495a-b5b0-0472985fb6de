create table connect.shopify_order
(
  guid            uuid,
  created_at      timestamptz not null default now(),
  updated_at      timestamptz not null default now(),

  business_guid   uuid        not null,
  connection_guid uuid        not null,
  shopify_id      bigint      not null,
  json            jsonb       not null,
  date            date        not null
);

alter table connect.shopify_order
  add constraint pkey__shopify_order primary key (guid);

alter table connect.shopify_order
  add constraint fk__shopify_order__connection_guid foreign key (connection_guid)
    references connect.shopify_connection (guid) on delete cascade;

create unique index uniq__shopify_order__shopify_id
  on connect.shopify_order (business_guid, connection_guid, shopify_id);

create index idx__shopify_order__date
  on connect.shopify_order (business_guid, connection_guid, date asc);
