create table credit.underwriting_factor_history
(
  guid       uuid
    constraint pkey__underwriting_factor_history primary key,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now(),
  factor     text        not null,
  data       jsonb       not null
);

create trigger on_update__underwriting_factor_history
  before update
  on credit.underwriting_factor_history
  for each row
execute procedure updated();

create index idx__underwriting_factor_history__factor
  on credit.underwriting_factor_history (factor);
