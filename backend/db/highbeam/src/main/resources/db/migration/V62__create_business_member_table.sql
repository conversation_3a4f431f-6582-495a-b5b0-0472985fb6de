create table business.business_member
(
  guid          uuid
    constraint pkey__business_member primary key,
  created_at    timestamptz not null default now(),
  updated_at    timestamptz not null default now(),

  business_guid uuid        not null,
  user_guid     uuid        not null,

  constraint fk__business_member__business_guid foreign key (business_guid)
    references business.business (guid)
    on delete cascade,

  constraint uniq__business_member__user_guid
    unique (business_guid, user_guid)
);

create trigger on_update__business_member
  before update
  on business.business_member
  for each row
execute procedure updated();

create index idx__business_member__business_guid
  on business.business_member (business_guid);

create index idx__business_member__user_guid
  on business.business_member (user_guid);
