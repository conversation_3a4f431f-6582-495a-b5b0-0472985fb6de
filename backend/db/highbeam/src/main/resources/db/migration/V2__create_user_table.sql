CREATE TABLE users.user
(
  created_at        TIMESTAMPTZ NOT NULL,
  guid              UUID        NOT NULL,
  email_address     VARCHAR     NOT NULL,
  first_name        <PERSON><PERSON><PERSON><PERSON>,
  last_name         <PERSON><PERSON><PERSON><PERSON>,
  profile_photo_url VARCHAR
);

CREATE UNIQUE INDEX uniq__user__guid
  ON users.user (guid);

CREATE UNIQUE INDEX uniq__user__email_address
  ON users.user (LOWER(email_address));
