CREATE TABLE businesses.unit_co_bank_application
(
  created_at                  TIMESTAMPTZ NOT NULL,
  guid                        UUID        NOT NULL,
  business_guid               UUID        NOT NULL,
  unit_co_application_form_id VARCHAR     NOT NULL
);

CREATE UNIQUE INDEX uniq__unit_co_bank_application__guid
  ON businesses.unit_co_bank_application (guid);

ALTER TABLE businesses.unit_co_bank_application
  ADD CONSTRAINT fk__unit_co_bank_application__business_guid FOREIGN KEY (business_guid)
    REFERENCES businesses.business (guid) ON DELETE CASCADE;

CREATE INDEX idx__unit_co_bank_application__business_guid
  ON businesses.unit_co_bank_application (business_guid);

CREATE UNIQUE INDEX uniq__unit_co_bank_application__unit_co_application_form_id
  ON businesses.unit_co_bank_application (unit_co_application_form_id);
