create table connect.daily_shopify_summary
(
  guid            uuid
    constraint pkey__daily_shopify_summary primary key,
  created_at      timestamptz not null default now(),
  updated_at      timestamptz not null default now(),

  business_guid   uuid        not null,
  connection_guid uuid        not null,
  date            date        not null,
  currency        char(3)     not null,
  amount          bigint      not null,
  fee             bigint      not null,
  net             bigint      not null,

  constraint fk__daily_shopify_summary__connection_guid foreign key (connection_guid)
    references connect.shopify_connection (guid) on delete cascade
);

create trigger on_update__daily_shopify_summary
  before update
  on connect.daily_shopify_summary
  for each row
execute procedure updated();

create index idx__daily_shopify_summary__date
  on connect.daily_shopify_summary (business_guid, connection_guid, date asc);
