create table bank_accounts.currency_cloud_bank_account
(
  guid                             uuid
    constraint pkey__currency_cloud_bank_account primary key,
  created_at                       timestamptz not null default now(),
  updated_at                       timestamptz not null default now(),

  business_guid                    uuid        not null,
  currency_cloud_bank_account_guid uuid        not null,

  currency_cloud_contact_guid      uuid,
  ach_routing_number               text,
  ach_account_number               text,
  wire_routing_number              text,
  wire_account_number              text,

  constraint uniq__currency_cloud_bank_account__business_guid
    unique (business_guid)
);

create trigger on_update__currency_cloud_bank_account
  before update
  on bank_accounts.currency_cloud_bank_account
  for each row
execute procedure updated();

