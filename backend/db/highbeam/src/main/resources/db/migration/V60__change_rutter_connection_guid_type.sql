alter table connect.rutter_connection
  add column rutter_connection_guid_new uuid;

update connect.rutter_connection
set rutter_connection_guid_new = rutter_connection_guid::uuid;

alter table connect.rutter_connection
  alter column rutter_connection_guid_new
    set not null;

alter table connect.rutter_connection
  drop column rutter_connection_guid;

alter table connect.rutter_connection
  rename column rutter_connection_guid_new
    to rutter_connection_guid;

alter table connect.rutter_connection
  add constraint uniq__rutter_connection__rutter_connection_guid
    unique (business_guid, rutter_connection_guid);

create index idx__rutter_connection__rutter_connection_guid
  on connect.rutter_connection (rutter_connection_guid);
