----------------------------------------------------------------
-- Create Credit schema.
----------------------------------------------------------------

create schema credit

----------------------------------------------------------------
-- Create Line of Credit Interest Fee table.
----------------------------------------------------------------

create table credit.line_of_credit_interest_fee
(
  guid                            uuid
    constraint pkey__line_of_credit_interest_fee primary key,
  created_at                      timestamptz not null default now(),
  updated_at                      timestamptz not null default now(),

  business_guid                   uuid        not null,
  state                           text        not null,
  amount                          bigint      not null,
  unit_response                   jsonb       not null
);

create trigger on_update__line_of_credit_interest_fee
  before update
  on credit.line_of_credit_interest_fee
  for each row
execute procedure updated();
