alter table bank_accounts.bank_account
  alter column created_at
    set default now();

alter table bank_accounts.bank_account
  add column updated_at timestamptz not null default now();
create trigger on_update__bank_account
  before update
  on bank_accounts.bank_account
  for each row
execute procedure updated();

alter table bank_accounts.bank_account
  alter column name type text;

alter table bank_accounts.bank_account
  alter column unit_co_deposit_account_id type text;

alter table bank_accounts.bank_account
  rename constraint uniq__bank_account__unit_co_deposit_account_id
    to uniq__bank_account__deposit_account_id;

alter table bank_accounts.unit_co_bank_account_transaction
  alter column created_at
    set default now();

alter table bank_accounts.unit_co_bank_account_transaction
  add column updated_at timestamptz not null default now();
create trigger on_update__unit_co_bank_account_transaction
  before update
  on bank_accounts.unit_co_bank_account_transaction
  for each row
execute procedure updated();

alter table bank_accounts.unit_co_bank_account_transaction
  alter column unit_co_deposit_account_id type text;

alter table bank_accounts.unit_co_bank_account_transaction
  alter column unit_co_transaction_id type text;

alter table bank_accounts.unit_co_bank_account_transaction
  alter column category type text;

alter table bank_accounts.unit_co_bank_account_transaction
  add constraint uniq__unit_co_bank_account_transaction__transaction_id
    unique using index uniq__unit_co_bank_account_transaction__transaction_id;

drop type businesses.bank_application_status;

drop schema businesses;

alter table connect.shopify_connection
  alter column connection_banner_disabled_at
    type timestamptz;

alter table connect.shopify_connection
  alter column last_payout_at
    type timestamptz;

alter table connect.shopify_connection
  rename constraint uniq__shopify_connection__business_guid__shop_subdomain
    to uniq__shopify_connection__shop_subdomain;

alter index connect.idx__shopify_connection__business
  rename to idx__shopify_connection__business_guid;

create trigger on_update__shopify_order
  before update
  on connect.shopify_order
  for each row
execute procedure updated();

create trigger on_update__shopify_payout
  before update
  on connect.shopify_payout
  for each row
execute procedure updated();

create trigger on_update__shopify_transaction
  before update
  on connect.shopify_transaction
  for each row
execute procedure updated();

alter table unit_co.webhook
  alter column created_at
    set default now();

alter table unit_co.webhook
  add column updated_at timestamptz not null default now();
create trigger on_update__webhook
  before update
  on unit_co.webhook
  for each row
execute procedure updated();

alter table unit_co.webhook
  alter column type type text;

alter table unit_co.webhook
  alter column unit_co_id type text;

alter table users.user
  alter column created_at
    set default now();

alter table users.user
  add column updated_at timestamptz not null default now();
create trigger on_update__user
  before update
  on users.user
  for each row
execute procedure updated();

alter table users.user
  alter column email_address type text;

alter table users.user
  alter column first_name type text;

alter table users.user
  alter column last_name type text;

alter table users.user
  alter column profile_photo_url type text;
