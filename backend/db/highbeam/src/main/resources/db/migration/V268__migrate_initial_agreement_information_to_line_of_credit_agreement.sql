insert into credit.line_of_credit_agreement (guid,
                                             created_at,
                                             updated_at,
                                             business_guid,
                                             line_of_credit_guid,
                                             data)
select gen_random_uuid(),
       now(),
       now(),
       line_of_credit.business_guid,
       line_of_credit.guid as line_of_credit_guid,
       (select jsonb_build_object(
                 'unsignedAgreementGuid', metadata ->> 'unsignedAgreementGuid',
                 'signedAgreementGuid', metadata ->> 'signedAgreementGuid',
                 'signatoryUserGuid', metadata ->> 'signatoryUserGuid',
                 'unsignedAgreementOpenedAt', metadata ->> 'unsignedAgreementOpenedAt',
                 'agreementSignedAt', metadata ->> 'agreementSignedAt',
                 'initialAgreement', metadata -> 'initialAgreement'
                 ))
from credit.line_of_credit;
