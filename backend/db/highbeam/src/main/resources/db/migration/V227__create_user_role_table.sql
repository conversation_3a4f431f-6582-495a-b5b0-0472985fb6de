create table auth.user_role
(
  guid          uuid
    constraint pkey__user_role primary key,
  created_at    timestamptz not null default now(),
  updated_at    timestamptz not null default now(),

  business_guid uuid        not null,
  name          text        not null,
  permissions   jsonb       not null
);

create trigger on_update__user_role
  before update
  on auth.user_role
  for each row
execute procedure updated();

create unique index uniq__user_role__name
  on auth.user_role (business_guid, lower(name));

insert into auth.user_role (guid, business_guid, name, permissions)
values (gen_random_uuid(), '00000000-0000-0000-0000-000000000000', 'Admin', '[
  "admin"
]'),
       (gen_random_uuid(), '00000000-0000-0000-0000-000000000000', 'AP user', '[
         "business:read",
         "paymentApproval:create",
         "paymentApproval:list",
         "paymentApproval:read"
       ]');
