create table business.referral_link
(
  guid          uuid
    constraint pkey__referral_link primary key,
  created_at    timestamptz not null default now(),
  updated_at    timestamptz not null default now(),

  slug          text        not null,
  business_guid uuid,
  sender_name   text,

  constraint fk__referral_link__business_guid foreign key (business_guid)
    references business.business (guid)
    on delete set null,

  constraint uniq__referral_link__slug
    unique (slug),
  constraint uniq__referral_link__business_guid
    unique (business_guid)
);

create trigger on_update__referral_link
  before update
  on business.referral_link
  for each row
execute procedure updated();
