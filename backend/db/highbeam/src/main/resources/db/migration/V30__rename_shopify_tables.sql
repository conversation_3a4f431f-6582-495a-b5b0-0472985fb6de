-- pending_shopify_connection

alter table connect.pending_shopify_auth
  rename to pending_shopify_connection;

alter table connect.pending_shopify_connection
  rename constraint pkey__pending_shopify_auth
    to pkey__pending_shopify_connection;

alter index connect.idx__pending_shopify_auth__business_guid
  rename to idx__pending_shopify_connection__business_guid;

alter index connect.idx__pending_shopify_auth__shop_name
  rename to idx__pending_shopify_connection__shop_name;

alter trigger on_update__pending_shopify_auth
  on connect.pending_shopify_connection
  rename to on_update__pending_shopify_connection;

-- shopify_connection

alter table connect.shopify_auth
  rename to shopify_connection;

alter table connect.shopify_connection
  rename constraint pkey__shopify_auth
    to pkey__shopify_connection;

alter table connect.shopify_connection
  rename constraint uniq__shopify_auth__business_guid__shop_name
    to uniq__shopify_connection__business_guid__shop_name;

alter index connect.idx__shopify_auth__business
  rename to idx__shopify_connection__business;

alter index connect.idx__shopify_auth__shop_name
  rename to idx__shopify_connection__shop_name;

alter trigger on_update__shopify_auth
  on connect.shopify_connection
  rename to on_update__shopify_connection;
