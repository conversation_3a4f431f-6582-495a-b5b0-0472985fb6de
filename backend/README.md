# Highbeam Backend

## Setup

### Set up Infrastructure
Follow the instructions in the
   [infrastructure repository](https://github.com/highbeamco/infrastructure)
   to [install and configure the GCP CLI](https://github.com/highbeamco/infrastructure#install-and-configure-the-gcp-cli)
   and [add variables to your dotfiles](https://github.com/highbeamco/infrastructure#add-variables-to-your-dotfiles).
   Don't worry about the rest of the instructions.

### Set Up Postgres

1. Add this block to your `.zshrc`:
    ```shell
    # Test DB management

    # Env vars for local testing backend-v1
    export HIGHBEAM_TEST_POSTGRES_USERNAME="highbeam"
    export HIGHBEAM_TEST_POSTGRES_PASSWORD="highbeam"
    # Env vars for local testing backend-v2
    export HIGHBEAM_TEST_SQL_USERNAME="highbeam"
    export HIGHBEAM_TEST_SQL_PASSWORD="highbeam"
    export ISOLATED_DATA_VIEW_TEST_SQL_USERNAME="highbeam"
    export ISOLATED_DATA_VIEW_TEST_SQL_PASSWORD="highbeam"

   # Download and launch the test dependency containers on Docker.
   # You should only need to do this once.
   function init_test_containers() {
       docker pull postgres:17;
       docker run \
           --name test-postgres \
           -e POSTGRES_USER=highbeam \
           -e POSTGRES_PASSWORD=highbeam \
           -e POSTGRES_DB=highbeam \
           -v highbeam_pgdata:/var/lib/postgresql/data \
           -p 5432:5432 \
           --restart always \
           -d pgvector/pgvector:pg17 \
           -c max_connections=200;
       docker pull redis:7;
       docker run \
           --name test-redis \
           -p 6379:6379 \
           --restart always \
           -d redis:7;
   }

    # Re-initialize test database states.
    # This is useful if your local migrations get into a bad state, or when switching
    # between running tests on backend-v1 and backend-v2.
    function reset_db() {
        docker exec test-postgres bash -c "psql -U highbeam -c 'DROP DATABASE IF EXISTS highbeam_test;' && psql -U highbeam -c 'CREATE DATABASE highbeam_test;' && psql -U highbeam -c 'DROP DATABASE IF EXISTS isolated_data_view_test;' && psql -U highbeam -c 'CREATE DATABASE isolated_data_view_test;'"
    }
    ```
2. Reload your shell to pull in the new changes
3. Restart IntelliJ to load new environment variables
4. Initialize the DB container with `init_test_db_container`
5. Initialize the local db with `reset_db`
6. Prefer using pgcli over psql for interacting with the database:
    ```shell
    brew install pgcli
    ```

### Set up IDE

#### Download IntelliJ IDEA

https://www.jetbrains.com/idea/download

> More information here: https://www.notion.so/highbeamco/IntelliJ-f3dd351283a54493ba24b006f2fe3576

Open this directory in IntelliJ.


#### Authenticate gcloud

```sh
gcloud auth application-default login
```


### Troubleshooting

#### psql: command not found
If you get an error stating *psql: command not found*, you may have to export the path for the postgres installation
1. Find where homebrew installed Postgres. It's frequently in `/Library/PostgreSQL/{version}/bin` or `/opt/homebrew/Cellar/postgresql@{major version}/{version}/bin` depending on how you installed Postgres.
2. Add an export to your dotfile. For example, I added this to my `.zprofile`
    ```
    # Postgres installation path
    export PATH=/opt/homebrew/Cellar/postgresql@13/13.7/bin:$PATH
    ```
3. Restart your shell or run:
    ```shell
    source ~/.zprofile
    ```
4. Verify you can run postgres and connect to your dev DB
    ```shell
    $ pgcli -U highbeam highbeam
    Server: PostgreSQL 13.7
    Version: 3.4.1
    Home: http://pgcli.com
    highbeam@(none):highbeam>
    ```
5. Type `exit` to exit pgcli

## Useful commands

### Run local backend connected to staging db (preferred)

If you don't need to make schema changes or make significant data changes,
you should connect to the staging database directly instead of maintaining
a local version of the database.

Migrations won't run when connected to the staging database, in fact,
we lack permissions to do any data definition manipulation (DDL) and so
you don't need to worry about applying them accidentally.

#### Run via IntelliJ (preferred)

Use the `Run backend (local-staging)` configuration (from the top-right in IntelliJ).

#### Run via command line

```shell
HIGHBEAM_CONFIG=local-staging \
HIGHBEAM_POSTGRES_JDBC_URL=$HIGHBEAM_STAGING_POSTGRES_JDBC_URL \
HIGHBEAM_POSTGRES_USERNAME=<username>@highbeam.co \
./gradlew server:backend:run
```

#### Interact with the staging database

```shell
highbeam_staging_db
```

### Run local backend connected to local db

In order to be able to run database migrations, or make other significant data
changes, you should connect to the local database.

#### Run via IntelliJ

Use the `Run backend (dev)` configuration (from the top-right in IntelliJ).

#### Run via command line

```shell
HIGHBEAM_CONFIG=dev ./gradlew server:backend:run
```

#### Interact with the local database

```shell
pgcli -U highbeam highbeam
```

### Run checks (test/lint)

With Gradle, "checks" include both tests and lint.

```shell
./gradlew check # Runs checks for all Gradle modules
./gradlew common:validation:check # Runs checks for the common/validation Gradle module
./gradlew common:validation:test # Runs tests for the common/validation module
./gradlew common:validation:detekt # Runs lint for the common/validation module
```

Note, it's far easier to run tests (including individual tests) through IntelliJ
by clicking the green play button next to a test or test class.
You can also run all of the tests in a Gradle module
by right clicking on it and selecting "run tests".

### Migrations

Database migrations live in the [db/highbeam](./db/highbeam) folder.
To **add a new migration**, create a new SQL file with an incremented version number in the name.
To **run migrations**, just [run the backend locally](#run-backend-locally).
To **generate a new checksum**, run `./scripts/migrations-checksum.sh generate` from the root of the repository.

**Do not modify existing merged migration files.**
If you want to reset your local database completely (NOTE: YOU WILL LOSE DATA),
run `dropdb highbeam && createdb highbeam -U highbeam`,
or `dropdb highbeam_test && createdb highbeam_test -U highbeam` for the test database.

### Clean up

If your build gets into a weird state or things are not making sense, try a `./gradlew clean`.

### pgcli/psql commands

We use schemas, here's some helpful commands:

```shell
# connect to the db
$ pgcli -U highbeam highbeam

# display all schemas
> \dn

# display all tables in the businesses schema
> \dt businesses.*

# display all fields in the business table
> \d businesses.business

# query the business table
> select * from businesses.business limit 1;
```

## Processes

* [Documentation for upgrading dependencies](./buildSrc/UPGRADING_DEPENDENCIES.md)
