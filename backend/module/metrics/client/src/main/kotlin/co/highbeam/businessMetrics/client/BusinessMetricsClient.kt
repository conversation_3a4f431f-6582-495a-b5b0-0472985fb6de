package co.highbeam.businessMetrics.client

import co.highbeam.businessMetrics.api.BusinessMetricsApi
import co.highbeam.businessMetrics.feature.METRICS_FEATURE
import co.highbeam.businessMetrics.rep.BusinessMetricsRep
import co.highbeam.client.HttpClient
import com.google.inject.Inject
import com.google.inject.name.Named

class BusinessMetricsClient @Inject constructor(
  @Named(METRICS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: BusinessMetricsApi.GetLatestByBusiness): BusinessMetricsRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMetricsApi.ManualRefresh): Unit =
    httpClient.request(endpoint).readValue()
}
