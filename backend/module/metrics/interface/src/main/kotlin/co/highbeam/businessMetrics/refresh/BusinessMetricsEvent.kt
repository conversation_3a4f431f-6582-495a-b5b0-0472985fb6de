package co.highbeam.businessMetrics.refresh

import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import com.fasterxml.jackson.annotation.JsonIgnore
import java.time.LocalDate
import java.util.UUID

data class BusinessMetricsEvent(
  val businessGuids: List<UUID>,
  val dates: List<LocalDate>,
  val metricSelection: MetricSelection,
  val propagate: Boolean,
) : CreatorRep {
  data class Single(
    val businessGuid: UUID,
    val date: LocalDate,
    val metricSelection: MetricSelection,
    val propagate: Boolean,
  ) : CreatorRep {
    override fun validate(): RepValidation =
      RepValidation.none()

    fun toEvent(): BusinessMetricsEvent =
      BusinessMetricsEvent(
        businessGuids = listOf(businessGuid),
        dates = listOf(date),
        metricSelection = metricSelection,
        propagate = propagate,
      )
  }

  override fun validate(): RepValidation =
    RepValidation.none()

  @JsonIgnore
  fun isSingle(): Boolean =
    businessGuids.size == 1 && dates.size == 1

  fun toSingle(): Single =
    Single(
      businessGuid = businessGuids.single(),
      date = dates.single(),
      metricSelection = metricSelection,
      propagate = propagate,
    )

  inline fun forEach(action: (Single) -> Unit) {
    dates.forEach { date ->
      businessGuids.forEach { businessGuid ->
        val single = Single(
          businessGuid = businessGuid,
          date = date,
          metricSelection = metricSelection,
          propagate = propagate,
        )
        action(single)
      }
    }
  }
}
