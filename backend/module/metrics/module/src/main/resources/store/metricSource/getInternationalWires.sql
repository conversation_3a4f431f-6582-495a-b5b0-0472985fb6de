select count(*)                                                as count,
       coalesce(sum(pm.currency_cloud_payment_amount /
                    coalesce(nullif(pm.currency_cloud_payment_client_buy_rate, 0), 1) +
                    pm.currency_cloud_payment_fee::bigint), 0) as volume
from transfer.payment_metadata pm
where pm.business_guid = :businessGuid
  and pm.created_at::date = :date
  and pm.currency_cloud_payment_status in ('ready_to_send', 'completed')
