package co.highbeam.businessMetrics.metric

import co.highbeam.businessMetrics.MetricCategory
import co.highbeam.businessMetrics.metricSource.HighbeamPayeeSource

data class HighbeamPayeesMetric(
  val activePayees: Int,
  val deletedPayees: Int,
  val totalPayees: Int,
) : Metric() {
  internal companion object Description : Metric.Description<HighbeamPayeesMetric>(
    type = HighbeamPayeesMetric::class,
    category = MetricCategory.Product,
  ) {
    override val source = HighbeamPayeeSource::class
  }
}
