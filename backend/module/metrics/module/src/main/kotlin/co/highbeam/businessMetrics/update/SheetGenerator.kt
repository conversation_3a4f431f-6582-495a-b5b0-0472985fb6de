package co.highbeam.businessMetrics.update

import co.highbeam.businessMetrics.update.SheetGenerator.Input
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters

internal abstract class SheetGenerator<I : Input, R : Any, T : Any> {
  abstract class Input {
    abstract val dateRange: ClosedRange<LocalDate>
  }

  fun create(input: I): List<List<String>> {
    val results = results(input)
    val aggregate = transform(input, results)
    return format(input, aggregate)
  }

  abstract fun results(input: I): List<R>

  abstract fun transform(input: I, results: List<R>): List<T>

  abstract fun format(input: I, aggregate: List<T>): List<List<String>>
}

internal fun firstDayOfWeek(date: LocalDate, latest: LocalDate): LocalDate {
  val lastDayOfWeek = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.SATURDAY))
  if (lastDayOfWeek.isAfter(latest)) {
    return latest
  }
  return lastDayOfWeek
}

internal fun lastDayOfWeek(date: LocalDate, latest: LocalDate): LocalDate {
  val lastDayOfWeek = date.with(TemporalAdjusters.nextOrSame(DayOfWeek.FRIDAY))
  if (lastDayOfWeek.isAfter(latest)) {
    return latest
  }
  return lastDayOfWeek
}

internal fun lastDayOfMonth(date: LocalDate, latest: LocalDate): LocalDate {
  val lastDayOfMonth = date.withDayOfMonth(1).plusMonths(1).minusDays(1)
  if (lastDayOfMonth.isAfter(latest)) {
    return latest
  }
  return lastDayOfMonth
}
