package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.ConnectedAccountsMetric
import co.highbeam.money.Balance
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

internal class ConnectedAccountsSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<ConnectedAccountsMetric>, SqlStore(jdbi) {
  override suspend fun get(businessGuid: UUID, date: LocalDate): ConnectedAccountsMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getConnectedAccountsBalance.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      val balance = query.mapToMap().single().let { Balance.fromCents(it["balance"] as BigDecimal) }
      return@handle ConnectedAccountsMetric(balance)
    }
}
