package co.highbeam.businessMetrics

import co.highbeam.businessMetrics.endpoint.BusinessMetricsManualRefresh
import co.highbeam.businessMetrics.endpoint.CreateBusinessMetricsSlackTask
import co.highbeam.businessMetrics.endpoint.ExecuteBusinessMetricsSlackTask
import co.highbeam.businessMetrics.endpoint.GetLatestBusinessMetricsByBusiness
import co.highbeam.businessMetrics.refresh.BusinessMetricsPublisherFactory
import co.highbeam.businessMetrics.refresh.BusinessMetricsService
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import co.highbeam.googleSheets.SheetsFactory
import com.google.api.services.sheets.v4.Sheets
import com.google.inject.Injector
import com.google.inject.Key
import com.google.inject.TypeLiteral

class BusinessMetricsFeature(
  private val config: BusinessMetricsConfig,
) : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()

  override fun bind() {
    bindConfig()
    bindApiEndpoints()
    bindPublishers()
    bindListeners()
    bindSheets()
  }

  private fun bindConfig() {
    bind(BusinessMetricsConfig::class.java).toInstance(config)
  }

  private fun bindApiEndpoints() {
    bind(GetLatestBusinessMetricsByBusiness::class.java).asEagerSingleton()
    bind(BusinessMetricsManualRefresh::class.java).asEagerSingleton()
    bind(CreateBusinessMetricsSlackTask::class.java).asEagerSingleton()
    bind(ExecuteBusinessMetricsSlackTask::class.java).asEagerSingleton()
  }

  private fun bindPublishers() {
    BusinessMetricsPublisherFactory.bind(binder(), publishers)
  }

  private fun bindListeners() {
    bind(BusinessMetricsService::class.java)
  }

  private fun bindSheets() {
    if (config.enabled) {
      bind(Sheets::class.java).toInstance(SheetsFactory.create())
    }
  }

  override fun stop(injector: Injector) {
    publishers.forEach { injector.getInstance(Key.get(it)).close() }
  }
}
