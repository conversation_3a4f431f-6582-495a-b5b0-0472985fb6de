package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.ChurnedCustomerMetric
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.LocalDate
import java.util.UUID

internal class ChurnedCustomerSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<ChurnedCustomerMetric>, SqlStore(jdbi) {
  override suspend fun get(businessGuid: UUID, date: LocalDate): ChurnedCustomerMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getChurnedCustomer.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      val isChurned = query.mapToMap().single()
        .let { it["churned"] as <PERSON><PERSON><PERSON> }
      return@handle ChurnedCustomerMetric(isChurned)
    }
}
