package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.HighbeamAccountsMetric
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.LocalDate
import java.util.UUID

internal class HighbeamAccountsSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<HighbeamAccountsMetric>, SqlStore(jdbi) {
  override suspend fun get(businessGuid: UUID, date: LocalDate): HighbeamAccountsMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getHighbeamAccounts.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      val accounts = query.mapTo(HighbeamAccountsMetric.HighbeamAccount::class.java).toList()
      return@handle HighbeamAccountsMetric(accounts)
    }
}
