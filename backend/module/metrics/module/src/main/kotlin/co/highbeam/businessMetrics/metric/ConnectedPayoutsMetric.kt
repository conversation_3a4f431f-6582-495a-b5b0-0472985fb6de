package co.highbeam.businessMetrics.metric

import co.highbeam.businessMetrics.MetricCategory
import co.highbeam.businessMetrics.metricSource.ConnectedPayoutsSource
import co.highbeam.money.Balance

data class ConnectedPayoutsMetric(
  val payouts: List<ConnectedPayout>,
) : Metric() {
  internal companion object Description : Metric.Description<ConnectedPayoutsMetric>(
    type = ConnectedPayoutsMetric::class,
    category = MetricCategory.Product,
  ) {
    override val source = ConnectedPayoutsSource::class
  }

  data class ConnectedPayout(
    val channel: SalesChannel,
    val volume: Balance,
  )

  private val shopify: List<ConnectedPayout> = payouts.filter { it.channel == "SHOPIFY" }
  private val amazon: List<ConnectedPayout> = payouts.filter { it.channel == "AMAZON" }
  private val other: List<ConnectedPayout> = payouts.filter { it !in shopify && it !in amazon }

  val shopifyVolume: Balance get() = sumVolumes(shopify)
  val shopifyCount: Int get() = shopify.size

  val amazonVolume: Balance get() = sumVolumes(amazon)
  val amazonCount: Int get() = amazon.size

  val otherVolume: Balance get() = sumVolumes(other)
  val otherCount: Int get() = other.size

  val totalVolume: Balance get() = sumVolumes(payouts)
  val totalCount: Int get() = payouts.size
}

private fun sumVolumes(payouts: List<ConnectedPayoutsMetric.ConnectedPayout>): Balance =
  payouts.fold(Balance.ZERO) { acc, payout -> acc + payout.volume }
