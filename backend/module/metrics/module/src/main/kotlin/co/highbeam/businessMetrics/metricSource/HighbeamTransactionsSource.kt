package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.HighbeamTransactionsMetric
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.LocalDate
import java.util.UUID

internal class HighbeamTransactionsSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<HighbeamTransactionsMetric>, SqlStore(jdbi) {

  override suspend fun get(businessGuid: UUID, date: LocalDate): HighbeamTransactionsMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getTransactionVolume.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      val transactions = query.mapTo(
        HighbeamTransactionsMetric.HighbeamTransactions::class.java
      ).toList()
      return@handle HighbeamTransactionsMetric(transactions)
    }
}
