package co.highbeam.businessMetrics.endpoint

import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import highbeam.task.TaskCreator
import highbeam.task.auth.AuthCloudScheduler
import io.ktor.server.application.ApplicationCall
import co.highbeam.businessMetrics.api.BusinessMetricsSlackTaskApi as Api

internal class CreateBusinessMetricsSlackTask @Inject constructor(
  private val authCloudScheduler: AuthCloudScheduler.Provider,
  private val taskCreator: TaskCreator,
) : EndpointHandler<Api.Create, Unit>(
  template = Api.Create::class.template(),
) {

  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(call.body())

  override fun loggingContext(endpoint: Api.Create) = super.loggingContext(endpoint) +
    mapOf("date" to endpoint.params.date.toString())

  override suspend fun Handler.handle(endpoint: Api.Create): Unit {
    auth(authCloudScheduler())
    taskCreator.create(Api.Execute(endpoint.params), "business-metrics-slack")
  }
}
