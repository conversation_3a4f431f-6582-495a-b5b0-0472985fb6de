package co.highbeam.businessMetrics.metric

import co.highbeam.businessMetrics.MetricCategory
import co.highbeam.businessMetrics.metricSource.LineOfCreditSource
import co.highbeam.money.Balance
import co.highbeam.money.Money

data class LineOfCreditMetric(
  val balance: Balance,
  val limit: Money,
  val revenue: Money,
  val averagePaidLoanAgeInWeeks: Long,
  val maxOpenLoanAgeInWeeks: Long,
  val outstandingMoreThanThresholdLoanAge: Money,
) : Metric() {
  val creditUtilizationPercentage: Long
    get() {
      if (limit <= Money.ZERO) return 0
      return -balance.rawCents * 100 / limit.rawCents
    }

  internal companion object Description : Metric.Description<LineOfCreditMetric>(
    type = LineOfCreditMetric::class,
    category = MetricCategory.Product,
  ) {
    override val source = LineOfCreditSource::class
  }
}
