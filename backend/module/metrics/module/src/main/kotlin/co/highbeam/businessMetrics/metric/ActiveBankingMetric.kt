package co.highbeam.businessMetrics.metric

import co.highbeam.businessMetrics.MetricCategory
import co.highbeam.businessMetrics.metricSource.ActiveBankingSource
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

data class ActiveBankingMetric @Json<PERSON><PERSON>(mode = JsonCreator.Mode.DELEGATING) constructor(
  @JsonValue val value: Boolean,
) : Metric() {
  internal companion object Description : Metric.Description<ActiveBankingMetric>(
    type = ActiveBankingMetric::class,
    category = MetricCategory.Activation,
  ) {
    override val source = ActiveBankingSource::class
  }
}
