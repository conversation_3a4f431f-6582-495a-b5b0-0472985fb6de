package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.EngagedCustomerMetric
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.LocalDate
import java.util.UUID

internal class EngagedCustomerSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<EngagedCustomerMetric>, SqlStore(jdbi) {
  override suspend fun get(businessGuid: UUID, date: LocalDate): EngagedCustomerMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getEngagedCustomer.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      val isEngaged = query.mapToMap().single()
        .let { it["engaged"] as <PERSON><PERSON><PERSON> }
      return@handle EngagedCustomerMetric(isEngaged)
    }
}
