package co.highbeam.businessMetrics.metricSource

import co.highbeam.businessMetrics.metric.TransfersMetric
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.LocalDate
import java.util.UUID

internal class TransfersSource @Inject constructor(
  jdbi: Jdbi,
) : MetricSource<TransfersMetric>, SqlStore(jdbi) {
  override suspend fun get(businessGuid: UUID, date: LocalDate): TransfersMetric =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        path = "store/metricSource/getTransfers.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("date", date)
      return@handle query.mapTo(TransfersMetric::class.java).single()
    }
}
