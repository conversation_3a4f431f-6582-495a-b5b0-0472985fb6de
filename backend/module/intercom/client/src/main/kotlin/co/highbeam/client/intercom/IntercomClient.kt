package co.highbeam.client.intercom

import co.highbeam.api.intercom.IntercomApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.intercom.INTERCOM_FEATURE
import co.highbeam.rep.intercom.IntercomMetadataRep
import com.google.inject.Inject
import com.google.inject.name.Named

class IntercomClient @Inject constructor(
  @Named(INTERCOM_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: IntercomApi.GetMetadata): IntercomMetadataRep =
    httpClient.request(endpoint).readValue()
}
