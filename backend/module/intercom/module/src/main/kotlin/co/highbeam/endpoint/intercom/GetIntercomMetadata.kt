package co.highbeam.endpoint.intercom

import co.highbeam.auth.auth.AuthUser
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.intercom.IntercomService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.intercom.IntercomApi as Api
import co.highbeam.rep.intercom.IntercomMetadataRep as Rep

internal class GetIntercomMetadata @Inject constructor(
  private val authUser: AuthUser.Provider,
  private val intercomService: IntercomService,
) : EndpointHandler<Api.GetMetadata, Rep>(
  template = Api.GetMetadata::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetMetadata =
    Api.GetMetadata(
      userGuid = call.getParam("userGuid"),
      businessGuid = call.getParam("businessGuid", optional = true),
    )

  override suspend fun Handler.handle(endpoint: Api.GetMetadata): Rep {
    auth(authUser(endpoint.userGuid))
    return intercomService.getMetadata(
      userGuid = endpoint.userGuid,
      businessGuid = endpoint.businessGuid,
    )
  }
}
