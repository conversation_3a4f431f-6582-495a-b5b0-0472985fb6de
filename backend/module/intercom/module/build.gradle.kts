plugins {
  id("highbeam-jvm")
}

dependencies {
  api(project(":common:config"))
  api(project(":common:hashing"))
  api(project(":common:rest-feature"))
  implementation(project(":module:business:business:client"))
  implementation(project(":module:highbeam:client"))
  api(project(":module:intercom:interface"))
  implementation(project(":module:metrics:client"))
  implementation(project(":module:user:client"))

  testImplementation(project(":common:integration-testing"))
  testImplementation(project(":common:rest-feature:testing"))
  testImplementation(project(":module:intercom:client"))
}
