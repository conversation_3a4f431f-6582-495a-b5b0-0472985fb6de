package co.highbeam.capital.reporting.service

import co.highbeam.capital.reporting.rep.LoanTapeRep
import com.google.inject.ImplementedBy
import java.time.LocalDate
import java.util.UUID

@Deprecated("Rethink when used properly")
@ImplementedBy(LoanTapeServiceImpl::class)
interface LoanTapeService {
  suspend fun monthly(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    snapshotDate: LocalDate,
  ): List<LoanTapeRep>
}
