package co.highbeam.capital.reporting.tape.source.cashAccessTransactions

import co.highbeam.money.Money
import java.time.ZonedDateTime

data class MatchedTransaction(
  val amount: Money,
  val type: Type,
  val drawdownDate: ZonedDateTime,
  val drawdownId: String,
  val repaymentDate: ZonedDateTime?,
  val repaymentId: String?,
) {
  enum class Type { Principle, Fees }

  val isPaid: Boolean
    get() = repaymentDate != null
}

