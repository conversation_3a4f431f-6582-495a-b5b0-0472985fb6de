package co.highbeam.capital.reporting.tape.data

import co.highbeam.api.business.BusinessApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi
import co.highbeam.client.business.BusinessClient
import com.google.inject.Inject
import java.time.LocalDate
import java.util.UUID

class TapeDataAggregatorImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val capitalAccountClient: CapitalAccountClient,
  private val chargeCardAccountClient: ChargeCardAccountClient,
) : TapeDataAggregator {
  override suspend operator fun invoke(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    snapshotDate: LocalDate,
  ): TapeData {
    val business = businessClient.request(BusinessApi.Get(businessGuid))
    val capitalAccount = capitalAccountClient(
      CapitalAccountApi.Get(
        businessGuid = businessGuid,
        guid = capitalAccountGuid
      )
    )

    val chargeCardAccount = if (capitalAccount?.type == CapitalAccountRep.Type.ChargeCardOnly) {
      chargeCardAccountClient(
        ChargeCardAccountApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
        )
      )
    } else {
      null
    }

    return TapeData(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      snapshotDate = snapshotDate,
      business = business,
      capitalAccount = capitalAccount,
      chargeCardAccount = chargeCardAccount,
    )
  }
}
