package co.highbeam.capital.reporting.tape.source.cashAccessTransactions

import co.highbeam.capital.reporting.tape.data.TapeData
import co.highbeam.capital.transaction.config.CapitalTransactionConfig
import co.unit.client.UnitCoClient
import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoTransactionRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.toList
import java.time.ZoneId

class SubledgerTransactions @Inject constructor(
  private val unitCoClient: UnitCoClient,
  private val config: CapitalTransactionConfig,
  private val objectMapper: ObjectMapper,
) {
  @OptIn(FlowPreview::class)
  suspend operator fun invoke(tapeData: TapeData): List<UnitCoTransactionRep> {
    return unitCoClient.transaction.list(
      customerId = null,
      accountId = null,
      accountIds = config.loanTapeSubledgerUnitAccountIds.toList(),
      query = null,
      from = null,
      to = tapeData.snapshotDate.plusDays(1).atStartOfDay(ZoneId.of("America/New_York")),
      tags = mapOf(
        "creditAccountGuid" to tapeData.capitalAccountGuid.toString(),
      )
    ).flatMapConcat { toUnitCoTransactionReps(it).asFlow() }
      .toList()
  }

  private fun toUnitCoTransactionReps(it: JsonNode): List<UnitCoTransactionRep> =
    objectMapper.convertValue<DataWrapper<List<UnitCoTransactionRep>>>(it).data
}
