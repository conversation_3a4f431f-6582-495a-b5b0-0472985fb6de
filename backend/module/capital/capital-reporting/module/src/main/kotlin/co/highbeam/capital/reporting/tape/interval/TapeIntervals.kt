package co.highbeam.capital.reporting.tape.interval

import co.highbeam.capital.reporting.tape.data.TapeData
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters.previous
import java.time.temporal.TemporalAdjusters.previousOrSame

object TapeIntervals {
  data class Interval(
    override val start: LocalDate,
    override val endInclusive: LocalDate
  ) : ClosedRange<LocalDate>

  fun TapeData.monthlyIntervals(): List<Interval> {
    val firstTransactionDate = firstTransactionDate() ?: return emptyList()
    return generateSequence(firstTransactionDate) { it.plusMonths(1).withDayOfMonth(1) }
      .map { start ->
        val end =
          start.withDayOfMonth(start.lengthOfMonth()).takeIf { it.isBefore(snapshotDate) }
            ?: snapshotDate
        Interval(start = start, endInclusive = end)
      }.takeWhile { it.start <= snapshotDate }.toList()
  }

  fun TapeData.weeklyIntervals(): List<Interval> {
    val firstTransactionDate = firstTransactionDate() ?: return emptyList()
    val sixteenWeeksAgo = snapshotDate.minusWeeks(16).with(previousOrSame(DayOfWeek.MONDAY))
    val startDate = if (firstTransactionDate.isBefore(sixteenWeeksAgo)) {
      sixteenWeeksAgo
    } else {
      firstTransactionDate
    }
    return generateSequence(startDate) {
      it.plusWeeks(1).with(previousOrSame(DayOfWeek.MONDAY))
    }.map { start ->
      val end = start.plusWeeks(1).with(previous(DayOfWeek.SUNDAY))
        .takeIf { it.isBefore(snapshotDate) } ?: snapshotDate
      Interval(start = start, endInclusive = end)
    }.takeWhile { it.start <= snapshotDate }.toList()
  }

  fun TapeData.dailyIntervals(): List<Interval> {
    val firstTransactionDate = firstTransactionDate() ?: return emptyList()
    val startDate = if (firstTransactionDate.isBefore(snapshotDate.minusDays(10))) {
      snapshotDate.minusDays(10)
    } else {
      firstTransactionDate
    }
    return generateSequence(startDate) { it.plusDays(1) }.map {
      Interval(start = it, endInclusive = it)
    }.takeWhile { it.start <= snapshotDate }.toList()
  }

  private fun TapeData.firstTransactionDate() =
    capitalAccount?.activatedAt?.withZoneSameInstant(ZoneId.of("America/New_York"))?.toLocalDate()
}
