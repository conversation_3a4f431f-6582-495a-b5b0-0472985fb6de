package co.highbeam.capital.reporting.tape.report

import co.highbeam.capital.reporting.tape.interval.TapeIntervals
import co.highbeam.money.Balance
import co.highbeam.money.Money
import java.math.BigDecimal
import java.math.MathContext
import java.time.LocalDate

data class LineTapeReport(
  val interval: TapeIntervals.Interval,
  val lineBeginningPrincipalBalance: Balance,
  val lineBeginningFeesBalance: Balance,
  val linePrincipalOriginated: Money,
  val linePrincipalRepaymentsPaid: Money,
  val lineFeesAccrued: Balance,
  val lineFeesPaid: Balance,
  val lineEndingPrincipalBalance: Balance,
  val lineEndingFeesBalance: Balance,
  val lineEndingStatus: Status? = null,
  val lineDefaultedAt: LocalDate? = null,
  val lineDelinquentAt: LocalDate? = null,
  val lineDailyAveragePrincipalBalance: Balance,
  val lineOldestUnpaidOriginationAt: LocalDate?,
  val lineRecentRepaymentPaid: LocalDate?,
  val lineEndingApr: BigDecimal,
  val lineEndingTargetRepaymentDays: Int,
) {
  enum class Status { Current, Delinquent, Default }

  val linePaymentRate: BigDecimal?
    get() = lineDailyAveragePrincipalBalance.takeIf { it > Balance.ZERO }
      ?.let {
        BigDecimal(linePrincipalRepaymentsPaid.rawCents)
          .divide(BigDecimal(it.rawCents), MathContext.DECIMAL128)
      }
}
