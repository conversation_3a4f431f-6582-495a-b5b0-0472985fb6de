package co.highbeam.capital.reporting.mapper

import co.highbeam.capital.reporting.rep.AccountTapeRep
import co.highbeam.capital.reporting.tape.report.AccountTapeReport
import co.highbeam.capital.reporting.tape.report.ChargeCardTapeReport
import co.highbeam.capital.reporting.tape.report.LineTapeReport
import co.highbeam.money.Balance
import co.highbeam.money.Money
import com.google.inject.Inject
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode

class AccountTapeMapper @Inject constructor() {
  fun toRep(
    accountTape: AccountTapeReport,
    lineTape: LineTapeReport,
    cardTape: ChargeCardTapeReport?,
  ): AccountTapeRep = AccountTapeRep(
    snapshotBeginningAt = accountTape.interval.start,
    snapshotEndingAt = accountTape.interval.endInclusive,
    businessName = accountTape.businessName,
    businessGuid = accountTape.businessGuid,
    capitalAccountGuid = accountTape.capitalAccountGuid,
    accountEndingLimit = accountTape.accountEndingLimit.toDollars(),
    accountEndingStatus = status(accountTape, lineTape, cardTape),
    accountActivatedAt = accountTape.accountActivatedAt,
    accountDefaultedAt = lineTape.lineDefaultedAt,
    accountTerminatedAt = accountTape.accountTerminatedAt,
    accountType = accountTape.accountType.name,
    accountDailyAveragePrincipalBalance = lineTape.lineDailyAveragePrincipalBalance.toDollars() +
      (cardTape?.cardDailyAveragePrincipalBalance?.toDollars() ?: BigDecimal.ZERO),
    lineBeginningPrincipalBalance = lineTape.lineBeginningPrincipalBalance.toDollars(),
    lineBeginningFeesBalance = lineTape.lineBeginningFeesBalance.toDollars(),
    linePrincipalOriginated = lineTape.linePrincipalOriginated.toDollars(),
    linePrincipalRepaymentsPaid = lineTape.linePrincipalRepaymentsPaid.toDollars(),
    lineFeesAccrued = lineTape.lineFeesAccrued.toDollars(),
    lineFeesPaid = lineTape.lineFeesPaid.toDollars(),
    lineEndingFeesBalance = lineTape.lineEndingFeesBalance.toDollars(),
    lineEndingPrincipalBalance = lineTape.lineEndingPrincipalBalance.toDollars(),
    lineEndingStatus = lineTape.lineEndingStatus?.name,
    lineDailyAveragePrincipalBalance = lineTape.lineDailyAveragePrincipalBalance.toDollars(),
    linePaymentRate = lineTape.linePaymentRate,
    lineOldestUnpaidOriginationAt = lineTape.lineOldestUnpaidOriginationAt,
    lineEndingApr = lineTape.lineEndingApr,
    lineEndingTargetRepaymentDays = lineTape.lineEndingTargetRepaymentDays,
    cardBeginningPrincipalBalance = cardTape?.cardBeginningPrincipalBalance?.toDollars(),
    cardPrincipalOriginated = cardTape?.cardPrincipalOriginated?.toDollars(),
    cardPrincipalRepaymentsPaid = cardTape?.cardPrincipalRepaymentsPaid?.toDollars(),
    cardNetInterchangeAccrued = cardTape?.cardNetInterchangeAccrued?.toDollars(),
    cardRewardsAccrued = cardTape?.cardRewardsAccrued?.toDollars(),
    cardEndingPrincipalBalance = cardTape?.cardEndingPrincipalBalance?.toDollars(),
    cardEndingStatus = cardTape?.cardEndingStatus?.name,
    cardDailyAveragePrincipalBalance = cardTape?.cardDailyAveragePrincipalBalance?.toDollars(),
    accountPaymentRate = accountPaymentRate(cardTape, lineTape),
    accountDelinquentAt = lineTape.lineDelinquentAt,
  )

  private fun status(
    accountTape: AccountTapeReport,
    lineTape: LineTapeReport,
    cardTape: ChargeCardTapeReport?,
  ): String? {
    val isTerminated = accountTape.accountTerminatedAt != null

    if (isTerminated) {
      val hasZeroBalance =
        lineTape.lineEndingPrincipalBalance <= Balance.ZERO &&
          (cardTape?.cardEndingPrincipalBalance ?: Balance.ZERO) <= Balance.ZERO
      return if (hasZeroBalance) {
        AccountTapeReport.Status.Closed.name
      } else {
        AccountTapeReport.Status.ChargedOff.name
      }
    }

    return lineTape.lineEndingStatus?.name
  }


  private fun accountPaymentRate(
    cardTape: ChargeCardTapeReport?,
    lineTape: LineTapeReport,
  ): BigDecimal? {
    if (cardTape == null) return lineTape.linePaymentRate

    val totalRepayment = BigDecimal(
      cardTape.cardPrincipalRepaymentsPaid.rawCents -
        lineTape.linePrincipalOriginated.rawCents +
        lineTape.linePrincipalRepaymentsPaid.rawCents
    )
    val averageBalance = BigDecimal(
      lineTape.lineDailyAveragePrincipalBalance.rawCents +
        cardTape.cardDailyAveragePrincipalBalance.rawCents
    )

    return averageBalance.takeIf { it > BigDecimal.ZERO }
      ?.let { totalRepayment.divide(averageBalance, MathContext.DECIMAL128) }
  }

  private fun Balance.toDollars(): BigDecimal =
    BigDecimal(rawCents).divide(BigDecimal(100), MathContext.DECIMAL128)
      .setScale(2, RoundingMode.HALF_UP)

  private fun Money.toDollars(): BigDecimal =
    BigDecimal(rawCents).divide(BigDecimal(100), MathContext.DECIMAL128)
      .setScale(2, RoundingMode.HALF_UP)
}
