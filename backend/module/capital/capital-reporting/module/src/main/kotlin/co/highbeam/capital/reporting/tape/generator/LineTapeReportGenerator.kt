package co.highbeam.capital.reporting.tape.generator

import co.highbeam.capital.reporting.tape.interval.TapeIntervals
import co.highbeam.capital.reporting.tape.report.LineTapeReport
import co.highbeam.capital.reporting.tape.source.capitalAccount.CapitalAccountDetailsSource
import co.highbeam.capital.reporting.tape.source.capitalAccountFees.CapitalAccountFeesSource
import co.highbeam.capital.reporting.tape.source.cashAccessTransactions.CashAccessTransactionsSource
import co.highbeam.capital.reporting.tape.source.cashAccessTransactions.MatchedTransaction

class LineTapeReportGenerator(
  private val cashAccessTransactions: CashAccessTransactionsSource.Data,
  private val capitalAccountDetails: CapitalAccountDetailsSource.Data,
  private val capitalAccountFees: CapitalAccountFeesSource.Data,
) {
  operator fun invoke(interval: TapeIntervals.Interval): LineTapeReport {
    return LineTapeReport(
      interval = interval,
      lineBeginningPrincipalBalance = cashAccessTransactions.balance(
        asOf = interval.start.minusDays(1),
        type = MatchedTransaction.Type.Principle,
      ),
      lineBeginningFeesBalance = capitalAccountFees.interestFeesAccrued(
        sinceInclusive = null,
        untilInclusive = interval.start.minusDays(1),
        transactions = cashAccessTransactions,
        capitalAccountData = capitalAccountDetails,
      ) - capitalAccountFees.interestFeesPaid(interval.start.minusDays(1)) +
        cashAccessTransactions.balance(
          asOf = interval.start.minusDays(1),
          type = MatchedTransaction.Type.Fees,
        ),
      linePrincipalOriginated = cashAccessTransactions.totalDrawdown(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
        type = MatchedTransaction.Type.Principle,
      ),
      linePrincipalRepaymentsPaid = cashAccessTransactions.totalRepayment(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
        type = MatchedTransaction.Type.Principle,
      ),
      lineFeesAccrued = capitalAccountFees.interestFeesAccrued(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
        transactions = cashAccessTransactions,
        capitalAccountData = capitalAccountDetails,
      ) + capitalAccountFees.defaultFeesPaid(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
      ),
      lineFeesPaid = capitalAccountFees.interestFeesPaid(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
      ) + capitalAccountFees.defaultFeesPaid(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
      ) - cashAccessTransactions.totalNet(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
        type = MatchedTransaction.Type.Fees,
      ),
      lineEndingPrincipalBalance = cashAccessTransactions.balance(
        asOf = interval.endInclusive,
        type = MatchedTransaction.Type.Principle,
      ),
      lineEndingFeesBalance = capitalAccountFees.interestFeesAccrued(
        sinceInclusive = null,
        untilInclusive = interval.endInclusive,
        transactions = cashAccessTransactions,
        capitalAccountData = capitalAccountDetails,
      ) - capitalAccountFees.interestFeesPaid(interval.endInclusive) +
        cashAccessTransactions.balance(
          asOf = interval.endInclusive,
          type = MatchedTransaction.Type.Fees,
        ),
      lineDailyAveragePrincipalBalance = cashAccessTransactions.averageBalance(
        sinceInclusive = interval.start,
        untilInclusive = interval.endInclusive,
        type = MatchedTransaction.Type.Principle,
      ),
      lineOldestUnpaidOriginationAt = cashAccessTransactions.oldestUnpaidPrincipleOrigination(
        interval.endInclusive
      ),
      lineRecentRepaymentPaid = cashAccessTransactions.recentPrincipleRepayment(
        interval.endInclusive
      ),
      lineEndingApr = capitalAccountDetails.asOf(interval.endInclusive).netApr,
      lineEndingTargetRepaymentDays = capitalAccountDetails
        .asOf(interval.endInclusive).targetRepaymentDays,
    )
  }
}
