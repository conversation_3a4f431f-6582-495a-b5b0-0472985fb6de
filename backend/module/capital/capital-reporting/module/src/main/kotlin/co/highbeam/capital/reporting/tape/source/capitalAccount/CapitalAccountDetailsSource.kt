package co.highbeam.capital.reporting.tape.source.capitalAccount

import co.highbeam.capital.account.CapitalAccountInternalClient
import co.highbeam.capital.account.api.CapitalAccountInternalApi
import co.highbeam.capital.account.rep.CapitalAccountInternalRep
import co.highbeam.capital.reporting.tape.data.TapeData
import com.google.inject.Inject
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.TreeMap

class CapitalAccountDetailsSource @Inject constructor(
  private val capitalAccountInternalClient: CapitalAccountInternalClient,
) {
  suspend operator fun invoke(tapeData: TapeData): Data {
    val capitalAccountDetails = capitalAccountInternalClient(
      CapitalAccountInternalApi.GetAllDetails(
        businessGuid = tapeData.businessGuid,
        guid = tapeData.capitalAccountGuid,
        beforeOrEqual = tapeData.snapshotDate,
      ),
    )
    return Data(TreeMap(capitalAccountDetails))
  }

  class Data(
    private val raw: TreeMap<ZonedDateTime, CapitalAccountInternalRep.CapitalAccountDetails>,
  ) {
    fun asOf(date: LocalDate): CapitalAccountInternalRep.CapitalAccountDetails {
      return raw.floorEntry(date.plusDays(1).atStartOfDay(ZoneId.of("America/New_York"))).value
    }
  }
}
