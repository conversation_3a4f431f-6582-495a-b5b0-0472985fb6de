package co.highbeam.capital.reporting.rep

import co.highbeam.rep.CompleteRep
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class AccountTapeRep(
  val snapshotBeginningAt: LocalDate,
  val snapshotEndingAt: LocalDate,
  val businessName: String?,
  val businessGuid: UUID,
  val capitalAccountGuid: UUID,
  val accountEndingLimit: BigDecimal?,
  val accountEndingStatus: String?,
  val accountActivatedAt: LocalDate?,
  val accountDefaultedAt: LocalDate?,
  val accountTerminatedAt: LocalDate?,
  val accountType: String,
  val accountDailyAveragePrincipalBalance: BigDecimal,
  val lineBeginningPrincipalBalance: BigDecimal,
  val lineBeginningFeesBalance: BigDecimal,
  val linePrincipalOriginated: BigDecimal,
  val linePrincipalRepaymentsPaid: BigDecimal,
  val lineFeesAccrued: BigDecimal,
  val lineFeesPaid: BigDecimal,
  val lineEndingFeesBalance: BigDecimal,
  val lineEndingPrincipalBalance: BigDecimal,
  val lineEndingStatus: String?,
  val lineDailyAveragePrincipalBalance: BigDecimal,
  val linePaymentRate: BigDecimal?,
  val lineOldestUnpaidOriginationAt: LocalDate?,
  val lineEndingApr: BigDecimal,
  val lineEndingTargetRepaymentDays: Int,
  val cardBeginningPrincipalBalance: BigDecimal?,
  val cardPrincipalOriginated: BigDecimal?,
  val cardPrincipalRepaymentsPaid: BigDecimal?,
  val cardNetInterchangeAccrued: BigDecimal?,
  val cardRewardsAccrued: BigDecimal?,
  val cardEndingPrincipalBalance: BigDecimal?,
  val cardEndingStatus: String?,
  val cardDailyAveragePrincipalBalance: BigDecimal?,
  val accountPaymentRate: BigDecimal?,
  val accountDelinquentAt: LocalDate?,
) : CompleteRep
