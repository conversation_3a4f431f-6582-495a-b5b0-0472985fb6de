package co.highbeam.capital.reporting.rep

import co.highbeam.rep.CompleteRep
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@Deprecated("Rethink when used properly")
data class LoanTapeRep(
  val snapshotDate: LocalDate,
  val businessName: String?,
  val businessGuid: UUID,
  val creditAccountGuid: UUID,
  val limit: BigDecimal,
  val drawdownId: String,
  val drawdownDate: LocalDate,
  val drawdownType: String,
  val drawdownAmount: String,
  val apr: BigDecimal,
  val fixedFee: String,
  val targetRepaymentDays: Int,
  val contractTermDays: Int,
  val daysSinceDisbursement: Long,
  val daysSinceLastPayment: Long?,
  val principalOutstanding: String,
  val principalPaid: String,
  val feesDue: String,
  val feesCollected: String,
  val status: Status,
  val chargedOffPrincipal: String,
  val chargedOffDate: LocalDate?,
  val monthVintageTrim: LocalDate?,
  val monthVintage: LocalDate,
  val monthsOnBook: Long,
  val beginningBalance: String,
  val principalCollected: String,
) : CompleteRep {
  enum class Status {
    Active,
    Closed,
  }
}
