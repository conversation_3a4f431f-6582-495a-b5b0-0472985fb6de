package co.highbeam.capital.transaction.endpoint.task

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.event.TransactionHandler
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.api.CapitalTransactionHandlerTaskApi as Api
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall

internal class ExecuteCapitalTransactionHandleDrawdownTask @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val transactionHandler: TransactionHandler,
) : EndpointHandler<Api.HandleDrawdown, CapitalDrawdownTransactionRep>(
  template = Api.HandleDrawdown::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.HandleDrawdown =
    Api.HandleDrawdown(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
      rep = call.body()
    )

  override suspend fun Handler.handle(
    endpoint: Api.HandleDrawdown,
  ): CapitalDrawdownTransactionRep {
    auth(authPlatformRole(PlatformRole.CLOUD_TASKS))

    transactionHandler.handle(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
      event = endpoint.rep
    )

    return endpoint.rep
  }
}
