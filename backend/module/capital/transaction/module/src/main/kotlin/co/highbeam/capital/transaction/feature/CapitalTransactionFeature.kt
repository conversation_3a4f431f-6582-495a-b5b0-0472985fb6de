package co.highbeam.capital.transaction.feature

import co.highbeam.capital.transaction.config.CapitalTransactionConfig
import co.highbeam.capital.transaction.endpoint.GetTransactions
import co.highbeam.capital.transaction.endpoint.GetTransactionsCsv
import co.highbeam.capital.transaction.endpoint.drawdownApproval.ApproveDrawdownApproval
import co.highbeam.capital.transaction.endpoint.drawdownApproval.CreateDrawdownApproval
import co.highbeam.capital.transaction.endpoint.drawdownApproval.DrawdownApprovalUploadUrl
import co.highbeam.capital.transaction.endpoint.drawdownApproval.GetDrawdownApproval
import co.highbeam.capital.transaction.endpoint.drawdownApproval.RejectDrawdownApproval
import co.highbeam.capital.transaction.endpoint.limits.GetTransactionLimits
import co.highbeam.capital.transaction.endpoint.summary.GetEndOfDayTransactionSummary
import co.highbeam.capital.transaction.endpoint.summary.GetTransactionSummary
import co.highbeam.capital.transaction.endpoint.task.ExecuteCapitalTransactionHandleDrawdownTask
import co.highbeam.capital.transaction.endpoint.task.ExecuteCapitalTransactionHandleRepaymentTask
import co.highbeam.feature.Feature

class CapitalTransactionFeature(
  val config: CapitalTransactionConfig,
) : Feature() {
  override fun bind() {
    bindConfigs()
    bindApiEndpoints()
  }

  private fun bindApiEndpoints() {
    bind(GetTransactionSummary::class.java).asEagerSingleton()
    bind(GetEndOfDayTransactionSummary::class.java).asEagerSingleton()


    bind(CreateDrawdownApproval::class.java).asEagerSingleton()
    bind(GetDrawdownApproval::class.java).asEagerSingleton()
    bind(DrawdownApprovalUploadUrl::class.java).asEagerSingleton()
    bind(ApproveDrawdownApproval::class.java).asEagerSingleton()
    bind(RejectDrawdownApproval::class.java).asEagerSingleton()

    bind(GetTransactions::class.java).asEagerSingleton()
    bind(GetTransactionsCsv::class.java).asEagerSingleton()

    bind(GetTransactionLimits::class.java).asEagerSingleton()

    bind(ExecuteCapitalTransactionHandleDrawdownTask::class.java).asEagerSingleton()
    bind(ExecuteCapitalTransactionHandleRepaymentTask::class.java).asEagerSingleton()
  }

  private fun bindConfigs() {
    bind(CapitalTransactionConfig::class.java).toInstance(config)
  }
}
