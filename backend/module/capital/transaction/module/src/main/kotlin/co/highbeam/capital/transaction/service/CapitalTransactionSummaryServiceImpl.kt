package co.highbeam.capital.transaction.service

import co.highbeam.capital.transaction.ledger.LedgerService
import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.money.Balance
import com.google.inject.Inject
import java.time.LocalDate
import java.util.UUID

internal class CapitalTransactionSummaryServiceImpl @Inject constructor(
  private val ledgerService: LedgerService,
) : CapitalTransactionSummaryService {
  override suspend fun runningBalance(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
  ): Balance = ledgerService.runningBalance(
    ledgerGuid = capitalAccountGuid,
    businessGuid = businessGuid,
  )

  override suspend fun balanceAsOfDateRange(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
    sinceInclusive: LocalDate,
    untilInclusive: LocalDate,
  ): List<CapitalTransactionSummaryRep> = ledgerService.balanceAsOfDateRange(
    ledgerGuid = capitalAccountGuid,
    businessGuid = businessGuid,
    sinceInclusive = sinceInclusive,
    untilInclusive = untilInclusive,
  ).map {
    CapitalTransactionSummaryRep(
      date = it.date,
      balance = it.amount,
    )
  }
}
