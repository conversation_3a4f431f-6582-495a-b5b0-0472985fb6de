package co.highbeam.capital.transaction.endpoint

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthCapitalAccount
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalTransactionsApi as Api

internal class GetTransactions @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val service: CapitalTransactionService,
) : EndpointHandler<Api.Get, List<CapitalTransactionRep>>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
      sinceInclusive = call.getParam("sinceInclusive", optional = true),
      untilInclusive = call.getParam("untilInclusive", optional = true),
    )

  override suspend fun Handler.handle(endpoint: Api.Get): List<CapitalTransactionRep> {
    authSome(
      authCapitalAccount(
        permission = Permission.Transaction_Read,
        businessGuid = endpoint.businessGuid,
        capitalAccountGuid = endpoint.capitalAccountGuid
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return service.transactions(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
      sinceInclusive = endpoint.sinceInclusive,
      untilInclusive = endpoint.untilInclusive,
    )
  }
}
