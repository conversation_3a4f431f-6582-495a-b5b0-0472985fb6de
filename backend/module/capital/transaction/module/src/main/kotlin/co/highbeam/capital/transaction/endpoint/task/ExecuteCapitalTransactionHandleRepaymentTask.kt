package co.highbeam.capital.transaction.endpoint.task

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.event.TransactionHandler
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.capital.transaction.api.CapitalTransactionHandlerTaskApi as Api
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall

internal class ExecuteCapitalTransactionHandleRepaymentTask @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val transactionHandler: TransactionHandler,
) : EndpointHandler<Api.HandleRepayment, CapitalRepaymentTransactionRep>(
  template = Api.HandleRepayment::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.HandleRepayment =
    Api.HandleRepayment(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
      rep = call.body()
    )

  override suspend fun Handler.handle(
    endpoint: Api.HandleRepayment,
  ): CapitalRepaymentTransactionRep {
    auth(authPlatformRole(PlatformRole.CLOUD_TASKS))

    transactionHandler.handle(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
      event = endpoint.rep
    )

    return endpoint.rep
  }
}
