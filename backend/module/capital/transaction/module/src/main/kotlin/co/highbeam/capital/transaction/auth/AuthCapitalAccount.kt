package co.highbeam.capital.transaction.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import com.google.inject.Inject
import io.ktor.http.Headers
import java.util.UUID

class AuthCapitalAccount private constructor(
  private val authPermission: AuthPermission.Provider,
  private val client: CapitalAccountClient,
  private val permission: Permission,
  private val capitalAccountGuid: UUID,
  private val businessGuid: UUID,
) : Auth() {
  class Provider @Inject constructor(
    private val authPermission: AuthPermission.Provider,
    private val client: CapitalAccountClient,
  ) {
    operator fun invoke(
      permission: Permission,
      capitalAccountGuid: UUID,
      businessGuid: UUID
    ): AuthCapitalAccount = AuthCapitalAccount(
      authPermission = authPermission,
      client = client,
      permission = permission,
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
    )
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    return listOf(
      businessGuid,
      client(
        CapitalAccountApi.Get(
          guid = capitalAccountGuid,
          businessGuid = businessGuid
        )
      )?.businessGuid,
    ).all { authPermission(permission) { it }.authorize(jwt = jwt, headers = headers) }
  }
}
