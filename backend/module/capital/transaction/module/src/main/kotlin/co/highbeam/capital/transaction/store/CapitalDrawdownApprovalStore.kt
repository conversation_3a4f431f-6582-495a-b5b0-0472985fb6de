package co.highbeam.capital.transaction.store

import co.highbeam.capital.transaction.model.CapitalDrawdownApprovalModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
class CapitalDrawdownApprovalStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(model: CapitalDrawdownApprovalModel.Creator): CapitalDrawdownApprovalModel =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/capitalDrawdownApproval/create.sql"))
      query.bindKotlin(model)
      return@transaction query.mapTo(CapitalDrawdownApprovalModel::class.java).single()
    }

  fun get(businessGuid: UUID, capitalAccountGuid: UUID): List<CapitalDrawdownApprovalModel> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/capitalDrawdownApproval/get.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("capitalAccountGuid", capitalAccountGuid)
      return@handle query.mapTo(CapitalDrawdownApprovalModel::class.java).toList()
    }

  fun getByGuid(guid: UUID, businessGuid: UUID): CapitalDrawdownApprovalModel? = handle { handle ->
    val query = handle.createQuery(sqlResource("store/capitalDrawdownApproval/getByGuid.sql"))
    query.bind("guid", guid)
    query.bind("businessGuid", businessGuid)
    return@handle query.mapTo(CapitalDrawdownApprovalModel::class.java).singleOrNull()
  }

  fun update(
    guid: UUID,
    state: CapitalDrawdownApprovalModel.State
  ): CapitalDrawdownApprovalModel =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/capitalDrawdownApproval/update.sql"))
      query.bind("guid", guid)
      query.bind("state", state)
      return@handle query.mapTo(CapitalDrawdownApprovalModel::class.java).single()
    }
}
