package co.highbeam.capital.transaction.endpoint.drawdownApproval

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi as Api

class RejectDrawdownApproval @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalDrawdownApprovalService,
) : EndpointHandler<Api.Reject, CapitalDrawdownApprovalRep>(
  template = Api.Reject::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Reject =
    Api.Reject(
      guid = call.getParam("guid"),
    )

  override suspend fun Handler.handle(endpoint: Api.Reject): CapitalDrawdownApprovalRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.reject(endpoint.guid)
  }
}
