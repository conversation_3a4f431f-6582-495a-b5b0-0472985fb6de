package co.highbeam.capital.transaction.endpoint.drawdownApproval

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.auth.AuthCapitalAccount
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi as Api

class GetDrawdownApproval @Inject constructor(
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalDrawdownApprovalService,
) : EndpointHandler<Api.Get, List<CapitalDrawdownApprovalRep>>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
    )

  override suspend fun Handler.handle(endpoint: Api.Get): List<CapitalDrawdownApprovalRep> {
    authSome(
      authCapitalAccount(
        permission = Permission.Capital_Drawdown,
        capitalAccountGuid = endpoint.capitalAccountGuid,
        businessGuid = endpoint.businessGuid,
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return service.get(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
    )
  }
}
