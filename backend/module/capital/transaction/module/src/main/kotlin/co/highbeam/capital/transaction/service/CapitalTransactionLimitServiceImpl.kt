package co.highbeam.capital.transaction.service

import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.transaction.ledger.LedgerTransactionService
import co.highbeam.capital.transaction.rep.CapitalTransactionLimitsRep
import co.highbeam.exception.unprocessable
import co.highbeam.money.Balance
import co.highbeam.money.Money
import com.google.inject.Inject
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

class CapitalTransactionLimitServiceImpl @Inject constructor(
  private val ledgerTransactionService: LedgerTransactionService,
  private val capitalAccountClient: CapitalAccountClient,
  private val clock: Clock,
) : CapitalTransactionLimitService {
  override suspend fun get(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
  ): CapitalTransactionLimitsRep {
    val capitalAccount = capitalAccount(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    val preApprovedLimit = capitalAccount.controls.drawdownRateLimit?.let { drawdownRateLimit ->
      val recentTransactionAmount = getRecentTransactionAmount(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccountGuid,
      )

      if (drawdownRateLimit <= recentTransactionAmount) {
        return@let Money.ZERO
      }

      return@let drawdownRateLimit - recentTransactionAmount
    }

    return CapitalTransactionLimitsRep(
      preApprovedLimit = preApprovedLimit,
    )
  }

  private suspend fun capitalAccount(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
  ) = capitalAccountClient(
    CapitalAccountApi.Get(
      businessGuid = businessGuid,
      guid = capitalAccountGuid,
    )
  ) ?: throw unprocessable(CapitalAccountNotFound())

  private suspend fun getRecentTransactionAmount(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
  ): Money = ledgerTransactionService.transactionsWithTimeFilter(
    businessGuid = businessGuid,
    ledgerGuid = capitalAccountGuid,
    sinceInclusive = ZonedDateTime.now(clock).minusHours(48),
    untilInclusive = null,
  ).sumOf { it.amount }.takeIf { it < Balance.ZERO }?.abs() ?: Money.ZERO
}
