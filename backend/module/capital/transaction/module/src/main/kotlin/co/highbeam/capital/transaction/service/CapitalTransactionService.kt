package co.highbeam.capital.transaction.service

import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import com.google.inject.ImplementedBy
import java.time.LocalDate
import java.util.UUID

@ImplementedBy(CapitalTransactionServiceImpl::class)
interface CapitalTransactionService {
  suspend fun makeDrawdown(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: CapitalDrawdownTransactionRep.Creator,
    transactionInitiator: CapitalDrawdownTransactionRep.TransactionInitiator,
  ): CapitalDrawdownTransactionRep

  suspend fun makeRepayment(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: CapitalRepaymentTransactionRep.Creator,
  ): CapitalRepaymentTransactionRep

  suspend fun transactions(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?,
  ): List<CapitalTransactionRep>

  suspend fun transactions(
    businessGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?,
  ): List<CapitalTransactionRep>

  suspend fun transactionsWithRunningBalance(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?,
  ): List<CapitalTransactionRep.Summary>

  suspend fun transactionsWithRunningBalance(
    businessGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?,
  ): List<CapitalTransactionRep.Summary>
}
