package co.highbeam.capital.transaction.endpoint

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthCapitalAccount
import co.highbeam.capital.transaction.mapper.CapitalTransactionResponseMapper
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalTransactionsApi as Api

internal class GetTransactionsCsv @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val service: CapitalTransactionService,
  private val mapper: CapitalTransactionResponseMapper,
) : EndpointHandler<Api.GetCsv, String>(
  template = Api.GetCsv::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetCsv =
    Api.GetCsv(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
      sinceInclusive = call.getParam("sinceInclusive"),
      untilInclusive = call.getParam("untilInclusive"),
    )

  override suspend fun Handler.handle(endpoint: Api.GetCsv): String {
    authSome(
      authCapitalAccount(
        permission = Permission.Transaction_Read,
        businessGuid = endpoint.businessGuid,
        capitalAccountGuid = endpoint.capitalAccountGuid
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return mapper.toCsv(
      service.transactionsWithRunningBalance(
        businessGuid = endpoint.businessGuid,
        capitalAccountGuid = endpoint.capitalAccountGuid,
        sinceInclusive = endpoint.sinceInclusive,
        untilInclusive = endpoint.untilInclusive,
      )
    )
  }
}
