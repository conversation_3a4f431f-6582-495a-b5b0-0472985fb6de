package co.highbeam.capital.transaction.endpoint.drawdownApproval

import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.auth.AuthBankAccount
import co.highbeam.capital.transaction.auth.AuthCapitalAccount
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi as Api

class CreateDrawdownApproval @Inject constructor(
  private val authBankAccount: AuthBankAccount.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val authMfa: AuthMfa.Provider,
  private val service: CapitalDrawdownApprovalService,
) : EndpointHandler<Api.Create, CapitalDrawdownApprovalRep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
      rep = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.Create): CapitalDrawdownApprovalRep {
    authAll(
      authCapitalAccount(
        permission = Permission.Capital_Drawdown,
        capitalAccountGuid = endpoint.capitalAccountGuid,
        businessGuid = endpoint.businessGuid,
      ),
      authMfa(),
      authBankAccount(
        permission = Permission.Capital_Drawdown,
        bankAccountGuid = endpoint.rep.bankAccountGuid,
        businessGuid = endpoint.businessGuid,
      ),
    )

    return service.create(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
      rep = endpoint.rep,
    )
  }
}
