package co.highbeam.capital.transaction.endpoint.drawdownApproval

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi as Api

class ApproveDrawdownApproval @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalDrawdownApprovalService,
) : EndpointHandler<Api.Approve, CapitalDrawdownApprovalRep>(
  template = Api.Approve::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Approve =
    Api.Approve(
      guid = call.getParam("guid"),
    )

  override suspend fun Handler.handle(endpoint: Api.Approve): CapitalDrawdownApprovalRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.approve(endpoint.guid)
  }
}
