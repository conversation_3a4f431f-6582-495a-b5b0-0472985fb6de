package co.highbeam.capital.transaction.service

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.CapitalAccountSummaryClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.api.CapitalAccountSummaryApi
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalAccountSummaryRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.transaction.api.CapitalTransactionHandlerTaskApi
import co.highbeam.capital.transaction.config.CapitalTransactionConfig
import co.highbeam.capital.transaction.controls.TransactionControllerData
import co.highbeam.capital.transaction.controls.TransactionControllerManager
import co.highbeam.capital.transaction.exception.CapitalDrawdownException
import co.highbeam.capital.transaction.exception.CapitalRepaymentException
import co.highbeam.capital.transaction.ledger.LedgerTransactionService
import co.highbeam.capital.transaction.mapper.CapitalTransactionMapper
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.Creator
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.Status
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.TransactionInitiator
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.exception.business.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.money.Money
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCompleteRep
import com.google.inject.Inject
import com.slack.client.SlackMessageClient
import highbeam.task.TaskCreator
import mu.KotlinLogging
import java.time.LocalDate
import java.util.UUID

@Suppress("TooManyFunctions")
internal class CapitalTransactionServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val config: CapitalTransactionConfig,
  private val ledgerTransactionService: LedgerTransactionService,
  private val unitCoClient: UnitCoClient,
  private val slackMessageClient: SlackMessageClient,
  private val transactionController: TransactionControllerManager,
  private val capitalAccountSummaryClient: CapitalAccountSummaryClient,
  private val capitalDrawdownApprovalService: CapitalDrawdownApprovalService,
  private val capitalAccountClient: CapitalAccountClient,
  private val mapper: CapitalTransactionMapper,
  private val businessClient: BusinessClient,
  private val taskCreator: TaskCreator,
  private val featureFlagService: FeatureFlagService,
) : CapitalTransactionService {
  private val logger = KotlinLogging.logger {}

  override suspend fun makeDrawdown(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: Creator,
    transactionInitiator: TransactionInitiator,
  ): CapitalDrawdownTransactionRep {
    logger.info { "Drawdown Creator:$creator, business:$businessGuid, account:$capitalAccountGuid" }

    val capitalAccount = capitalAccountSummaryClient(
      CapitalAccountSummaryApi.Get(
        businessGuid = businessGuid,
        guid = capitalAccountGuid,
      )
    ) ?: throw unprocessable(CapitalAccountNotFound())

    notifySlack(
      amount = creator.amount,
      capitalAccount = capitalAccount,
    )

    val controllerStatus = shouldAllow(
      capitalAccount = capitalAccount,
      businessGuid = businessGuid,
      creator = creator,
      transactionInitiator = transactionInitiator,
    )

    when (controllerStatus) {
      TransactionControllerData.Status.Denied -> throw CapitalDrawdownException()
      TransactionControllerData.Status.ApprovalNeeded -> return createDrawdownApproval(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccountGuid,
        creator = creator,
      )
      TransactionControllerData.Status.Allowed -> {} // Follow through
    }

    val res = makeTransferToCustomer(
      creator = creator,
      capitalAccount = capitalAccount,
      transactionInitiator = transactionInitiator,
    )

    return mapper.toDrawdownTransactionRep(res).also {
      taskCreator.create(
        CapitalTransactionHandlerTaskApi.HandleDrawdown(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          rep = it
        ),
        queueName = "capital-transactions-sync"
      )
    }
  }

  override suspend fun makeRepayment(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: CapitalRepaymentTransactionRep.Creator,
  ): CapitalRepaymentTransactionRep {
    logger.info { "Repay Creator:$creator, business:$businessGuid, account:$capitalAccountGuid" }

    val capitalAccount = capitalAccountSummaryClient(
      CapitalAccountSummaryApi.Get(
        businessGuid = businessGuid,
        guid = capitalAccountGuid,
      )
    ) ?: throw unprocessable(CapitalAccountNotFound())

    if (!shouldAllow(
        capitalAccount = capitalAccount,
        businessGuid = businessGuid,
        creator = creator,
      )
    ) {
      throw CapitalRepaymentException()
    }

    val res = makeTransferFromCustomer(
      creator = creator,
      capitalAccount = capitalAccount,
    )

    return mapper.toRepaymentTransactionRep(res).also {
      taskCreator.create(
        CapitalTransactionHandlerTaskApi.HandleRepayment(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          rep = it
        ),
        queueName = "capital-transactions-sync"
      )
    }
  }

  override suspend fun transactions(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?
  ): List<CapitalTransactionRep> = ledgerTransactionService.transactions(
    businessGuid = businessGuid,
    ledgerGuid = capitalAccountGuid,
    sinceInclusive = sinceInclusive,
    untilInclusive = untilInclusive,
  ).map {
    mapper.toCapitalTransactionRep(ledgerTransaction = it, businessGuid = businessGuid)
  }.sortedByDescending { it.date }

  override suspend fun transactions(
    businessGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?
  ): List<CapitalTransactionRep> = ledgerTransactionService.transactions(
    businessGuid = businessGuid,
    ledgerGuids = capitalAccountClient(CapitalAccountApi.GetAll(businessGuid)).map { it.guid },
    sinceInclusive = sinceInclusive,
    untilInclusive = untilInclusive,
  ).map {
    mapper.toCapitalTransactionRep(ledgerTransaction = it, businessGuid = businessGuid)
  }.sortedByDescending { it.date }

  override suspend fun transactionsWithRunningBalance(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?
  ): List<CapitalTransactionRep.Summary> {
    val transactions = transactions(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      sinceInclusive = null,
      untilInclusive = untilInclusive,
    )

    val openingBalance = transactions.filter { it.date.toLocalDate() < sinceInclusive }
      .sumOf { it.amount }

    return mapper.toCapitalTransactionSummaryRep(
      transactions = transactions.filter { it.date.toLocalDate() >= sinceInclusive },
      openingBalance = openingBalance,
    )
  }

  override suspend fun transactionsWithRunningBalance(
    businessGuid: UUID,
    sinceInclusive: LocalDate?,
    untilInclusive: LocalDate?
  ): List<CapitalTransactionRep.Summary> {
    val transactions = transactions(
      businessGuid = businessGuid,
      sinceInclusive = null,
      untilInclusive = untilInclusive,
    )

    val openingBalance = transactions.filter { it.date.toLocalDate() < sinceInclusive }
      .sumOf { it.amount }

    return mapper.toCapitalTransactionSummaryRep(
      transactions = transactions.filter { it.date.toLocalDate() >= sinceInclusive },
      openingBalance = openingBalance,
    )
  }

  private suspend fun makeTransferToCustomer(
    creator: Creator,
    capitalAccount: CapitalAccountSummaryRep,
    transactionInitiator: TransactionInitiator,
  ): UnitCompleteRep<BookPaymentRep> {
    val customerAccount = getCustomerAccount(creator.bankAccountGuid)

    val transactionType = when (transactionInitiator) {
      TransactionInitiator.User, TransactionInitiator.Risk ->
        UnitCoTransactionRep.TransactionType.LineOfCreditPayment
      TransactionInitiator.InterestFees ->
        UnitCoTransactionRep.TransactionType.LineOfCreditInterestDrawdown
      TransactionInitiator.ChargeCardRepayment ->
        UnitCoTransactionRep.TransactionType.LineOfCreditCardRepaymentDrawdown
    }

    val description = when (transactionInitiator) {
      TransactionInitiator.User, TransactionInitiator.Risk -> "${capitalAccount.name} drawdown"
      TransactionInitiator.InterestFees -> "${capitalAccount.name} interest drawdown"
      TransactionInitiator.ChargeCardRepayment ->
        "${capitalAccount.name} overdue transfer"
    }

    return unitCoClient.payment.createBook(
      BookPaymentRep.Creator(
        amount = creator.amount,
        description = description,
        idempotencyKey = creator.idempotencyKey,
        fromAccountId = getDrawdownAccount(capitalAccount),
        toAccountId = customerAccount,
        tags = BookPaymentRep.Tags(
          transactionType = transactionType,
          creditAccountGuid = capitalAccount.guid,
        ),
      )
    )
  }

  private suspend fun makeTransferFromCustomer(
    creator: CapitalRepaymentTransactionRep.Creator,
    capitalAccount: CapitalAccountSummaryRep,
  ): UnitCompleteRep<BookPaymentRep> {
    val customerAccount = getCustomerAccount(creator.bankAccountGuid)
    val description = when (capitalAccount.type) {
      CapitalAccountRep.Type.CashAccessOnly -> "${capitalAccount.name} repayment"
      CapitalAccountRep.Type.ChargeCardAndCashAccess, CapitalAccountRep.Type.ChargeCardOnly ->
        "${capitalAccount.name} overdue repayment"
    }

    return unitCoClient.payment.createBook(
      BookPaymentRep.Creator(
        amount = creator.amount,
        description = description,
        idempotencyKey = creator.idempotencyKey,
        fromAccountId = customerAccount,
        toAccountId = getRepaymentAccount(capitalAccount),
        tags = BookPaymentRep.Tags(
          transactionType = UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
          creditAccountGuid = capitalAccount.guid,
        ),
      )
    )
  }

  private fun getDrawdownAccount(capitalAccount: CapitalAccountSummaryRep): String {
    return when (capitalAccount.lender) {
      CapitalLender.Highbeam -> {
        if (featureFlagService.isEnabled(
            BusinessFlag.CapitalSpvFlowOfFunds,
            capitalAccount.businessGuid,
          )
        ) {
          config.highbeamFundingHighYieldSavingUnitAccountId
        } else {
          config.highbeamOldFundingUnitAccountId
        }
      }
      else -> config.externalLenderLineOfCreditUnitAccountId[capitalAccount.lender]
        ?: throw BankAccountNotFound()
    }
  }

  private fun getRepaymentAccount(capitalAccount: CapitalAccountSummaryRep): String {
    return when (capitalAccount.lender) {
      CapitalLender.Highbeam -> {
        if (featureFlagService.isEnabled(
            BusinessFlag.CapitalSpvFlowOfFunds,
            capitalAccount.businessGuid
          )
        ) {
          config.highbeamSpvCollectionUnitAccountId
        } else {
          config.highbeamOldFundingUnitAccountId
        }
      }
      else -> config.externalLenderLineOfCreditUnitAccountId[capitalAccount.lender]
        ?: throw BankAccountNotFound()
    }
  }

  private suspend fun getCustomerAccount(bankAccountGuid: UUID): String =
    bankAccountClient.request(BankAccountApi.Get(bankAccountGuid))?.unitCoDepositAccountId
      ?: throw BankAccountNotFound()

  @SuppressWarnings("TooGenericExceptionCaught")
  private suspend fun notifySlack(
    amount: Money,
    capitalAccount: CapitalAccountSummaryRep,
  ) {
    try {
      // TODO(justin): Move this to a job queue or pubsub.
      val business = businessClient.request(BusinessApi.Get(capitalAccount.businessGuid))
      val webhookPath =
        if (capitalAccount.lender == CapitalLender.Highbeam) {
          config.capitalDrawdownWebhookPath
        } else {
          config.capitalExternalLenderDrawdownWebhookPath
        }
      slackMessageClient.sendMessage(
        key = null,
        webhookPath = webhookPath,
        body = mapOf(
          "businessGuid" to "${capitalAccount.businessGuid}",
          "amount" to amount.formatString(withDollarSign = true, withComma = true),
          "internalName" to "${business?.internalName}",
          "lender" to capitalAccount.lender.name,
        )
      )
    } catch (e: Exception) {
      logger.warn(e) { "Failed to send slack message for drawdown" }
    }
  }

  private suspend fun createDrawdownApproval(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: Creator
  ): CapitalDrawdownTransactionRep {
    capitalDrawdownApprovalService.create(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      rep = CapitalDrawdownApprovalRep.Creator(
        amount = creator.amount,
        idempotencyKey = creator.idempotencyKey,
        bankAccountGuid = creator.bankAccountGuid,
        note = "Drawdown approval needed due to transaction controller check.",
      )
    )
    return CapitalDrawdownTransactionRep(
      unitCoId = "",
      status = Status.Pending,
    )
  }

  private suspend fun shouldAllow(
    capitalAccount: CapitalAccountSummaryRep,
    businessGuid: UUID,
    creator: Creator,
    transactionInitiator: TransactionInitiator,
  ): TransactionControllerData.Status {
    return transactionController.shouldAllow(
      data = TransactionControllerData.Drawdown(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccount.guid,
        account = capitalAccount,
        creator = creator,
      ),
      force = transactionInitiator != TransactionInitiator.User,
    )
  }

  private suspend fun shouldAllow(
    capitalAccount: CapitalAccountSummaryRep,
    businessGuid: UUID,
    creator: CapitalRepaymentTransactionRep.Creator,
  ): Boolean {
    return transactionController.shouldAllow(
      data = TransactionControllerData.Repayment(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccount.guid,
        account = capitalAccount,
        creator = creator,
      ),
      force = false,
    )
  }
}
