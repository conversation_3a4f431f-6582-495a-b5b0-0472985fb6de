package co.highbeam.capital.transaction.service

import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.money.Balance
import com.google.inject.ImplementedBy
import java.time.LocalDate
import java.util.UUID

@ImplementedBy(CapitalTransactionSummaryServiceImpl::class)
interface CapitalTransactionSummaryService {
  suspend fun runningBalance(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
  ): Balance

  suspend fun balanceAsOfDateRange(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
    sinceInclusive: LocalDate,
    untilInclusive: LocalDate,
  ): List<CapitalTransactionSummaryRep>
}
