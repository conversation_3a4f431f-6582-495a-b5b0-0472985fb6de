package co.highbeam.capital.transaction.endpoint.limits

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthCapitalAccount
import co.highbeam.capital.transaction.rep.CapitalTransactionLimitsRep
import co.highbeam.capital.transaction.service.CapitalTransactionLimitService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalTransactionLimitsApi as Api

internal class GetTransactionLimits @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val service: CapitalTransactionLimitService,
) : EndpointHandler<Api.Get, CapitalTransactionLimitsRep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get(
    businessGuid = call.getParam("businessGuid"),
    capitalAccountGuid = call.getParam("capitalAccountGuid"),
  )

  override suspend fun Handler.handle(endpoint: Api.Get): CapitalTransactionLimitsRep {
    authSome(
      authCapitalAccount(
        permission = Permission.Transaction_Read,
        businessGuid = endpoint.businessGuid,
        capitalAccountGuid = endpoint.capitalAccountGuid
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return service.get(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
    )
  }
}
