package co.highbeam.capital.transaction.controls

import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService

class DrawdownApprovalController(
  private val drawdownApprovalService: CapitalDrawdownApprovalService,
) : TransactionController {
  override suspend fun shouldAllow(
    data: TransactionControllerData.Drawdown,
    force: <PERSON><PERSON><PERSON>,
  ): TransactionControllerData.Status {
    if (force) return TransactionControllerData.Status.Allowed

    data.account.controls.drawdownRequiresApproval?.let {
      if (!it) return TransactionControllerData.Status.Allowed
    } ?: return TransactionControllerData.Status.Allowed

    val approval = drawdownApprovalService.getApproved(
      businessGuid = data.businessGuid,
      capitalAccountGuid = data.capitalAccountGuid,
    ).singleOrNull {
      it.idempotencyKey == data.creator.idempotencyKey &&
        it.amount == data.creator.amount &&
        it.bankAccountGuid == data.creator.bankAccountGuid
    }


    return if (approval != null) {
      TransactionControllerData.Status.Allowed
    } else {
      TransactionControllerData.Status.Denied
    }
  }

  override suspend fun shouldAllow(
    data: TransactionControllerData.Repayment,
    force: <PERSON>ole<PERSON>,
  ): Boolean = true
}
