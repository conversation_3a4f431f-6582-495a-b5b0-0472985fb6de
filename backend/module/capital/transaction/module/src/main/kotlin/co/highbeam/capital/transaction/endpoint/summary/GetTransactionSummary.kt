package co.highbeam.capital.transaction.endpoint.summary

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.capital.transaction.service.CapitalTransactionSummaryService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import java.time.Clock
import java.time.LocalDate
import co.highbeam.capital.transaction.api.CapitalTransactionSummaryApi as Api

internal class GetTransactionSummary @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalTransactionSummaryService,
  private val clock: Clock,
) : EndpointHandler<Api.Get, CapitalTransactionSummaryRep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(
      businessGuid = call.getParam("businessGuid"),
      capitalAccountGuid = call.getParam("capitalAccountGuid"),
    )

  override suspend fun Handler.handle(endpoint: Api.Get): CapitalTransactionSummaryRep {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))

    val runningBalance = service.runningBalance(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
    )
    return CapitalTransactionSummaryRep(
      date = LocalDate.now(clock),
      balance = runningBalance,
    )
  }
}
