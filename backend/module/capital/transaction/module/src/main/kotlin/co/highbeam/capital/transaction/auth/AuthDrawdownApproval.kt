package co.highbeam.capital.transaction.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import com.google.inject.Inject
import io.ktor.http.Headers
import java.util.UUID

class AuthDrawdownApproval private constructor(
  private val authPermission: AuthPermission.Provider,
  private val service: CapitalDrawdownApprovalService,
  private val permission: Permission,
  private val guid: UUID,
  private val businessGuid: UUID,
) : Auth() {
  class Provider @Inject constructor(
    private val authPermission: AuthPermission.Provider,
    private val service: CapitalDrawdownApprovalService,
  ) {
    operator fun invoke(
      permission: Permission,
      guid: UUID,
      businessGuid: UUID
    ): AuthDrawdownApproval = AuthDrawdownApproval(
      authPermission = authPermission,
      service = service,
      permission = permission,
      guid = guid,
      businessGuid = businessGuid,
    )
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    return listOf(
      businessGuid,
      service.getByGuid(
        guid = guid,
        businessGuid = businessGuid,
      )?.businessGuid,
    ).all { authPermission(permission) { it }.authorize(jwt = jwt, headers = headers) }
  }
}
