package co.highbeam.capital.transaction.endpoint.drawdownApproval

import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.auth.AuthDrawdownApproval
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi as Api

class DrawdownApprovalUploadUrl @Inject constructor(
  private val authDrawdownApproval: AuthDrawdownApproval.Provider,
  private val service: CapitalDrawdownApprovalService,
) : EndpointHandler<Api.UploadUrl, CapitalDrawdownApprovalRep.UploadUrl>(
  template = Api.UploadUrl::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.UploadUrl =
    Api.UploadUrl(
      businessGuid = call.getParam("businessGuid"),
      guid = call.getParam("guid"),
    )

  override suspend fun Handler.handle(
    endpoint: Api.UploadUrl,
  ): CapitalDrawdownApprovalRep.UploadUrl {
    auth(
      authDrawdownApproval(
        permission = Permission.Capital_Drawdown,
        guid = endpoint.guid,
        businessGuid = endpoint.businessGuid,
      )
    )

    return service.uploadUrl(endpoint.guid)
  }
}
