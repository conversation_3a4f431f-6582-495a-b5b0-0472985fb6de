{"data": [{"type": "bookTransaction", "id": "2319039", "attributes": {"createdAt": "2023-01-17T03:49:10.278Z", "amount": 10000, "direction": "Credit", "balance": 24500, "summary": "<PERSON><PERSON><PERSON> Goyal  |  Primary  |  Line of credit repayment", "counterparty": {"name": "<PERSON><PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "**********", "accountType": "Checking"}, "tags": {"transactionType": "LineOfCreditPayment", "bankAccountGuid": "********-0000-0000-abcd-********0010", "creditAccountGuid": "********-0000-0000-abcd-********0100"}}, "relationships": {"account": {"data": {"type": "account", "id": "1066686"}}, "customer": {"data": {"type": "customer", "id": "431742"}}, "customers": {"data": [{"type": "customer", "id": "431742"}]}, "payment": {"data": {"type": "payment", "id": "1514166"}}, "counterpartyAccount": {"data": {"type": "account", "id": "1111"}}, "counterpartyCustomer": {"data": {"type": "customer", "id": "431742"}}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "bookTransaction", "id": "2319037", "attributes": {"createdAt": "2023-01-17T03:48:36.675Z", "amount": 10000, "direction": "Credit", "balance": 14500, "summary": "<PERSON><PERSON><PERSON> Goyal  |  Primary  |  Line of credit repayment", "counterparty": {"name": "<PERSON><PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "**********", "accountType": "Checking"}, "tags": {"transactionType": "LineOfCreditPayment", "bankAccountGuid": "********-0000-0000-abcd-********0010", "creditAccountGuid": "********-0000-0000-abcd-********0100"}}, "relationships": {"account": {"data": {"type": "account", "id": "1066686"}}, "customer": {"data": {"type": "customer", "id": "431742"}}, "customers": {"data": [{"type": "customer", "id": "431742"}]}, "payment": {"data": {"type": "payment", "id": "1514163"}}, "counterpartyAccount": {"data": {"type": "account", "id": "1111"}}, "counterpartyCustomer": {"data": {"type": "customer", "id": "431742"}}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "bookTransaction", "id": "2318985", "attributes": {"createdAt": "2023-01-17T02:58:35.110Z", "amount": 1000, "direction": "Debit", "balance": 4500, "summary": "<PERSON><PERSON><PERSON>yal  |  Primary  |  Line of credit drawdown", "counterparty": {"name": "<PERSON><PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "**********", "accountType": "Checking"}, "tags": {"transactionType": "LineOfCreditPayment", "bankAccountGuid": "********-0000-0000-abcd-********0010", "creditAccountGuid": "********-0000-0000-abcd-********0100"}}, "relationships": {"account": {"data": {"type": "account", "id": "1066685"}}, "customer": {"data": {"type": "customer", "id": "1111"}}, "customers": {"data": [{"type": "customer", "id": "431742"}]}, "payment": {"data": {"type": "payment", "id": "1514041"}}, "counterpartyAccount": {"data": {"type": "account", "id": "602675"}}, "counterpartyCustomer": {"data": {"type": "customer", "id": "279812"}}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "bookTransaction", "id": "2318984", "attributes": {"createdAt": "2023-01-17T02:57:48.674Z", "amount": 1000, "direction": "Debit", "balance": 5500, "summary": "<PERSON><PERSON><PERSON>yal  |  Primary  |  Line of credit drawdown", "counterparty": {"name": "<PERSON><PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "**********", "accountType": "Checking"}, "tags": {"transactionType": "LineOfCreditPayment", "bankAccountGuid": "********-0000-0000-abcd-********0010", "creditAccountGuid": "********-0000-0000-abcd-********0100"}}, "relationships": {"account": {"data": {"type": "account", "id": "1066685"}}, "customer": {"data": {"type": "customer", "id": "2222"}}, "customers": {"data": [{"type": "customer", "id": "431742"}]}, "payment": {"data": {"type": "payment", "id": "1514039"}}, "counterpartyAccount": {"data": {"type": "account", "id": "602675"}}, "counterpartyCustomer": {"data": {"type": "customer", "id": "431742"}}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "bookTransaction", "id": "2274099", "attributes": {"createdAt": "2023-01-02T19:39:11.164Z", "amount": 1000, "direction": "Credit", "balance": 1000, "summary": "<PERSON><PERSON><PERSON> Goyal  |  Line of credit manual payment", "counterparty": {"name": "<PERSON><PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "**********", "accountType": "Checking"}, "tags": {"creditAccountGuid": "********-0000-0000-abcd-********0100"}}, "relationships": {"account": {"data": {"type": "account", "id": "1066686"}}, "customer": {"data": {"type": "customer", "id": "431742"}}, "customers": {"data": [{"type": "customer", "id": "431742"}]}, "payment": {"data": {"type": "payment", "id": "1461745"}}, "counterpartyAccount": {"data": {"type": "account", "id": "602675"}}, "counterpartyCustomer": {"data": {"type": "customer", "id": "431742"}}, "org": {"data": {"type": "org", "id": "553"}}}}], "included": [{"type": "depositAccount", "id": "1066685", "attributes": {"name": "<PERSON><PERSON><PERSON>", "createdAt": "2022-12-20T17:12:01.117Z", "routingNumber": "*********", "accountNumber": "**********", "depositProduct": "checking", "balance": 24500, "hold": 0, "available": 24500, "tags": {"businessGuid": "e9477d2e-c976-4cca-b947-dca2dbf27007", "bankAccountGuid": "12564bf2-7db3-4f04-9d8b-b1bd58dec6a4", "plaidOfficialName": "Highbeam LoC"}, "currency": "USD", "status": "Open", "updatedAt": "2023-01-17T03:49:10.278Z"}, "relationships": {"customer": {"data": {"type": "customer", "id": "431742"}}, "org": {"data": {"type": "org", "id": "553"}}}}], "meta": {"pagination": {"total": 16, "limit": 25, "offset": 0}}}