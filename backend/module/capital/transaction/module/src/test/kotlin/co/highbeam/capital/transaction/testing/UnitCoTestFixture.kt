package co.highbeam.capital.transaction.testing

import co.unit.client.UnitCoClient
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import kotlinx.coroutines.flow.flowOf

class UnitCoTestFixture(private val test: CapitalTransactionFeatureIntegrationTest) {
  fun mockUnitCoTransactionClient(
    path: String = "transaction/happy.json"
  ) {
    coEvery {
      test.get<UnitCoClient>().transaction.list(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )
    } returns flowOf(
      test.objectMapper.readValue(
        Resources.getResource(path)
      )
    )
  }
}
