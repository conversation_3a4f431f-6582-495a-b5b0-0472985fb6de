package co.highbeam.capital.transaction

import co.highbeam.capital.transaction.api.CapitalTransactionSummaryApi
import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.money.Balance
import co.highbeam.time.toList
import java.time.LocalDate
import java.util.TreeMap
import java.util.UUID

class FakeCapitalTransactionSummaryClient : CapitalTransactionSummaryClient {
  private val store: MutableMap<String, TreeMap<LocalDate, CapitalTransactionSummaryRep>> =
    mutableMapOf()

  override suspend fun invoke(
    endpoint: CapitalTransactionSummaryApi.Get,
  ): CapitalTransactionSummaryRep {
    val key = key(
      capitalAccountGuid = endpoint.capitalAccountGuid,
      businessGuid = endpoint.businessGuid,
    )

    return store[key]?.lastEntry()?.value ?: CapitalTransactionSummaryRep(
      date = LocalDate.now(),
      balance = Balance(0),
    )
  }

  override suspend fun invoke(
    endpoint: CapitalTransactionSummaryApi.GetEndOfDayBalance,
  ): List<CapitalTransactionSummaryRep> {
    val key = key(
      capitalAccountGuid = endpoint.capitalAccountGuid,
      businessGuid = endpoint.businessGuid,
    )

    val data = store[key]
    return (endpoint.sinceInclusive..endpoint.untilInclusive)
      .toList()
      .map {
        data?.floorEntry(it)?.value?.copy(date = it) ?: CapitalTransactionSummaryRep(
          date = it,
          balance = Balance(0),
        )
      }
  }

  fun set(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
    date: LocalDate,
    balance: Balance,
  ) {
    val key = key(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
    )
    val rep = CapitalTransactionSummaryRep(date, balance)
    if (!store.contains(key)) store[key] = TreeMap()

    store[key]?.put(date, rep)
  }

  fun reset() {
    store.clear()
  }

  private fun key(capitalAccountGuid: UUID, businessGuid: UUID): String =
    "$capitalAccountGuid-$businessGuid"
}
