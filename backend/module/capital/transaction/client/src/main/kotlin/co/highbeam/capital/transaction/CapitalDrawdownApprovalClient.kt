package co.highbeam.capital.transaction

import co.highbeam.capital.transaction.api.CapitalDrawdownApprovalApi
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import com.google.inject.ImplementedBy

@ImplementedBy(HttpCapitalDrawdownApprovalClient::class)
interface CapitalDrawdownApprovalClient {
  suspend operator fun invoke(endpoint: CapitalDrawdownApprovalApi.Create):
    CapitalDrawdownApprovalRep

  suspend operator fun invoke(endpoint: CapitalDrawdownApprovalApi.Get):
    List<CapitalDrawdownApprovalRep>

  suspend operator fun invoke(endpoint: CapitalDrawdownApprovalApi.UploadUrl):
    CapitalDrawdownApprovalRep.UploadUrl

  suspend operator fun invoke(endpoint: CapitalDrawdownApprovalApi.Approve):
    CapitalDrawdownApprovalRep

  suspend operator fun invoke(endpoint: CapitalDrawdownApprovalApi.Reject):
    CapitalDrawdownApprovalRep
}
