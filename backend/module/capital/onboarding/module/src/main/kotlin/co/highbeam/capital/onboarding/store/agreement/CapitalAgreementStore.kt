package co.highbeam.capital.onboarding.store.agreement

import co.highbeam.capital.onboarding.model.agreement.CapitalAgreementModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
class CapitalAgreementStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(model: CapitalAgreementModel): CapitalAgreementModel = transaction { handle ->
    val query = handle.createQuery(sqlResource("store/capitalAgreement/create.sql"))
    query.bindKotlin(model)
    return@transaction query.mapTo(CapitalAgreementModel::class.java).single()
  }

  fun get(businessGuid: UUID, capitalAccountGuid: UUID): CapitalAgreementModel? = handle { handle ->
    val query = handle.createQuery(sqlResource("store/capitalAgreement/get.sql"))
    query.bind("businessGuid", businessGuid)
    query.bind("capitalAccountGuid", capitalAccountGuid)
    return@handle query.mapTo(CapitalAgreementModel::class.java).singleNullOrThrow()
  }

  fun getPerType(businessGuid: UUID, capitalAccountGuid: UUID): List<CapitalAgreementModel> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/capitalAgreement/getByType.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("capitalAccountGuid", capitalAccountGuid)
      return@handle query.mapTo(CapitalAgreementModel::class.java).toList()
    }
}
