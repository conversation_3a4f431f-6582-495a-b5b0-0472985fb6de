package co.highbeam.capital.onboarding.store.application

import co.highbeam.capital.onboarding.model.application.CapitalApplicationDocumentModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
class CapitalApplicationDocumentStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(model: CapitalApplicationDocumentModel): CapitalApplicationDocumentModel =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/capitalApplicationDocument/create.sql"))
      query.bindKotlin(model)
      return@transaction query.mapTo(CapitalApplicationDocumentModel::class.java).single()
    }

  fun get(capitalApplicationGuid: UUID, businessGuid: UUID): List<CapitalApplicationDocumentModel> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/capitalApplicationDocument/get.sql"))
      query.bind("capitalApplicationGuid", capitalApplicationGuid)
      query.bind("businessGuid", businessGuid)
      return@handle query.mapTo(CapitalApplicationDocumentModel::class.java).toList()
    }

  fun delete(
    guid: UUID,
    capitalApplicationGuid: UUID,
    businessGuid: UUID,
  ): CapitalApplicationDocumentModel? = transaction { handle ->
    val query = handle.createQuery(sqlResource("store/capitalApplicationDocument/delete.sql"))
    query.bind("guid", guid)
    query.bind("capitalApplicationGuid", capitalApplicationGuid)
    query.bind("businessGuid", businessGuid)
    return@transaction query.mapTo(CapitalApplicationDocumentModel::class.java).singleNullOrThrow()
  }
}
