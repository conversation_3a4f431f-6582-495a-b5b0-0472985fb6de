package co.highbeam.capital.repaymentschedule.service

import co.highbeam.capital.repaymentschedule.model.RepaymentScheduleOverrideModel
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(RepaymentScheduleOverrideServiceImpl::class)
interface RepaymentScheduleOverrideService {
  suspend fun create(
    businessGuid: UUID,
    creator: RepaymentScheduleOverrideModel
  ): RepaymentScheduleOverrideModel

  suspend fun getAllByCapitalAccount(
    businessGuid: UUID,
    capitalAccountGuid: UUID
  ): List<RepaymentScheduleOverrideModel>

  suspend fun update(
    businessGuid: UUID,
    overrideGuid: UUID,
    updater: RepaymentScheduleOverrideModel.Updater
  ): RepaymentScheduleOverrideModel

  suspend fun delete(
    businessGuid: UUID,
    overrideGuid: UUID
  ): RepaymentScheduleOverrideModel
}
