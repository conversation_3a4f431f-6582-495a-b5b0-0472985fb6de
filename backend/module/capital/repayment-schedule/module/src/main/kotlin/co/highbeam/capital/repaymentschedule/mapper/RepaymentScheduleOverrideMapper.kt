package co.highbeam.capital.repaymentschedule.mapper

import co.highbeam.capital.repaymentschedule.model.RepaymentScheduleOverrideModel
import co.highbeam.capital.repaymentschedule.rep.RepaymentScheduleOverrideRep
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import java.util.UUID

class RepaymentScheduleOverrideMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator
) {
  fun toRep(
    model: RepaymentScheduleOverrideModel
  ): RepaymentScheduleOverrideRep = RepaymentScheduleOverrideRep(
    guid = model.guid,
    businessGuid = model.businessGuid,
    capitalAccountGuid = model.capitalAccountGuid,
    startAt = model.startAt,
    endAtInclusive = model.endAtInclusive,
    option = model.data.option
  )

  fun toModel(
    businessGuid: UUID,
    creator: RepaymentScheduleOverrideRep.Creator
  ): RepaymentScheduleOverrideModel = RepaymentScheduleOverrideModel(
    guid = uuidGenerator.generate(),
    businessGuid = businessGuid,
    capitalAccountGuid = creator.capitalAccountGuid,
    startAt = creator.startAt,
    endAtInclusive = creator.endAtInclusive,
    data = RepaymentScheduleOverrideModel.Data(
      option = creator.option
    )
  )

  fun toUpdaterModel(
    updater: RepaymentScheduleOverrideRep.Updater
  ): RepaymentScheduleOverrideModel.Updater = RepaymentScheduleOverrideModel.Updater(
    startAt = updater.startAt,
    endAtInclusive = updater.endAtInclusive,
    data = updater.option?.let { RepaymentScheduleOverrideModel.Data(it) }
  )
}
