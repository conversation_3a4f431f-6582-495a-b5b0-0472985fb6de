package co.highbeam.capital.repaymentschedule.endpoint

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.repaymentschedule.mapper.RepaymentScheduleOverrideMapper
import co.highbeam.capital.repaymentschedule.rep.RepaymentScheduleOverrideRep
import co.highbeam.capital.repaymentschedule.service.RepaymentScheduleOverrideService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.repaymentschedule.api.RepaymentScheduleOverrideApi as Api

internal class DeleteRepaymentScheduleOverride @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: RepaymentScheduleOverrideService,
  private val mapper: RepaymentScheduleOverrideMapper,
) : EndpointHandler<Api.Delete, RepaymentScheduleOverrideRep>(
  template = Api.Delete::class.template()
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Delete =
    Api.Delete(
      businessGuid = call.getParam("businessGuid"),
      guid = call.getParam("guid")
    )

  override suspend fun EndpointHandler<Api.Delete, RepaymentScheduleOverrideRep>.Handler.handle(
    endpoint: Api.Delete
  ): RepaymentScheduleOverrideRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val deletedModel = service.delete(
      businessGuid = endpoint.businessGuid,
      overrideGuid = endpoint.guid
    )
    return mapper.toRep(deletedModel)
  }
}
