package co.highbeam.capital.account.fixture

import co.highbeam.capital.account.event.CapitalAccountUpdateEvent
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.event.publisher.FakePubSubEventPublisher
import org.assertj.core.api.Assertions.assertThat

class CapitalAccountUpdatePublisherFixture(
  private val publisher: FakePubSubEventPublisher<CapitalAccountUpdateEvent>
) {
  private var initialSize = 0
  fun init() {
    initialSize = publisher.events.size
  }

  fun assertEventPublished(account: CapitalAccountRep, events: Int = 1) {
    assertThat(publisher.events.size).isEqualTo(initialSize + events)
    publisher.events.subList(initialSize, publisher.events.size).forEach {
      assertThat(it).isEqualTo(
        Pair(
          CapitalAccountUpdateEvent(
            businessGuid = account.businessGuid,
            capitalAccountGuid = account.guid,
          ), emptyMap<String, String>()
        )
      )
    }
  }

  fun assertNoEventPublished() {
    assertThat(publisher.events.size).isEqualTo(initialSize)
  }
}
