package co.highbeam.capital.account.endpoint

import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.testing.CapitalAccountIntegrationTest
import co.highbeam.capital.treasury.CapitalTreasurySyncService
import co.highbeam.server.Server
import co.highbeam.util.time.inUTC
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.runs
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class ActivateCapitalAccountTest(
  server: Server<*>,
) : CapitalAccountIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @Test
  fun `activate account - without accepting`() = integrationTest {
    val account = offeredCapitalAccount(businessGuid)

    assertThat(account.state).isEqualTo(CapitalAccountRep.State.Offered)
    eventAssertion.init()

    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.Activate(guid = account.guid, businessGuid = businessGuid)
        )
      }
    }
    eventAssertion.assertNoEventPublished()
  }

  @Test
  fun `activate account`() = integrationTest {
    val account = acceptedCapitalAccount(businessGuid)
    assertThat(account.state).isEqualTo(CapitalAccountRep.State.OfferAccepted)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Activate(guid = account.guid, businessGuid = businessGuid)
      )
    ).isEqualTo(
      account.copy(
        state = CapitalAccountRep.State.Active,
        activatedAt = ZonedDateTime.now(clock).inUTC(),
      )
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `activate account - idempotent`() = integrationTest {
    val account = acceptedCapitalAccount(businessGuid)
    assertThat(account.state).isEqualTo(CapitalAccountRep.State.OfferAccepted)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Activate(guid = account.guid, businessGuid = businessGuid)
      )
    ).isEqualTo(
      account.copy(
        state = CapitalAccountRep.State.Active,
        activatedAt = ZonedDateTime.now(clock).inUTC(),
      )
    )
    eventAssertion.assertEventPublished(account)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Activate(guid = account.guid, businessGuid = businessGuid)
      )
    ).isEqualTo(
      account.copy(
        state = CapitalAccountRep.State.Active,
        activatedAt = ZonedDateTime.now(clock).inUTC(),
      )
    )
    eventAssertion.assertNoEventPublished()
  }

  @Test
  fun `activate account - not found`() = integrationTest {
    eventAssertion.init()
    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.Activate(guid = UUID.randomUUID(), businessGuid = businessGuid)
        )
      }
    }
    eventAssertion.assertNoEventPublished()
  }

  @Test
  fun `accept account - terminated account`() = integrationTest {
    val account = acceptedCapitalAccount(businessGuid)

    coEvery { get<CapitalTreasurySyncService>().sync(any()) } just runs

    val terminatedAccount = capitalAccountClient(
      CapitalAccountApi.Delete(guid = account.guid, businessGuid = businessGuid)
    )
    assertThat(terminatedAccount.state).isEqualTo(CapitalAccountRep.State.Terminated)
    eventAssertion.init()

    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.Activate(guid = account.guid, businessGuid = UUID.randomUUID())
        )
      }
    }

    coVerify(exactly = 1) {
      get<CapitalTreasurySyncService>().sync(
        terminatedAccount
      )
    }

    eventAssertion.assertNoEventPublished()
  }

  @Test
  fun `accept account = mismatched business guid`() = integrationTest {
    val account = acceptedCapitalAccount(businessGuid)
    assertThat(account.state).isEqualTo(CapitalAccountRep.State.OfferAccepted)
    eventAssertion.init()

    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.Activate(guid = account.guid, businessGuid = UUID.randomUUID())
        )
      }
    }
    eventAssertion.assertNoEventPublished()
  }
}
