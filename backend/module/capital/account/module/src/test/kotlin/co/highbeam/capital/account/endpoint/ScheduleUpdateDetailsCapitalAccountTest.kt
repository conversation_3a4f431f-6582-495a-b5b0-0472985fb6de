package co.highbeam.capital.account.endpoint

import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.testing.CapitalAccountIntegrationTest
import co.highbeam.money.Money
import co.highbeam.server.Server
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.TimeUnit

internal class ScheduleUpdateDetailsCapitalAccountTest(
  server: Server<*>,
) : CapitalAccountIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @Test
  fun `change in future`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.limit).isEqualTo(Money.fromDollarsAndCents(10_000, 0))
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.ScheduleUpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(10, 0)),
          effectiveAt = LocalDate.now(clock).plusDays(2),
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )
    eventAssertion.assertEventPublished(account)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(account)

    clock.add(1, TimeUnit.DAYS)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(account)

    clock.add(1, TimeUnit.DAYS)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )
  }

  @Test
  fun `change in past`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.limit).isEqualTo(Money.fromDollarsAndCents(10_000, 0))
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(account)

    clock.add(3, TimeUnit.DAYS)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(account)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.ScheduleUpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(10, 0)),
          effectiveAt = LocalDate.now(clock).minusDays(1),
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )
    eventAssertion.assertEventPublished(account)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.ScheduleUpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(1, 0)),
          effectiveAt = LocalDate.now(clock).minusDays(2),
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(1, 0))
      )
    )
    eventAssertion.assertEventPublished(account)

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid = businessGuid, guid = account.guid)
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )
  }
}
