package co.highbeam.capital.account.endpoint

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.testing.CapitalAccountIntegrationTest
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.capital.treasury.CapitalTreasurySyncService
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.money.Money
import co.highbeam.server.Server
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import java.math.BigDecimal
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class UpdateDetailsCapitalAccountTest(
  server: Server<*>,
) : CapitalAccountIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @BeforeEach
  fun setup() {
    coEvery {
      get<CapitalTreasurySyncService>().sync(any())
    } just runs
  }

  @Test
  fun `change limit`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.limit).isEqualTo(Money.fromDollarsAndCents(10_000, 0))
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(10, 0))
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(limit = Money.fromDollarsAndCents(10, 0))
      )
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change apr`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.apr).isEqualTo(BigDecimal("0.16"))
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(apr = BigDecimal("0.01"))
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(apr = BigDecimal("0.01"))
      )
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change line type`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.lineType).isEqualTo(CapitalAccountDetailsRep.LineType.Revolving)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(
            lineType = CapitalAccountDetailsRep.LineType.NonRevolving,
          ),
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(lineType = CapitalAccountDetailsRep.LineType.NonRevolving)
      )
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change secured status`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.securedStatus)
      .isEqualTo(CapitalAccountDetailsRep.SecuredStatus.Unsecured)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(
            securedStatus = CapitalAccountDetailsRep.SecuredStatus.Secured,
          ),
        )
      )
    ).isEqualTo(
      account.copy(
        details = account.details.copy(
          securedStatus = CapitalAccountDetailsRep.SecuredStatus.Secured,
        )
      )
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `add default fees`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.defaultInterestFees).isNull()
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(defaultInterestFees = BigDecimal("0.04")),
        )
      )
    ).isEqualTo(
      account.copy(details = account.details.copy(defaultInterestFees = BigDecimal("0.04")))
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change target repayment days`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.targetRepaymentDays).isEqualTo(120)
    eventAssertion.init()

    assertThat(
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(targetRepaymentDays = 180),
        )
      )
    ).isEqualTo(
      account.copy(details = account.details.copy(targetRepaymentDays = 180))
    )
    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change repayment`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.repayment).isEqualTo(
      CapitalAccountDetailsRep.Repayment(
        option = CapitalRepaymentOption.None,
        bankAccountGuid = null,
      )
    )
    eventAssertion.init()

    val modifiedAccount =
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(
            repayment = CapitalAccountDetailsRep.Repayment(
              option = CapitalRepaymentOption.DailyInstallments(120),
              bankAccountGuid = uuidGenerator[121],
            )
          )
        )
      )
    assertThat(
      modifiedAccount
    ).isEqualTo(
      account.copy(
        details = account.details.copy(
          repayment = CapitalAccountDetailsRep.Repayment(
            option = CapitalRepaymentOption.DailyInstallments(120),
            bankAccountGuid = uuidGenerator[121],
          )
        )
      )
    )
    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid, account.guid)
      )?.details?.repayment?.option
    ).isEqualTo(CapitalRepaymentOption.DailyInstallments(120))

    coVerify(exactly = 1) {
      get<CapitalTreasurySyncService>().sync(modifiedAccount)
    }

    eventAssertion.assertEventPublished(account)
  }

  @Test
  fun `change repayment - but not bank account guid`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    assertThat(account.details.repayment).isEqualTo(
      CapitalAccountDetailsRep.Repayment(
        option = CapitalRepaymentOption.None,
        bankAccountGuid = null,
      )
    )

    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
      )
    } returns mockk { every { <EMAIL> } returns uuidGenerator[1001] }

    eventAssertion.init()

    val modifiedAccount =
      capitalAccountClient(
        CapitalAccountApi.UpdateDetails(
          guid = account.guid,
          businessGuid = businessGuid,
          updater = CapitalAccountDetailsRep.Updater(
            repayment = CapitalAccountDetailsRep.Repayment(
              option = CapitalRepaymentOption.DailyInstallments(120),
              bankAccountGuid = null,
            )
          )
        )
      )
    assertThat(modifiedAccount).isEqualTo(
      account.copy(
        details = account.details.copy(
          repayment = CapitalAccountDetailsRep.Repayment(
            option = CapitalRepaymentOption.DailyInstallments(120),
            bankAccountGuid = uuidGenerator[1001],
          )
        )
      )
    )
    assertThat(
      capitalAccountClient(
        CapitalAccountApi.Get(businessGuid, account.guid)
      )?.details?.repayment?.option
    ).isEqualTo(CapitalRepaymentOption.DailyInstallments(120))

    coVerify(exactly = 1) {
      get<CapitalTreasurySyncService>().sync(modifiedAccount)
    }

    eventAssertion.assertEventPublished(account)
  }


  @Test
  fun `account doesn't exist`() = integrationTest {
    eventAssertion.init()
    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.UpdateDetails(
            guid = UUID.randomUUID(),
            businessGuid = businessGuid,
            updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(10, 0))
          )
        )
      }
    }
    eventAssertion.assertNoEventPublished()
  }

  @Test
  fun `guid business guid mismatch`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    eventAssertion.init()

    assertThatThrownBy {
      runBlocking {
        capitalAccountClient(
          CapitalAccountApi.UpdateDetails(
            guid = account.guid,
            businessGuid = UUID.randomUUID(),
            updater = CapitalAccountDetailsRep.Updater(limit = Money.fromDollarsAndCents(10, 0))
          )
        )
      }
    }
    eventAssertion.assertNoEventPublished()
  }
}
