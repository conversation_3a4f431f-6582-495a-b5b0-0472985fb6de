package co.highbeam.capital.account.endpoint.offer

import co.highbeam.capital.account.api.CapitalOfferApi
import co.highbeam.capital.account.rep.CapitalOfferRep
import co.highbeam.capital.account.testing.CapitalAccountIntegrationTest
import co.highbeam.server.Server
import co.highbeam.util.time.inUTC
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class GetLastOfferByCapitalAccountTest(
  server: Server<*>,
) : CapitalAccountIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @Test
  fun `default values`() = integrationTest {
    val account = activeCapitalAccount(businessGuid)
    val offer = capitalOfferClient(
      CapitalOfferApi.GetLastByCapitalAccount(
        businessGuid = businessGuid,
        capitalAccountGuid = account.guid,
      )
    )
    assertThat(offer).isEqualTo(
      CapitalOfferRep(
        guid = uuidGenerator[2],
        generatedAt = ZonedDateTime.now(clock).inUTC(),
        autoActivate = false,
      )
    )
  }
}
