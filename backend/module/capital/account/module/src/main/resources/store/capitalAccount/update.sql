update credit.capital_account
set state         = coalesce(:state, state),
    name          = coalesce(:name, name),
    type          = coalesce(:type, type),
    lender        = coalesce(:lender, lender),
    activated_at  = coalesce(:activatedAt, activated_at),
    terminated_at = coalesce(:terminatedAt, terminated_at),
    controls      = coalesce(:controls, controls)
where guid = :guid
  and business_guid = :businessGuid
returning *
