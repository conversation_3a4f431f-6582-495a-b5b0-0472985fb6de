package co.highbeam.capital.account.endpoint

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.service.CapitalAccountService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import java.time.Clock
import java.time.ZonedDateTime
import co.highbeam.capital.account.api.CapitalAccountApi as Api

internal class UpdateCapitalAccountDetails @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalAccountService,
  private val clock: Clock,
) : EndpointHandler<Api.UpdateDetails, CapitalAccountRep>(
  template = Api.UpdateDetails::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.UpdateDetails =
    Api.UpdateDetails(
      businessGuid = call.getParam("businessGuid"),
      guid = call.getParam("guid"),
      updater = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.UpdateDetails): CapitalAccountRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.updateDetails(
      guid = endpoint.guid,
      businessGuid = endpoint.businessGuid,
      effectiveAt = ZonedDateTime.now(clock),
      updater = endpoint.updater,
    )
  }
}
