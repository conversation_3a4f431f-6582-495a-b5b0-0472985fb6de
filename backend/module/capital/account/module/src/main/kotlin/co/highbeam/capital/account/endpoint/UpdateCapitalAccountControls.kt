package co.highbeam.capital.account.endpoint

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.service.CapitalAccountService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.account.api.CapitalAccountApi as Api

internal class UpdateCapitalAccountControls @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: CapitalAccountService,
) : EndpointHandler<Api.UpdateControls, CapitalAccountRep>(
  template = Api.UpdateControls::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.UpdateControls =
    Api.UpdateControls(
      businessGuid = call.getParam("businessGuid"),
      guid = call.getParam("guid"),
      updater = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.UpdateControls): CapitalAccountRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.updateControls(
      guid = endpoint.guid,
      businessGuid = endpoint.businessGuid,
      updater = endpoint.updater,
    )
  }
}
