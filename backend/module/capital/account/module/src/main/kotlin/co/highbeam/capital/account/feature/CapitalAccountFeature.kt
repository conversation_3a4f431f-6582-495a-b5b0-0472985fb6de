package co.highbeam.capital.account.feature

import co.highbeam.capital.account.endpoint.AcceptCapitalAccount
import co.highbeam.capital.account.endpoint.ActivateCapitalAccount
import co.highbeam.capital.account.endpoint.CreateCapitalAccount
import co.highbeam.capital.account.endpoint.DeleteCapitalAccount
import co.highbeam.capital.account.endpoint.GetAllCapitalAccounts
import co.highbeam.capital.account.endpoint.GetCapitalAccount
import co.highbeam.capital.account.endpoint.ScheduleUpdateCapitalAccountDetails
import co.highbeam.capital.account.endpoint.UpdateCapitalAccount
import co.highbeam.capital.account.endpoint.UpdateCapitalAccountControls
import co.highbeam.capital.account.endpoint.UpdateCapitalAccountDetails
import co.highbeam.capital.account.endpoint.UpdateCapitalAccountRepaymentAccountDetails
import co.highbeam.capital.account.endpoint.internal.GetAllCapitalAccountDetails
import co.highbeam.capital.account.endpoint.internal.GetCapitalAccountDetails
import co.highbeam.capital.account.endpoint.offer.CreateCapitalOffer
import co.highbeam.capital.account.endpoint.offer.GetLastOfferByCapitalAccount
import co.highbeam.capital.account.endpoint.offer.UpdateCapitalOffer
import co.highbeam.capital.account.endpoint.summary.GetCapitalAccountSummary
import co.highbeam.capital.account.event.publisher.CapitalAccountUpdatePublisherFactory
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import com.google.inject.Injector
import com.google.inject.Key
import com.google.inject.TypeLiteral

class CapitalAccountFeature : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()

  override fun bind() {
    bindCapitalAccountEndpoints()
    bindCapitalAccountInternalEndpoints()
    bindCapitalAccountSummaryEndpoints()
    bindPublishers()
  }

  override fun stop(injector: Injector) {
    publishers.forEach { injector.getInstance(Key.get(it)).close() }
  }

  private fun bindPublishers() {
    CapitalAccountUpdatePublisherFactory.bind(binder(), publishers)
  }

  private fun bindCapitalAccountInternalEndpoints() {
    bind(GetCapitalAccountDetails::class.java).asEagerSingleton()
    bind(GetAllCapitalAccountDetails::class.java).asEagerSingleton()
  }

  private fun bindCapitalAccountEndpoints() {
    bind(AcceptCapitalAccount::class.java).asEagerSingleton()
    bind(ActivateCapitalAccount::class.java).asEagerSingleton()
    bind(CreateCapitalAccount::class.java).asEagerSingleton()
    bind(CreateCapitalOffer::class.java).asEagerSingleton()

    bind(GetAllCapitalAccounts::class.java).asEagerSingleton()
    bind(GetCapitalAccount::class.java).asEagerSingleton()
    bind(GetLastOfferByCapitalAccount::class.java).asEagerSingleton()

    bind(ScheduleUpdateCapitalAccountDetails::class.java).asEagerSingleton()
    bind(UpdateCapitalAccount::class.java).asEagerSingleton()
    bind(UpdateCapitalAccountControls::class.java).asEagerSingleton()
    bind(UpdateCapitalAccountDetails::class.java).asEagerSingleton()
    bind(UpdateCapitalAccountRepaymentAccountDetails::class.java).asEagerSingleton()
    bind(UpdateCapitalOffer::class.java).asEagerSingleton()

    bind(DeleteCapitalAccount::class.java).asEagerSingleton()
  }

  private fun bindCapitalAccountSummaryEndpoints() {
    bind(GetCapitalAccountSummary::class.java).asEagerSingleton()
  }
}
