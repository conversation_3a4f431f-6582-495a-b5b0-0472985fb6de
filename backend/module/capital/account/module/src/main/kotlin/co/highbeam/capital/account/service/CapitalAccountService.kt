package co.highbeam.capital.account.service

import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import com.google.inject.ImplementedBy
import java.time.ZonedDateTime
import java.util.UUID

@ImplementedBy(CapitalAccountServiceImpl::class)
@SuppressWarnings("ComplexInterface")
interface CapitalAccountService {
  suspend fun create(businessGuid: UUID, creator: CapitalAccountRep.Creator): CapitalAccountRep

  suspend fun accept(businessGuid: UUID, guid: UUID): CapitalAccountRep

  suspend fun activate(businessGuid: UUID, guid: UUID): CapitalAccountRep

  suspend fun get(businessGuid: UUID, guid: UUID): CapitalAccountRep?

  suspend fun getAll(businessGuid: UUID?): List<CapitalAccountRep>

  suspend fun update(
    businessGuid: UUID,
    guid: UUID,
    updater: CapitalAccountRep.Updater,
  ): CapitalAccountRep

  suspend fun updateDetails(
    businessGuid: UUID,
    guid: UUID,
    effectiveAt: ZonedDateTime,
    updater: CapitalAccountDetailsRep.Updater,
  ): CapitalAccountRep

  suspend fun updateRepaymentAccountDetails(
    businessGuid: UUID,
    guid: UUID,
    effectiveAt: ZonedDateTime,
    updater: CapitalAccountDetailsRep.RepaymentAccountUpdater,
  ): CapitalAccountRep

  suspend fun updateControls(
    businessGuid: UUID,
    guid: UUID,
    updater: CapitalAccountControlsRep.Updater,
  ): CapitalAccountRep

  suspend fun delete(businessGuid: UUID, guid: UUID): CapitalAccountRep
}
