package co.highbeam.capital.account.service

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.capital.account.event.publisher.CapitalAccountUpdatePublisher
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.account.mapper.CapitalAccountMapper
import co.highbeam.capital.account.model.CapitalAccountControlsModel
import co.highbeam.capital.account.model.CapitalAccountDetailsModel
import co.highbeam.capital.account.model.CapitalAccountModel
import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.account.store.CapitalAccountDetailStore
import co.highbeam.capital.account.store.CapitalAccountStore
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.capital.treasury.CapitalTreasurySyncService
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.unprocessable
import co.highbeam.sql.store.transaction
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

class CapitalAccountServiceImpl @Inject constructor(
  private val store: CapitalAccountStore,
  private val detailStore: CapitalAccountDetailStore,
  private val mapper: CapitalAccountMapper,
  private val publisher: CapitalAccountUpdatePublisher,
  private val jdbi: Jdbi,
  private val clock: Clock,
  private val bankAccountClient: BankAccountClient,
  private val treasurySyncService: CapitalTreasurySyncService,
) : CapitalAccountService {
  override suspend fun create(
    businessGuid: UUID,
    creator: CapitalAccountRep.Creator
  ): CapitalAccountRep {
    val model = mapper.toModel(businessGuid = businessGuid, creator = creator)
    val detailModel = mapper.toDetailModel(
      businessGuid = businessGuid,
      capitalAccountGuid = model.guid,
      creator = creator,
    )
    if (creator.lender != CapitalLender.Highbeam) {
      validateExternalLenderProduct(creator)
    }

    jdbi.transaction {
      store.create(model)
      detailStore.create(detailModel)
    }
    return toRep(model = model, details = detailModel)
  }

  private fun validateExternalLenderProduct(creator: CapitalAccountRep.Creator) {
    require(creator.type == CapitalAccountRep.Type.CashAccessOnly) {
      "External lenders can only create cash access only accounts."
    }
  }

  private fun validateExternalLenderProduct(model: CapitalAccountModel) {
    require(model.type == CapitalAccountRep.Type.CashAccessOnly) {
      "External lenders can only create cash access only accounts."
    }
    require(model.state == CapitalAccountRep.State.Offered) {
      "External lenders can only update offered accounts."
    }
  }

  override suspend fun accept(businessGuid: UUID, guid: UUID): CapitalAccountRep {
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: throw unprocessable(CapitalAccountNotFound())

    if (model.state == CapitalAccountRep.State.OfferAccepted) {
      return toRep(model = model, details = details)
    }

    require(model.state == CapitalAccountRep.State.Offered)

    val updatedModel = store.update(
      businessGuid = businessGuid,
      guid = guid,
      updater = CapitalAccountModel.Updater(
        state = CapitalAccountRep.State.OfferAccepted,
      ),
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    return toRep(model = updatedModel, details = details)
  }

  override suspend fun activate(businessGuid: UUID, guid: UUID): CapitalAccountRep {
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: throw unprocessable(CapitalAccountNotFound())

    if (model.state == CapitalAccountRep.State.Active) {
      return toRep(model = model, details = details)
    }

    require(model.state == CapitalAccountRep.State.OfferAccepted)

    val updatedModel = store.update(
      businessGuid = businessGuid,
      guid = guid,
      updater = CapitalAccountModel.Updater(
        state = CapitalAccountRep.State.Active,
        activatedAt = ZonedDateTime.now(clock),
      ),
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    return toRep(model = updatedModel, details = details)
  }

  override suspend fun get(businessGuid: UUID, guid: UUID): CapitalAccountRep? {
    val model = store.get(guid = guid, businessGuid = businessGuid) ?: return null
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: return null

    return toRep(model = model, details = details)
  }

  override suspend fun getAll(businessGuid: UUID?): List<CapitalAccountRep> {
    val allAccounts = if (businessGuid == null) store.getAll() else store.getAll(businessGuid)
    if (allAccounts.isEmpty()) return emptyList()

    val allAccountsDetails = detailStore.getMultiple(
      capitalAccountGuids = allAccounts.map { it.guid },
      beforeOrEqual = ZonedDateTime.now(clock),
    ).associateBy { it.capitalAccountGuid }
    return allAccounts.mapNotNull {
      val details = allAccountsDetails[it.guid] ?: return@mapNotNull null

      return@mapNotNull toRep(model = it, details = details)
    }
  }

  override suspend fun update(
    businessGuid: UUID,
    guid: UUID,
    updater: CapitalAccountRep.Updater
  ): CapitalAccountRep {
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: throw unprocessable(CapitalAccountNotFound())
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())

    if (updater.lender != null && updater.lender != model.lender) {
      validateExternalLenderProduct(model)
    }

    val updatedModel = store.update(
      businessGuid = businessGuid,
      guid = guid,
      updater = CapitalAccountModel.Updater(
        name = updater.name,
        type = updater.type,
        lender = updater.lender,
      ),
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    return toRep(model = updatedModel, details = details)
  }

  override suspend fun updateDetails(
    businessGuid: UUID,
    guid: UUID,
    effectiveAt: ZonedDateTime,
    updater: CapitalAccountDetailsRep.Updater,
  ): CapitalAccountRep {
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = effectiveAt,
    ) ?: throw unprocessable(CapitalAccountNotFound())

    val updatedDetails = detailStore.create(
      mapper.toUpdatedDetailModel(
        businessGuid = businessGuid,
        capitalAccountGuid = guid,
        details = details,
        updater = updater,
        effectiveAt = effectiveAt,
      )
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    val result = toRep(model = model, details = updatedDetails)

    if (updater.repayment != null) {
      treasurySyncService.sync(capitalAccount = result)
    }

    return result
  }

  override suspend fun updateRepaymentAccountDetails(
    businessGuid: UUID,
    guid: UUID,
    effectiveAt: ZonedDateTime,
    updater: CapitalAccountDetailsRep.RepaymentAccountUpdater,
  ): CapitalAccountRep {
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = effectiveAt,
    ) ?: throw unprocessable(CapitalAccountNotFound())

    val updatedDetails = detailStore.create(
      mapper.toUpdatedDetailModel(
        details = details,
        updater = updater,
        effectiveAt = effectiveAt,
      )
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    val result = toRep(model = model, details = updatedDetails)

    treasurySyncService.sync(capitalAccount = result)

    return result
  }

  override suspend fun updateControls(
    businessGuid: UUID,
    guid: UUID,
    updater: CapitalAccountControlsRep.Updater,
  ): CapitalAccountRep {
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: throw unprocessable(CapitalAccountNotFound())

    val updatedModel = store.update(
      businessGuid = businessGuid,
      guid = guid,
      updater = CapitalAccountModel.Updater(
        controls = CapitalAccountControlsModel.Updater(
          drawdownEnabled = updater.drawdownEnabled,
          chargeCardEnabled = updater.chargeCardEnabled,
          totalDrawdownLimit = updater.totalDrawdownLimit,
          drawdownRequiresApproval = updater.drawdownRequiresApproval,
          drawdownPeriodEndsAt = updater.drawdownPeriodEndsAt,
          drawdownRateLimit = updater.drawdownRateLimit,
        ),
      ),
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    return toRep(model = updatedModel, details = details)
  }

  override suspend fun delete(businessGuid: UUID, guid: UUID): CapitalAccountRep {
    val model = store.get(guid = guid, businessGuid = businessGuid)
      ?: throw unprocessable(CapitalAccountNotFound())
    val details = detailStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = guid,
      beforeOrEqual = ZonedDateTime.now(clock),
    ) ?: throw unprocessable(CapitalAccountNotFound())

    if (model.state == CapitalAccountRep.State.Terminated) {
      return toRep(model = model, details = details)
    }

    val updatedModel = store.update(
      businessGuid = businessGuid,
      guid = guid,
      updater = CapitalAccountModel.Updater(
        state = CapitalAccountRep.State.Terminated,
        terminatedAt = ZonedDateTime.now(clock),
      ),
    ).also { publisher(businessGuid = businessGuid, guid = guid) }

    val result = toRep(model = updatedModel, details = details)

    treasurySyncService.sync(capitalAccount = result)

    return result
  }

  suspend fun toRep(
    model: CapitalAccountModel,
    details: CapitalAccountDetailsModel
  ): CapitalAccountRep {
    val repaymentOption = details.data.repayment.option

    // NB(jashan): Set the bankAccountGuid during update following a migration in future
    //   so the toRep can be simple. We should not have this business logic here.
    val bankAccountGuid = when (repaymentOption) {
      is CapitalRepaymentOption.None -> null
      is CapitalRepaymentOption.PayoutPercentage, is CapitalRepaymentOption.DailyInstallments -> {
        details.data.repayment.bankAccountGuid ?: bankAccountClient.request(
          BankAccountApi.GetPrimaryBankAccountByBusinessGuid(
            businessGuid = model.businessGuid,
          )
        )?.guid
      }
    }

    val repaymentDetails = CapitalAccountDetailsRep.Repayment(
      option = repaymentOption,
      bankAccountGuid = bankAccountGuid,
    )
    return mapper.toRep(model = model, details = details, repaymentDetails = repaymentDetails)
  }
}
