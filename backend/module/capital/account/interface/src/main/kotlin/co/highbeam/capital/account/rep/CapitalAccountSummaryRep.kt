package co.highbeam.capital.account.rep

import co.highbeam.money.Balance
import co.highbeam.rep.CompleteRep
import java.time.ZonedDateTime
import java.util.UUID

data class CapitalAccountSummaryRep(
  val name: String,
  val type: CapitalAccountRep.Type,
  val guid: UUID,
  val businessGuid: UUID,
  val runningBalance: Balance,
  val cardBalance: Balance,
  val cardPending: Balance,
  val details: CapitalAccountDetailsRep,
  val state: CapitalAccountRep.State,
  val controls: CapitalAccountControlsRep,
  val lender: CapitalLender,
  val activatedAt: ZonedDateTime?,
) : CompleteRep {
  val available: Balance
    get() = Balance.fromCents(details.limit.rawCents) + runningBalance + cardBalance + cardPending
}
