package co.highbeam.capital.account.api

import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.time.LocalDate
import java.util.UUID

object CapitalAccountApi {
  data class Create(val businessGuid: UUID, val creator: CapitalAccountRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/capital-accounts",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
    body = creator,
  )

  data class Accept(val guid: UUID, val businessGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/capital-accounts/$guid/accept",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
  )

  data class Activate(val guid: UUID, val businessGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/capital-accounts/$guid/activate",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
  )

  data class GetAll(val businessGuid: UUID? = null) : Endpoint(
    path = "/capital-accounts",
    qp = buildMap {
      businessGuid?.let { put("businessGuid", listOf(businessGuid.toString())) }
    },
  )

  data class Get(
    val businessGuid: UUID,
    val guid: UUID,
  ) : Endpoint(
    path = "/capital-accounts/$guid",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
  )

  data class Update(
    val guid: UUID,
    val businessGuid: UUID,
    val updater: CapitalAccountRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/capital-accounts/$guid",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
    body = updater,
  )

  data class UpdateDetails(
    val guid: UUID,
    val businessGuid: UUID,
    val updater: CapitalAccountDetailsRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/capital-accounts/$guid/details",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
    body = updater,
  )

  data class UpdateRepaymentDetails(
    val guid: UUID,
    val businessGuid: UUID,
    val updater: CapitalAccountDetailsRep.RepaymentAccountUpdater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/capital-accounts/$guid/details/repayment-account",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
    body = updater,
  )

  data class ScheduleUpdateDetails(
    val guid: UUID,
    val businessGuid: UUID,
    val updater: CapitalAccountDetailsRep.Updater,
    val effectiveAt: LocalDate,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/capital-accounts/$guid/details",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "effectiveAt" to listOf(effectiveAt.toString()),
    ),
    body = updater,
  )

  data class UpdateControls(
    val guid: UUID,
    val businessGuid: UUID,
    val updater: CapitalAccountControlsRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/capital-accounts/$guid/controls",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
    body = updater,
  )

  data class Delete(
    val businessGuid: UUID,
    val guid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Delete,
    path = "/capital-accounts/$guid",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    ),
  )
}
