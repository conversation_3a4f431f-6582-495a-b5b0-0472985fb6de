package co.highbeam.capital.account.rep

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.time.ZonedDateTime
import java.util.UUID

data class CapitalAccountRep(
  val name: String,
  val type: Type,
  val guid: UUID,
  val businessGuid: UUID,
  val details: CapitalAccountDetailsRep,
  val state: State,
  val lender: CapitalLender,
  val controls: CapitalAccountControlsRep,
  val activatedAt: ZonedDateTime?,
  val terminatedAt: ZonedDateTime?,
) : CompleteRep {
  enum class State { Active, Terminated, Offered, OfferAccepted }

  enum class Type { CashAccessOnly, ChargeCardAndCashAccess, ChargeCardOnly }

  data class Creator(
    val name: String,
    val type: Type,
    val lender: CapitalLender = CapitalLender.Highbeam,
    val details: CapitalAccountDetailsRep.Creator,
    val controls: CapitalAccountControlsRep.Creator,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Updater(
    val name: String? = null,
    val lender: CapitalLender? = null,
    val type: Type? = null,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
