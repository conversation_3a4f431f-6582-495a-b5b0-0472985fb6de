package co.highbeam.capital.account.rep

import co.highbeam.money.Money
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.time.LocalDate

data class CapitalAccountControlsRep(
  val drawdownEnabled: Boolean,
  val chargeCardEnabled: Boolean? = null,
  val totalDrawdownLimit: Money? = null,
  val drawdownRequiresApproval: Boolean? = null,
  val drawdownPeriodEndsAt: LocalDate? = null,
  val drawdownRateLimit: Money? = null,
) {
  data class Creator(
    val drawdownEnabled: Boolean = false,
    val chargeCardEnabled: Boolean? = null,
    val totalDrawdownLimit: Money? = null,
    val drawdownRequiresApproval: Boolean? = null,
    val drawdownPeriodEndsAt: LocalDate? = null,
    val drawdownRateLimit: Money? = null,
  )

  data class Updater(
    val drawdownEnabled: Boolean? = null,
    val chargeCardEnabled: Boolean? = null,
    val totalDrawdownLimit: Money? = null,
    val drawdownRequiresApproval: Boolean? = null,
    val drawdownPeriodEndsAt: LocalDate? = null,
    val drawdownRateLimit: Money? = null,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
