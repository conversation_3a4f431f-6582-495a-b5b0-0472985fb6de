package co.highbeam.capital.repayment.rep

import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.util.UUID

data class CapitalRepaymentRep(
  val repayments: List<Complete>,
) : CompleteRep {
  enum class RepaymentProduct { ChargeCardRepayment, LineRepayment }

  data class Creator(
    val amount: Money,
    val bankAccountGuid: UUID,
    val idempotencyKey: UUID,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Complete(
    val amount: Money,
    val repaymentProduct: RepaymentProduct,
    val unitCoId: String,
  )
}
