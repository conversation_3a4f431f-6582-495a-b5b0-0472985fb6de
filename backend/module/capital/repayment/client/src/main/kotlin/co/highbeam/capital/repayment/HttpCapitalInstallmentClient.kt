package co.highbeam.capital.repayment

import co.highbeam.capital.repayment.api.CapitalInstallmentApi
import co.highbeam.capital.repayment.feature.CAPITAL_REPAYMENT_FEATURE
import co.highbeam.capital.repayment.rep.CapitalDebtScheduleRep
import co.highbeam.capital.repayment.rep.CapitalUnpaidDrawdownRep
import co.highbeam.client.HttpClient
import com.google.inject.Inject
import com.google.inject.name.Named

class HttpCapitalInstallmentClient @Inject constructor(
  @Named(CAPITAL_REPAYMENT_FEATURE) private val httpClient: HttpClient,
) : CapitalInstallmentClient {
  override suspend fun invoke(endpoint: CapitalInstallmentApi.Get): List<CapitalUnpaidDrawdownRep> =
    httpClient.request(endpoint).readValue()

  override suspend fun invoke(
    endpoint: CapitalInstallmentApi.GetDebtSchedule,
  ): List<CapitalDebtScheduleRep> = httpClient.request(endpoint).readValue()
}
