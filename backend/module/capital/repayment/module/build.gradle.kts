plugins {
  id("highbeam-jvm")
}

dependencies {
  implementation(project(":common:sql"))
  implementation(project(":module:treasury:client"))

  api(project(":common:feature"))
  api(project(":common:rutter"))
  api(project(":module:capital:account:client"))
  api(project(":module:capital:credit:client"))
  api(project(":module:capital:repayment:interface"))
  api(project(":module:capital:repayment-schedule:client"))
  api(project(":module:capital:charge-card:client"))
  api(project(":module:capital:transaction:client"))

  testImplementation(project(":module:capital:repayment:client"))
  testImplementation(project(":module:capital:repayment-schedule:client"))
  testImplementation(project(":module:capital:transaction:testing"))
  testImplementation(project(":module:capital:account:testing"))
  testImplementation(project(":module:capital:repayment:testing"))
  testImplementation(project(":module:capital:charge-card:testing"))
  testImplementation(project(":common:integration-testing"))
  testImplementation(project(":common:rest-feature:testing"))
  testImplementation(project(":common:sql:testing"))
  testImplementation(project(":db:highbeam"))
}
