package co.highbeam.capital.repayment.endpoint.installment

import co.highbeam.capital.repayment.api.CapitalInstallmentApi
import co.highbeam.capital.repayment.rep.CapitalUnpaidDrawdownRep
import co.highbeam.capital.repayment.testing.CapitalRepaymentIntegrationTest
import co.highbeam.capital.transaction.CapitalTransactionsClient
import co.highbeam.capital.transaction.api.CapitalTransactionsApi
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.server.Server
import co.highbeam.util.time.inUTC
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class GetUnpaidDrawdownsTest(
  server: Server<*>,
) : CapitalRepaymentIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val capitalAccountGuid: UUID = UUID.randomUUID()

  @Test
  fun `Single Draw`() = integrationTest {
    mockCapitalTransaction(listOf(Balance.fromDollars(-20000)))
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).inUTC(),
          drawdownAmount = Money.fromDollars(20000),
          repaymentAmount = Money.ZERO,
        )
      )
    )
  }

  @Test
  fun `Multi Draw`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(-20000),
        Balance.fromDollars(-10000),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).inUTC(),
          drawdownAmount = Money.fromDollars(20000),
          repaymentAmount = Money.ZERO,
        ),
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(1).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.ZERO,
        )
      )
    )
  }

  @Test
  fun `Multi Draw - single repayment`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(-20000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(20000),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).inUTC(),
          drawdownAmount = Money.fromDollars(20000),
          repaymentAmount = Money.fromCents(1333333),
        ),
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(1).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.fromCents(666667),
        )
      )
    )
  }

  @Test
  fun `Multi Draw - penny repayment`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(-20000),
        Balance.fromDollarsAndCents(19999, 99),
        Balance.fromDollars(-10000),
        Balance.fromCents(1),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(2).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.ZERO,
        )
      )
    )
  }

  @Test
  fun `Multi Draw - multi repayment`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(-20000),
        Balance.fromDollars(10000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(15000),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(2).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.fromDollars(5000),
        ),
      )
    )
  }

  @Test
  fun `Multi Draw - multi repayment - multi prorate`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(-20000),
        Balance.fromDollars(10000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(12000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(16000),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(4).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.fromDollars(8000),
        ),
      )
    )
  }

  @Test
  fun `Multi Draw - multi extra repayment - multi prorate`() = integrationTest {
    mockCapitalTransaction(
      amounts = listOf(
        Balance.fromDollars(20000),
        Balance.fromDollars(-40000),
        Balance.fromDollars(10000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(12000),
        Balance.fromDollars(-10000),
        Balance.fromDollars(16000),
      )
    )
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = null,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(5).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.fromDollars(8000),
        ),
      )
    )
  }

  @Test
  fun `Multi Draw - multi extra repayment - multi prorate - till date`() = integrationTest {
    coEvery {
      get<CapitalTransactionsClient>()(
        CapitalTransactionsApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          sinceInclusive = null,
          untilInclusive = ZonedDateTime.now(clock).plusDays(4).toLocalDate(),
        )
      )
    } returns listOf(
      Balance.fromDollars(20000),
      Balance.fromDollars(-40000),
      Balance.fromDollars(10000),
      Balance.fromDollars(-10000),
      Balance.fromDollars(12000),
      Balance.fromDollars(-10000),
      Balance.fromDollars(16000),
    ).mapIndexed { index, amount ->
      CapitalTransactionRep(
        capitalAccountGuid = capitalAccountGuid,
        businessGuid = businessGuid,
        unitCoId = UUID.randomUUID().toString(),
        amount = amount,
        date = ZonedDateTime.now(clock).plusDays(index.toLong()),
        summary = "",
        unitCoDepositAccountId = "",
        unitCoCounterpartyDepositAccountId = null,
        type = null,
      )
    }
    assertThat(
      installmentClient(
        CapitalInstallmentApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          untilInclusive = ZonedDateTime.now(clock).plusDays(4).toLocalDate(),
        )
      )
    ).isEqualTo(
      listOf(
        CapitalUnpaidDrawdownRep(
          drawdownDate = ZonedDateTime.now(clock).plusDays(5).inUTC(),
          drawdownAmount = Money.fromDollars(10000),
          repaymentAmount = Money.fromDollars(8000),
        ),
      )
    )
  }

  private fun mockCapitalTransaction(amounts: List<Balance>) {
    coEvery {
      get<CapitalTransactionsClient>()(
        CapitalTransactionsApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid,
          sinceInclusive = null,
          untilInclusive = null,
        )
      )
    } returns amounts.mapIndexed { index, amount ->
      CapitalTransactionRep(
        capitalAccountGuid = capitalAccountGuid,
        businessGuid = businessGuid,
        unitCoId = UUID.randomUUID().toString(),
        amount = amount,
        date = ZonedDateTime.now(clock).plusDays(index.toLong()),
        summary = "",
        unitCoDepositAccountId = "",
        unitCoCounterpartyDepositAccountId = null,
        type = null,
      )
    }
  }
}
