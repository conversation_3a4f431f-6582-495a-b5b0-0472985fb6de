package co.highbeam.capital.repayment.service

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.account.CapitalAccountSummaryClient
import co.highbeam.capital.account.api.CapitalAccountSummaryApi
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.ChargeCardRepaymentClient
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi
import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.capital.repayment.rep.CapitalRepaymentRep
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep.Status.Sent
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import java.util.UUID

class CapitalRepaymentServiceImpl @Inject constructor(
  private val lineOfCreditTransactionClient: LineOfCreditTransactionsClient,
  private val capitalAccountSummaryClient: CapitalAccountSummaryClient,
  private val chargeCardRepaymentClient: ChargeCardRepaymentClient,
  private val chargeCardAccountClient: ChargeCardAccountClient,
  private val bankAccountClient: BankAccountClient,
  private val uuidGenerator: UuidGenerator,
) : CapitalRepaymentService {
  override suspend fun createRepayment(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    creator: CapitalRepaymentRep.Creator,
  ): CapitalRepaymentRep {
    if (!hasEnoughBalance(bankAccountGuid = creator.bankAccountGuid, amount = creator.amount)) {
      return CapitalRepaymentRep(listOf())
    }

    val overdueAmount = overdueAmount(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      amount = creator.amount,
    )
    val lineRepayment = repayOverdueBalance(
      overdueAmount = overdueAmount,
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      bankAccountGuid = creator.bankAccountGuid,
      idempotencyKey = creator.idempotencyKey,
    )

    val remainingAmount = creator.amount - overdueAmount
    val chargeCardRepayment = repayChargeCard(
      remainingAmount = remainingAmount,
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      bankAccountGuid = creator.bankAccountGuid,
      idempotencyKey = if (lineRepayment == null) {
        creator.idempotencyKey
      } else {
        // Don't use the client idempotency key if a payment has already been sent towards overdue
        //  balance.
        uuidGenerator.generate()
      }
    )

    return CapitalRepaymentRep(listOfNotNull(lineRepayment, chargeCardRepayment))
  }

  private suspend fun hasEnoughBalance(
    bankAccountGuid: UUID,
    amount: Money,
  ): Boolean {
    val bankAccount =
      bankAccountClient.request(BankAccountApi.Get(bankAccountGuid))
        ?: throw unprocessable(BankAccountNotFound())
    return bankAccount.availableBalance >= Balance.fromCents(amount.rawCents)
  }

  private suspend fun overdueAmount(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    amount: Money,
  ): Money {
    val capitalAccount = getCapitalAccount(businessGuid, capitalAccountGuid)
    if (capitalAccount.runningBalance >= Balance.ZERO) return Money.ZERO
    return Money.fromCents(
      minOf(
        amount.rawCents,
        -1 * capitalAccount.runningBalance.rawCents
      )
    )
  }

  private suspend fun getCapitalAccount(
    businessGuid: UUID,
    capitalAccountGuid: UUID
  ) = capitalAccountSummaryClient(
    CapitalAccountSummaryApi.Get(businessGuid = businessGuid, guid = capitalAccountGuid)
  ) ?: throw unprocessable(CapitalAccountNotFound())

  private suspend fun repayOverdueBalance(
    overdueAmount: Money,
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    bankAccountGuid: UUID,
    idempotencyKey: UUID,
  ): CapitalRepaymentRep.Complete? {
    if (overdueAmount <= Money.ZERO) {
      return null
    }

    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateRepayment(
        businessGuid = businessGuid,
        creditAccountGuid = capitalAccountGuid,
        rep = CapitalRepaymentTransactionRep.Creator(
          amount = overdueAmount,
          idempotencyKey = idempotencyKey,
          bankAccountGuid = bankAccountGuid,
        ),
      )
    )

    if (res.status != Sent) {
      return null
    }

    return CapitalRepaymentRep.Complete(
      amount = overdueAmount,
      repaymentProduct = CapitalRepaymentRep.RepaymentProduct.LineRepayment,
      unitCoId = res.unitCoId,
    )
  }

  private suspend fun repayChargeCard(
    remainingAmount: Money,
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    bankAccountGuid: UUID,
    idempotencyKey: UUID,
  ): CapitalRepaymentRep.Complete? {
    if (remainingAmount <= Money.ZERO) {
      return null
    }

    val chargeCardAccount = chargeCardAccountClient(
      ChargeCardAccountApi.Get(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccountGuid,
      )
    ) ?: throw unprocessable(ChargeCardAccountNotFound())

    val res = chargeCardRepaymentClient(
      ChargeCardRepaymentApi.Create(
        businessGuid = businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
        rep = ChargeCardRepaymentRep.Creator(
          amount = remainingAmount,
          idempotencyKey = idempotencyKey,
          bankAccountGuid = bankAccountGuid,
        )
      )
    )

    return CapitalRepaymentRep.Complete(
      amount = remainingAmount,
      repaymentProduct = CapitalRepaymentRep.RepaymentProduct.ChargeCardRepayment,
      unitCoId = res.unitCoTransactionId,
    )
  }
}
