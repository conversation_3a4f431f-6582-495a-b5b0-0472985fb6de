package co.highbeam.capital.repayment.feature

import co.highbeam.capital.repayment.endpoint.CreateRepayment
import co.highbeam.capital.repayment.endpoint.GetRepaymentAmount
import co.highbeam.capital.repayment.endpoint.installment.GetDebtSchedule
import co.highbeam.capital.repayment.endpoint.installment.GetUnpaidDrawdowns
import co.highbeam.feature.Feature

class CapitalRepaymentFeature : Feature() {
  override fun bind() {
    bind(CreateRepayment::class.java).asEagerSingleton()

    bind(GetRepaymentAmount::class.java).asEagerSingleton()

    bind(GetUnpaidDrawdowns::class.java).asEagerSingleton()
    bind(GetDebtSchedule::class.java).asEagerSingleton()
  }
}
