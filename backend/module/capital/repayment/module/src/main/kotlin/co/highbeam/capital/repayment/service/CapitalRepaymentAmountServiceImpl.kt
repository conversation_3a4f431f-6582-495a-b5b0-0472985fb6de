package co.highbeam.capital.repayment.service

import co.highbeam.capital.account.CapitalAccountSummaryClient
import co.highbeam.capital.account.api.CapitalAccountSummaryApi
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.account.rep.CapitalAccountSummaryRep
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.ChargeCardRepaymentClient
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi
import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.capital.chargeCard.rep.ChargeCardCreditTerm
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentInfoRep
import co.highbeam.capital.repayment.rep.CapitalRepaymentAmountRep
import co.highbeam.exception.unprocessable
import co.highbeam.money.Balance
import co.highbeam.money.Money
import com.google.inject.Inject
import java.math.BigDecimal
import java.math.MathContext
import java.time.Clock
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.math.min

class CapitalRepaymentAmountServiceImpl @Inject constructor(
  private val capitalAccountSummaryClient: CapitalAccountSummaryClient,
  private val chargeCardAccountClient: ChargeCardAccountClient,
  private val chargeCardRepaymentClient: ChargeCardRepaymentClient,
  private val capitalInstallmentService: CapitalInstallmentService,
  private val clock: Clock,
) : CapitalRepaymentAmountService {
  override suspend fun get(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
  ): CapitalRepaymentAmountRep {
    val capitalAccountSummary = capitalAccountSummaryClient(
      CapitalAccountSummaryApi.Get(
        businessGuid = businessGuid,
        guid = capitalAccountGuid
      )
    ) ?: throw unprocessable(CapitalAccountNotFound())

    val chargeCardAccount = chargeCardAccountClient(
      ChargeCardAccountApi.Get(
        businessGuid = capitalAccountSummary.businessGuid,
        capitalAccountGuid = capitalAccountSummary.guid,
      )
    ) ?: throw unprocessable(ChargeCardAccountNotFound())

    val chargeCardRepaymentInfo = chargeCardRepaymentClient(
      ChargeCardRepaymentApi.GetInfo(
        businessGuid = chargeCardAccount.businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
      )
    )

    return CapitalRepaymentAmountRep(
      amounts = listOfNotNull(
        overdueAmountRep(capitalAccountSummary),
        lastStatementAmountRep(
          chargeCardAccount = chargeCardAccount,
          chargeCardRepaymentInfo = chargeCardRepaymentInfo,
        ),
        currentStatementAmountRep(
          capitalAccountSummary = capitalAccountSummary,
          chargeCardAccount = chargeCardAccount,
          chargeCardRepaymentInfo = chargeCardRepaymentInfo,
        ),
      )
    )
  }

  private suspend fun overdueAmountRep(
    capitalAccountSummary: CapitalAccountSummaryRep,
  ): CapitalRepaymentAmountRep.Complete? {
    val overdueAmount = capitalAccountSummary.runningBalance.negate()
    if (overdueAmount == Balance.ZERO) {
      return null
    }
    val unpaidDrawdowns = capitalInstallmentService.get(
      businessGuid = capitalAccountSummary.businessGuid,
      capitalAccountGuid = capitalAccountSummary.guid,
      untilInclusive = null,
    )
    val installment = Money.fromCents(
      BigDecimal(unpaidDrawdowns.sumOf { it.drawdownAmount }.rawCents).divide(
        BigDecimal(capitalAccountSummary.details.targetRepaymentDays.toLong()),
        MathContext.DECIMAL128
      )
    ).takeIf { it.rawCents < overdueAmount.rawCents } ?: overdueAmount.abs()

    return CapitalRepaymentAmountRep.Complete(
      amount = overdueAmount,
      type = CapitalRepaymentAmountRep.Type.Overdue,
      dueDate = unpaidDrawdowns.minOf { it.drawdownDate }
        .plusDays(capitalAccountSummary.details.targetRepaymentDays.toLong())
        .withZoneSameInstant(ZoneId.of("America/New_York"))
        .toLocalDate(),
      nextRepaymentAmount = installment,
      nextRepaymentDate = nextRepaymentDate(),
    )
  }

  private fun lastStatementAmountRep(
    chargeCardAccount: ChargeCardAccountRep,
    chargeCardRepaymentInfo: ChargeCardRepaymentInfoRep
  ): CapitalRepaymentAmountRep.Complete? {
    if (!chargeCardAccount.creditTerms.isExtendTerm) {
      return null
    }

    if (chargeCardRepaymentInfo.remainingAmountDue == Money.ZERO) {
      return null
    }

    return CapitalRepaymentAmountRep.Complete(
      amount = Balance.fromCents(chargeCardRepaymentInfo.remainingAmountDue.rawCents),
      type = CapitalRepaymentAmountRep.Type.LastStatement,
      dueDate = chargeCardAccount.nextRepaymentDate
        .withZoneSameInstant(ZoneId.of("America/New_York"))
        .toLocalDate(),
      nextRepaymentAmount = chargeCardRepaymentInfo.remainingAmountDue,
      nextRepaymentDate = chargeCardAccount.nextRepaymentDate
        .withZoneSameInstant(ZoneId.of("America/New_York"))
        .toLocalDate(),
    )
  }

  private fun currentStatementAmountRep(
    capitalAccountSummary: CapitalAccountSummaryRep,
    chargeCardAccount: ChargeCardAccountRep,
    chargeCardRepaymentInfo: ChargeCardRepaymentInfoRep
  ): CapitalRepaymentAmountRep.Complete? {
    val currentBalance = if (chargeCardAccount.creditTerms.isCashTerm) {
      (capitalAccountSummary.cardBalance + capitalAccountSummary.cardPending).negate()
    } else {
      (capitalAccountSummary.cardBalance + capitalAccountSummary.cardPending).negate() -
        chargeCardRepaymentInfo.remainingAmountDue
    }

    if (currentBalance == Balance.ZERO) {
      return null
    }

    val repaymentDate = when (chargeCardAccount.creditTerms) {
      ChargeCardCreditTerm.ProductionCash, ChargeCardCreditTerm.SandboxCash ->
        nextRepaymentDate()
      ChargeCardCreditTerm.Production, ChargeCardCreditTerm.Sandbox,
      ChargeCardCreditTerm.ProductionFlex, ChargeCardCreditTerm.SandboxFlex ->
        chargeCardAccount.nextRepaymentDate
          .withZoneSameInstant(ZoneId.of("America/New_York"))
          .toLocalDate()

      ChargeCardCreditTerm.SandboxExtend, ChargeCardCreditTerm.ProductionExtend -> {
        val nextMonthDate =
          chargeCardAccount.nextRepaymentDate.withZoneSameInstant(ZoneId.of("America/New_York"))
            .plusMonths(1)
            .withHour(23)
            .withMinute(59)
            .withSecond(59)
            .withNano(0)
        nextMonthDate.withDayOfMonth(
          min(YearMonth.of(nextMonthDate.year, nextMonthDate.month).lengthOfMonth(), 30)
        ).toLocalDate()
      }
    }

    return CapitalRepaymentAmountRep.Complete(
      amount = currentBalance,
      type = CapitalRepaymentAmountRep.Type.CurrentStatement,
      dueDate = repaymentDate,
      nextRepaymentAmount = currentBalance.takeIf { it > Balance.ZERO }?.let {
        Money.fromCents(it.rawCents)
      } ?: Money.ZERO,
      nextRepaymentDate = repaymentDate,
    )
  }

  private fun nextRepaymentDate(): LocalDate {
    val now = ZonedDateTime.now(clock).withZoneSameInstant(ZoneId.of("America/New_York"))
    return if (now.toLocalTime() > LocalTime.of(10, 0)) {
      now.plusDays(1).toLocalDate()
    } else {
      now.toLocalDate()
    }
  }
}
