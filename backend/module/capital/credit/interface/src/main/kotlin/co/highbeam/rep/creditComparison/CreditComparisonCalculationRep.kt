package co.highbeam.rep.creditComparison

import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.math.BigDecimal
import java.time.Period

data class CreditComparisonCalculationRep(
  val providerName: String,
  val fundingAmount: Money,
  val feeAmount: Money,
  val apr: BigDecimal?,
  val repaymentPeriod: Period,
  val averageWeeklyRepayment: Money,
  val remittanceRate: BigDecimal?,
) : CreatorRep, CompleteRep {
  override fun validate(): RepValidation = RepValidation.none()
}
