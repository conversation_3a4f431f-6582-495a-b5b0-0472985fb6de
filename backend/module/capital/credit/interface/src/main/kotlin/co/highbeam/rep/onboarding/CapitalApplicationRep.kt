package co.highbeam.rep.onboarding

import co.highbeam.capital.onboarding.rep.signatory.SignatoryV1
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep.Type
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep.Type.DrawPeriodV1
import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.validation.RepValidation
import co.highbeam.validation.ifPresent
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.math.BigDecimal
import java.time.Duration
import java.time.YearMonth
import java.time.ZonedDateTime
import java.util.UUID

data class CapitalApplicationRep(
  val guid: UUID,
  val businessGuid: UUID,
  val state: State,
  val submittedAt: ZonedDateTime?,
  val offer: CreditOfferRep?,
  val userProvidedDetails: UserProvidedDetailsRep?,
  val businessDetails: BusinessDetailsRep?,
) : CompleteRep {
  data class CreditOfferRep(
    val initialLimit: Money,
    val nextLimit: Money,
    val goalLimit: Money,
    val apr: BigDecimal,
    val remittanceRate: BigDecimal?,
    val repaymentTermInDays: Long?,
    val agreementType: Type = DrawPeriodV1,
    val drawPeriodEndsAt: ZonedDateTime?,
  )

  data class BusinessDetailsRep(
    val businessName: String,
    val businessIncorporationState: BusinessIncorporationState,
    val signatory: SignatoryV1,
  )

  data class OtherDetailsRep(
    val internalNotes: String,
  )

  data class UserProvidedDetailsRep(
    val userNotes: String?,
    val requestedAmount: Money?,
    val reasonForRequest: String?,
    val skipFinancialsRequirementReason: SkipFinancialsRequirementReasonRep?,
    val skipInventoryRequirementReason: SkipInventoryRequirementReasonRep?,
    val inventoryLeadTime: Duration?,
    val copackers: List<CopackerRep>?,
    val numberOfRetailLocations: Long?,
    val industry: Industry?,
    val otherIndustry: String?,
    val orgStructure: String?,
    val singleEntityBusiness: Boolean?,
    val securedLenders: Boolean?,
    val securedLendersExplanation: String?,
    val firstSaleAt: YearMonth?,
    val booksClosedAt: BooksClosedAtRep?,
    val repaymentTermsPreference: Duration?,
  ) {
    data class AddressRep(
      val street: String? = null,
      val street2: String? = null,
      val city: String? = null,
      val state: String? = null,
      val postalCode: String? = null,
      val country: String,
    )

    data class CopackerRep(
      val businessName: String,
      val address: AddressRep,
    )

    data class SkipFinancialsRequirementReasonRep(val reason: Reason, val explanation: String?) {
      enum class Reason {
        FinancialsNotUpToDate,
        DoNotTrackFinancials,
        CombinedDocuments,
        Other,
      }
    }

    data class SkipInventoryRequirementReasonRep(val reason: Reason, val explanation: String?) {
      enum class Reason {
        DoNotCarryInventory,
        Other,
      }
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
    @JsonSubTypes(
      JsonSubTypes.Type(BooksClosedAtRep.Exactly::class, name = "Exactly"),
      JsonSubTypes.Type(BooksClosedAtRep.Before::class, name = "Before"),
    )
    sealed class BooksClosedAtRep {
      data class Exactly(val yearMonth: YearMonth) : BooksClosedAtRep()
      data class Before(val yearMonth: YearMonth) : BooksClosedAtRep()
    }

    enum class Industry {
      Apparel, // Apparel
      Beauty, // Beauty
      Collectibles, // Collectibles
      Electronics, // Electronics
      FoodAndBeverage, // Food & bev
      Gifts, // Gifts
      HealthAndNutrition, // Health & nutrition
      HomeGoods, // Home goods
      Jewelry, // Jewelry
      ProfessionalServices, // Professional services
      SportingGoods, // Sporting goods
      Other, // Other
    }
  }

  /*
  *  New -> Submitted
  *  Submitted -> Approved, Rejected
  *  Approved -> ApprovalProcessed -> OfferNotified -> OfferRejected, OfferExpired, OfferAccepted
  *  OfferAccepted -> Activated
  *  Rejected -> RejectionNotified
  *
  *  Any -> Canceled
  *
  *  Final states: OfferRejected, OfferExpired, Activated, RejectionNotified, Canceled
  * */

  enum class State {
    New, Submitted,

    Approved, ApprovalProcessed, OfferNotified, OfferAccepted, PayoutsConnected, Activated,

    OfferExpired,
    OfferRejected,

    Rejected, RejectionNotified,

    Canceled
  }

  enum class BusinessIncorporationState(val fullName: String) {
    AL("Alabama"),
    AK("Alaska"),
    AZ("Arizona"),
    AR("Arkansas"),
    CA("California"),
    CO("Colorado"),
    CT("Connecticut"),
    DE("Delaware"),
    FL("Florida"),
    GA("Georgia"),
    HI("Hawaii"),
    ID("Idaho"),
    IL("Illinois"),
    IN("Indiana"),
    IA("Iowa"),
    KS("Kansas"),
    KY("Kentucky"),
    LA("Louisiana"),
    ME("Maine"),
    MD("Maryland"),
    MA("Massachusetts"),
    MI("Michigan"),
    MN("Minnesota"),
    MS("Mississippi"),
    MO("Missouri"),
    MT("Montana"),
    NE("Nebraska"),
    NV("Nevada"),
    NH("New Hampshire"),
    NJ("New Jersey"),
    NM("New Mexico"),
    NY("New York"),
    NC("North Carolina"),
    ND("North Dakota"),
    OH("Ohio"),
    OK("Oklahoma"),
    OR("Oregon"),
    PA("Pennsylvania"),
    PR("Puerto Rico"),
    RI("Rhode Island"),
    SC("South Carolina"),
    SD("South Dakota"),
    TN("Tennessee"),
    TX("Texas"),
    UT("Utah"),
    VT("Vermont"),
    VA("Virginia"),
    WA("Washington"),
    WV("West Virginia"),
    WI("Wisconsin"),
    WY("Wyoming");
  }

  data class Updater(
    val state: State? = null,
    val offer: CreditOfferRep? = null,
    val businessDetails: BusinessDetailsRep? = null,
    val userProvidedDetails: UserProvidedDetailsRep? = null,
    val otherDetails: OtherDetailsRep? = null,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::state) {
        ifPresent {
          when (this) {
            State.New, State.Submitted -> false
            else -> true
          }
        }
      }
    }
  }
}
