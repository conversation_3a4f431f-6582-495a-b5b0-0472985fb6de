package co.highbeam.rep.creditComparison

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import java.util.UUID

data class CreditComparisonSessionRep(
  val guid: UUID,
  val businessGuid: UUID?,
  val emailAddress: String?,
) : CompleteRep {
  data class Updater(val emailAddress: String?) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::emailAddress) { this == null || Validator.emailAddress(this) }
    }
  }
}
