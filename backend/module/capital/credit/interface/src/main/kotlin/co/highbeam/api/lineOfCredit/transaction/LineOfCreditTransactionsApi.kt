package co.highbeam.api.lineOfCredit.transaction

import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import java.time.LocalDate
import java.util.UUID

object LineOfCreditTransactionsApi {
  /**
   * Transfers money from Line of credit to the primary bank account.
   * @property businessGuid
   * @property creditAccountGuid
   * @property rep Line of credit draw down rep.
   */
  data class CreateDrawdown(
    val businessGuid: UUID,
    val creditAccountGuid: UUID,
    val rep: CapitalDrawdownTransactionRep.Creator,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/businesses/$businessGuid/credit-accounts/$creditAccountGuid/drawdown",
    body = rep,
  )

  data class ForceDrawdown(
    val businessGuid: UUID,
    val creditAccountGuid: UUID,
    val rep: CapitalDrawdownTransactionRep.Creator,
    val transactionInitiator: CapitalDrawdownTransactionRep.TransactionInitiator,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/businesses/$businessGuid/credit-accounts/$creditAccountGuid/drawdown/force",
    body = rep,
    qp = mapOf(
      "transactionInitiator" to listOf(transactionInitiator.toString()),
    )
  )

  /**
   * Transfers money from primary bank account to Line of credit.
   * @property businessGuid
   * @property creditAccountGuid
   * @property rep Line of credit repayment rep.
   */
  data class CreateRepayment(
    val businessGuid: UUID,
    val creditAccountGuid: UUID,
    val rep: CapitalRepaymentTransactionRep.Creator,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/businesses/$businessGuid/credit-accounts/$creditAccountGuid/repayment",
    body = rep,
  )

  data class GetTransactions(
    val businessGuid: UUID,
    val since: LocalDate,
    val until: LocalDate,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/lines-of-credit/transactions",
    qp = mapOf(
      "since" to listOf(since.toString()),
      "until" to listOf(until.toString())
    ),
  )

  data class GetTransactionsCsv(
    val businessGuid: UUID,
    val since: LocalDate,
    val until: LocalDate,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/lines-of-credit/transactions",
    qp = mapOf(
      "since" to listOf(since.toString()),
      "until" to listOf(until.toString())
    ),
    contentType = ContentType.Text.CSV
  )

  //Todo (shubham): Move to the new transactions module and remove this endpoint
  data class GetDetailedTransactions(
    val businessGuid: UUID,
    val since: LocalDate,
    val until: LocalDate,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/businesses/$businessGuid/lines-of-credit/transactions-detailed",
    qp = mapOf(
      "since" to listOf(since.toString()),
      "until" to listOf(until.toString())
    ),
  )
}
