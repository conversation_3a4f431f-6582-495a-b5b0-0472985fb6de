package co.highbeam.service.lineOfCredit.risk

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.config.CreditConfig
import co.highbeam.email.EmailService
import co.highbeam.email.template.AdverseActionEmailTemplate
import co.highbeam.email.template.EmailTemplate
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.mapper.documents.DocumentMapper
import co.highbeam.rep.DocumentRep
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep
import co.highbeam.service.documents.DocumentStorageService
import co.highbeam.sql.store.transaction
import co.highbeam.store.documents.DocumentStore
import com.github.mustachejava.DefaultMustacheFactory
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import java.io.StringWriter
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

internal class CreditAdverseActionServiceImpl @Inject constructor(
  private val documentStore: DocumentStore,
  private val creditConfig: CreditConfig,
  private val businessClient: BusinessClient,
  private val businessMemberClient: BusinessMemberClient,
  private val emailService: EmailService,
  private val documentStorageService: DocumentStorageService,
  private val documentMapper: DocumentMapper,
  private val jdbi: Jdbi,
) : CreditAdverseActionService {
  override suspend fun createAdverseAction(
    businessGuid: UUID,
    rep: AdverseActionRep.Creator,
  ): DocumentRep {
    val business = businessClient.request(BusinessApi.Get(businessGuid)) ?: throw BusinessNotFound()
    return documentMapper.toRep(
      jdbi.transaction {
        val documentModel = documentStore.create(
          documentMapper.toModel(
            businessGuid = businessGuid,
            lineOfCreditGuid = rep.lineOfCreditGuid,
            rep = rep,
          )
        )
        documentStorageService.upload(
          documentModel.guid,
          generateAdverseActionXhtml(
            businessName = checkNotNull(business.name) { "Business must have a name" },
            adverseAction = rep,
            generatedAt = documentModel.generatedAt
          )
        )
        return@transaction documentModel
      }
    )
  }

  override suspend fun notifyAdverseAction(businessGuid: UUID) {
    val business = businessClient.request(
      BusinessApi.Get(businessGuid)
    ) ?: throw BusinessNotFound()

    val owner = businessMemberClient.request(
      BusinessMemberApi.GetByUser(businessGuid = businessGuid, userGuid = business.ownerUserGuid)
    ) ?: throw BusinessNotFound()

    emailService.sync(key = null) { sendEmail ->
      sendEmail(
        AdverseActionEmailTemplate(
          recipient = EmailTemplate.Recipient(
            emailAddress = checkNotNull(
              owner.emailAddress
            ) {
              "The owner's email address is null, cannot notify " +
                "business $businessGuid of adverse action"
            },
            name = owner.fullName,
          ),
        )
      )
    }
  }

  private fun generateAdverseActionXhtml(
    businessName: String,
    adverseAction: AdverseActionRep.Creator,
    generatedAt: ZonedDateTime,
  ): String {
    StringWriter().use {
      DefaultMustacheFactory(creditConfig.mustacheTemplateRoot).compile(
        "AdverseAction.hbs"
      ).execute(
        it,
        mapOf(
          "businessName" to businessName,
          "shortDate" to generatedAt.format(
            DateTimeFormatter.ofPattern("M/d/yyyy")
          ),
          "longDate" to generatedAt.format(
            DateTimeFormatter.ofPattern("MMMM d, yyyy")
          ),
          "subject" to adverseAction.adverseActionType.subject,
          "action" to adverseAction.adverseActionType.action,
          "reasons" to adverseAction.reasons.map { reason -> reason.value },
          "movingForward" to adverseAction.adverseActionType.movingForward,
        )
      )
      return it.toString()
    }
  }
}
