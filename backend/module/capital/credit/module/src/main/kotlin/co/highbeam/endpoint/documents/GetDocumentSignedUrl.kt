package co.highbeam.endpoint.documents

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.documents.DocumentStorageService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.documents.DocumentsApi as Api

internal class GetDocumentSignedUrl @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val documentsStorageService: DocumentStorageService,
) : EndpointHandler<Api.GetDocumentUrl, String>(
  template = Api.GetDocumentUrl::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetDocumentUrl =
    Api.GetDocumentUrl(
      businessGuid = call.getParam("businessGuid"),
      documentGuid = call.getParam("documentGuid"),
    )

  override suspend fun Handler.handle(
    endpoint: Api.GetDocumentUrl,
  ): String {
    authSome(
      authPermission(Permission.CreditApplication_Read) { endpoint.businessGuid },
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )
    return documentsStorageService.getDocumentSignedUrl(
      businessGuid = endpoint.businessGuid,
      documentGuid = endpoint.documentGuid,
    )
  }
}
