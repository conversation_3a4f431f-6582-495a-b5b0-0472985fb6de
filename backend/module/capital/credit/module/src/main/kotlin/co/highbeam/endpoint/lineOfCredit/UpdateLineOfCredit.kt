package co.highbeam.endpoint.lineOfCredit

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.LineOfCreditService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.LineOfCreditApi as Api

internal class UpdateLineOfCredit @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditService: LineOfCreditService,
) : EndpointHandler<Api.Update, LineOfCreditRep>(
  template = Api.Update::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Update = Api.Update(
    businessGuid = call.getParam("businessGuid"),
    lineOfCreditGuid = call.getParam("lineOfCreditGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(endpoint: Api.Update): LineOfCreditRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    return lineOfCreditService.update(
      businessGuid = endpoint.businessGuid,
      lineOfCreditGuid = endpoint.lineOfCreditGuid,
      rep = endpoint.rep
    )
  }
}

