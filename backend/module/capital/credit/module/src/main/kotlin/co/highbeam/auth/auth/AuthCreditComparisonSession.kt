package co.highbeam.auth.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.permissions.Permission
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.http.Headers
import java.util.UUID

internal class AuthCreditComparisonSession private constructor(
  private val authPermission: AuthPermission.Provider,
  private val creditComparisonService: CreditComparisonService,
  private val operation: Operation,
  private val sessionGuid: UUID,
) : Auth() {
  internal enum class Operation(val permission: Permission) {
    Read(Permission.CreditComparison_Read),
    Write(Permission.CreditComparison_Write),
  }

  internal class Provider @Inject constructor(
    private val authPermission: AuthPermission.Provider,
    private val creditComparisonService: CreditComparisonService,
  ) {
    operator fun invoke(operation: Operation, sessionGuid: UUID): AuthCreditComparisonSession =
      AuthCreditComparisonSession(
        authPermission = authPermission,
        creditComparisonService = creditComparisonService,
        operation = operation,
        sessionGuid = sessionGuid,
      )
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    val session = creditComparisonService.getSession(sessionGuid)
    if (session.businessGuid == null) return true
    return authPermission(operation.permission) { checkNotNull(session.businessGuid) }
      .authorize(jwt, headers)
  }
}
