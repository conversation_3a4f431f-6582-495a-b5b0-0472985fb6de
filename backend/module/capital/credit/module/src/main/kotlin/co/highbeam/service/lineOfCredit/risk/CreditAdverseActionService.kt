package co.highbeam.service.lineOfCredit.risk

import co.highbeam.rep.DocumentRep
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(CreditAdverseActionServiceImpl::class)
interface CreditAdverseActionService {
  suspend fun createAdverseAction(
    businessGuid: UUID,
    rep: AdverseActionRep.Creator
  ): DocumentRep

  suspend fun notifyAdverseAction(
    businessGuid: UUID,
  )
}
