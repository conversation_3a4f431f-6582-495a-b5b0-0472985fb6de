package co.highbeam.service.onboarding

import co.highbeam.capital.onboarding.model.agreement.CapitalAgreementModel
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementTerms
import co.highbeam.capital.onboarding.store.agreement.CapitalAgreementStore
import co.highbeam.exception.lineOfCredit.agreement.LineOfCreditAgreementNotFound
import co.highbeam.exception.lineOfCredit.agreement.SignedLineOfCreditAgreementNotFound
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.mapper.documents.DocumentMapper
import co.highbeam.mapper.onboarding.LineOfCreditAgreementMapper
import co.highbeam.service.documents.DocumentStorageService
import co.highbeam.sql.store.transaction
import co.highbeam.store.documents.DocumentStore
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import mu.KotlinLogging
import org.jdbi.v3.core.Jdbi
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class CapitalAgreementServiceImpl @Inject constructor(
  private val capitalAgreementStore: CapitalAgreementStore,
  private val clock: Clock,
  private val documentMapper: DocumentMapper,
  private val documentStorageService: DocumentStorageService,
  private val documentStore: DocumentStore,
  private val jdbi: Jdbi,
  private val lineOfCreditAgreementMapper: LineOfCreditAgreementMapper,
  private val lineOfCreditAgreementXhtml: LineOfCreditAgreementHtml,
  private val storage: GoogleCloudStorage,
  private val uuidGenerator: UuidGenerator,
) : CapitalAgreementService {
  private val logger = KotlinLogging.logger {}
  override fun createCapitalAgreement(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    rep: CapitalAgreementRep.Creator,
    withCounterpartySignature: Boolean,
    generatePdf: Boolean
  ): CapitalAgreementRep {
    logger.info {
      "Generating line of credit agreement for: " +
        "[businessGuid=$businessGuid, " +
        "capitalAccountGuid=$capitalAccountGuid, " +
        "rep=$rep, " +
        "withCounterpartySignature=$withCounterpartySignature, " +
        "generatePdf=$generatePdf]"
    }

    val capitalAgreement = capitalAgreementStore.get(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid
    )

    val agreementSignedAt = when {
      withCounterpartySignature && capitalAgreement == null ->
        throw LineOfCreditAgreementNotFound()
      withCounterpartySignature ->
        capitalAgreement?.data?.agreementSignedAt ?: throw SignedLineOfCreditAgreementNotFound()
      else -> null
    }

    val xhtml = lineOfCreditAgreementXhtml.generateXhtml(
      capitalAgreementTerms = rep.terms,
      agreementSignedAt = agreementSignedAt,
    )
    val pdfByteArray = documentStorageService.convertXhtmlToPdfByteArray(xhtml)

    return jdbi.transaction {
      if (generatePdf) {
        documentStore.create(
          documentMapper.toModel(businessGuid, capitalAccountGuid, rep)
        ).also { documentModel ->
          documentStorageService.upload(documentModel.guid, xhtml)
        }
      }

      val agreementMetadata = if (withCounterpartySignature) {
        capitalAgreement?.let {
          updateSignedAgreement(
            pdfByteArray = pdfByteArray,
            lineOfCreditAgreement = capitalAgreement
          )
        } ?: throw LineOfCreditAgreementNotFound()
      } else {
        updateUnsignedAgreement(pdfByteArray = pdfByteArray, capitalAgreementTerms = rep.terms)
      }

      capitalAgreementStore.create(
        lineOfCreditAgreementMapper.toCreatorModel(
          businessGuid = businessGuid,
          lineOfCreditGuid = capitalAccountGuid,
          creditApplicationGuid = null,
          lineOfCreditAgreementMetadataRep = agreementMetadata,
          effectiveAt = ZonedDateTime.now(clock),
        )
      )

      agreementMetadata
    }
  }

  override fun regenerateSignedAgreement(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
  ): CapitalAgreementRep {
    val capitalAgreement = capitalAgreementStore.get(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    ) ?: throw LineOfCreditAgreementNotFound()

    return capitalAgreement.data.let {
      if (it.agreementSignedAt == null) {
        return lineOfCreditAgreementMapper.toLineOfCreditAgreementMetadataRep(capitalAgreement)
      }

      generateSignedAgreement(
        businessGuid = businessGuid,
        lineOfCreditGuid = capitalAccountGuid,
        creditApplicationGuid = null,
        lineOfCreditAgreementMetadataRep = lineOfCreditAgreementMapper
          .toLineOfCreditAgreementMetadataRep(capitalAgreement),
        // Backfill signed agreement PDF generation
        generatePdf = it.signedAgreementGuid == null,
      )
    }
  }

  override fun generateSignedAgreement(
    businessGuid: UUID,
    lineOfCreditGuid: UUID,
    creditApplicationGuid: UUID?,
    lineOfCreditAgreementMetadataRep: CapitalAgreementRep,
    generatePdf: Boolean,
  ): CapitalAgreementRep {
    val agreement = lineOfCreditAgreementMapper.toLineOfCreditAgreementMetadataRep(
      capitalAgreementStore.create(
        lineOfCreditAgreementMapper.toCreatorModel(
          businessGuid = businessGuid,
          lineOfCreditGuid = lineOfCreditGuid,
          creditApplicationGuid = creditApplicationGuid,
          lineOfCreditAgreementMetadataRep = lineOfCreditAgreementMetadataRep,
          effectiveAt = ZonedDateTime.now(clock),
        )
      )
    )

    agreement.terms?.let { terms ->
      createCapitalAgreement(
        businessGuid = businessGuid,
        capitalAccountGuid = lineOfCreditGuid,
        rep = CapitalAgreementRep.Creator(
          terms = terms,
          generatedAt = ZonedDateTime.now(clock),
        ),
        withCounterpartySignature = true,
        generatePdf = generatePdf,
      )
    }

    if (agreement.terms == null) {
      logger.error {
        "Initial line of credit agreement is null, please generate a signed agreement for " +
          "business: $businessGuid, lineOfCredit: $lineOfCreditGuid."
      }
    }
    return agreement
  }

  private fun updateSignedAgreement(
    pdfByteArray: ByteArray,
    lineOfCreditAgreement: CapitalAgreementModel,
  ): CapitalAgreementRep {
    val agreementGuid = uuidGenerator.generate()
    val objectName = "${AgreementDirectory.SIGNED_LOC_AGREEMENT_DIR.path}/$agreementGuid"
    storage.upload(objectName, pdfByteArray)

    return CapitalAgreementRep(
      unsignedAgreementGuid = lineOfCreditAgreement.data.unsignedAgreementGuid,
      signedAgreementGuid = agreementGuid,
      unsignedAgreementOpenedAt = lineOfCreditAgreement.data.unsignedAgreementOpenedAt,
      signatoryUserGuid = lineOfCreditAgreement.data.signatoryUserGuid,
      agreementSignedAt = lineOfCreditAgreement.data.agreementSignedAt,
      terms = lineOfCreditAgreement.data.terms,
    )
  }

  private fun updateUnsignedAgreement(
    pdfByteArray: ByteArray,
    capitalAgreementTerms: CapitalAgreementTerms
  ): CapitalAgreementRep {
    val agreementGuid = uuidGenerator.generate()
    val objectName = "${AgreementDirectory.UNSIGNED_LOC_AGREEMENT_DIR.path}/$agreementGuid"
    storage.upload(objectName, pdfByteArray)
    return CapitalAgreementRep(
      unsignedAgreementGuid = agreementGuid,
      signedAgreementGuid = null,
      unsignedAgreementOpenedAt = null,
      signatoryUserGuid = null,
      agreementSignedAt = null,
      terms = capitalAgreementTerms,
    )
  }
}
