package co.highbeam.endpoint.lineOfCredit

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.LineOfCreditService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.LineOfCreditApi as Api

internal class CreateLineOfCredit @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditService: LineOfCreditService,
) : EndpointHandler<Api.Create, LineOfCreditRep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(
      businessGuid = call.getParam("businessGuid"),
      rep = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.Create): LineOfCreditRep {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    return lineOfCreditService.create(endpoint.businessGuid, endpoint.rep)
  }
}

