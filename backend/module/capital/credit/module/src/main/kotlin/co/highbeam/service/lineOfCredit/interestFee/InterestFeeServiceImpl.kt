package co.highbeam.service.lineOfCredit.interestFee

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.account.service.CapitalAccountInternalService
import co.highbeam.capital.account.service.CapitalAccountService
import co.highbeam.capital.account.store.CapitalAccountStore
import co.highbeam.capital.transaction.service.CapitalTransactionSummaryService
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.config.CreditConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.exception.business.BankAccountNotFound
import co.highbeam.exception.lineOfCredit.LineOfCreditDetailsNotFound
import co.highbeam.exception.lineOfCredit.LineOfCreditNotFound
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.mapper.lineOfCredit.interestFee.LineOfCreditInterestFeeMapper
import co.highbeam.mapper.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferMapper
import co.highbeam.model.lineOfCredit.interestFee.LineOfCreditInterestFeeDataModel
import co.highbeam.model.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.UUIDSelection
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.lineOfCredit.interestFee.CalculateInterestFeeRep
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeeRep
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeesRep
import co.highbeam.rep.lineOfCredit.interestFee.DailyInterestFeeRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeMetadataRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.BusinessWiseInterestFees
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.State.Completed
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.State.Failed
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.State.ManuallyCompleted
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.State.New
import co.highbeam.sql.store.coTransaction
import co.highbeam.sql.store.transaction
import co.highbeam.store.lineOfCredit.interestFee.LineOfCreditInterestFeeStore
import co.highbeam.store.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferStore
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCompleteRep
import co.unit.rep.unitCoCustomerId
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.google.inject.name.Named
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging
import org.jdbi.v3.core.Jdbi
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.time.Clock
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalTime
import java.time.Year
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.UUID

@Suppress("TooManyFunctions", "LargeClass")
internal class InterestFeeServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val unitCoClient: UnitCoClient,
  private val lineOfCreditInterestFeeTransferStore: LineOfCreditInterestFeeTransferStore,
  private val jdbi: Jdbi,
  @Named("line-of-credit-interest-transfer")
  private val publisher: EventPublisher<CompleteInterestFeeRep>,
  private val capitalAccountInternalService: CapitalAccountInternalService,
  private val capitalAccountService: CapitalAccountService,
  private val capitalAccountStore: CapitalAccountStore,
  private val clock: Clock,
  private val creditConfig: CreditConfig,
  private val interestFeeTransferMapper: LineOfCreditInterestFeeTransferMapper,
  private val capitalTransactionSummaryService: CapitalTransactionSummaryService,
  private val objectMapper: ObjectMapper,
  private val interestFeeMapper: LineOfCreditInterestFeeMapper,
  private val interestFeeStore: LineOfCreditInterestFeeStore,
  private val featureFlagService: FeatureFlagService,
) : InterestFeeService {
  private data class DateRange(
    override val start: LocalDate,
    override val endInclusive: LocalDate
  ) : ClosedRange<LocalDate>

  private val logger = KotlinLogging.logger {}

  private suspend fun makePayment(
    transferAmount: Money,
    fromUnitCoAccountId: String,
    toUnitCoAccountId: String,
    idempotencyKey: UUID,
  ): UnitCompleteRep<BookPaymentRep> {
    return unitCoClient.payment.createBook(
      BookPaymentRep.Creator(
        amount = transferAmount,
        description = "Highbeam Capital interest payment",
        idempotencyKey = idempotencyKey,
        fromAccountId = fromUnitCoAccountId,
        toAccountId = toUnitCoAccountId,
        tags = BookPaymentRep.Tags(
          UnitCoTransactionRep.TransactionType.LineOfCreditInterestPayment
        ),
      )
    )
  }

  /**
   * Calculates interest fees from the last fee date or the activation time of the LOC if no past
   * fee exists. This range will be Tuesday to Monday (inclusive). Fee calculations are not made
   * if the range includes "today".
   */
  override suspend fun createInterestFeeTransfers() {
    val linesOfCredit = capitalAccountStore.getAll()
      .filter { it.state == CapitalAccountRep.State.Active }

    linesOfCredit.forEach { lineOfCredit ->
      val pastTransfers = lineOfCreditInterestFeeTransferStore.getByLineOfCreditGuid(
        businessGuid = lineOfCredit.businessGuid,
        lineOfCreditGuid = lineOfCredit.guid,
      )
      val nextStart = if (pastTransfers.isEmpty()) {
        val refetchedLineOfCredit = capitalAccountStore.get(
          guid = lineOfCredit.guid,
          businessGuid = lineOfCredit.businessGuid,
        ) ?: throw LineOfCreditNotFound()
        val activatedAt = checkNotNull(refetchedLineOfCredit.activatedAt) {
          "Line of credit must have an activated_at date if it is Active"
        }
        activatedAt.toLocalDate()
      } else {
        pastTransfers.last().until.plusDays(1)
      }
      createInterestFeeTransfer(
        lineOfCreditGuid = lineOfCredit.guid,
        businessGuid = lineOfCredit.businessGuid,
        // We want to create interest fees only on Tuesdays because that's when
        // Shopify payouts typically come through (arbitrary).
        transferRanges = generateSequence(getPeriodToNextMonday(nextStart = nextStart)) {
          getPeriodToNextMonday(nextStart = it.endInclusive.plusDays(1))
        }.takeWhile {
          // This prevents interests to be calculated on days where the daily
          // balance doesn't exist or hasn't happened yet.
          // For example, we don't want to create interest fees for today since
          // it hasn't ended yet.
          it.endInclusive < LocalDate.now(clock.withZone(ZoneId.of("America/New_York")))
        }.toList(),
      )
    }
  }

  private suspend fun createInterestFeeTransfer(
    lineOfCreditGuid: UUID,
    businessGuid: UUID,
    transferRanges: List<ClosedRange<LocalDate>>,
  ) {
    transferRanges.map { transferRange ->
      val interestFeeRep = calculateInterestFee(
        lineOfCreditGuid = lineOfCreditGuid,
        businessGuid = businessGuid,
        since = transferRange.start,
        until = transferRange.endInclusive,
      )

      jdbi.transaction {
        val transfer = lineOfCreditInterestFeeTransferStore.create(
          interestFeeTransferMapper.model(
            rep = interestFeeRep,
            lineOfCreditGuid = lineOfCreditGuid,
            transferRange = transferRange,
          )
        ) ?: error(
          "Unable to create interest fee: " +
            "interestFeeRep=$interestFeeRep, " +
            "transferRange=$transferRange"
        )

        interestFeeStore.update(
          interestFeeMapper.updaterModels(
            guids = interestFeeRep.interestFeeGuids,
            interestFeeTransferGuid = transfer.guid,
          ),
        )
      }
    }
  }

  private fun getPeriodToNextMonday(nextStart: LocalDate): ClosedRange<LocalDate> {
    // NB(justin): If today is Monday, we want it to run the calculation on only today.
    //  Otherwise, we want it to go to the following Monday.
    if (nextStart.dayOfWeek == DayOfWeek.MONDAY) {
      return DateRange(
        start = nextStart,
        endInclusive = nextStart,
      )
    }
    return DateRange(
      start = nextStart,
      endInclusive = nextStart.with(TemporalAdjusters.next(DayOfWeek.MONDAY))
    )
  }

  override suspend fun calculateInterestFees(
    since: LocalDate,
    until: LocalDate
  ): List<CalculateInterestFeeRep> {
    logger.info { "Calculating interest fees from $since to $until" }
    val linesOfCredit = capitalAccountStore.getAll()
      .filter { it.state == CapitalAccountRep.State.Active }

    return linesOfCredit.map {
      calculateInterestFee(
        it.guid,
        businessGuid = it.businessGuid,
        since = since,
        until = until,
      )
    }
  }

  private suspend fun calculateInterestFee(
    lineOfCreditGuid: UUID,
    businessGuid: UUID,
    since: LocalDate,
    until: LocalDate,
  ): CalculateInterestFeeRep {
    // TODO(justin): This no longer is valid since APR can change between since and until
    val lineOfCredit = capitalAccountService.get(
      businessGuid = businessGuid,
      guid = lineOfCreditGuid,
    ) ?: throw LineOfCreditNotFound()

    val dailyBalances = capitalTransactionSummaryService.balanceAsOfDateRange(
      businessGuid = businessGuid,
      capitalAccountGuid = lineOfCreditGuid,
      sinceInclusive = since,
      untilInclusive = until,
    )

    val interestFees = dailyBalances.map {
      val lineOfCreditHistoricalDetail = capitalAccountInternalService.getDetails(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        beforeOrEqual = it.date.atTime(LocalTime.MAX).atZone(clock.zone),
      ) ?: throw LineOfCreditDetailsNotFound()

      if (since.until(until, ChronoUnit.DAYS) + 1 != dailyBalances.size.toLong()) {
        error(
          "Invalid number of days in Unit's Account End of Day: " +
            "businessGuid=$businessGuid, " +
            "since=$since, " +
            "until=$until, " +
            "dailyBalances=$dailyBalances"
        )
      }

      val apr = lineOfCreditHistoricalDetail.netApr
      val dailyApr = dailyApr(apr)
      interestFeeMapper.creatorModel(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        interestFeeTransferGuid = null,
        date = it.date,
        data = LineOfCreditInterestFeeDataModel(
          interestFee = Money.fromCents(interest(it.balance, dailyApr)),
          runningBalance = it.balance,
          apr = apr,
          dailyApr = dailyApr,
        ),
      )
    }

    interestFeeStore.create(lineOfCreditInterestFees = interestFees)
    val totalInterestFee = interestFees.sumOf { it.data.interestFee }
    logger.info(
      "Interest fee of $totalInterestFee between " +
        "$since and $until for $businessGuid"
    )

    return CalculateInterestFeeRep(
      businessGuid = businessGuid,
      fee = totalInterestFee,
      accountEndOfDayBalances = dailyBalances,
      // TODO(justin): We no longer need this since this information is stored in the interest_fee
      //  table.
      interestFeeMetadataRep = LineOfCreditInterestFeeMetadataRep(
        limit = lineOfCredit.details.limit,
        apr = lineOfCredit.details.netApr,
      ),
      interestFeeGuids = interestFees.map { it.guid },
    )
  }

  private fun dailyApr(apr: BigDecimal): BigDecimal =
    apr.divide(BigDecimal(Year.now(clock).length()), 10, RoundingMode.HALF_UP)

  private fun interest(balance: Balance, aprDailyRate: BigDecimal): BigDecimal =
    if (balance < Balance.ZERO)
      BigDecimal(balance.rawCents * -1) * aprDailyRate
    else
      BigDecimal.ZERO

  override suspend fun publishLineOfCreditInterestFeeTransferEvent(
    rep: CompleteInterestFeesRep.Creator,
  ) {
    jdbi.transaction {
      val interestFees = lineOfCreditInterestFeeTransferStore
        .getByState(LineOfCreditInterestFeeTransferRep.State.New)

      when (rep.completedState) {
        Completed ->
          interestFees.forEach {
            publisher.publishEvent(
              CompleteInterestFeeRep(it.guid, rep.completedState)
            )
          }

        ManuallyCompleted ->
          lineOfCreditInterestFeeTransferStore.batchUpdate(
            interestFees
              .map {
                LineOfCreditInterestFeeTransferModel.Update(
                  guid = it.guid,
                  state = ManuallyCompleted,
                  unitBookPaymentResponse = it.unitBookPaymentResponse,
                )
              })

        LineOfCreditInterestFeeTransferRep.State.New,
        LineOfCreditInterestFeeTransferRep.State.WrittenOff,
        Failed ->
          error("Unhandled completedState=${rep.completedState}")
      }
    }
  }

  override fun completeInterestFee(completeInterestFeeRep: CompleteInterestFeeRep) {
    jdbi.coTransaction {
      val fee = lineOfCreditInterestFeeTransferStore.get(
        completeInterestFeeRep.interestFeeGuid,
        forUpdate = true,
      )

      if (fee.state != New || fee.state == completeInterestFeeRep.completedState) {
        logger.warn { "Cannot complete interest fee in current state: fee=$fee" }
        return@coTransaction
      }

      if (fee.amount == Money.ZERO) {
        lineOfCreditInterestFeeTransferStore.update(
          update = LineOfCreditInterestFeeTransferModel.Update(
            guid = fee.guid,
            state = completeInterestFeeRep.completedState,
            unitBookPaymentResponse = null,
          )
        )
        return@coTransaction
      }

      val customerAccount = try {
        getCustomerAccount(
          businessGuid = fee.businessGuid,
          bankAccountGuid = completeInterestFeeRep.bankAccountGuid,
        )
      } catch (e: BankAccountNotFound) {
        logger.warn(e) { "Unable to find bank account for interestFeeGuid=${fee.guid}" }
        lineOfCreditInterestFeeTransferStore.update(
          update = LineOfCreditInterestFeeTransferModel.Update(
            guid = fee.guid,
            state = Failed,
            unitBookPaymentResponse = null,
          )
        )
        return@coTransaction
      }

      val capitalAccount = capitalAccountService.get(
        businessGuid = fee.businessGuid,
        guid = fee.lineOfCreditGuid,
      ) ?: run {
        logger.warn { "Unable to find capital account for fee=$fee." }
        return@coTransaction
      }

      val fundingAccountId = getFundingAccount(capitalAccount, fee) ?: run {
        logger.warn {
          "Unable to find funding account for capitalAccount=$capitalAccount " +
            "while processing fee=$fee."
        }
        return@coTransaction
      }

      val unitResponse = makePayment(
        transferAmount = fee.amount,
        fromUnitCoAccountId = customerAccount.unitCoDepositAccountId,
        toUnitCoAccountId = fundingAccountId,
        idempotencyKey = fee.guid,
      )

      lineOfCreditInterestFeeTransferStore.update(
        update = LineOfCreditInterestFeeTransferModel.Update(
          guid = fee.guid,
          state = interestFeeState(
            unitResponse.attributes.status,
            completeInterestFeeRep.completedState
          ),
          unitBookPaymentResponse = unitResponse.json,
        )
      )
    }
  }

  private fun getFundingAccount(
    capitalAccount: CapitalAccountRep,
    fee: LineOfCreditInterestFeeTransferModel
  ): String? {
    return when (capitalAccount.lender) {
      CapitalLender.Highbeam -> if (featureFlagService.isEnabled(
          BusinessFlag.CapitalSpvFlowOfFunds,
          fee.businessGuid,
        )
      ) {
        creditConfig.highbeamSpvCollectionUnitAccountId
      } else {
        creditConfig.highbeamOldFundingUnitAccountId
      }
      else -> creditConfig.externalLenderLineOfCreditUnitAccountId[capitalAccount.lender]
    }
  }

  override suspend fun getAccruedInterest(
    lineOfCreditGuid: UUID,
    businessGuid: UUID,
    since: LocalDate,
    untilInclusive: LocalDate,
  ): List<DailyInterestFeeRep> {
    return capitalTransactionSummaryService.balanceAsOfDateRange(
      capitalAccountGuid = lineOfCreditGuid,
      businessGuid = businessGuid,
      sinceInclusive = since,
      untilInclusive = untilInclusive,
    ).map {
      val details = capitalAccountInternalService.getDetails(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        beforeOrEqual = it.date.atStartOfDay(clock.zone),
      ) ?: throw LineOfCreditDetailsNotFound()

      DailyInterestFeeRep(
        date = it.date,
        amount = Balance(
          BigDecimal(-1 * it.balance.rawCents).multiply(
            details.netApr.divide(BigDecimal(it.date.lengthOfYear()), MathContext.DECIMAL128)
          ).setScale(0, RoundingMode.HALF_EVEN).toLong()
        )
      )
    }
  }

  override suspend fun retryFailedInterestFees(interestFeeGuids: UUIDSelection) {
    jdbi.coTransaction {
      val interestFees = when (interestFeeGuids) {
        is UUIDSelection.All -> {
          lineOfCreditInterestFeeTransferStore
            .getByState(Failed)
        }

        is UUIDSelection.AllNonArchived ->
          error("UUIDSelection.AllNonArchived not currently supported")

        is UUIDSelection.Explicit -> {
          interestFeeGuids.guids.map { lineOfCreditInterestFeeTransferStore.get(it) }
            .filter { it.state == Failed }
        }

        else -> error("Unsupported UUIDSelection: $interestFeeGuids")
      }.filter {
        it.metadata.retryGuid == null //Has never been retried.
      }

      interestFees.forEach { fee ->
        val retryFee = lineOfCreditInterestFeeTransferStore.create(
          interestFeeTransferMapper.toNewModel(fee),
        )
          ?: error("Unable to create interest fee: interestFeeRep=$fee ")
        lineOfCreditInterestFeeTransferStore.updateMetadata(
          guid = fee.guid,
          metadata = objectMapper.writeValueAsString(fee.metadata.copy(retryGuid = retryFee.guid)),
        )
        publisher.publishEvent(
          CompleteInterestFeeRep(retryFee.guid, Completed)
        )
      }
    }
  }

  override suspend fun retryFailedInterestFee(
    interestFeeGuid: UUID,
    bankAccountGuid: UUID?,
  ) {
    jdbi.coTransaction {
      val fee = lineOfCreditInterestFeeTransferStore.get(interestFeeGuid)
        .takeIf {
          it.state == Failed && it.metadata.retryGuid == null
        } ?: return@coTransaction

      val retryFee = lineOfCreditInterestFeeTransferStore.create(
        interestFeeTransferMapper.toNewModel(fee)
      ) ?: error("Unable to create interest fee: interestFeeRep=$fee ")

      lineOfCreditInterestFeeTransferStore.updateMetadata(
        guid = fee.guid,
        metadata = objectMapper.writeValueAsString(fee.metadata.copy(retryGuid = retryFee.guid)),
      )
      publisher.publishEvent(
        CompleteInterestFeeRep(
          interestFeeGuid = retryFee.guid,
          completedState = Completed,
          bankAccountGuid = bankAccountGuid,
        )
      )
    }
  }


  override suspend fun getBusinessFees(): List<BusinessWiseInterestFees> {
    val interestFees = unitCoClient.transaction.list(
      null, null, null, null, null,
      mapOf(
        "transactionType" to UnitCoTransactionRep.TransactionType.LineOfCreditInterestPayment.value
      )
    ).toList()
      .flatMap { objectMapper.convertValue<DataWrapper<List<UnitCoTransactionRep>>>(it).data }
      .groupBy { it.unitCoCustomerId }
      .mapValues { entry -> entry.value.sumOf { it.amount } }
      .filter { it.value < Balance.ZERO }
      .mapValues { entry -> entry.value.abs() }

    return businessClient.request(BusinessApi.GetAll)
      .map {
        BusinessWiseInterestFees(
          businessGuid = it.guid,
          amount = interestFees.getOrDefault(it.unitCoCustomerId, Money.ZERO),
          businessName = it.internalName,
        )
      }
  }

  private fun interestFeeState(
    status: UnitCoPayment.Status,
    completedState: LineOfCreditInterestFeeTransferRep.State,
  ) =
    when (status) {
      UnitCoPayment.Status.Sent -> completedState
      UnitCoPayment.Status.Rejected -> Failed
      else -> Failed
    }

  private suspend fun getCustomerAccount(
    businessGuid: UUID,
    bankAccountGuid: UUID?
  ): BankAccountRep.Complete {
    val customerAccount = bankAccountGuid?.let {
      bankAccountClient.request(
        BankAccountApi.GetByBusiness(
          businessGuid = businessGuid,
          accountGuid = it,
        )
      )
    } ?: bankAccountClient.request(BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid))
    ?: throw BankAccountNotFound()

    return customerAccount
  }
}
