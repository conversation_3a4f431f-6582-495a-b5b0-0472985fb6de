package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.auth.AuthCreditComparisonSession
import co.highbeam.rep.creditComparison.CreditComparisonSnapshotRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class CreateCreditComparisonSnapshot @Inject constructor(
  private val authCreditComparisonSession: AuthCreditComparisonSession.Provider,
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.CreateCreditComparisonSnapshot, CreditComparisonSnapshotRep>(
  template = Api.CreateCreditComparisonSnapshot::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall,
  ) = Api.CreateCreditComparisonSnapshot(
    creditComparisonSessionGuid = call.getParam("creditComparisonSessionGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(
    endpoint: Api.CreateCreditComparisonSnapshot
  ): CreditComparisonSnapshotRep {
    auth(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = endpoint.creditComparisonSessionGuid,
      )
    )

    return creditComparisonService.createSnapshot(
      creditComparisonSessionGuid = endpoint.creditComparisonSessionGuid,
      rep = endpoint.rep
    )
  }
}
