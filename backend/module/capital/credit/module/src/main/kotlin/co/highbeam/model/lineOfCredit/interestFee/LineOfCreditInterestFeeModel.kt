package co.highbeam.model.lineOfCredit.interestFee

import org.jdbi.v3.json.Json
import java.time.LocalDate
import java.util.UUID

data class LineOfCreditInterestFeeModel(
  val guid: UUID,
  val businessGuid: UUID,
  val lineOfCreditGuid: UUID,
  val interestFeeTransferGuid: UUID?,
  val date: LocalDate,
  @Json val data: LineOfCreditInterestFeeDataModel,
) {
  internal data class Creator(
    val guid: UUID,
    val businessGuid: UUID,
    val lineOfCreditGuid: UUID,
    val interestFeeTransferGuid: UUID?,
    val date: LocalDate,
    @Json val data: LineOfCreditInterestFeeDataModel,
  )

  internal data class Updater(
    val guid: UUID,
    val interestFeeTransferGuid: UUID?,
  )
}
