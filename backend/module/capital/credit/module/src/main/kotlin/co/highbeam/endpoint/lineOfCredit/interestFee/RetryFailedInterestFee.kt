package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.interestFee.InterestFeeService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi as Api

internal class RetryFailedInterestFee @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val interestFeeService: InterestFeeService
) : EndpointHandler<Api.RetryInterestFee, Unit>(
  template = Api.RetryInterestFee::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.RetryInterestFee =
    Api.RetryInterestFee(
      interestFeeGuid = call.getParam("interestFeeGuid"),
      creator = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.RetryInterestFee) {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    interestFeeService.retryFailedInterestFee(
      interestFeeGuid = endpoint.interestFeeGuid,
      bankAccountGuid = endpoint.creator.bankAccountGuid,
    )
  }
}
