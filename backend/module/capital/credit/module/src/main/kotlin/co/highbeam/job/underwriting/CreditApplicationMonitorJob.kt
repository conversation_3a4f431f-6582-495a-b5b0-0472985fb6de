package co.highbeam.job.underwriting

import co.highbeam.capital.onboarding.store.application.CapitalApplicationStore
import co.highbeam.job.HighbeamJob
import co.highbeam.listener.lineOfCredit.LineOfCreditApplicationApprovedListener
import co.highbeam.rep.onboarding.CapitalApplicationRep
import com.google.inject.Inject
import co.highbeam.job.underwriting.CreditApplicationMonitorJob as Job

/**
 * Monitors credit application statuses and takes action accordingly.
 */
internal class CreditApplicationMonitorJob @Inject constructor(
  private val creditApplicationApprovedListener: LineOfCreditApplicationApprovedListener,
  private val capitalApplicationStore: CapitalApplicationStore,
) : HighbeamJob<Job.Params>() {
  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  internal object Params : HighbeamJob.Params()

  override suspend fun execute(params: Params) {
    // Currently only support Approved applications
    capitalApplicationStore.getAll(CapitalApplicationRep.State.Approved).forEach {
      creditApplicationApprovedListener.publish(
        it.businessGuid,
        it.guid,
      )
    }
  }
}
