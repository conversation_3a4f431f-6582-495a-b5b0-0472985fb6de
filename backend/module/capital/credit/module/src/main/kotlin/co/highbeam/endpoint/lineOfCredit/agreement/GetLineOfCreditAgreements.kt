package co.highbeam.endpoint.lineOfCredit.agreement

import co.highbeam.api.lineOfCredit.agreement.LineOfCreditAgreementApi
import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthCapitalAccount
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.agreement.LineOfCreditAgreementService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall

internal class GetLineOfCreditAgreements @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditAgreementService: LineOfCreditAgreementService,
) : EndpointHandler<LineOfCreditAgreementApi.GetAgreements, List<CapitalAgreementRep>>(
  template = LineOfCreditAgreementApi.GetAgreements::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): LineOfCreditAgreementApi.GetAgreements =
    LineOfCreditAgreementApi.GetAgreements(
      businessGuid = call.getParam("businessGuid"),
      lineOfCreditGuid = call.getParam("lineOfCreditGuid"),
    )

  override fun loggingContext(
    endpoint: LineOfCreditAgreementApi.GetAgreements,
  ) = super.loggingContext(endpoint) +
    mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "lineOfCreditGuid" to endpoint.lineOfCreditGuid.toString(),
    )

  override suspend fun Handler.handle(
    endpoint: LineOfCreditAgreementApi.GetAgreements
  ): List<CapitalAgreementRep> {
    authSome(
      Auth.All(
        listOf(
          authPermission(Permission.CreditApplication_Read) { endpoint.businessGuid },
          authCapitalAccount(
            permission = Permission.CreditApplication_Read,
            businessGuid = endpoint.businessGuid,
            capitalAccountGuid = endpoint.lineOfCreditGuid,
          )
        )
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return lineOfCreditAgreementService.getAgreements(
      businessGuid = endpoint.businessGuid,
      lineOfCreditGuid = endpoint.lineOfCreditGuid,
    )
  }
}
