package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.account.service.CapitalAccountService
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.lineOfCredit.LineOfCreditNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class CreateDrawdown @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authMfa: AuthMfa.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val bankAccountClient: BankAccountClient,
  private val capitalTransactionService: CapitalTransactionService,
  private val capitalAccountService: CapitalAccountService,
) : EndpointHandler<Api.CreateDrawdown, CapitalDrawdownTransactionRep>(
  template = Api.CreateDrawdown::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall
  ): Api.CreateDrawdown = Api.CreateDrawdown(
    businessGuid = call.getParam("businessGuid"),
    creditAccountGuid = call.getParam("creditAccountGuid"),
    rep = call.body(),
  )

  override fun loggingContext(endpoint: Api.CreateDrawdown) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(
    endpoint: Api.CreateDrawdown
  ): CapitalDrawdownTransactionRep {
    authSome(
      Auth.All(
        listOf(
          authPermission(Permission.Capital_Drawdown) { endpoint.businessGuid },
          authMfa(),
          authPermission(Permission.Capital_Drawdown) {
            bankAccountClient.request(
              BankAccountApi.Get(endpoint.rep.bankAccountGuid)
            )?.businessGuid
          },
        )
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS)
    )

    val creditAccount = capitalAccountService.get(
      guid = endpoint.creditAccountGuid,
      businessGuid = endpoint.businessGuid,
    ) ?: throw unprocessable(LineOfCreditNotFound())
    authSome(
      authPermission(Permission.Capital_Drawdown) { creditAccount.businessGuid },
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return capitalTransactionService.makeDrawdown(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.creditAccountGuid,
      creator = endpoint.rep,
      transactionInitiator = CapitalDrawdownTransactionRep.TransactionInitiator.User,
    )
  }
}
