package co.highbeam.endpoint.onboarding.creditApplication

import co.highbeam.api.onboarding.CreditApplicationApi
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.onboarding.CreditApplicationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall

internal class GetCreditApplication @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val creditApplicationService: CreditApplicationService,
) : EndpointHandler<CreditApplicationApi.Get, CapitalApplicationRep>(
  template = CreditApplicationApi.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): CreditApplicationApi.Get =
    CreditApplicationApi.Get(
      businessGuid = call.getParam("businessGuid"),
      creditApplicationGuid = call.getParam("creditApplicationGuid"),
    )

  override suspend fun Handler.handle(
    endpoint: CreditApplicationApi.Get,
  ): CapitalApplicationRep {
    auth(authPermission(Permission.CreditApplication_Read) { endpoint.businessGuid })
    val creditApplication = creditApplicationService.get(
      businessGuid = endpoint.businessGuid,
      creditApplicationGuid = endpoint.creditApplicationGuid,
    )
    auth(authPermission(Permission.CreditApplication_Read) { creditApplication.businessGuid })

    return creditApplicationService.get(
      businessGuid = endpoint.businessGuid,
      creditApplicationGuid = endpoint.creditApplicationGuid,
    )
  }
}
