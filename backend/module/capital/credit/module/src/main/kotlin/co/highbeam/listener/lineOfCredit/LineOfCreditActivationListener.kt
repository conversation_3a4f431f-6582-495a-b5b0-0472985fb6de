package co.highbeam.listener.lineOfCredit

import co.highbeam.config.CreditConfig
import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.rep.onboarding.CreditApplicationPubsubEventRep
import co.highbeam.service.onboarding.CreditApplicationService
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.slack.client.SlackMessageClient
import mu.KotlinLogging
import java.util.UUID

internal const val ACTIVATE_LINE_OF_CREDIT_TOPIC_NAME = "activate-line-of-credit"

@Singleton
internal class LineOfCreditActivationListener @Inject constructor(
  listenerFactory: EventListenerFactory,
  private val creditApplicationService: CreditApplicationService,
  @Named(ACTIVATE_LINE_OF_CREDIT_TOPIC_NAME)
  private val publisher: EventPublisher<CreditApplicationPubsubEventRep>,
  private val slackMessageClient: SlackMessageClient,
  private val creditConfig: CreditConfig,
) {
  private val logger = KotlinLogging.logger {}

  init {
    listenerFactory.startAsync(
      topicConfig = TopicConfig(ACTIVATE_LINE_OF_CREDIT_TOPIC_NAME),
      subscriptionConfig = SubscriptionConfig("default"),
      clazz = CreditApplicationPubsubEventRep::class.java,
      listener = ::onReceive,
    )
  }

  fun publish(businessGuid: UUID, applicationGuid: UUID) {
    logger.info { "Publishing application approved event: applicationGuid: $applicationGuid" }
    publisher.publishEvent(
      CreditApplicationPubsubEventRep(
        businessGuid,
        applicationGuid,
      )
    )
  }

  suspend fun onReceive(rep: CreditApplicationPubsubEventRep) {
    logger.info {
      "Received line of credit activation event for - ${rep.creditApplicationGuid}"
    }

    // Get credit application info
    val creditApplication: CapitalApplicationRep = creditApplicationService.get(
      rep.businessGuid, rep.creditApplicationGuid
    )

    slackMessageClient.sendMessage(
      key = null,
      creditConfig.lineOfCreditGenericMessageSlackWebhookPath,
      body = mapOf(
        "message" to "${creditApplication.businessDetails?.businessName}(${rep.businessGuid}) " +
          "have all payouts routed. Please activate their line of credit.",
      )
    )

    creditApplicationService.update(
      businessGuid = rep.businessGuid,
      creditApplicationGuid = rep.creditApplicationGuid,
      CapitalApplicationRep.Updater(
        state = CapitalApplicationRep.State.PayoutsConnected
      )
    )
  }
}
