package co.highbeam.service.onboarding

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessAddress.BusinessAddressApi
import co.highbeam.api.businessDetails.BusinessDetailsApi
import co.highbeam.capital.onboarding.store.application.CapitalApplicationDocumentStore
import co.highbeam.capital.onboarding.store.application.CapitalApplicationStore
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessAddress.BusinessAddressClient
import co.highbeam.client.businessDetails.BusinessDetailsClient
import co.highbeam.config.CreditConfig
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.onboarding.CapitalApplicationNotFound
import co.highbeam.exception.onboarding.CreditApplicationUnprocessableStateException
import co.highbeam.mapper.onboarding.CreditApplicationDocumentMapper
import co.highbeam.mapper.onboarding.CreditApplicationMapper
import co.highbeam.rep.onboarding.CapitalApplicationDocumentRep
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.rep.onboarding.CapitalApplicationRep.State.New
import co.highbeam.rep.onboarding.CapitalApplicationRep.State.Submitted
import co.highbeam.sql.store.coTransaction
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import com.slack.client.SlackMessageClient
import com.taktile.client.TaktileClient
import com.taktile.config.TaktileConfig
import com.taktile.rep.DecisionControl
import com.taktile.rep.DecisionMetadata
import com.taktile.rep.DecisionRequest
import com.taktile.rep.ExecutionMode
import com.taktile.rep.SchemaRep
import com.taktile.rep.SchemaRep.AddressRep
import org.jdbi.v3.core.Jdbi
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class CreditApplicationServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val businessDetailsClient: BusinessDetailsClient,
  private val businessAddressClient: BusinessAddressClient,
  private val capitalApplicationStore: CapitalApplicationStore,
  private val capitalApplicationDocumentStore: CapitalApplicationDocumentStore,
  private val creditApplicationMapper: CreditApplicationMapper,
  private val creditApplicationDocumentMapper: CreditApplicationDocumentMapper,
  private val creditConfig: CreditConfig,
  private val clock: Clock,
  private val jdbi: Jdbi,
  private val slackMessageClient: SlackMessageClient,
  private val taktileClient: TaktileClient,
  private val taktileConfig: TaktileConfig,
  private val unitCoClient: UnitCoClient,
) : CreditApplicationService {
  override suspend fun create(
    businessGuid: UUID,
  ) = creditApplicationMapper.toRep(
    capitalApplicationStore.create(creditApplicationMapper.new(businessGuid))
  )

  override suspend fun update(
    businessGuid: UUID,
    creditApplicationGuid: UUID,
    rep: CapitalApplicationRep.Updater
  ): CapitalApplicationRep {
    return creditApplicationMapper.toRep(
      capitalApplicationStore.update(
        guid = creditApplicationGuid,
        businessGuid = businessGuid,
        updater = creditApplicationMapper.toModel(
          rep
        )
      )
    )
  }

  override suspend fun get(
    businessGuid: UUID,
    creditApplicationGuid: UUID
  ) =
    creditApplicationMapper.toRep(
      capitalApplicationStore.get(creditApplicationGuid, businessGuid)
        ?: throw CapitalApplicationNotFound()
    )

  override suspend fun getByBusiness(businessGuid: UUID) =
    capitalApplicationStore.getAll(businessGuid).map {
      creditApplicationMapper.toRep(it)
    }

  override suspend fun submit(
    creditApplicationGuid: UUID,
    businessGuid: UUID,
    rep: CapitalApplicationRep.Updater
  ): CapitalApplicationRep {
    try {
      slackMessageClient.notifySlack(
        businessGuid = businessGuid
      )
    } catch (_: Exception) {
    }

    return jdbi.coTransaction {
      val creditApplicationModel = capitalApplicationStore.get(
        guid = creditApplicationGuid,
        businessGuid = businessGuid,
      ) ?: throw CapitalApplicationNotFound()
      // TODO(justin): Run validate function on the customerApplicationData to make sure that the
      //  content is well-formed before we put it in a Submitted state.
      when (creditApplicationModel.state) {
        New -> {
          val updatedApplication = creditApplicationMapper.toRep(
            capitalApplicationStore.update(
              guid = creditApplicationGuid,
              businessGuid = businessGuid,
              updater = creditApplicationMapper.toModel(
                rep.copy(
                  state = Submitted,
                )
              ).copy(
                submittedAt = ZonedDateTime.now(clock),
              )
            )
          )

          // Send to Taktile after successful submission
          try {
            sendToTaktile(businessGuid, creditApplicationGuid)
          } catch (e: Exception) {
            // Log the error but don't fail the submission
            // TODO: Add proper logging
          }

          updatedApplication
        }

        else ->
          throw CreditApplicationUnprocessableStateException(
            creditApplicationModel.state,
          )
      }
    }
  }

  override suspend fun deleteDocument(
    creditApplicationGuid: UUID,
    businessGuid: UUID,
    creditApplicationDocumentGuid: UUID,
  ): CapitalApplicationDocumentRep {
    return creditApplicationDocumentMapper.toRep(
      capitalApplicationDocumentStore.delete(
        capitalApplicationGuid = creditApplicationGuid,
        businessGuid = businessGuid,
        guid = creditApplicationDocumentGuid,
      ) ?: throw CapitalApplicationNotFound(),
      CreditApplicationDocumentMapper.SignedUrlType.ForFetch
    )
  }

  override suspend fun createDocument(
    businessGuid: UUID,
    creditApplicationGuid: UUID,
    rep: CapitalApplicationDocumentRep.Creator,
  ) = creditApplicationDocumentMapper.toRep(
    model = capitalApplicationDocumentStore.create(
      creditApplicationDocumentMapper.new(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplicationGuid,
        rep = rep,
      )
    ),
    purpose = CreditApplicationDocumentMapper.SignedUrlType.ForUpload
  )

  override suspend fun getDocuments(
    businessGuid: UUID,
    creditApplicationGuid: UUID,
  ) = capitalApplicationDocumentStore.get(
    businessGuid = businessGuid,
    capitalApplicationGuid = creditApplicationGuid,
  ).map {
    creditApplicationDocumentMapper.toRep(
      model = it,
      purpose = CreditApplicationDocumentMapper.SignedUrlType.ForFetch
    )
  }

  private suspend fun SlackMessageClient.notifySlack(
    businessGuid: UUID,
  ) {
    sendMessage(
      key = null,
      webhookPath = creditConfig.lineOfCreditAppSubmittedSlackWebhookPath,
      body = mapOf(
        "businessGuid" to "$businessGuid",
      )
    )
  }

  private suspend fun sendToTaktile(businessGuid: UUID, creditApplicationGuid: UUID) {
    val business = businessClient.request(
      BusinessApi.Get(businessGuid)
    ) ?: throw BusinessNotFound()

    val businessDetails = businessDetailsClient.request(
      BusinessDetailsApi.GetInternal(businessGuid)
    ) ?: throw BusinessNotFound()

    val businessAddress = businessAddressClient.request(
      BusinessAddressApi.Get(businessGuid)
    ) ?: throw BusinessNotFound()

    taktileClient.decide<SchemaRep.Creator>(
      flowSlug = taktileConfig.flowSlug,
      DecisionRequest(
        data = SchemaRep.Creator(
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplicationGuid,
          businessName = business.name,
          businessDba = business.dba,
          phoneNumber = businessDetails.phoneNumber,
          ein = businessDetails.ein,
          incorporationState = businessDetails.incorporationState,
          associatedPerson = businessDetails.associatedPerson,
          address = AddressRep(
            line1 = businessAddress.line1,
            line2 = businessAddress.line2,
            city = businessAddress.city,
            state = businessAddress.state,
            postalCode = businessAddress.postalCode,
            country = businessAddress.country,
          ),
        ),
        metadata = DecisionMetadata(
          version = taktileConfig.flowVersion,
          entityId = businessGuid.toString(),
        ),
        control = DecisionControl(
          executionMode = ExecutionMode.ASYNC,
        ),
      ),
    )
  }
}
