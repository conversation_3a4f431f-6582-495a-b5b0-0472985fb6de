package co.highbeam.model.documents

import org.jdbi.v3.json.Json
import java.time.ZonedDateTime
import java.util.UUID

data class DocumentModel(
  val guid: UUID,
  val generatedAt: ZonedDateTime,
  val businessGuid: UUID,
  val lineOfCreditGuid: UUID?,
  @Json val data: DocumentDataModel,
) {
  data class Creator(
    val guid: UUID,
    val generatedAt: ZonedDateTime,
    val businessGuid: UUID,
    val lineOfCreditGuid: UUID?,
    @Json val data: DocumentDataModel,
  )
}
