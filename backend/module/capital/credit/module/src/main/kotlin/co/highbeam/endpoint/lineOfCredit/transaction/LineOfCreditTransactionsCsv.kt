package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.mapper.CapitalTransactionResponseMapper
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class LineOfCreditTransactionsCsv @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val capitalTransactionService: CapitalTransactionService,
  private val capitalTransactionResponseMapper: CapitalTransactionResponseMapper,
) : EndpointHandler<Api.GetTransactionsCsv, String>(
  template = Api.GetTransactionsCsv::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall) = Api.GetTransactionsCsv(
    businessGuid = call.getParam("businessGuid"),
    since = call.getParam("since"),
    until = call.getParam("until"),
  )

  override suspend fun Handler.handle(endpoint: Api.GetTransactionsCsv): String {
    auth(authPermission(Permission.Transaction_Read) { endpoint.businessGuid })
    return capitalTransactionResponseMapper.toCsv(
      capitalTransactionService.transactionsWithRunningBalance(
        businessGuid = endpoint.businessGuid,
        sinceInclusive = endpoint.since,
        untilInclusive = endpoint.until,
      )
    )
  }
}
