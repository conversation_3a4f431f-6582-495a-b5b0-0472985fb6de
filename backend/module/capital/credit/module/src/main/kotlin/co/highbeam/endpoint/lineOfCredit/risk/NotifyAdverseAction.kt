package co.highbeam.endpoint.lineOfCredit.risk

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.risk.CreditAdverseActionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.risk.CreditAdverseActionApi as Api

internal class NotifyAdverseAction @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val creditAdverseActionService: CreditAdverseActionService,
) : EndpointHandler<Api.NotifyAdverseAction, Unit>(
  template = Api.NotifyAdverseAction::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.NotifyAdverseAction =
    Api.NotifyAdverseAction(
      businessGuid = call.getParam("businessGuid"),
    )

  override suspend fun Handler.handle(
    endpoint: Api.NotifyAdverseAction,
  ) {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    creditAdverseActionService.notifyAdverseAction(
      businessGuid = endpoint.businessGuid,
    )
  }
}
