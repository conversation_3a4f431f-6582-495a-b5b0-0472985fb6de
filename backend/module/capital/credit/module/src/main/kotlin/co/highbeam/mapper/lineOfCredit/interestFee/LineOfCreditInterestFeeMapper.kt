package co.highbeam.mapper.lineOfCredit.interestFee

import co.highbeam.model.lineOfCredit.interestFee.LineOfCreditInterestFeeDataModel
import co.highbeam.model.lineOfCredit.interestFee.LineOfCreditInterestFeeModel
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import java.time.LocalDate
import java.util.UUID

internal class LineOfCreditInterestFeeMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun creatorModel(
    businessGuid: UUID,
    lineOfCreditGuid: UUID,
    interestFeeTransferGuid: UUID?,
    date: LocalDate,
    data: LineOfCreditInterestFeeDataModel,
  ): LineOfCreditInterestFeeModel.Creator =
    LineOfCreditInterestFeeModel.Creator(
      guid = uuidGenerator.generate(),
      businessGuid = businessGuid,
      lineOfCreditGuid = lineOfCreditGuid,
      interestFeeTransferGuid = interestFeeTransferGuid,
      date = date,
      data = data,
    )

  fun updaterModels(
    guids: List<UUID>,
    interestFeeTransferGuid: UUID?,
  ): List<LineOfCreditInterestFeeModel.Updater> =
    guids.map {
      LineOfCreditInterestFeeModel.Updater(
        guid = it,
        interestFeeTransferGuid = interestFeeTransferGuid,
      )
    }
}
