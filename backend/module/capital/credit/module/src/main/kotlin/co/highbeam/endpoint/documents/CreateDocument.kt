package co.highbeam.endpoint.documents

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.documents.DocumentStorageService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.documents.DocumentsApi as Api

internal class CreateDocument @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val documentsStorageService: DocumentStorageService,
) : EndpointHandler<Api.Create, String>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(
      businessGuid = call.getParam("businessGuid"),
      rep = call.body(),
    )

  override suspend fun Handler.handle(
    endpoint: Api.Create,
  ): String {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    return documentsStorageService.create(
      businessGuid = endpoint.businessGuid,
      rep = endpoint.rep,
    )
  }
}
