package co.highbeam.mapper.onboarding

import co.highbeam.capital.onboarding.model.application.CapitalApplicationDataModel
import co.highbeam.capital.onboarding.model.application.CapitalApplicationModel
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.rep.onboarding.CapitalApplicationRep.BusinessDetailsRep
import co.highbeam.rep.onboarding.CapitalApplicationRep.CreditOfferRep
import co.highbeam.rep.onboarding.CapitalApplicationRep.UserProvidedDetailsRep.SkipFinancialsRequirementReasonRep
import co.highbeam.rep.onboarding.CapitalApplicationRep.UserProvidedDetailsRep.SkipInventoryRequirementReasonRep
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import java.util.UUID

internal class CreditApplicationMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun toRep(
    model: CapitalApplicationModel,
  ): CapitalApplicationRep {
    return CapitalApplicationRep(
      guid = model.guid,
      businessGuid = model.businessGuid,
      state = model.state,
      submittedAt = model.submittedAt,
      offer = model.data.offer?.let {
        CreditOfferRep(
          initialLimit = it.initialLimit,
          nextLimit = it.nextLimit,
          goalLimit = it.goalLimit,
          apr = it.apr,
          remittanceRate = it.remittanceRate,
          repaymentTermInDays = it.repaymentTermInDays,
          agreementType = it.agreementType,
          drawPeriodEndsAt = it.drawPeriodEndsAt,
        )
      },
      userProvidedDetails = model.data.userProvidedDetails?.let {
        CapitalApplicationRep.UserProvidedDetailsRep(
          userNotes = it.userNotes,
          requestedAmount = it.requestedAmount,
          reasonForRequest = it.reasonForRequest,
          skipFinancialsRequirementReason = it.skipFinancialsRequirementReason?.let { reason ->
            SkipFinancialsRequirementReasonRep(
              reason = reason,
              explanation = it.skipFinancialsExplanation,
            )
          },
          skipInventoryRequirementReason = it.skipInventoryRequirementReason?.let { reason ->
            SkipInventoryRequirementReasonRep(
              reason = reason,
              explanation = it.skipInventoryExplanation,
            )
          },
          inventoryLeadTime = it.inventoryLeadTime,
          copackers = it.copackers,
          numberOfRetailLocations = it.numberOfRetailLocations,
          industry = it.industries?.first(),
          otherIndustry = it.otherIndustries?.first(),
          orgStructure = it.orgStructure,
          singleEntityBusiness = it.singleEntityBusiness,
          securedLenders = it.securedLenders,
          securedLendersExplanation = it.securedLendersExplanation,
          firstSaleAt = it.firstSaleAt,
          booksClosedAt = it.booksClosedAt,
          repaymentTermsPreference = it.repaymentTermsPreference
        )
      },
      businessDetails = model.data.businessDetails?.let {
        BusinessDetailsRep(
          businessName = it.businessName,
          businessIncorporationState = it.businessIncorporationState,
          signatory = it.signatory
        )
      }
    )
  }

  fun toModel(
    rep: CapitalApplicationRep.Updater
  ): CapitalApplicationModel.Updater {
    return CapitalApplicationModel.Updater(
      state = rep.state,
      data = CapitalApplicationDataModel(
        offer = rep.offer?.let {
          CapitalApplicationDataModel.CapitalOfferDataModel(
            initialLimit = it.initialLimit,
            nextLimit = it.nextLimit,
            goalLimit = it.goalLimit,
            apr = it.apr,
            remittanceRate = it.remittanceRate,
            repaymentTermInDays = it.repaymentTermInDays,
            agreementType = it.agreementType,
            drawPeriodEndsAt = it.drawPeriodEndsAt,
          )
        },
        businessDetails = rep.businessDetails?.let {
          CapitalApplicationDataModel.BusinessDetails(
            businessName = it.businessName,
            businessIncorporationState = it.businessIncorporationState,
            signatory = it.signatory,
          )
        },
        userProvidedDetails = rep.userProvidedDetails?.let {
          CapitalApplicationDataModel.UserProvidedDetails(
            userNotes = it.userNotes,
            requestedAmount = it.requestedAmount,
            reasonForRequest = it.reasonForRequest,
            skipFinancialsRequirementReason = it.skipFinancialsRequirementReason?.reason,
            skipFinancialsExplanation = it.skipFinancialsRequirementReason?.explanation,
            skipInventoryRequirementReason = it.skipInventoryRequirementReason?.reason,
            skipInventoryExplanation = it.skipInventoryRequirementReason?.explanation,
            inventoryLeadTime = it.inventoryLeadTime,
            copackers = it.copackers,
            numberOfRetailLocations = it.numberOfRetailLocations,
            industries = it.industry?.let { industry -> listOf(industry) },
            otherIndustries = it.otherIndustry?.let { industry -> listOf(industry) },
            orgStructure = it.orgStructure,
            securedLenders = it.securedLenders,
            singleEntityBusiness = it.singleEntityBusiness,
            securedLendersExplanation = it.securedLendersExplanation,
            firstSaleAt = it.firstSaleAt,
            booksClosedAt = it.booksClosedAt,
            repaymentTermsPreference = it.repaymentTermsPreference
          )
        },
        otherDetails = rep.otherDetails?.let {
          CapitalApplicationDataModel.OtherDetails(
            internalNotes = it.internalNotes
          )
        },
      ),
    )
  }

  fun new(businessGuid: UUID) = CapitalApplicationModel.Creator(
    guid = uuidGenerator.generate(),
    businessGuid = businessGuid,
    state = CapitalApplicationRep.State.New,
    submittedAt = null,
    data = CapitalApplicationDataModel(
      offer = null,
      businessDetails = null,
      userProvidedDetails = null,
      otherDetails = null,
    ),
  )
}
