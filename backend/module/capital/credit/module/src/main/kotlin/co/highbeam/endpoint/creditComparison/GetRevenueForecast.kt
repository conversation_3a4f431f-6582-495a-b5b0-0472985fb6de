package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.creditComparison.RevenueForecastRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class GetRevenueForecast @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.GetRevenueForecast, RevenueForecastRep>(
  template = Api.GetRevenueForecast::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetRevenueForecast =
    Api.GetRevenueForecast(
      businessGuid = call.getParam("businessGuid"),
      fromDate = call.getParam("fromDate"),
      intervalType = call.getParam("intervalType"),
      numberOfIntervals = call.getParam("numberOfIntervals")
    )

  override fun loggingContext(endpoint: Api.GetRevenueForecast) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.GetRevenueForecast): RevenueForecastRep {
    auth(authPermission(Permission.CreditComparison_Read) { endpoint.businessGuid })
    return creditComparisonService.revenueForecast(
      businessGuid = endpoint.businessGuid,
      fromDate = endpoint.fromDate,
      intervalType = endpoint.intervalType,
      numberOfIntervals = endpoint.numberOfIntervals,
    )
  }
}
