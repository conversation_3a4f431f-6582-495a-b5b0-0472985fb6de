package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.Auth
import co.highbeam.rep.creditComparison.CreditComparisonCalculationRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class CalculateCreditComparisonSnapshot @Inject constructor(
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.CalculateCreditComparisonSnapshot, List<CreditComparisonCalculationRep>>(
  template = Api.CalculateCreditComparisonSnapshot::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall,
  ): Api.CalculateCreditComparisonSnapshot =
    Api.CalculateCreditComparisonSnapshot(
      creditComparisonSnapshotSlug = call.getParam("creditComparisonSnapshotSlug")
    )

  override fun loggingContext(
    endpoint: Api.CalculateCreditComparisonSnapshot,
  ) = super.loggingContext(endpoint) + mapOf(
    "creditComparisonSnapshotSlug" to endpoint.creditComparisonSnapshotSlug.toString(),
  )

  override suspend fun Handler.handle(
    endpoint: Api.CalculateCreditComparisonSnapshot,
  ): List<CreditComparisonCalculationRep> {
    auth(Auth.Allow)

    return creditComparisonService.calculateSnapshot(
      creditComparisonSnapshot = endpoint.creditComparisonSnapshotSlug,
    )
  }
}
