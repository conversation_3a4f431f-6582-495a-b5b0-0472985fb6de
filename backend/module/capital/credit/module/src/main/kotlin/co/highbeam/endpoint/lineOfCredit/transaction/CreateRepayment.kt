package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.account.service.CapitalAccountService
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.lineOfCredit.LineOfCreditNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class CreateRepayment @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val authMfa: AuthMfa.Provider,
  private val bankAccountClient: BankAccountClient,
  private val capitalTransactionService: CapitalTransactionService,
  private val capitalAccountService: CapitalAccountService,
) : EndpointHandler<Api.CreateRepayment, CapitalRepaymentTransactionRep>(
  template = Api.CreateRepayment::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.CreateRepayment = Api.CreateRepayment(
    businessGuid = call.getParam("businessGuid"),
    creditAccountGuid = call.getParam("creditAccountGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(
    endpoint: Api.CreateRepayment,
  ): CapitalRepaymentTransactionRep {
    authSome(
      Auth.All(
        listOf(
          authPermission(Permission.Capital_Repay) { endpoint.businessGuid },
          authMfa(),
          authPermission(Permission.Capital_Repay) {
            bankAccountClient.request(
              BankAccountApi.Get(endpoint.rep.bankAccountGuid)
            )?.businessGuid
          },
        )
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS)
    )

    val creditAccount = capitalAccountService.get(
      guid = endpoint.creditAccountGuid,
      businessGuid = endpoint.businessGuid,
    ) ?: throw unprocessable(LineOfCreditNotFound())

    authSome(
      authPermission(Permission.Capital_Repay) { creditAccount.businessGuid },
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return capitalTransactionService.makeRepayment(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.creditAccountGuid,
      creator = endpoint.rep,
    )
  }
}
