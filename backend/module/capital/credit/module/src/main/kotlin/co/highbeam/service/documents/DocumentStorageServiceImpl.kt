package co.highbeam.service.documents

import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.mapper.documents.DocumentMapper
import co.highbeam.rep.DocumentRep
import co.highbeam.store.documents.DocumentStore
import com.google.cloud.storage.HttpMethod
import com.google.inject.Inject
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder
import java.io.ByteArrayOutputStream
import java.util.UUID

internal class DocumentStorageServiceImpl @Inject constructor(
  private val storage: GoogleCloudStorage,
  private val documentStore: DocumentStore,
  private val documentMapper: DocumentMapper,
) : DocumentStorageService {
  // TODO(justin): Make this private after we stop using it inside of the onboarding
  //  service.
  override fun convertXhtmlToPdfByteArray(xhtml: String): ByteArray {
    return ByteArrayOutputStream().use { outputStream ->
      PdfRendererBuilder().apply {
        useFastMode()
        withHtmlContent(xhtml, null)
        toStream(outputStream)
      }.run()
      return@use outputStream.toByteArray()
    }
  }

  override fun upload(documentGuid: UUID, xhtml: String) {
    storage.upload(objectName(documentGuid), convertXhtmlToPdfByteArray(xhtml))
  }

  override fun getDocuments(businessGuid: UUID): List<DocumentRep> {
    return documentStore.getAllDocuments(businessGuid).map {
      documentMapper.toRep(it)
    }
  }

  override fun getDocumentSignedUrl(
    businessGuid: UUID,
    documentGuid: UUID
  ): String {
    // Check that the document belongs to the business
    documentStore.get(businessGuid, documentGuid)
    return storage.userAccessUrl(
      objectName = objectName(documentGuid),
      httpMethod = HttpMethod.GET,
    ).toString()
  }

  override fun create(businessGuid: UUID, rep: DocumentRep.Creator): String {
    val documentModel = documentStore.create(
      documentMapper.toModel(
        businessGuid = businessGuid,
        rep = rep,
      ),
    )

    return storage.userAccessUrl(
      objectName = objectName(documentModel.guid),
      httpMethod = HttpMethod.PUT,
    ).toString()
  }

  private fun objectName(documentGuid: UUID): String =
    "$DocumentsFolder/$documentGuid"

  companion object {
    const val DocumentsFolder = "Documents"
  }
}
