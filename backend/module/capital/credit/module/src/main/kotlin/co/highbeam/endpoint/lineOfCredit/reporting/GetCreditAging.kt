package co.highbeam.endpoint.lineOfCredit.reporting

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.reporting.LineOfCreditAgingRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.reporting.LineOfCreditBusinessReportingService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.reporting.LineOfCreditBusinessReportingApi as Api

internal class GetCreditAging @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditBusinessReportingService: LineOfCreditBusinessReportingService,
) : EndpointHandler<Api.GetCreditAging, List<LineOfCreditAgingRep>>(
  template = Api.GetCreditAging::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetCreditAging =
    Api.GetCreditAging(
      businessGuid = call.getParam("businessGuid"),
      creditAccountGuid = call.getParam("creditAccountGuid"),
    )

  override suspend fun Handler.handle(
    endpoint: Api.GetCreditAging,
  ): List<LineOfCreditAgingRep> {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    return lineOfCreditBusinessReportingService.agingReport(
      businessGuid = endpoint.businessGuid,
      creditAccountGuid = endpoint.creditAccountGuid,
    )
  }
}
