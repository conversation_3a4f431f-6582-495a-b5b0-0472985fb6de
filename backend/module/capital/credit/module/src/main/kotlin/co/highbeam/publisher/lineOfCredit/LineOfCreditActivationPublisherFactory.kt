package co.highbeam.publisher.lineOfCredit

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.listener.lineOfCredit.ACTIVATE_LINE_OF_CREDIT_TOPIC_NAME
import co.highbeam.rep.onboarding.CreditApplicationPubsubEventRep
import com.google.inject.Inject

internal class LineOfCreditActivationPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<CreditApplicationPubsubEventRep>>() {
  override fun get(): EventPublisher<CreditApplicationPubsubEventRep> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<CreditApplicationPubsubEventRep>(
    topic = TopicConfig(ACTIVATE_LINE_OF_CREDIT_TOPIC_NAME),
    typeLiteral = typeLiteral(),
    provider = LineOfCreditActivationPublisherFactory::class,
  )
}
