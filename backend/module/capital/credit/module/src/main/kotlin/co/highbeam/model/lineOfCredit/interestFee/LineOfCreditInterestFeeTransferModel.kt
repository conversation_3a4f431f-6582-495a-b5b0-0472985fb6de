package co.highbeam.model.lineOfCredit.interestFee

import co.highbeam.money.Money
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeMetadataRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep
import com.fasterxml.jackson.databind.JsonNode
import org.jdbi.v3.json.Json
import java.time.LocalDate
import java.util.UUID

data class LineOfCreditInterestFeeTransferModel(
  val guid: UUID,
  val businessGuid: UUID,
  val lineOfCreditGuid: UUID,
  val state: LineOfCreditInterestFeeTransferRep.State,
  val amount: Money,
  @Json val unitBookPaymentResponse: JsonNode?,
  val since: LocalDate,
  val until: LocalDate,
  @Json val unitAccountEndOfDayResponse: List<JsonNode>,
  @Json val metadata: LineOfCreditInterestFeeMetadataRep,
) {
  internal data class Update(
    val guid: UUID,
    val state: LineOfCreditInterestFeeTransferRep.State,
    @Json val unitBookPaymentResponse: JsonNode?,
  )
}
