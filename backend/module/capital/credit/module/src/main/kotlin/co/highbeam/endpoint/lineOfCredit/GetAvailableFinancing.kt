package co.highbeam.endpoint.lineOfCredit

import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.auth.auth.AuthToken
import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.LineOfCreditService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.LineOfCreditApi as Api

internal class GetAvailableFinancing @Inject constructor(
  private val authToken: AuthToken.Provider,
  private val lineOfCreditService: LineOfCreditService,
) : EndpointHandler<LineOfCreditApi.GetAvailableFinancing, List<CapitalTransactionSummaryRep>>(
  template = Api.GetAvailableFinancing::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): LineOfCreditApi.GetAvailableFinancing =
    Api.GetAvailableFinancing(
      businessGuid = call.getParam("businessGuid"),
      since = call.getParam("since"),
      until = call.getParam("until")
    )

  override fun loggingContext(endpoint: LineOfCreditApi.GetAvailableFinancing) =
    super.loggingContext(endpoint) +
      mapOf(
        "businessGuid" to endpoint.businessGuid.toString(),
        "since" to endpoint.since.toString(),
        "until" to endpoint.until.toString(),
      )

  override suspend fun Handler.handle(
    endpoint: LineOfCreditApi.GetAvailableFinancing
  ): List<CapitalTransactionSummaryRep> {
    auth(authToken(endpoint.businessGuid, "accountBalanceReport:create"))

    return lineOfCreditService.getAvailableFinancing(
      businessGuid = endpoint.businessGuid,
      since = endpoint.since,
      until = endpoint.until
    )
  }
}
