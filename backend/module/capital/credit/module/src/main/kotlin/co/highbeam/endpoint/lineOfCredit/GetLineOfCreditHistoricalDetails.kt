package co.highbeam.endpoint.lineOfCredit

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.lineOfCredit.LineOfCreditHistoricalDetailsRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.LineOfCreditService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.LineOfCreditApi as Api

internal class GetLineOfCreditHistoricalDetails @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val lineOfCreditService: LineOfCreditService,
) : EndpointHandler<Api.GetLineOfCreditHistoricalDetails, LineOfCreditHistoricalDetailsRep>(
  template = Api.GetLineOfCreditHistoricalDetails::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetLineOfCreditHistoricalDetails =
    Api.GetLineOfCreditHistoricalDetails(
      businessGuid = call.getParam("businessGuid"),
      lineOfCreditGuid = call.getParam("lineOfCreditGuid"),
      date = call.getParam("date"),
    )

  override fun loggingContext(
    endpoint: Api.GetLineOfCreditHistoricalDetails
  ) = super.loggingContext(endpoint) +
    mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "lineOfCreditGuid" to endpoint.lineOfCreditGuid.toString(),
    )

  override suspend fun Handler.handle(
    endpoint: Api.GetLineOfCreditHistoricalDetails
  ): LineOfCreditHistoricalDetailsRep {
    auth(authPermission(Permission.BankAccount_Read) { endpoint.businessGuid })
    return lineOfCreditService.getLineOfCreditHistoricalDetails(
      businessGuid = endpoint.businessGuid,
      lineOfCreditGuid = endpoint.lineOfCreditGuid,
      date = endpoint.date
    )
  }
}
