package co.highbeam.endpoint.onboarding.creditAgreement

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.onboarding.LineOfCreditOnboardingService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.onboarding.CapitalAgreementApi as Api

internal class UpdateLineOfCreditUserActionsMetadata @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val lineOfCreditOnboardingService: LineOfCreditOnboardingService,
) : EndpointHandler<Api.UpdateLineOfCreditUserActionsMetadata, CapitalAgreementRep>(
  template = Api.UpdateLineOfCreditUserActionsMetadata::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall
  ): Api.UpdateLineOfCreditUserActionsMetadata = Api.UpdateLineOfCreditUserActionsMetadata(
    businessGuid = call.getParam("businessGuid"),
    lineOfCreditGuid = call.getParam("lineOfCreditGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(
    endpoint: Api.UpdateLineOfCreditUserActionsMetadata
  ): CapitalAgreementRep {
    auth(authPermission(Permission.CreditApplication_Write) { endpoint.businessGuid })
    return lineOfCreditOnboardingService.updateLineOfCreditUserActionsMetadata(
      businessGuid = endpoint.businessGuid,
      lineOfCreditGuid = endpoint.lineOfCreditGuid,
      rep = endpoint.rep
    )
  }
}
