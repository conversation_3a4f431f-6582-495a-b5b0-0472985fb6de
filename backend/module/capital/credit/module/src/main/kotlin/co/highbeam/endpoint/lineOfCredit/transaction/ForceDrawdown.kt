package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class ForceDrawdown @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val capitalTransactionService: CapitalTransactionService,
) : EndpointHandler<Api.ForceDrawdown, CapitalDrawdownTransactionRep>(
  template = Api.ForceDrawdown::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall
  ): Api.ForceDrawdown = Api.ForceDrawdown(
    businessGuid = call.getParam("businessGuid"),
    creditAccountGuid = call.getParam("creditAccountGuid"),
    rep = call.body(),
    transactionInitiator = call.getParam("transactionInitiator"),
  )

  override fun loggingContext(endpoint: Api.ForceDrawdown) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(
    endpoint: Api.ForceDrawdown
  ): CapitalDrawdownTransactionRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return capitalTransactionService.makeDrawdown(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.creditAccountGuid,
      creator = endpoint.rep,
      transactionInitiator = endpoint.transactionInitiator,
    )
  }
}
