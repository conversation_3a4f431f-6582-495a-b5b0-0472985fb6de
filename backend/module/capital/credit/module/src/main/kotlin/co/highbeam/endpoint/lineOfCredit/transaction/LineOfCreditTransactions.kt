package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class LineOfCreditTransactions @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val capitalTransactionService: CapitalTransactionService,
) : EndpointHandler<Api.GetTransactions, List<CapitalTransactionRep>>(
  template = Api.GetTransactions::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetTransactions = Api.GetTransactions(
    businessGuid = call.getParam("businessGuid"),
    since = call.getParam("since"),
    until = call.getParam("until"),
  )

  override suspend fun Handler.handle(
    endpoint: Api.GetTransactions,
  ): List<CapitalTransactionRep> {
    authSome(
      authPermission(Permission.Transaction_Read) { endpoint.businessGuid },
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )
    return capitalTransactionService.transactions(
      businessGuid = endpoint.businessGuid,
      sinceInclusive = endpoint.since,
      untilInclusive = endpoint.until,
    )
  }
}
