package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.creditComparison.CreditComparisonSessionRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class CreateCreditComparisonSession @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.CreateCreditComparisonSession, CreditComparisonSessionRep>(
  template = Api.CreateCreditComparisonSession::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall,
  ) = Api.CreateCreditComparisonSession(
    businessGuid = call.getParam("businessGuid", optional = true),
  )

  override suspend fun Handler.handle(
    endpoint: Api.CreateCreditComparisonSession
  ): CreditComparisonSessionRep {
    auth(
      Auth.Conditional(
        on = endpoint.businessGuid != null,
        ifTrue = {
          authPermission(Permission.CreditComparison_Write) {
            checkNotNull(endpoint.businessGuid)
          }
        },
        ifFalse = { Auth.Allow },
      )
    )

    return creditComparisonService.createSession(endpoint.businessGuid)
  }
}
