package co.highbeam.listener.lineOfCredit

import co.highbeam.config.CreditConfig
import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.mapper.onboarding.LineOfCreditAgreementMapper
import co.highbeam.rep.lineOfCredit.LineOfCreditSetupRep
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.rep.onboarding.CreditApplicationPubsubEventRep
import co.highbeam.service.onboarding.CreditApplicationService
import co.highbeam.service.onboarding.LineOfCreditOnboardingService
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import com.slack.client.SlackMessageClient
import mu.KotlinLogging
import java.time.Clock
import java.time.LocalDate
import java.util.UUID

internal const val CREDIT_APPLICATION_APPROVED_TOPIC_NAME = "line-of-credit-application-approved"

@Singleton
internal class LineOfCreditApplicationApprovedListener @Inject constructor(
  listenerFactory: EventListenerFactory,
  private val lineOfCreditOnboardingService: LineOfCreditOnboardingService,
  private val creditApplicationService: CreditApplicationService,
  @Named(CREDIT_APPLICATION_APPROVED_TOPIC_NAME)
  private val publisher: EventPublisher<CreditApplicationPubsubEventRep>,
  private val slackMessageClient: SlackMessageClient,
  private val creditConfig: CreditConfig,
  private val lineOfCreditAgreementMapper: LineOfCreditAgreementMapper,
  private val clock: Clock,
) {
  private val logger = KotlinLogging.logger {}

  init {
    listenerFactory.startAsync(
      topicConfig = TopicConfig(CREDIT_APPLICATION_APPROVED_TOPIC_NAME),
      subscriptionConfig = SubscriptionConfig("default"),
      clazz = CreditApplicationPubsubEventRep::class.java,
      listener = ::onReceive,
    )
  }

  fun publish(businessGuid: UUID, applicationGuid: UUID) {
    logger.info { "Publishing application approved event: applicationGuid: $applicationGuid" }
    publisher.publishEvent(
      CreditApplicationPubsubEventRep(
        businessGuid,
        applicationGuid,
      )
    )
  }

  @Suppress("SwallowedException")
  private suspend fun onReceive(rep: CreditApplicationPubsubEventRep) {
    logger.info {
      "Received application approved event: applicationGuid - ${rep.creditApplicationGuid}"
    }
    // Get credit application info
    val creditApplication: CapitalApplicationRep = creditApplicationService.get(
      businessGuid = rep.businessGuid,
      creditApplicationGuid = rep.creditApplicationGuid
    )

    val offer = checkNotNull(creditApplication.offer) {
      "Cannot setup LoC due to credit offer details = null for ${rep.creditApplicationGuid} "
    }

    val businessDetails =
      checkNotNull(creditApplication.businessDetails) {
        "Cannot setup LoC due to business details = null for ${rep.creditApplicationGuid} "
      }

    lineOfCreditOnboardingService.setUpLineOfCredit(
      businessGuid = rep.businessGuid,
      rep = LineOfCreditSetupRep(
        terms = lineOfCreditAgreementMapper.toLineOfCreditTermsRep(
          offerRep = offer,
          businessDetailsRep = businessDetails,
          date = LocalDate.now(clock),
        )
      )
    )

    @Suppress("TooGenericExceptionCaught")
    try {
      creditApplicationService.update(
        businessGuid = rep.businessGuid,
        creditApplicationGuid = rep.creditApplicationGuid,
        rep = CapitalApplicationRep.Updater(
          state = CapitalApplicationRep.State.ApprovalProcessed
        )
      )
    } catch (e: Exception) {
      logger.error(e) {
        "Failed to update state application state for ${rep.creditApplicationGuid}."
      }
    }
    slackMessageClient.sendMessage(
      key = null,
      creditConfig.lineOfCreditApprovalProcessedSlackWebhookPath,
      body = mapOf(
        "businessGuid" to rep.businessGuid.toString(),
        "businessName" to businessDetails.businessName,
        "signatoryFullName" to businessDetails.signatory.fullName,
        "signatoryEmail" to businessDetails.signatory.email,
      )
    )
  }
}
