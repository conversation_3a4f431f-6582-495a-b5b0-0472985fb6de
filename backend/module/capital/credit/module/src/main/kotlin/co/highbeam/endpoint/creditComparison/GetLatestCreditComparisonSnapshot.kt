package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.auth.AuthCreditComparisonSession
import co.highbeam.rep.creditComparison.CreditComparisonSnapshotRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class GetLatestCreditComparisonSnapshot @Inject constructor(
  private val authCreditComparisonSession: AuthCreditComparisonSession.Provider,
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.GetLatestCreditComparisonSnapshot, CreditComparisonSnapshotRep>(
  template = Api.GetLatestCreditComparisonSnapshot::class.template(),
) {

  override suspend fun endpoint(
    call: ApplicationCall,
  ) = Api.GetLatestCreditComparisonSnapshot(
    creditComparisonSessionGuid = call.getParam("creditComparisonSessionGuid"),
  )

  override suspend fun Handler.handle(
    endpoint: Api.GetLatestCreditComparisonSnapshot
  ): CreditComparisonSnapshotRep {
    auth(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Read,
        sessionGuid = endpoint.creditComparisonSessionGuid,
      )
    )

    return creditComparisonService.getLatestSnapshot(endpoint.creditComparisonSessionGuid)
  }
}
