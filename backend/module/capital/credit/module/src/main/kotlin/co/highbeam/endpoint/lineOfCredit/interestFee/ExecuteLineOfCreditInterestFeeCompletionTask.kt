package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeClient
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeesRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import mu.KotlinLogging
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeCompletionTaskApi as Api

internal class ExecuteLineOfCreditInterestFeeCompletionTask @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditInterestFeeClient: LineOfCreditInterestFeeClient,
) : EndpointHandler<Api.Execute, Unit>(
  template = Api.Execute::class.template(),
) {
  private val logger = KotlinLogging.logger {}

  override suspend fun endpoint(call: ApplicationCall): Api.Execute = Api.Execute

  override suspend fun Handler.handle(endpoint: Api.Execute) {
    auth(authPlatformRole(PlatformRole.CLOUD_TASKS))
    logger.info("Running complete interest job.")
    lineOfCreditInterestFeeClient.request(
      LineOfCreditInterestFeeApi.CompleteInterestFees(
        CompleteInterestFeesRep.Creator(
          completedState = LineOfCreditInterestFeeTransferRep.State.Completed,
        ),
      ),
    )
  }
}
