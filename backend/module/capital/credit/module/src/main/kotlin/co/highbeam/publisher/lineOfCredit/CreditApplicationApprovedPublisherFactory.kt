package co.highbeam.publisher.lineOfCredit

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.listener.lineOfCredit.CREDIT_APPLICATION_APPROVED_TOPIC_NAME
import co.highbeam.rep.onboarding.CreditApplicationPubsubEventRep
import com.google.inject.Inject

internal class CreditApplicationApprovedPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<CreditApplicationPubsubEventRep>>() {
  override fun get(): EventPublisher<CreditApplicationPubsubEventRep> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<CreditApplicationPubsubEventRep>(
    topic = TopicConfig(CREDIT_APPLICATION_APPROVED_TOPIC_NAME),
    typeLiteral = typeLiteral(),
    provider = CreditApplicationApprovedPublisherFactory::class,
  )
}
