package co.highbeam.mapper.creditComparison

import co.highbeam.model.creditComparison.CreditComparisonSnapshotModel
import co.highbeam.rep.creditComparison.CreditComparisonDataRep
import co.highbeam.rep.creditComparison.CreditComparisonSnapshotRep
import co.highbeam.slug.Slug
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import java.util.UUID

internal class CreditComparisonSnapshotMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun toRep(model: CreditComparisonSnapshotModel): CreditComparisonSnapshotRep =
    CreditComparisonSnapshotRep(
      guid = model.guid,
      slug = model.slug,
      creditComparisonSessionGuid = model.creditComparisonSessionGuid,
      revenueProjections = model.data.revenueProjections,
      offers = model.data.offers,
    )

  fun toModel(
    creditComparisonSessionGuid: UUID,
    rep: CreditComparisonSnapshotRep.Creator,
  ): CreditComparisonSnapshotModel.Creator {
    val guid = uuidGenerator.generate()
    return CreditComparisonSnapshotModel.Creator(
      guid = guid,
      slug = Slug(guid),
      creditComparisonSessionGuid = creditComparisonSessionGuid,
      data = toDataModel(rep),
    )
  }

  private fun toDataModel(rep: CreditComparisonSnapshotRep.Creator) = CreditComparisonDataRep(
    revenueProjections = rep.revenueProjections,
    offers = rep.offers,
  )
}
