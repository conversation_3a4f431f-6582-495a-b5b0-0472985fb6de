package co.highbeam.publisher.lineOfCredit

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeeRep
import com.google.inject.Inject

internal class LineOfCreditInterestTransferPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<CompleteInterestFeeRep>>() {
  override fun get(): EventPublisher<CompleteInterestFeeRep> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<CompleteInterestFeeRep>(
    topic = TopicConfig("line-of-credit-interest-transfer"),
    typeLiteral = typeLiteral(),
    provider = LineOfCreditInterestTransferPublisherFactory::class,
  )
}
