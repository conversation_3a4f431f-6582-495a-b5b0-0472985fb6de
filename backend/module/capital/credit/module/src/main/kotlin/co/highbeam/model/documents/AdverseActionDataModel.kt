package co.highbeam.model.documents

import co.highbeam.rep.DocumentRep
import co.highbeam.rep.DocumentRep.DocumentType.AdverseActionNotice
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep

data class AdverseActionDataModel(
  val adverseActionType: AdverseActionRep.AdverseActionType,
  val reasons: List<AdverseActionRep.Reason>,
) : DocumentDataModel() {
  override val name: String = adverseActionType.subject
  override val documentType: DocumentRep.DocumentType = AdverseActionNotice
}
