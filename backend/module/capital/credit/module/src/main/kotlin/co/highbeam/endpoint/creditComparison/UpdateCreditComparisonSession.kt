package co.highbeam.endpoint.creditComparison

import co.highbeam.auth.auth.AuthCreditComparisonSession
import co.highbeam.rep.creditComparison.CreditComparisonSessionRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.creditComparison.CreditComparisonService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.creditComparison.CreditComparisonApi as Api

internal class UpdateCreditComparisonSession @Inject constructor(
  private val authCreditComparisonSession: AuthCreditComparisonSession.Provider,
  private val creditComparisonService: CreditComparisonService,
) : EndpointHandler<Api.UpdateCreditComparisonSession, CreditComparisonSessionRep>(
  template = Api.UpdateCreditComparisonSession::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall,
  ) = Api.UpdateCreditComparisonSession(
    creditComparisonSessionGuid = call.getParam("creditComparisonSessionGuid"),
    rep = call.body(),
  )

  override fun loggingContext(endpoint: Api.UpdateCreditComparisonSession) =
    super.loggingContext(endpoint) +
      mapOf("creditComparisonSessionGuid" to endpoint.creditComparisonSessionGuid.toString())

  override suspend fun Handler.handle(
    endpoint: Api.UpdateCreditComparisonSession
  ): CreditComparisonSessionRep {
    auth(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = endpoint.creditComparisonSessionGuid,
      )
    )

    return creditComparisonService.updateSession(
      guid = endpoint.creditComparisonSessionGuid,
      rep = endpoint.rep,
    )
  }
}
