package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import highbeam.task.TaskCreator
import highbeam.task.auth.AuthCloudScheduler
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeCalculationTaskApi as Api

internal class CreateLineOfCreditInterestFeeCalculationTask @Inject constructor(
  private val authCloudScheduler: AuthCloudScheduler.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val taskCreator: TaskCreator,
) : EndpointHandler<Api.Create, Unit>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create

  override suspend fun Handler.handle(endpoint: Api.Create) {
    authSome(
      authCloudScheduler(),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )
    taskCreator.create(Api.Execute, "line-of-credit-interest-fee-calculation")
  }
}
