package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi
import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthCapitalAccount
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.interestFee.DailyInterestFeeRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.interestFee.InterestFeeService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi as Api

internal class GetAccruedInterestFees @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val interestFeeService: InterestFeeService,
) : EndpointHandler<LineOfCreditInterestFeeApi.GetAccruedInterestFees, List<DailyInterestFeeRep>>(
  template = Api.GetAccruedInterestFees::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall):
    LineOfCreditInterestFeeApi.GetAccruedInterestFees = Api.GetAccruedInterestFees(
    businessGuid = call.getParam("businessGuid"),
    lineOfCreditGuid = call.getParam("lineOfCreditGuid"),
    since = call.getParam("since"),
    untilInclusive = call.getParam("untilInclusive"),
  )

  override suspend fun Handler.handle(
    endpoint: LineOfCreditInterestFeeApi.GetAccruedInterestFees,
  ): List<DailyInterestFeeRep> {
    authSome(
      Auth.All(
        listOf(
          authPermission(Permission.Transaction_Read) { endpoint.businessGuid },
          authCapitalAccount(
            Permission.Transaction_Read,
            capitalAccountGuid = endpoint.lineOfCreditGuid,
            businessGuid = endpoint.businessGuid,
          ),
        )
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return interestFeeService.getAccruedInterest(
      lineOfCreditGuid = endpoint.lineOfCreditGuid,
      businessGuid = endpoint.businessGuid,
      since = endpoint.since,
      untilInclusive = endpoint.untilInclusive,
    )
  }
}
