package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.capital.transaction.service.CapitalTransactionService
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi as Api

internal class LineOfCreditDetailedTransactions @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val capitalTransactionService: CapitalTransactionService,
) : EndpointHandler<Api.GetDetailedTransactions, List<CapitalTransactionRep.Summary>>(
  template = Api.GetDetailedTransactions::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall) =
    Api.GetDetailedTransactions(
      businessGuid = call.getParam("businessGuid"),
      since = call.getParam("since"),
      until = call.getParam("until"),
    )

  override suspend fun Handler.handle(
    endpoint: Api.GetDetailedTransactions,
  ): List<CapitalTransactionRep.Summary> {
    auth(authPermission(Permission.Transaction_Read) { endpoint.businessGuid })

    return capitalTransactionService.transactionsWithRunningBalance(
      businessGuid = endpoint.businessGuid,
      sinceInclusive = endpoint.since,
      untilInclusive = endpoint.until,
    )
  }
}
