package co.highbeam.endpoint.lineOfCredit.reporting

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.lineOfCredit.reporting.LineOfCreditLoanBookSummaryRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lineOfCredit.reporting.LineOfCreditReportingService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lineOfCredit.reporting.LineOfCreditReportingApi as Api

internal class GetLoanBookSummary @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val lineOfCreditReportingService: LineOfCreditReportingService,
) : EndpointHandler<Api.GetLoanBookSummary, List<LineOfCreditLoanBookSummaryRep>>(
  template = Api.GetLoanBookSummary::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetLoanBookSummary =
    Api.GetLoanBookSummary

  override suspend fun Handler.handle(
    endpoint: Api.GetLoanBookSummary,
  ): List<LineOfCreditLoanBookSummaryRep> {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    return lineOfCreditReportingService.loanBookSummary()
  }
}
