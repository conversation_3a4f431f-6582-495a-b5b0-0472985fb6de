package co.highbeam.listener.lineOfCredit

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeeRep
import co.highbeam.service.lineOfCredit.interestFee.InterestFeeService
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging

@Singleton
internal class LineOfCreditInterestTransferListener @Inject constructor(
  factory: EventListenerFactory,
  private val interestFeeService: InterestFeeService,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig("line-of-credit-interest-transfer"),
      subscriptionConfig = SubscriptionConfig("default"),
      clazz = CompleteInterestFeeRep::class.java,
      listener = ::onReceive,
    )
  }

  private fun onReceive(rep: CompleteInterestFeeRep) {
    logger.info { "Received event: $rep." }
    interestFeeService.completeInterestFee(rep)
  }
}
