package co.highbeam.service.creditComparison

import co.highbeam.api.rutter.RutterPayoutApi
import co.highbeam.api.shopify.ShopifyPayoutApi
import co.highbeam.client.rutter.InternalRutterPayoutClient
import co.highbeam.client.shopify.ShopifyPayoutClient
import co.highbeam.exception.creditComparison.CreditComparisonSnapshotNotFound
import co.highbeam.mapper.creditComparison.CreditComparisonSessionMapper
import co.highbeam.mapper.creditComparison.CreditComparisonSnapshotMapper
import co.highbeam.money.Money
import co.highbeam.rep.creditComparison.CreditComparisonCalculationRep
import co.highbeam.rep.creditComparison.CreditComparisonDataRep
import co.highbeam.rep.creditComparison.CreditComparisonDataRep.RepaymentOption.CollectionPercentage
import co.highbeam.rep.creditComparison.CreditComparisonSessionRep
import co.highbeam.rep.creditComparison.CreditComparisonSnapshotRep
import co.highbeam.rep.creditComparison.IntervalType
import co.highbeam.rep.creditComparison.RevenueForecastRep
import co.highbeam.rep.shopify.ShopifyPayoutRep
import co.highbeam.slug.Slug
import co.highbeam.store.creditComparison.CreditComparisonSessionStore
import co.highbeam.store.creditComparison.CreditComparisonSnapshotStore
import com.google.inject.Inject
import mu.KotlinLogging
import org.decampo.xirr.NonconvergenceException
import org.decampo.xirr.OverflowException
import org.decampo.xirr.Transaction
import org.decampo.xirr.Xirr
import org.decampo.xirr.ZeroValuedDerivativeException
import java.lang.Long.max
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Clock
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.Period
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.math.min
import kotlin.math.pow

@Suppress("TooManyFunctions")
internal class CreditComparisonServiceImpl @Inject constructor(
  private val creditComparisonSessionStore: CreditComparisonSessionStore,
  private val creditComparisonSnapshotStore: CreditComparisonSnapshotStore,
  private val creditComparisonSessionMapper: CreditComparisonSessionMapper,
  private val creditComparisonSnapshotMapper: CreditComparisonSnapshotMapper,
  private val internalRutterPayoutClient: InternalRutterPayoutClient,
  private val shopifyPayoutClient: ShopifyPayoutClient,
  private val clock: Clock,
) : CreditComparisonService {
  private val logger = KotlinLogging.logger {}

  override suspend fun revenueForecast(
    businessGuid: UUID,
    fromDate: LocalDate,
    intervalType: IntervalType,
    numberOfIntervals: Long,
  ): RevenueForecastRep {
    // Get the average payout of the past year
    val rutterPayouts = internalRutterPayoutClient.request(
      RutterPayoutApi.GetByBusinessInDateRange(
        businessGuid = businessGuid,
        fromDate = LocalDate.now(clock).minusYears(1),
      ),
    )

    val shopifyPayouts = shopifyPayoutClient.request(
      ShopifyPayoutApi.GetByBusiness(
        businessGuid = businessGuid,
        pending = false,
        fromDate = LocalDate.now(clock).minusYears(1),
      ),
    ).filter { it.status == ShopifyPayoutRep.Status.Paid }

    val averageDailyRutterPayout = averagePayouts(
      rutterPayouts.map { Payout(rawCents = it.depositedAmount.rawCents, fromDate = it.fromDate) }
    )
    val averageDailyShopifyPayout = averagePayouts(
      shopifyPayouts.map { Payout(rawCents = it.amount.rawCents, fromDate = it.date) }
    )

    val averageDailyPayout = averageDailyRutterPayout + averageDailyShopifyPayout
    val growthPercentage = 0.016 // Assuming ~20% growth year over year
    return when (intervalType) {
      IntervalType.Monthly ->
        // Return a list of numberOfIntervals with the same number
        RevenueForecastRep(
          revenueProjections = List(numberOfIntervals.toInt()) { i ->
            val totalPayoutInThisInterval = averageDailyPayout *
              30L * (1 + growthPercentage).pow(i)

            return@List CreditComparisonDataRep.RevenueProjection(
              start = fromDate
                .plusMonths(i.toLong())
                .with(TemporalAdjusters.firstDayOfMonth()),
              endInclusive = fromDate
                .plusMonths(i.toLong())
                .with(TemporalAdjusters.lastDayOfMonth()),
              amount = Money.fromCents(
                totalPayoutInThisInterval.toLong().coerceAtLeast(0)
              ),
            )
          }
        )
    }
  }

  override fun createSession(businessGuid: UUID?): CreditComparisonSessionRep =
    creditComparisonSessionMapper.toRep(
      creditComparisonSessionStore.create(
        creditComparisonSessionMapper.toCreationModel(
          businessGuid = businessGuid,
        ),
      ),
    )

  override fun getSession(guid: UUID): CreditComparisonSessionRep =
    creditComparisonSessionStore.get(guid).let(creditComparisonSessionMapper::toRep)

  override fun updateSession(
    guid: UUID,
    rep: CreditComparisonSessionRep.Updater,
  ): CreditComparisonSessionRep = creditComparisonSessionMapper.toRep(
    creditComparisonSessionStore.update(
      guid = guid,
      updater = creditComparisonSessionMapper.toUpdateModel(
        data = creditComparisonSessionMapper.toDataModel(rep),
      ),
    ),
  )

  override fun createSnapshot(
    creditComparisonSessionGuid: UUID,
    rep: CreditComparisonSnapshotRep.Creator,
  ): CreditComparisonSnapshotRep = creditComparisonSnapshotMapper.toRep(
    creditComparisonSnapshotStore.create(
      creditComparisonSnapshotMapper.toModel(
        creditComparisonSessionGuid = creditComparisonSessionGuid,
        rep = rep,
      ),
    ),
  )

  override fun getLatestSnapshot(
    creditComparisonSnapshotGuid: UUID,
  ): CreditComparisonSnapshotRep =
    creditComparisonSnapshotStore.getLatest(creditComparisonSnapshotGuid)
      .let(creditComparisonSnapshotMapper::toRep)

  override fun calculateSnapshot(
    creditComparisonSnapshot: Slug,
  ): List<CreditComparisonCalculationRep> {
    val creditComparisonSnapshotModel =
      creditComparisonSnapshotStore.get(creditComparisonSnapshot)
        ?: throw CreditComparisonSnapshotNotFound()
    return calculate(creditComparisonSnapshotModel.data)
  }

  private data class Payout(val rawCents: Long, val fromDate: LocalDate)

  private fun averagePayouts(payouts: List<Payout>): Double {
    if (payouts.isEmpty()) return 0.0
    return payouts.sumOf { it.rawCents }.div(
      (ChronoUnit.DAYS.between(
        payouts.minBy { it.fromDate }.fromDate, LocalDate.now(clock)
      ) + 1).toDouble(),
    )
  }

  private fun calculate(
    creditComparisonData: CreditComparisonDataRep,
  ): List<CreditComparisonCalculationRep> {
    return creditComparisonData.offers.map { offer ->
      val (transactions, remittanceRate) = when (val repaymentOption = offer.repaymentOption) {
        is CollectionPercentage -> {
          val transactions = transactions(
            offer = offer,
            revenueProjections = creditComparisonData.revenueProjections,
            repaymentOption = repaymentOption,
          )
          Pair(transactions, repaymentOption.remittanceRate)
        }
      }

      val fee = when (val fee = offer.fee) {
        is CreditComparisonDataRep.FeeType.FlatFee -> fee.amount
      }
      val startOfLoan = transactions.first().getWhen()
      val endOfLoan = transactions.last().getWhen()
      CreditComparisonCalculationRep(
        providerName = offer.providerName,
        fundingAmount = offer.loanAmount,
        feeAmount = fee,
        apr = calculateApr(transactions)?.max(BigDecimal.ZERO),
        repaymentPeriod = Period.between(startOfLoan, endOfLoan),
        averageWeeklyRepayment = Money(
          transactions.drop(1).map { it.amount }.average().toLong() *
            min(7L, ChronoUnit.DAYS.between(startOfLoan, endOfLoan))
        ),
        remittanceRate = remittanceRate,
      )
    }

  }

  /**
   * This operation uses Newton-Raphson method to calculate APR via XIRR. Depending on the number
   * of transactions and convergence, it can be an expensive operation.
   * More information here: https://github.com/RayDeCampo/java-xirr
   */
  private fun calculateApr(transactions: List<Transaction>): BigDecimal? {
    @Suppress("TooGenericExceptionCaught")
    return try {
      BigDecimal(Xirr(transactions).xirr()).setScale(4, RoundingMode.HALF_UP)
    } catch (e: Exception) {
      when (e) {
        // NB(justin): We decided to catch this third-party exception to be able to elegantly
        //  recover from a possible non-convergence. This way offers that don't converge still
        //  return, and we can page and investigate the cause of the non-convergence.
        is ZeroValuedDerivativeException, is NonconvergenceException, is OverflowException -> {
          logger.error {
            "Could not calculate APR credit comparison: " +
              "exception=$e, " +
              "transactions=$transactions"
          }
          null
        }

        else -> throw e
      }
    }
  }

  private data class LoanSnapshot(
    val date: LocalDate,
    val repaymentAmount: Money,
    val paidSoFarThisWeek: Money,
    val remainingLoanBalance: Money,
  )

  private fun transactions(
    offer: CreditComparisonDataRep.OfferDetail,
    revenueProjections: List<CreditComparisonDataRep.RevenueProjection>,
    repaymentOption: CollectionPercentage,
  ): List<Transaction> {
    val fee = when (val fee = offer.fee) {
      is CreditComparisonDataRep.FeeType.FlatFee -> fee.amount
    }
    val firstDate = revenueProjections.first().start
    val loanSnapshots = generateSequence(
      nextDailyLoan(
        date = firstDate,
        paymentAmount = paymentAmount(
          revenueForDay = getRevenueForDay(firstDate, revenueProjections),
          collectionPercentage = repaymentOption,
          loanAmountBeforePayment = offer.loanAmount + fee,
        ),
        loanAmountBeforePayment = offer.loanAmount + fee,
      )
    ) {
      val nextDay = it.date.plusDays(1)
      nextDailyLoan(
        date = nextDay,
        paymentAmount = paymentAmount(
          revenueForDay = getRevenueForDay(nextDay, revenueProjections),
          collectionPercentage = repaymentOption,
          loanAmountBeforePayment = it.remainingLoanBalance,
          paidSoFarThisWeek = it.paidSoFarThisWeek,
        ),
        loanAmountBeforePayment = it.remainingLoanBalance,
        weeklyPaymentBeforePayment = it.paidSoFarThisWeek,
      )
    }.takeWhile {
      // We will generate transactions until the loan is paid off AND the repayment amount is 0
      // *repayment amount can be zero if it reaches a weekly limit
      // *loan can be zero but repayment non-zero if it is the last payment
      it.repaymentAmount > Money.ZERO || it.remainingLoanBalance > Money.ZERO
    }.take(firstDate.lengthOfYear() * 2 + 1) // Only allow maximum repayment period of 2 years

    return listOf(
      Transaction(-offer.loanAmount.rawCents.toDouble(), firstDate.minusDays(1))
    ) +
      loanSnapshots.map {
        Transaction(it.repaymentAmount.rawCents.toDouble(), it.date)
      }.toList()
  }

  private fun nextDailyLoan(
    date: LocalDate,
    paymentAmount: Money,
    loanAmountBeforePayment: Money,
    weeklyPaymentBeforePayment: Money = Money.ZERO,
  ): LoanSnapshot {
    return LoanSnapshot(
      date = date,
      repaymentAmount = paymentAmount,
      // Reset the amount paid this week at the start of every week
      paidSoFarThisWeek = if (date.dayOfWeek == DayOfWeek.SUNDAY) {
        Money.ZERO
      } else {
        weeklyPaymentBeforePayment + paymentAmount
      },
      remainingLoanBalance = Money(
        max(loanAmountBeforePayment.rawCents - paymentAmount.rawCents, 0L)
      ),
    )
  }

  private fun paymentAmount(
    revenueForDay: Money,
    collectionPercentage: CollectionPercentage,
    loanAmountBeforePayment: Money,
    paidSoFarThisWeek: Money = Money.ZERO,
  ): Money {
    val paymentAmount = min(
      BigDecimal(revenueForDay.rawCents).multiply(
        collectionPercentage.remittanceRate.setScale(
          4, RoundingMode.HALF_UP
        ),
      ).toLong(), loanAmountBeforePayment.rawCents
    )

    collectionPercentage.weeklyCap?.let { weeklyCap ->
      if (paidSoFarThisWeek + Money(paymentAmount) > weeklyCap) {
        return weeklyCap - paidSoFarThisWeek
      }
    }

    return Money(paymentAmount)
  }

  private fun getRevenueForDay(
    date: LocalDate,
    projections: List<CreditComparisonDataRep.RevenueProjection>,
  ): Money {
    val projection = projections.find { date in it }
      ?: getLastProjection(projections) // Defaults to last projection if out of range
    val days = Period.between(projection.start, projection.endInclusive).days
    // NB(justin): We don't need to be too careful with our rounding since this is just a
    //  projection calculator.
    return Money(projection.amount.rawCents / days)
  }

  // TODO(justin): Replace this with a more accurate projection such as linear regression.
  private fun getLastProjection(
    projections: List<CreditComparisonDataRep.RevenueProjection>,
  ) =
    projections.maxByOrNull { it.start } ?: error("No projections")
}
