package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.capital.account.model.CapitalAccountModel
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.store.CapitalAccountStore
import co.highbeam.client.treasury.TreasuryRuleClient
import co.highbeam.model.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeMetadataRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep.State
import co.highbeam.server.Server
import co.highbeam.store.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferStore
import co.highbeam.testing.CreditFeatureIntegrationTest
import com.fasterxml.jackson.databind.node.NullNode
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.concurrent.TimeUnit
import java.util.stream.Collectors

@Suppress("LargeClass")
internal class CalculateCapitalAccountDetailsTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuids = List(7) { UUID.randomUUID() }
  private val businessGuid = businessGuids.first()
  private val lineOfCreditInterestFeeTransferStore: LineOfCreditInterestFeeTransferStore = get()
  private val capitalAccountStore: CapitalAccountStore = get()
  private val primaryBankAccount = bankAccountRep(
    businessGuid = businessGuid,
    unitCoDepositAccountId = "1111",
    isPrimary = true,
    name = "Primary",
    highbeamType = BankAccountRep.Type.DepositAccount,
    availableBalance = Balance.fromCents(10_000),
  )

  private data class DateRange(
    override val start: LocalDate,
    override val endInclusive: LocalDate
  ) : ClosedRange<LocalDate> {
    operator fun iterator(): Iterator<LocalDate> {
      val dateIterator = start.datesUntil(endInclusive)
        .collect(Collectors.toList()).iterator()

      return object : Iterator<LocalDate> {
        override fun hasNext() = dateIterator.hasNext()
        override fun next(): LocalDate {
          if (!hasNext()) {
            throw NoSuchElementException()
          }
          return dateIterator.next()
        }
      }
    }
  }

  @BeforeEach
  fun beforeEach() {
    mockGetPrimaryBankAccountByBusinessGuid(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
    businessGuids.forEach {
      mockBusinessClient(it)
      coEvery {
        get<TreasuryRuleClient>()(TreasuryRuleApi.GetForBusiness(it))
      } returns emptyList()
    }
    mockUnitCoTransactionClient("transaction/interest_fee.json")
  }

  @Test
  fun `LOC starts on Tuesday, interest job runs every day of the week`() = integrationTest {
    clock.next(DayOfWeek.TUESDAY)
    val activatedAt = clock.date()
    val lineOfCredit = activateLineOfCredit(businessGuid = businessGuid, activatedAt = activatedAt)
    repeat(7) {
      lineOfCreditInterestFeeClient.request(
        LineOfCreditInterestFeeApi.CalculateInterestFees
      )
      // No fees are created until Tuesday
      assertNoTransfers(businessGuid)
      clock.add(1, TimeUnit.DAYS)
    }
    lineOfCreditInterestFeeClient.request(
      LineOfCreditInterestFeeApi.CalculateInterestFees
    )
    // Fees created since it's Tuesday
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(
        activatedAt.toLocalDate(),
        activatedAt.plusWeeks(1).minusDays(1).toLocalDate()
      ),
      totalNumberOfTransfers = 1
    )
  }

  @Test
  fun `LOC starts on Monday, job doesn't run until 2 weeks later on Tuesday`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    val lineOfCredit = activateLineOfCredit(businessGuid = businessGuid, activatedAt = initialDate)
    clock.next(DayOfWeek.TUESDAY) // Advance to tomorrow
    clock.next(DayOfWeek.TUESDAY) // Advance one week to Tuesday
    clock.next(DayOfWeek.TUESDAY) // Advance to next Tuesday
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 3
    )
  }

  @Test
  fun `LOC starting every day of the week, job runs Tuesday`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    // Create 7 lines of credit every day of the week
    val businessToLineOfCreditGuid = mutableMapOf<UUID, UUID>()
    repeat(7) { i ->
      val lineOfCredit = activateLineOfCredit(
        businessGuid = businessGuids[i],
        activatedAt = initialDate.plusDays(i.toLong())
      )
      mockGetPrimaryBankAccountByBusinessGuid(
        businessGuid = businessGuids[i],
        bankAccount = bankAccountRep(
          businessGuid = businessGuids[i],
          unitCoDepositAccountId = "1111",
          isPrimary = true,
          name = "Primary",
          highbeamType = BankAccountRep.Type.DepositAccount,
          availableBalance = Balance.fromCents(10_000),
        ),
      )
      businessToLineOfCreditGuid[businessGuids[i]] = lineOfCredit
    }
    clock.add(8, TimeUnit.DAYS) // Advance a week to the next Tuesday
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuids.first(),
      lineOfCreditGuid = businessToLineOfCreditGuid.getValue(businessGuids.first()),
      // NB(justin): If LOC was started on a Monday and today is Tuesday, we want to make the
      //  single day transfer instead of waiting until next Monday.
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 2,
    )
    businessGuids.drop(1).mapIndexed { i, businessGuid ->
      assertTransfers(
        businessGuid,
        lineOfCreditGuid = businessToLineOfCreditGuid.getValue(businessGuid),
        firstTransferDateRange = DateRange(
          initialDate.plusDays(i + 1L).toLocalDate(),
          clock.date().minusDays(1).toLocalDate()
        ),
        totalNumberOfTransfers = 1,
      )
    }
  }

  @Test
  fun `LOC starts on Monday, run weekly on Tuesday for 3 weeks`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    val lineOfCredit = activateLineOfCredit(businessGuid = businessGuid, activatedAt = initialDate)

    clock.add(1, TimeUnit.DAYS) // Today is now Tuesday
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 1,
    )

    clock.next(DayOfWeek.TUESDAY)
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 2,
    )

    clock.next(DayOfWeek.TUESDAY)
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 3,
    )

    // Check idempotence
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 3,
    )
  }

  @Test
  fun `end of day balances greater than limit (negative interest calculation)`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    val lineOfCredit = activateLineOfCredit(businessGuid, activatedAt = initialDate)
    mockUnitCoTransactionClient("transaction/interest_fee_overflow.json")

    clock.next(DayOfWeek.TUESDAY) // Advance to tomorrow
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid = businessGuid,
      lineOfCreditGuid = lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 1,
      expectedAmountOverride = Money.ZERO,
    )
  }

  @Test
  fun `no drawdowns`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    val lineOfCredit = activateLineOfCredit(businessGuid, activatedAt = initialDate)
    mockUnitCoTransactionClient("transaction/interest_fee_no_drawdown.json")

    clock.next(DayOfWeek.TUESDAY) // Advance to tomorrow
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid = businessGuid,
      lineOfCreditGuid = lineOfCredit,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 1,
      expectedAmountOverride = Money(0),
    )
  }

  @Test
  fun `multiple varying drawdowns`() = integrationTest {
    clock.next(DayOfWeek.TUESDAY) // Start on Monday
    val initialDate = clock.date()
    val expectedTransferRange = DateRange(
      initialDate.toLocalDate(),
      initialDate.plusWeeks(1).minusDays(1).toLocalDate()
    )
    val lineOfCredit = activateLineOfCredit(businessGuid, activatedAt = initialDate)
    mockUnitCoTransactionClient("transaction/interest_fee_drawdown.json")

    clock.next(DayOfWeek.TUESDAY) // Advance to tomorrow
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid = businessGuid,
      lineOfCreditGuid = lineOfCredit,
      firstTransferDateRange = expectedTransferRange,
      totalNumberOfTransfers = 1,
      expectedAmountOverride = Money(17_185),
    )
  }

  @Test
  fun `LOC with changing limits`() = integrationTest {
    clock.next(DayOfWeek.MONDAY) // Start on Monday
    val initialDate = clock.date()
    val lineOfCreditGuid = setUpLineOfCredit(businessGuid = businessGuid)

    lineOfCreditClient.request(
      LineOfCreditApi.Update(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        rep = LineOfCreditRep.Updater(
          effectiveAt = clock.date().plusDays(2),
          limit = Money.fromDollarsAndCents(1_000_000, 0),
        )
      )
    )

    clock.add(1, TimeUnit.DAYS) // Today is now Tuesday
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    assertTransfers(
      businessGuid,
      lineOfCreditGuid,
      firstTransferDateRange = DateRange(initialDate.toLocalDate(), initialDate.toLocalDate()),
      totalNumberOfTransfers = 1,
    )

    clock.next(DayOfWeek.TUESDAY)
    lineOfCreditInterestFeeClient.request(LineOfCreditInterestFeeApi.CalculateInterestFees)

    // Cannot use the assertTransfers because of the stable limit assumption
    assertThat(
      lineOfCreditInterestFeeTransferStore.getByLineOfCreditGuid(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
      )
    ).usingRecursiveComparison()
      .ignoringFields("guid")
      .isEqualTo(
        listOf(
          expectedLineOfCreditInterestFeeModel(
            businessGuid = businessGuid,
            lineOfCreditGuid = lineOfCreditGuid,
            start = initialDate.toLocalDate(),
            endInclusive = initialDate.toLocalDate(),
            expectedAmount = Money.fromDollarsAndCents(4, 11),
          ),
          expectedLineOfCreditInterestFeeModel(
            businessGuid = businessGuid,
            lineOfCreditGuid = lineOfCreditGuid,
            start = initialDate.plusDays(1).toLocalDate(),
            endInclusive = initialDate.plusDays(7).toLocalDate(),
            expectedAmount = Money.fromDollarsAndCents(28, 77),
            limit = Money.fromDollarsAndCents(1_000_000, 0)
          ),
        )
      )
  }

  private fun assertTransfers(
    businessGuid: UUID,
    lineOfCreditGuid: UUID,
    firstTransferDateRange: DateRange,
    totalNumberOfTransfers: Int = 1,
    expectedAmountOverride: Money? = null,
  ) {
    assertThat(
      lineOfCreditInterestFeeTransferStore.getByLineOfCreditGuid(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
      )
    ).usingRecursiveComparison()
      .ignoringFields("guid")
      .isEqualTo(
        listOf(
          expectedLineOfCreditInterestFeeModel(
            businessGuid = businessGuid,
            lineOfCreditGuid = lineOfCreditGuid,
            start = firstTransferDateRange.start,
            endInclusive = firstTransferDateRange.endInclusive,
            expectedAmount = expectedAmountOverride,
          )
        ).run {
          val nextStartingDate = firstTransferDateRange.endInclusive.plusDays(1)
          this + List(totalNumberOfTransfers - 1) { i ->
            expectedLineOfCreditInterestFeeModel(
              businessGuid = businessGuid,
              lineOfCreditGuid = lineOfCreditGuid,
              start = nextStartingDate.plusWeeks(i.toLong()),
              endInclusive = nextStartingDate
                .plusWeeks(1L + i)
                .minusDays(1),
              expectedAmount = expectedAmountOverride,
            )
          }
        }
      )
  }

  private fun expectedLineOfCreditInterestFeeModel(
    businessGuid: UUID,
    lineOfCreditGuid: UUID,
    start: LocalDate,
    endInclusive: LocalDate,
    expectedAmount: Money?,
    limit: Money? = null,
  ) = LineOfCreditInterestFeeTransferModel(
    // 2 non-interest fee transfers were created in other parts of the code
    guid = UUID.randomUUID(),
    businessGuid = businessGuid,
    lineOfCreditGuid = lineOfCreditGuid,
    state = State.New,
    amount = expectedAmount ?: expectedTransferAmount(
      start.until(endInclusive, ChronoUnit.DAYS) + 1L
    ),
    unitBookPaymentResponse = NullNode.getInstance(),
    since = start,
    until = endInclusive,
    unitAccountEndOfDayResponse = listOf(),
    metadata = LineOfCreditInterestFeeMetadataRep(
      limit = limit ?: Money(10_000_000), apr = BigDecimal("0.15"),
    )
  )

  private fun expectedTransferAmount(numberOfDays: Long) = Money(411L * numberOfDays)

  private suspend fun activateLineOfCredit(
    businessGuid: UUID = this.businessGuid,
    activatedAt: ZonedDateTime
  ): UUID {
    val lineOfCredit = lineOfCreditClient.request(
      LineOfCreditApi.Create(
        businessGuid = businessGuid,
        LineOfCreditRep.Creator(
          limit = Money.fromDollarsAndCents(100_000, 0),
          apr = BigDecimal("0.15"),
        )
      )
    )
    return capitalAccountStore.update(
      businessGuid = businessGuid,
      guid = lineOfCredit.guid,
      updater = CapitalAccountModel.Updater(
        activatedAt = activatedAt,
        state = CapitalAccountRep.State.Active,
      )
    ).guid
  }

  private fun assertNoTransfers(
    businessGuid: UUID
  ) {
    val transfers =
      lineOfCreditInterestFeeTransferStore.getByState(State.New).filter {
        it.businessGuid == businessGuid
      }
    assertThat(transfers).hasSize(0)
  }
}
