package co.highbeam.auth.auth

import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.jwt.JwtBusiness
import co.highbeam.auth.permissions.AclValue
import co.highbeam.auth.permissions.Permission
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.creditComparison.CreditComparisonSessionRep
import co.highbeam.service.creditComparison.CreditComparisonService
import io.ktor.http.Headers
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class AuthCreditComparisonSessionTest {
  private val businessGuid: UUID = UUID.randomUUID()
  private val sessionGuid: UUID = UUID.randomUUID()

  private val creditComparisonService: CreditComparisonService = mockk()

  private val authCreditComparisonSession: AuthCreditComparisonSession.Provider =
    AuthCreditComparisonSession.Provider(
      authPermission = AuthPermission.Provider(),
      creditComparisonService = creditComparisonService,
    )

  @Test
  fun `No JWT, null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = null)

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(null, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `No JWT, not null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = businessGuid)

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(null, Headers.Empty)
    )
      .isFalse
  }

  @Test
  fun `JWT has no businesses, null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = null)

    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `JWT has no businesses, not null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = businessGuid)

    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isFalse
  }

  @Test
  fun `Business GUID mismatch, null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = null)

    val businessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(businessGuid to AclValue.from(Permission.CreditComparison_Write)),
      bankAccounts = null,
      businesses = mapOf(
        businessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `Business GUID mismatch, not null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = businessGuid)

    val businessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(businessGuid to AclValue.from(Permission.CreditComparison_Write)),
      bankAccounts = null,
      businesses = mapOf(
        UUID.randomUUID() to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isFalse
  }

  @Test
  fun `Business GUID match, null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = null)

    val jwt = Jwt(
      acl = mapOf(businessGuid to AclValue.from(Permission.CreditComparison_Write)),
      bankAccounts = null,
      businesses = mapOf(
        businessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `Business GUID match, not null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = businessGuid)

    val jwt = Jwt(
      acl = mapOf(businessGuid to AclValue.from(Permission.CreditComparison_Write)),
      bankAccounts = null,
      businesses = mapOf(
        businessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `JWT superuser override, null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = null)

    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = setOf(PlatformRole.SUPERUSER),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  @Test
  fun `JWT superuser override, not null business`() = runBlocking<Unit> {
    mockCreditComparison(businessGuid = businessGuid)

    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = setOf(PlatformRole.SUPERUSER),
      user = null,
    )

    assertThat(
      authCreditComparisonSession(
        operation = AuthCreditComparisonSession.Operation.Write,
        sessionGuid = sessionGuid
      ).authorize(jwt, Headers.Empty)
    )
      .isTrue
  }

  private fun mockCreditComparison(businessGuid: UUID?) {
    every { creditComparisonService.getSession(sessionGuid) } returns CreditComparisonSessionRep(
      guid = sessionGuid,
      businessGuid = businessGuid,
      emailAddress = null,
    )
  }
}
