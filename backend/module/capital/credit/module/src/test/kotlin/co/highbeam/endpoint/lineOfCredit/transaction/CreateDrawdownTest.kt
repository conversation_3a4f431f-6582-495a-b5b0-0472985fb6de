package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.transaction.exception.CapitalDrawdownException
import co.highbeam.capital.transaction.rep.CapitalDrawdownApprovalRep
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.capital.transaction.service.CapitalDrawdownApprovalService
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCompleteRep
import com.fasterxml.jackson.databind.JsonNode
import com.google.cloud.storage.HttpMethod
import io.mockk.coEvery
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.net.URL
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID

internal class CreateDrawdownTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  private val primaryBankAccount = bankAccountRep(
    unitCoDepositAccountId = "1111",
    isPrimary = true,
    name = "Primary",
    highbeamType = BankAccountRep.Type.DepositAccount,
    balance = Balance.fromCents(10_000),
  )

  @BeforeEach
  fun beforeEach() {
    mockGetBankAccount(
      bankAccountGuid = primaryBankAccount.guid,
      bankAccount = primaryBankAccount,
    )
    mockGetPrimaryBankAccountByBusinessGuid(businessGuid, primaryBankAccount)

    mockUnitCoPaymentCreation()
    mockBusinessClient(businessGuid)
    mockUnitCoTransactionClient("transaction/happy-prejit.json")
  }

  @Test
  fun `happy path`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }
  }

  @Test
  fun `happy path - to non primary account`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())
    val bankAccountGuid = UUID.randomUUID()
    mockGetBankAccount(
      bankAccountGuid = bankAccountGuid,
      bankAccount = bankAccountRep(
        unitCoDepositAccountId = "2222",
        isPrimary = false,
        name = "Another one",
        highbeamType = BankAccountRep.Type.DepositAccount,
        balance = Balance.fromCents(10_000),
        depositProduct = DepositAccountRep.DepositProduct.CheckingThread,
      )
    )
    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = bankAccountGuid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "2222", // Unit id of another account
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }
  }

  @Test
  fun `drawdown disabled line of credit`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownEnabled = false,
        )
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(1_00),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }
  }

  @Test
  fun `Terminated line`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())

    get<CapitalAccountClient>()(
      CapitalAccountApi.Delete(
        businessGuid = businessGuid,
        guid = lineOfCredit,
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCredit,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(1_00),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }
  }

  @Test
  fun `Card only account`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())

    get<CapitalAccountClient>()(
      CapitalAccountApi.Update(
        businessGuid = businessGuid,
        guid = lineOfCredit,
        updater = CapitalAccountRep.Updater(type = CapitalAccountRep.Type.ChargeCardOnly),
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCredit,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(1_00),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }
  }

  @Test
  fun `total drawdown limit hit in line of credit`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          totalDrawdownLimit = Money.fromCents(100_00),
        )
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(100_00L),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())
  }

  @Test
  fun `drawdown rate limit not hit in line of credit`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRateLimit = Money.fromCents(120_000_00L),
        )
      )
    )

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111", // Unit id of another account
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCreditGuid,
          ),
        )
      )
    }
  }

  @Test
  fun `drawdown rate limit hit in line of credit`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRateLimit = Money.fromCents(99815_00L),
        )
      )
    )

    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = "CapitalDrawdownApproval/${uuidGenerator[11]}",
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = "",
        status = CapitalDrawdownTransactionRep.Status.Pending,
      )
    )

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }
  }

  @Test
  fun `drawdown rate limit hit and approved in line of credit`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRateLimit = Money.fromCents(99815_00L),
        )
      )
    )

    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = "CapitalDrawdownApproval/${uuidGenerator[11]}",
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = "",
        status = CapitalDrawdownTransactionRep.Status.Pending,
      )
    )

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }

    get<CapitalDrawdownApprovalService>().approve(uuidGenerator[11])

    val responsePostApproval = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(responsePostApproval).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[12].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111", // Unit id of another account
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCreditGuid,
          ),
        )
      )
    }
  }

  @Test
  fun `drawdown approval required - not created`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRequiresApproval = true,
        )
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(100_00L),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())
  }

  @Test
  fun `drawdown approval required - not approved`() = integrationTest {
    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = any(),
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")

    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRequiresApproval = true,
        )
      )
    )

    get<CapitalDrawdownApprovalService>().create(
      businessGuid = businessGuid,
      capitalAccountGuid = lineOfCreditGuid,
      rep = CapitalDrawdownApprovalRep.Creator(
        amount = Money.fromCents(1_000),
        idempotencyKey = UUID.randomUUID(),
        bankAccountGuid = primaryBankAccount.guid,
        note = "",
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(100_00L),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())
  }

  @Test
  fun `drawdown approval required - rejected`() = integrationTest {
    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = any(),
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")

    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRequiresApproval = true,
        )
      )
    )

    val approval = get<CapitalDrawdownApprovalService>().create(
      businessGuid = businessGuid,
      capitalAccountGuid = lineOfCreditGuid,
      rep = CapitalDrawdownApprovalRep.Creator(
        amount = Money.fromCents(1_000),
        idempotencyKey = UUID.randomUUID(),
        bankAccountGuid = primaryBankAccount.guid,
        note = "",
      )
    )

    get<CapitalDrawdownApprovalService>().reject(approval.guid)

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(100_00L),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())
  }

  @Test
  fun `drawdown approval required - approved`() = integrationTest {
    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = any(),
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")

    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownRequiresApproval = true,
        )
      )
    )

    val key = UUID.randomUUID()
    val approval = get<CapitalDrawdownApprovalService>().create(
      businessGuid = businessGuid,
      capitalAccountGuid = lineOfCreditGuid,
      rep = CapitalDrawdownApprovalRep.Creator(
        amount = Money.fromCents(1_000),
        idempotencyKey = key,
        bankAccountGuid = primaryBankAccount.guid,
        note = "",
      )
    )

    get<CapitalDrawdownApprovalService>().approve(approval.guid)

    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[12].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCreditGuid,
          ),
        )
      )
    }
  }

  @Test
  fun `drawdown period valid`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownPeriodEndsAt = LocalDate.now(clock).plusDays(1),
        )
      )
    )

    val key = UUID.randomUUID()

    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCreditGuid,
          ),
        )
      )
    }
  }

  @Test
  fun `drawdown period last day today`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownPeriodEndsAt = LocalDate.now(clock),
        )
      )
    )

    val key = UUID.randomUUID()

    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCreditGuid,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCreditGuid,
          ),
        )
      )
    }
  }

  @Test
  fun `draw period expired`() = integrationTest {
    val lineOfCreditGuid = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )
    get<CapitalAccountClient>()(
      CapitalAccountApi.UpdateControls(
        businessGuid = businessGuid,
        guid = lineOfCreditGuid,
        updater = CapitalAccountControlsRep.Updater(
          drawdownPeriodEndsAt = LocalDate.now(clock).minusWeeks(1),
        )
      )
    )

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid = businessGuid,
          creditAccountGuid = lineOfCreditGuid,
          rep = CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(100_00L),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())
  }

  @Test
  fun `insufficient line of credit`() = integrationTest {
    val lineOfCredit =
      setUpLineOfCredit(businessGuid = businessGuid, userGuid = UUID.randomUUID(), limit = ********)

    val key = UUID.randomUUID()
    assertHighbeamException {
      lineOfCreditTransactionClient.request(
        LineOfCreditTransactionsApi.CreateDrawdown(
          businessGuid,
          creditAccountGuid = lineOfCredit,
          CapitalDrawdownTransactionRep.Creator(
            amount = Money.fromCents(1_000_000_000),
            idempotencyKey = key,
            bankAccountGuid = primaryBankAccount.guid,
          )
        )
      )
    }.isHighbeamException(CapitalDrawdownException())

    coVerify(exactly = 0) {
      get<UnitCoClient>().payment.createBook(any())
    }
  }

  @Test
  fun `external lender`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())
    get<CapitalAccountClient>().invoke(
      CapitalAccountApi.Update(
        businessGuid = businessGuid,
        guid = lineOfCredit,
        updater = CapitalAccountRep.Updater(
          lender = CapitalLender.Sandbox,
        )
      )
    )

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.CreateDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        )
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "4133904",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }
  }

  private fun mockUnitCoPaymentCreation() {
    coEvery { get<UnitCoClient>().payment.createBook(any()) } answers {
      val creator = firstArg<BookPaymentRep.Creator>()
      val id = uuidGenerator.generate().toString()
      return@answers UnitCompleteRep(
        type = "book",
        id = id,
        attributes = BookPaymentRep(
          createdAt = ZonedDateTime.of(
            2007, 12, 3, 5, 15, 30, 789_000_000,
            ZoneOffset.UTC,
          ),
          amount = creator.amount,
          description = creator.description,
          status = UnitCoPayment.Status.Sent,
          reason = null
        ),
        relationships = mapOf(),
        json = objectMapper.readValue("{}", JsonNode::class.java),
      )
    }
  }

  private fun bankAccountRep(
    unitCoDepositAccountId: String,
    isPrimary: Boolean,
    name: String,
    highbeamType: BankAccountRep.Type,
    balance: Balance,
    depositProduct: DepositAccountRep.DepositProduct = DepositAccountRep.DepositProduct.Checking,
  ): BankAccountRep.Complete =
    BankAccountRep.Complete(
      guid = UUID.randomUUID(),
      unitCoDepositAccountId = unitCoDepositAccountId,
      businessGuid = businessGuid,
      name = name,
      status = BankAccountRep.Status.OPEN,
      isPrimary = isPrimary,
      availableBalance = balance,
      routingNumber = "123",
      accountNumber = "456",
      type = "depositAccount",
      highbeamType = highbeamType,
      depositProduct = depositProduct,
      minimumRequiredBalance = Balance.ZERO,
    )

}
