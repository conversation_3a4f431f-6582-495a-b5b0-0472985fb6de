package co.highbeam.job.underwriting

import co.highbeam.api.onboarding.CreditApplicationApi
import co.highbeam.listener.lineOfCredit.LineOfCreditActivationListener
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.rep.onboarding.CreditApplicationPubsubEventRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import java.util.UUID

internal class LineOfCreditActivationListenerTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @Test
  fun `happy path - credit application is updated to PayoutsActivated`() = integrationTest {
    val creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    creditApplicationClient.request(
      CreditApplicationApi.Update(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationRep.Updater(
          state = CapitalApplicationRep.State.OfferAccepted,
        )
      )
    )

    get<LineOfCreditActivationListener>().onReceive(
      CreditApplicationPubsubEventRep(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid
      )
    )

    Assertions.assertThat(
      creditApplicationClient.request(CreditApplicationApi.GetByBusiness(businessGuid))
    ).containsExactlyInAnyOrder(
      CapitalApplicationRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        state = CapitalApplicationRep.State.PayoutsConnected,
        submittedAt = null,
        offer = null,
        businessDetails = null,
        userProvidedDetails = null,
      )
    )
  }
}
