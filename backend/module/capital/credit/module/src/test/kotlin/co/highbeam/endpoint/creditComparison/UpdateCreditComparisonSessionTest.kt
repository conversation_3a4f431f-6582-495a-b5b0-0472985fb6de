package co.highbeam.endpoint.creditComparison

import co.highbeam.api.creditComparison.CreditComparisonApi
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.rep.creditComparison.CreditComparisonSessionRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import java.util.UUID
import java.util.stream.Stream

internal class UpdateCreditComparisonSessionTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {

  companion object {
    @JvmStatic
    fun businessGuids(): Stream<out Arguments> {
      return Stream.of(
        Arguments.of(null),
        Arguments.of(UUID.randomUUID()),
      )
    }
  }

  @ParameterizedTest
  @MethodSource("businessGuids")
  fun `happy path`(businessGuid: UUID?) = integrationTest {
    val creditComparisonSessionRep = creditComparisonClient.request(
      CreditComparisonApi.CreateCreditComparisonSession(businessGuid)
    )

    assertThat(
      creditComparisonClient.request(
        CreditComparisonApi.UpdateCreditComparisonSession(
          creditComparisonSessionGuid = creditComparisonSessionRep.guid,
          rep = CreditComparisonSessionRep.Updater(
            emailAddress = "<EMAIL>"
          )
        )
      )
    ).isEqualTo(
      CreditComparisonSessionRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        emailAddress = "<EMAIL>",
      )
    )

    assertThat(
      creditComparisonClient.request(
        CreditComparisonApi.UpdateCreditComparisonSession(
          creditComparisonSessionGuid = creditComparisonSessionRep.guid,
          rep = CreditComparisonSessionRep.Updater(
            emailAddress = null
          )
        )
      )
    ).isEqualTo(
      CreditComparisonSessionRep(
        guid = uuidGenerator[0],
        businessGuid = businessGuid,
        emailAddress = null,
      )
    )
  }

  @ParameterizedTest
  @MethodSource("businessGuids")
  fun `invalid email`(businessGuid: UUID?) = integrationTest {
    val creditComparisonSessionRep = creditComparisonClient.request(
      CreditComparisonApi.CreateCreditComparisonSession(businessGuid)
    )

    assertThrows<HighbeamHttpClientException> {
      creditComparisonClient.request(
        CreditComparisonApi.UpdateCreditComparisonSession(
          creditComparisonSessionGuid = creditComparisonSessionRep.guid,
          rep = CreditComparisonSessionRep.Updater(
            emailAddress = "justin"
          )
        )
      )
    }
  }
}
