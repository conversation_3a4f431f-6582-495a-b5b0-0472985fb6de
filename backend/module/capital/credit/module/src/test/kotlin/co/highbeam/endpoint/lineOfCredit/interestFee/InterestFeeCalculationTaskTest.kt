package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeCalculationTaskApi
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeClient
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import highbeam.task.FakeTaskCreator
import highbeam.task.TaskCreator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class InterestFeeCalculationTaskTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  @Test
  fun `Create submits task`() = integrationTest {
    lineOfCreditInterestFeeCalculationTaskClient.request(
      LineOfCreditInterestFeeCalculationTaskApi.Create
    )
    val taskCreator = get<TaskCreator>() as FakeTaskCreator
    assertThat(taskCreator.tasks["line-of-credit-interest-fee-calculation"])
      .containsExactly(LineOfCreditInterestFeeCalculationTaskApi.Execute)
  }

  @Test
  fun `Executes task by calling client`() = integrationTest {
    coEvery {
      get<LineOfCreditInterestFeeClient>().request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    } answers { return@answers mockk() }

    lineOfCreditInterestFeeCalculationTaskClient.request(
      LineOfCreditInterestFeeCalculationTaskApi.Execute
    )

    coVerify(exactly = 1) {
      get<LineOfCreditInterestFeeClient>().request(LineOfCreditInterestFeeApi.CalculateInterestFees)
    }
  }
}
