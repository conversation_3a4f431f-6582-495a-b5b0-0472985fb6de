package co.highbeam.endpoint.creditComparison

import co.highbeam.api.creditComparison.CreditComparisonApi
import co.highbeam.api.rutter.RutterPayoutApi
import co.highbeam.api.shopify.ShopifyPayoutApi
import co.highbeam.client.rutter.InternalRutterPayoutClient
import co.highbeam.client.shopify.ShopifyPayoutClient
import co.highbeam.money.Balance
import co.highbeam.rep.creditComparison.IntervalType
import co.highbeam.rep.rutter.RutterPayoutRep
import co.highbeam.rep.shopify.ShopifyPayoutRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import com.rutter.rep.RutterPlatform
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.math.pow

internal class GetRevenueForecastTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private val growthPercentage = 0.016 // Assuming ~20% growth year over year

  @Test
  fun `happy path - one payout`() = integrationTest {
    // Just 1 payout the past year from each platform
    val currentTime = LocalDate.now(clock)

    val firstDayOfCurrentMonth = currentTime.with(
      TemporalAdjusters.firstDayOfMonth()
    )

    coEvery {
      get<InternalRutterPayoutClient>().request(
        RutterPayoutApi.GetByBusinessInDateRange(
          businessGuid = businessGuid,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      RutterPayoutRep(
        businessGuid = businessGuid,
        connectionGuid = UUID.randomUUID(),
        platform = RutterPlatform.AMAZON,
        fromDate = currentTime,
        depositedAmount = Balance.fromCents(30L),
        pendingAmount = Balance.ZERO,
      )
    )

    coEvery {
      get<ShopifyPayoutClient>().request(
        ShopifyPayoutApi.GetByBusiness(
          businessGuid = businessGuid,
          pending = false,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      ShopifyPayoutRep.Complete(
        guid = UUID.randomUUID(),
        connectionGuid = UUID.randomUUID(),
        status = ShopifyPayoutRep.Status.Paid,
        amount = Balance.fromCents(10L),
        date = currentTime,
      )
    )
    val fromDate = firstDayOfCurrentMonth.plusMonths(1)
    val creditComparison = creditComparisonClient.request(
      CreditComparisonApi.GetRevenueForecast(
        businessGuid = businessGuid,
        fromDate = fromDate,
        intervalType = IntervalType.Monthly,
        numberOfIntervals = 5,
      )
    )

    val averagePerDay = 40.00
    creditComparison.revenueProjections.forEachIndexed { index, value ->
      assertThat(value.amount.rawCents).isEqualTo(
        (averagePerDay * 30L * (1 + growthPercentage).pow(index)).toLong()
      )
      val intervalStart = fromDate.plusMonths(index.toLong())
      assertThat(value.start).isEqualTo(intervalStart)
      assertThat(value.endInclusive).isEqualTo(
        intervalStart.with(
          TemporalAdjusters.lastDayOfMonth()
        )
      )
    }
    assertThat(creditComparison.revenueProjections.size).isEqualTo(5)
  }

  @Test
  fun `happy path - multi payouts`() = integrationTest {
    clock.nextMonth()
    val currentTime = LocalDate.now(clock)

    val firstDayOfCurrentMonth = currentTime.with(
      TemporalAdjusters.firstDayOfMonth()
    )

    coEvery {
      get<InternalRutterPayoutClient>().request(
        RutterPayoutApi.GetByBusinessInDateRange(
          businessGuid = businessGuid,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      RutterPayoutRep(
        businessGuid = businessGuid,
        connectionGuid = UUID.randomUUID(),
        platform = RutterPlatform.AMAZON,
        fromDate = currentTime,
        depositedAmount = Balance.fromCents(30L),
        pendingAmount = Balance.ZERO,
      ),
      RutterPayoutRep(
        businessGuid = businessGuid,
        connectionGuid = UUID.randomUUID(),
        platform = RutterPlatform.AMAZON,
        fromDate = currentTime.minusMonths(1),
        depositedAmount = Balance.fromCents(30L),
        pendingAmount = Balance.fromCents(50L),
      )
    )

    coEvery {
      get<ShopifyPayoutClient>().request(
        ShopifyPayoutApi.GetByBusiness(
          businessGuid = businessGuid,
          pending = false,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      ShopifyPayoutRep.Complete(
        guid = UUID.randomUUID(),
        connectionGuid = UUID.randomUUID(),
        status = ShopifyPayoutRep.Status.Paid,
        amount = Balance.fromCents(10L),
        date = currentTime,
      ),
      ShopifyPayoutRep.Complete(
        guid = UUID.randomUUID(),
        connectionGuid = UUID.randomUUID(),
        status = ShopifyPayoutRep.Status.Paid,
        amount = Balance.fromCents(30L),
        date = currentTime,
      ),
      ShopifyPayoutRep.Complete(
        // Shouldn't count due to status
        guid = UUID.randomUUID(),
        connectionGuid = UUID.randomUUID(),
        status = ShopifyPayoutRep.Status.InTransit,
        amount = Balance.fromCents(10L),
        date = currentTime,
      ),
    )
    val fromDate = firstDayOfCurrentMonth.plusMonths(1)
    val creditComparison = creditComparisonClient.request(
      CreditComparisonApi.GetRevenueForecast(
        businessGuid = businessGuid,
        fromDate = fromDate,
        intervalType = IntervalType.Monthly,
        numberOfIntervals = 5,
      )
    )

    // (30 + 30) / 32 + (30 + 10), Rutter has 2 payouts over 32 days and
    // Shopify has 2 payouts over 1 day
    val averagePerDay = 41.875
    creditComparison.revenueProjections.forEachIndexed { index, value ->
      assertThat(value.amount.rawCents).isEqualTo(
        (averagePerDay * 30L * (1 + growthPercentage).pow(index)).toLong()
      )
      val intervalStart = fromDate.plusMonths(index.toLong())
      assertThat(value.start).isEqualTo(intervalStart)
      assertThat(value.endInclusive).isEqualTo(
        intervalStart.with(
          TemporalAdjusters.lastDayOfMonth()
        )
      )
    }
    assertThat(creditComparison.revenueProjections.size).isEqualTo(5)
  }

  @Test
  fun `happy path - negative payout`() = integrationTest {
    // Just 1 payout the past year from each platform
    val currentTime = LocalDate.now(clock)

    val firstDayOfCurrentMonth = currentTime.with(
      TemporalAdjusters.firstDayOfMonth()
    )

    coEvery {
      get<InternalRutterPayoutClient>().request(
        RutterPayoutApi.GetByBusinessInDateRange(
          businessGuid = businessGuid,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      RutterPayoutRep(
        businessGuid = businessGuid,
        connectionGuid = UUID.randomUUID(),
        platform = RutterPlatform.AMAZON,
        fromDate = currentTime,
        depositedAmount = Balance.fromCents(-30L),
        pendingAmount = Balance.ZERO,
      )
    )

    coEvery {
      get<ShopifyPayoutClient>().request(
        ShopifyPayoutApi.GetByBusiness(
          businessGuid = businessGuid,
          pending = false,
          fromDate = currentTime.minusYears(1),
        )
      )
    } returns listOf(
      ShopifyPayoutRep.Complete(
        guid = UUID.randomUUID(),
        connectionGuid = UUID.randomUUID(),
        status = ShopifyPayoutRep.Status.Paid,
        amount = Balance.fromCents(10L),
        date = currentTime,
      )
    )
    val fromDate = firstDayOfCurrentMonth.plusMonths(1)
    val creditComparison = creditComparisonClient.request(
      CreditComparisonApi.GetRevenueForecast(
        businessGuid = businessGuid,
        fromDate = fromDate,
        intervalType = IntervalType.Monthly,
        numberOfIntervals = 5,
      )
    )

    val averagePerDay = 0
    creditComparison.revenueProjections.forEachIndexed { index, value ->
      assertThat(value.amount.rawCents).isEqualTo(
        (averagePerDay * 30L * (1 + growthPercentage).pow(index)).toLong()
      )
      val intervalStart = fromDate.plusMonths(index.toLong())
      assertThat(value.start).isEqualTo(intervalStart)
      assertThat(value.endInclusive).isEqualTo(
        intervalStart.with(
          TemporalAdjusters.lastDayOfMonth()
        )
      )
    }
    assertThat(creditComparison.revenueProjections.size).isEqualTo(5)
  }
}
