package co.highbeam.endpoint.underwriting

import co.highbeam.api.onboarding.CreditApplicationApi
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.rep.onboarding.CapitalApplicationDocumentRep
import co.highbeam.rep.onboarding.CapitalApplicationRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import com.google.cloud.storage.HttpMethod
import io.mockk.every
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.net.URL
import java.util.UUID

internal class GetCreditApplicationDocumentsTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private lateinit var creditApplication: CapitalApplicationRep

  @BeforeEach
  fun setUp() = integrationTest {
    creditApplication = creditApplicationClient.request(
      CreditApplicationApi.Create(businessGuid = businessGuid)
    )

    every {
      get<GoogleCloudStorage>().userAccessUrl(any(), HttpMethod.PUT)
    }.returns(URL("http://mocked-upload.url"))

    every {
      get<GoogleCloudStorage>().userAccessUrl(any(), HttpMethod.GET)
    }.returns(URL("http://mocked-fetch.url"))
  }

  @Test
  fun `one document`() = integrationTest {
    creditApplicationClient.request(
      CreditApplicationApi.CreateDocument(
        businessGuid = businessGuid,
        creditApplicationGuid = creditApplication.guid,
        rep = CapitalApplicationDocumentRep.Creator(
          type = CapitalApplicationDocumentRep.Type.ProfitAndLossStatement,
          fileName = "file-name.png",
          notes = "P&L 2022"
        ),
      )
    )
    assertThat(
      creditApplicationClient.request(
        CreditApplicationApi.GetDocuments(
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplication.guid,
        )
      )
    ).isEqualTo(
      listOf(
        CapitalApplicationDocumentRep(
          guid = uuidGenerator[1],
          type = CapitalApplicationDocumentRep.Type.ProfitAndLossStatement,
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplication.guid,
          signedUploadUrl = "http://mocked-fetch.url",
          fileName = "file-name.png",
          notes = "P&L 2022"
        )
      )
    )
  }

  @Test
  fun `many documents`() = integrationTest {
    repeat(3) {
      creditApplicationClient.request(
        CreditApplicationApi.CreateDocument(
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplication.guid,
          rep = CapitalApplicationDocumentRep.Creator(
            type = CapitalApplicationDocumentRep.Type.values()[it],
            fileName = "file-name.png",
            notes = CapitalApplicationDocumentRep.Type.values()[it].toString()
          ),
        )
      )
    }
    assertThat(
      creditApplicationClient.request(
        CreditApplicationApi.GetDocuments(
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplication.guid,
        )
      )
    ).isEqualTo(
      List(3) {
        CapitalApplicationDocumentRep(
          guid = uuidGenerator[it + 1],
          type = CapitalApplicationDocumentRep.Type.values()[it],
          businessGuid = businessGuid,
          creditApplicationGuid = creditApplication.guid,
          signedUploadUrl = "http://mocked-fetch.url",
          fileName = "file-name.png",
          notes = CapitalApplicationDocumentRep.Type.values()[it].toString(),
        )
      }
    )
  }
}
