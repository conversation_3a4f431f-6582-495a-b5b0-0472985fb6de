package co.highbeam.endpoint.lineOfCredit.interestFee

import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeApi
import co.highbeam.api.lineOfCredit.interestFee.LineOfCreditInterestFeeCompletionTaskApi
import co.highbeam.client.lineOfCredit.interestFee.LineOfCreditInterestFeeClient
import co.highbeam.rep.lineOfCredit.interestFee.CompleteInterestFeesRep
import co.highbeam.rep.lineOfCredit.interestFee.LineOfCreditInterestFeeTransferRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import highbeam.task.FakeTaskCreator
import highbeam.task.TaskCreator
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class InterestFeeCompletionTaskTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  @Test
  fun `Create submits task`() = integrationTest {
    lineOfCreditInterestFeeCompletionTaskClient.request(
      LineOfCreditInterestFeeCompletionTaskApi.Create
    )
    val taskCreator = get<TaskCreator>() as FakeTaskCreator
    assertThat(taskCreator.tasks["line-of-credit-interest-fee-completion"])
      .containsExactly(LineOfCreditInterestFeeCompletionTaskApi.Execute)
  }

  @Test
  fun `Executes task by calling client`() = integrationTest {
    coEvery {
      get<LineOfCreditInterestFeeClient>().request(
        endpoint = any() as LineOfCreditInterestFeeApi.CompleteInterestFees,
      )
    } answers { return@answers mockk() }

    lineOfCreditInterestFeeCompletionTaskClient.request(
      LineOfCreditInterestFeeCompletionTaskApi.Execute
    )

    coVerify(exactly = 1) {
      get<LineOfCreditInterestFeeClient>().request(
        LineOfCreditInterestFeeApi.CompleteInterestFees(
          CompleteInterestFeesRep.Creator(
            completedState = LineOfCreditInterestFeeTransferRep.State.Completed,
          ),
        ),
      )
    }
  }
}
