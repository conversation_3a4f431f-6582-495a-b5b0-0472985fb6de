package co.highbeam.endpoint.documents

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.documents.DocumentsApi
import co.highbeam.api.lineOfCredit.risk.CreditAdverseActionApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.feature.googleCloudStorage.GoogleCloudStorage
import co.highbeam.rep.DocumentRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep.AdverseActionType.ApplicationRejected
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep.Reason.NotEnoughTimeOnHighbeam
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep.Reason.NotEnoughTransactions
import co.highbeam.rep.lineOfCredit.risk.AdverseActionRep.Reason.TooMuchDebt
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import com.google.cloud.storage.HttpMethod
import io.mockk.coEvery
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.net.URL
import java.time.ZoneOffset.UTC
import java.util.UUID

internal class GetCapitalAccountDocumentsTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private val lineOfCreditGuid = UUID.randomUUID()
  private val business = BusinessRep.Complete(
    guid = businessGuid,
    dba = null,
    name = "Big Mac Buns LLC",
    referralLinkGuid = null,
    ownerUserGuid = UUID.randomUUID(),
    unitCoCustomerId = "1234",
    status = BusinessRep.Complete.Status.Active,
    stateOfIncorporation = null,
    naics = null,
  )

  @BeforeEach
  fun beforeEach() {
    coEvery {
      get<BusinessClient>().request(BusinessApi.Get(businessGuid))
    } returns business

    coEvery {
      get<GoogleCloudStorage>().upload(any(), any())
    } returns mockk()

    coEvery {
      get<GoogleCloudStorage>().userAccessUrl(
        objectName = "Documents/${uuidGenerator[0]}",
        httpMethod = HttpMethod.GET,
      )
    } returns URL("http://mocked.url")
  }

  @Test
  fun `happy path - works for one document`() = integrationTest {
    creditAdverseActionClient.request(
      CreditAdverseActionApi.CreateAdverseAction(
        businessGuid = businessGuid,
        rep = AdverseActionRep.Creator(
          adverseActionType = ApplicationRejected,
          reasons = listOf(NotEnoughTimeOnHighbeam, NotEnoughTransactions, TooMuchDebt),
          lineOfCreditGuid = lineOfCreditGuid,
        )
      )
    )

    verify(exactly = 1) {
      get<GoogleCloudStorage>().upload(any(), any())
    }

    val response = documentsClient.request(
      DocumentsApi.GetDocuments(
        businessGuid,
      )
    )

    assertThat(response.first()).isEqualTo(
      DocumentRep(
        guid = uuidGenerator[0],
        generatedAt = clock.date(UTC),
        name = "Re: Credit Application",
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        category = DocumentRep.Category.Notice,
      )
    )
  }

  @Test
  fun `happy path - works for multiple document`() = integrationTest {

    val reasonsList = listOf(NotEnoughTimeOnHighbeam, NotEnoughTransactions, TooMuchDebt)

    for (reason in reasonsList) {
      creditAdverseActionClient.request(
        CreditAdverseActionApi.CreateAdverseAction(
          businessGuid = businessGuid,
          rep = AdverseActionRep.Creator(
            adverseActionType = ApplicationRejected,
            reasons = listOf(reason),
            lineOfCreditGuid = lineOfCreditGuid,
          )
        )
      )
    }

    verify(exactly = 3) {
      get<GoogleCloudStorage>().upload(any(), any())
    }

    val response = documentsClient.request(
      DocumentsApi.GetDocuments(
        businessGuid,
      )
    )

    assertThat(response).isEqualTo(
      List(reasonsList.size) { index ->
        DocumentRep(
          guid = uuidGenerator[index],
          generatedAt = clock.date(UTC),
          name = "Re: Credit Application",
          businessGuid = businessGuid,
          lineOfCreditGuid = lineOfCreditGuid,
          category = DocumentRep.Category.Notice,
        )
      }
    )
  }
}
