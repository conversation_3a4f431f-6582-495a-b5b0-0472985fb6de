package co.highbeam.endpoint.lineOfCredit.transaction

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCompleteRep
import com.fasterxml.jackson.databind.node.NullNode
import io.mockk.coEvery
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID

internal class ForceDrawdownTest(
  server: Server<*>,
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  private val primaryBankAccount = bankAccountRep(
    unitCoDepositAccountId = "1111",
    isPrimary = true,
    name = "Primary",
    highbeamType = BankAccountRep.Type.DepositAccount,
    balance = Balance.fromCents(10_000),
  )

  @BeforeEach
  fun beforeEach() {
    mockGetBankAccount(
      bankAccountGuid = primaryBankAccount.guid,
      bankAccount = primaryBankAccount,
    )

    mockUnitCoPaymentCreation()
    mockBusinessClient(businessGuid)
    mockUnitCoTransactionClient("transaction/happy-prejit.json")
  }

  @Test
  fun `happy path`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.ForceDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        ),
        transactionInitiator = CapitalDrawdownTransactionRep.TransactionInitiator.Risk,
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }
  }

  @Test
  fun `happy path - to non primary account`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(businessGuid, UUID.randomUUID())
    val bankAccountGuid = UUID.randomUUID()
    mockGetBankAccount(
      bankAccountGuid = bankAccountGuid,
      bankAccount = bankAccountRep(
        unitCoDepositAccountId = "2222",
        isPrimary = false,
        name = "Another one",
        highbeamType = BankAccountRep.Type.DepositAccount,
        balance = Balance.fromCents(10_000),
        depositProduct = DepositAccountRep.DepositProduct.CheckingThread,
      )
    )
    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.ForceDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000),
          idempotencyKey = key,
          bankAccountGuid = bankAccountGuid,
        ),
        transactionInitiator = CapitalDrawdownTransactionRep.TransactionInitiator.Risk,
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "2222", // Unit id of another account
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }

    coVerify(atLeast = 1) {
      get<BankAccountClient>().request(
        BankAccountApi.Get(accountGuid = bankAccountGuid)
      )
    }
  }

  @Test
  fun `drawdown disabled line of credit`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = 100_000_00L,
    )

    lineOfCreditClient.request(
      LineOfCreditApi.Update(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCredit,
        rep = LineOfCreditRep.Updater(
          drawdownEnabled = false,
        )
      )
    )

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.ForceDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000_000_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        ),
        transactionInitiator = CapitalDrawdownTransactionRep.TransactionInitiator.Risk,
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[12].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000_000_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          ),
        )
      )
    }
  }

  @Test
  fun `insufficient line of credit`() = integrationTest {
    val lineOfCredit = setUpLineOfCredit(
      businessGuid = businessGuid,
      userGuid = UUID.randomUUID(),
      limit = ********,
    )

    val key = UUID.randomUUID()
    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.ForceDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = lineOfCredit,
        rep = CapitalDrawdownTransactionRep.Creator(
          amount = Money.fromCents(1_000_000_000),
          idempotencyKey = key,
          bankAccountGuid = primaryBankAccount.guid,
        ),
        transactionInitiator = CapitalDrawdownTransactionRep.TransactionInitiator.Risk,
      )
    )

    assertThat(res).isEqualTo(
      CapitalDrawdownTransactionRep(
        unitCoId = uuidGenerator[11].toString(),
        status = CapitalDrawdownTransactionRep.Status.Sent,
      )
    )

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money(1_000_000_000),
          description = "Highbeam Card drawdown",
          idempotencyKey = key,
          fromAccountId = "421321",
          toAccountId = "1111",
          tags = BookPaymentRep.Tags(
            UnitCoTransactionRep.TransactionType.LineOfCreditPayment,
            creditAccountGuid = lineOfCredit,
          )
        )
      )
    }
  }

  private fun mockUnitCoPaymentCreation() {
    coEvery { get<UnitCoClient>().payment.createBook(any()) } answers {
      val creator = firstArg<BookPaymentRep.Creator>()
      val id = uuidGenerator.generate().toString()
      return@answers UnitCompleteRep(
        type = "book",
        id = id,
        attributes = BookPaymentRep(
          createdAt = ZonedDateTime.of(
            2007, 12, 3, 5, 15, 30, 789_000_000,
            ZoneOffset.UTC,
          ),
          amount = creator.amount,
          description = creator.description,
          status = UnitCoPayment.Status.Sent,
          reason = null
        ),
        relationships = mapOf(),
        json = NullNode.instance,
      )
    }
  }

  private fun bankAccountRep(
    unitCoDepositAccountId: String,
    isPrimary: Boolean,
    name: String,
    highbeamType: BankAccountRep.Type,
    balance: Balance,
    depositProduct: DepositAccountRep.DepositProduct = DepositAccountRep.DepositProduct.Checking,
  ): BankAccountRep.Complete =
    BankAccountRep.Complete(
      guid = UUID.randomUUID(),
      unitCoDepositAccountId = unitCoDepositAccountId,
      businessGuid = businessGuid,
      name = name,
      status = BankAccountRep.Status.OPEN,
      isPrimary = isPrimary,
      availableBalance = balance,
      routingNumber = "123",
      accountNumber = "456",
      type = "depositAccount",
      highbeamType = highbeamType,
      depositProduct = depositProduct,
      minimumRequiredBalance = Balance.ZERO,
    )
}
