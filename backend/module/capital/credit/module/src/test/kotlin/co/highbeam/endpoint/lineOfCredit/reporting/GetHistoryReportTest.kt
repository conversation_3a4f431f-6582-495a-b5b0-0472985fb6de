package co.highbeam.endpoint.lineOfCredit.reporting

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.api.lineOfCredit.reporting.LineOfCreditBusinessReportingApi
import co.highbeam.capital.account.model.CapitalAccountDetailsModel
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.store.CapitalAccountDetailStore
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.client.business.BusinessClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import co.highbeam.rep.lineOfCredit.reporting.LineOfCreditHistoryRep
import co.highbeam.server.Server
import co.highbeam.testing.CreditFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID

internal class GetHistoryReportTest(
  server: Server<*>
) : CreditFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private lateinit var lineOfCreditGuid: UUID
  private val lineOfCreditDetailStore: CapitalAccountDetailStore = get()

  @BeforeEach
  fun setUp() = integrationTest {
    mockBusinessClient(businessGuid, "Highbeam")
    mockClock()
    lineOfCreditGuid = setUpLineOfCredit(businessGuid, UUID.randomUUID())
    mockLimitChanges(lineOfCreditGuid)
  }

  @Test
  fun `Inactive line of credit`() = integrationTest {
    mockUnitCoTransactionClient("transaction/history/all.json")
    lineOfCreditClient.request(
      LineOfCreditApi.Update(
        businessGuid = businessGuid,
        lineOfCreditGuid = lineOfCreditGuid,
        rep = LineOfCreditRep.Updater(
          state = LineOfCreditRep.State.Terminated
        )
      )
    )

    val result = lineOfCreditBusinessReportingClient.request(
      LineOfCreditBusinessReportingApi.GetLineOfCreditHistoryReport(
        businessGuid,
        LocalDate.of(2022, 1, 1)
      )
    )
    assertThat(result).isEqualTo(
      LineOfCreditHistoryRep(
        runningBalance = Balance.ZERO,
        limit = Money.ZERO,
        apr = BigDecimal.ZERO,
        totalPaidLoans = 0L,
        averagePaidLoanAgeInWeeks = 0L,
        maxOpenLoanAgeInWeeks = 0L,
        outstandingMoreThanThresholdLoanAge = Money.ZERO,
      )
    )
  }

  @Test
  fun `before any transaction`() = integrationTest {
    mockUnitCoTransactionClient("transaction/history/before_start.json")

    val result = lineOfCreditBusinessReportingClient.request(
      LineOfCreditBusinessReportingApi.GetLineOfCreditHistoryReport(
        businessGuid,
        LocalDate.of(2022, 1, 1)
      )
    )
    assertThat(result).isEqualTo(
      LineOfCreditHistoryRep(
        runningBalance = Balance.ZERO,
        limit = Money.ZERO,
        apr = BigDecimal.ZERO,
        totalPaidLoans = 0L,
        averagePaidLoanAgeInWeeks = 0L,
        maxOpenLoanAgeInWeeks = 0L,
        outstandingMoreThanThresholdLoanAge = Money.ZERO,
      )
    )
  }

  @Test
  fun `after one transaction`() = integrationTest {
    mockUnitCoTransactionClient("transaction/history/after_one.json")

    val result = lineOfCreditBusinessReportingClient.request(
      LineOfCreditBusinessReportingApi.GetLineOfCreditHistoryReport(
        businessGuid,
        LocalDate.of(2023, 1, 18)
      )
    )
    assertThat(result).isEqualTo(
      LineOfCreditHistoryRep(
        runningBalance = Balance.fromCents(-100000),
        limit = Money.fromCents(100000),
        apr = BigDecimal("0.2"),
        totalPaidLoans = 0L,
        averagePaidLoanAgeInWeeks = 0L,
        maxOpenLoanAgeInWeeks = 0L,
        outstandingMoreThanThresholdLoanAge = Money.ZERO,
      )
    )
  }

  @Test
  fun `before last transaction`() = integrationTest {
    mockUnitCoTransactionClient("transaction/history/before_last.json")

    val result = lineOfCreditBusinessReportingClient.request(
      LineOfCreditBusinessReportingApi.GetLineOfCreditHistoryReport(
        businessGuid,
        LocalDate.of(2023, 7, 30)
      )
    )
    assertThat(result).isEqualTo(
      LineOfCreditHistoryRep(
        runningBalance = Balance.fromCents(-50000),
        limit = Money.fromCents(50000),
        apr = BigDecimal("0.15"),
        totalPaidLoans = 0L,
        averagePaidLoanAgeInWeeks = 0L,
        maxOpenLoanAgeInWeeks = 27L,
        outstandingMoreThanThresholdLoanAge = Money.fromCents(40000),
      )
    )
  }

  @Test
  fun `after all transaction`() = integrationTest {
    mockUnitCoTransactionClient("transaction/history/all.json")

    val result = lineOfCreditBusinessReportingClient.request(
      LineOfCreditBusinessReportingApi.GetLineOfCreditHistoryReport(
        businessGuid,
        LocalDate.of(2023, 12, 31)
      )
    )
    assertThat(result).isEqualTo(
      LineOfCreditHistoryRep(
        runningBalance = Balance.ZERO,
        limit = Money.fromCents(10000000),
        apr = BigDecimal.ZERO,
        totalPaidLoans = 2L,
        averagePaidLoanAgeInWeeks = 19L,
        maxOpenLoanAgeInWeeks = 0L,
        outstandingMoreThanThresholdLoanAge = Money.ZERO,
      )
    )
  }

  private fun mockBusinessClient(businessGuid: UUID, name: String) {
    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns
      BusinessRep.Complete(
        guid = businessGuid,
        dba = null,
        name = name,
        referralLinkGuid = null,
        ownerUserGuid = UUID.randomUUID(),
        unitCoCustomerId = "431742",
        status = BusinessRep.Complete.Status.Active,
        stateOfIncorporation = BusinessRep.StateOfIncorporation.NY,
        naics = null,
      )
  }

  private fun mockLimitChanges(lineOfCreditGuid: UUID) {
    lineOfCreditDetailStore.create(
      model = CapitalAccountDetailsModel(
        guid = UUID.randomUUID(),
        businessGuid = businessGuid,
        capitalAccountGuid = lineOfCreditGuid,
        effectiveAt = LocalDate.of(2023, 1, 10).atStartOfDay(clock.zone),
        data = CapitalAccountDetailsModel.Data(
          apr = BigDecimal("0.2"),
          limit = Money(100000),
          repayment = CapitalAccountDetailsRep.Repayment(
            bankAccountGuid = null,
            option = CapitalRepaymentOption.None
          ),
          lineType = CapitalAccountDetailsRep.LineType.Revolving,
          targetRepaymentDays = 120,
          securedStatus = CapitalAccountDetailsRep.SecuredStatus.Unsecured,
        ),
      )
    )
    lineOfCreditDetailStore.create(
      model = CapitalAccountDetailsModel(
        guid = UUID.randomUUID(),
        businessGuid = businessGuid,
        capitalAccountGuid = lineOfCreditGuid,
        effectiveAt = LocalDate.of(2023, 6, 10).atStartOfDay(clock.zone),
        data = CapitalAccountDetailsModel.Data(
          apr = BigDecimal("0.15"),
          limit = Money(50000),
          repayment = CapitalAccountDetailsRep.Repayment(
            bankAccountGuid = null,
            option = CapitalRepaymentOption.None
          ),
          lineType = CapitalAccountDetailsRep.LineType.Revolving,
          targetRepaymentDays = 120,
          securedStatus = CapitalAccountDetailsRep.SecuredStatus.Unsecured,
        ),
      )
    )
  }

  private fun mockClock() {
    clock.setNow(
      ZonedDateTime.of(
        2023, 12, 17, 5, 15, 30, 789_000_000,
        ZoneOffset.UTC,
      )
    )
  }

}
