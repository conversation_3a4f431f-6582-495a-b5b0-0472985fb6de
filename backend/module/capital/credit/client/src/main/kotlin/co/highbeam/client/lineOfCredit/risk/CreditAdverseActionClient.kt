package co.highbeam.client.lineOfCredit.risk

import co.highbeam.api.lineOfCredit.risk.CreditAdverseActionApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.credit.CREDIT_FEATURE
import co.highbeam.rep.DocumentRep
import com.google.inject.Inject
import com.google.inject.name.Named

class CreditAdverseActionClient @Inject constructor(
  @Named(CREDIT_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(
    endpoint: CreditAdverseActionApi.CreateAdverseAction,
  ): DocumentRep =
    httpClient.request(endpoint).readValue()
}
