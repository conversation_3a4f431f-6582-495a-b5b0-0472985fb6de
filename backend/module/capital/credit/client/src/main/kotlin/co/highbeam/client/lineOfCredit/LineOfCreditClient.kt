package co.highbeam.client.lineOfCredit

import co.highbeam.api.lineOfCredit.LineOfCreditApi
import co.highbeam.capital.onboarding.rep.agreement.CapitalAgreementRep
import co.highbeam.capital.transaction.rep.CapitalTransactionSummaryRep
import co.highbeam.client.HttpClient
import co.highbeam.feature.credit.CREDIT_FEATURE
import co.highbeam.rep.lineOfCredit.LineOfCreditHistoricalDetailsRep
import co.highbeam.rep.lineOfCredit.LineOfCreditRep
import com.google.inject.Inject
import com.google.inject.name.Named
import java.util.UUID

class LineOfCreditClient @Inject constructor(
  @Named(CREDIT_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(
    endpoint: LineOfCreditApi.Create,
  ): LineOfCreditRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.Update,
  ): LineOfCreditRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.GetAvailableFinancing,
  ): List<CapitalTransactionSummaryRep> =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.AcceptOffer,
  ): CapitalAgreementRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.ActivateLineOfCredit,
  ): LineOfCreditRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.GetLineOfCreditHistoricalDetails,
  ): LineOfCreditHistoricalDetailsRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: LineOfCreditApi.SetUpLineOfCredit,
  ): UUID =
    httpClient.request(endpoint).readValue()
}
