package co.highbeam.chargeCard

import co.highbeam.capital.chargeCard.ChargeCardSummaryClient
import co.highbeam.capital.chargeCard.api.ChargeCardSummaryApi
import co.highbeam.capital.chargeCard.rep.ChargeCardSummaryRep
import co.highbeam.money.Balance
import java.time.LocalDate
import java.util.TreeMap
import java.util.UUID

class FakeChargeCardSummaryClient : ChargeCardSummaryClient {
  private val store: MutableMap<String, TreeMap<LocalDate, ChargeCardSummaryRep>> =
    mutableMapOf()

  override suspend fun invoke(endpoint: ChargeCardSummaryApi.Get): ChargeCardSummaryRep {
    val key = key(
      capitalAccountGuid = endpoint.capitalAccountGuid,
      businessGuid = endpoint.businessGuid,
    )

    return store[key]?.lastEntry()?.value ?: ChargeCardSummaryRep(
      date = LocalDate.now(),
      balance = Balance.ZERO,
      pending = Balance.ZERO,
    )
  }

  fun set(
    capitalAccountGuid: UUID,
    businessGuid: UUID,
    date: LocalDate,
    balance: Balance,
    pending: Balance,
  ) {
    val key = key(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
    )
    val rep = ChargeCardSummaryRep(date = date, balance = balance, pending = pending)
    if (!store.contains(key)) store[key] = TreeMap()

    store[key]?.put(date, rep)
  }

  fun reset() {
    store.clear()
  }

  private fun key(capitalAccountGuid: UUID, businessGuid: UUID): String =
    "$capitalAccountGuid-$businessGuid"
}
