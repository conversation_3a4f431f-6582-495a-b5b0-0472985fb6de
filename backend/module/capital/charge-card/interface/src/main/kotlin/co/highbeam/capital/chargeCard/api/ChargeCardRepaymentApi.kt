package co.highbeam.capital.chargeCard.api

import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import java.util.UUID

object ChargeCardRepaymentApi {
  data class Create(
    val businessGuid: UUID,
    val chargeCardAccountGuid: UUID,
    val rep: ChargeCardRepaymentRep.Creator,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/charge-card-repayments",
    body = rep,
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "chargeCardAccountGuid" to listOf(chargeCardAccountGuid.toString()),
    )
  )

  data class CreateStatementBalancePayment(
    val businessGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/charge-card-repayments/statement-balance",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    )
  )

  data class Get(
    val businessGuid: UUID,
    val chargeCardAccountGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/charge-card-repayments",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "chargeCardAccountGuid" to listOf(chargeCardAccountGuid.toString()),
    )
  )

  data class GetCsv(
    val businessGuid: UUID,
    val chargeCardAccountGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/charge-card-repayments",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "chargeCardAccountGuid" to listOf(chargeCardAccountGuid.toString()),
    ),
    contentType = ContentType.Text.CSV
  )

  data class GetInfo(
    val businessGuid: UUID,
    val chargeCardAccountGuid: UUID
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/charge-card-repayments/info",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "chargeCardAccountGuid" to listOf(chargeCardAccountGuid.toString()),
    ),
  )
}
