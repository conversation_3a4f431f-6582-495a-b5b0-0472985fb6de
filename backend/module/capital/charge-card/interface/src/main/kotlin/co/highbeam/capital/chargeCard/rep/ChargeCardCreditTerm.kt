package co.highbeam.capital.chargeCard.rep

enum class ChargeCardCreditTerm {
  @Deprecated("Use new terms")
  Production,

  @Deprecated("Use new terms")
  Sandbox,

  ProductionExtend,
  ProductionFlex,
  ProductionCash,

  SandboxExtend,
  SandboxFlex,
  SandboxCash;

  val isCashTerm: Boolean
    get() = when (this) {
      ProductionCash, SandboxCash -> true
      Production, Sandbox, ProductionExtend, ProductionFlex, SandboxExtend, SandboxFlex -> false
    }

  val isExtendTerm: Boolean
    get() = when (this) {
      ProductionExtend, SandboxExtend -> true
      Production, Sandbox, ProductionCash, SandboxCash, ProductionFlex, SandboxFlex -> false
    }
}
