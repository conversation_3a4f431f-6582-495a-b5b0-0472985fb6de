package co.highbeam.capital.chargeCard.api

import co.highbeam.capital.chargeCard.rep.ChargeCardOfferRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object ChargeCardOfferApi {
  data class Create(
    val businessGuid: UUID,
    val capitalAccountGuid: UUID,
    val rep: ChargeCardOfferRep.Creator
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/charge-card-offers",
    body = rep,
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "capitalAccountGuid" to listOf(capitalAccountGuid.toString()),
    )
  )

  data class Get(
    val businessGuid: UUID,
    val capitalAccountGuid: UUID,
  ) : Endpoint(
    path = "/charge-card-offers",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "capitalAccountGuid" to listOf(capitalAccountGuid.toString()),
    )
  )
}
