package co.highbeam.capital.chargeCard.api

import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object ChargeCardAccountApi {
  data class Create(
    val businessGuid: UUID,
    val capitalAccountGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/charge-card-accounts",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "capitalAccountGuid" to listOf(capitalAccountGuid.toString()),
    )
  )

  data class Get(
    val businessGuid: UUID,
    val capitalAccountGuid: UUID,
  ) : Endpoint(
    path = "/charge-card-accounts",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "capitalAccountGuid" to listOf(capitalAccountGuid.toString()),
    )
  )

  data class GetAll(
    val businessGuid: UUID,
  ) : Endpoint(
    path = "/charge-card-accounts",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    )
  )

  data class GetByUnitCoCreditAccountId(
    val unitCoCreditAccountId: String,
  ) : Endpoint(
    path = "/charge-card-accounts",
    qp = buildMap {
      put("unitCoCreditAccountId", listOf(unitCoCreditAccountId))
    }
  )

  data class Update(
    val businessGuid: UUID,
    val chargeCardAccountGuid: UUID,
    val updater: ChargeCardAccountRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/charge-card-accounts/$chargeCardAccountGuid",
    body = updater,
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
    )
  )
}
