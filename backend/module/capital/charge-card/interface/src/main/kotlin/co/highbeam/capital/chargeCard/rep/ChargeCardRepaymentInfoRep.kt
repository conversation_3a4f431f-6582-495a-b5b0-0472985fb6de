package co.highbeam.capital.chargeCard.rep

import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import java.time.LocalDate

data class ChargeCardRepaymentInfoRep(
  val remainingAmountDue: Money,
  val remainingAmountOverdue: Money,
  val initiatedRepayments: Money,
  val previousStatementPeriodStart: LocalDate,
  val previousStatementPeriodEnd: LocalDate,
  val currentStatementPeriodStart: LocalDate,
  val currentStatementPeriodEnd: LocalDate,
  val nextRepaymentDueDate: LocalDate,
) : CompleteRep
