package co.highbeam.capital.chargeCard

import co.highbeam.capital.chargeCard.api.ChargeCardInternalApi
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountHistoryRep
import com.google.inject.ImplementedBy

@ImplementedBy(HttpChargeCardInternalClient::class)
fun interface ChargeCardInternalClient {
  suspend operator fun invoke(
    endpoint: ChargeCardInternalApi.GetHistory,
  ): List<ChargeCardAccountHistoryRep>
}
