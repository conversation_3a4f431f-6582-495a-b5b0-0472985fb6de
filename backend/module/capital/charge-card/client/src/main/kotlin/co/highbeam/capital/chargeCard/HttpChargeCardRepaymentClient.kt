package co.highbeam.capital.chargeCard

import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi
import co.highbeam.capital.chargeCard.feature.CHARGE_CARD_FEATURE
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentInfoRep
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.client.HttpClient
import com.google.inject.Inject
import com.google.inject.name.Named

class HttpChargeCardRepaymentClient @Inject constructor(
  @Named(CHARGE_CARD_FEATURE) private val httpClient: HttpClient,
) : ChargeCardRepaymentClient {
  override suspend fun invoke(endpoint: ChargeCardRepaymentApi.Create): ChargeCardRepaymentRep =
    httpClient.request(endpoint).readValue()

  override suspend fun invoke(
    endpoint: ChargeCardRepaymentApi.GetInfo,
  ): ChargeCardRepaymentInfoRep = httpClient.request(endpoint).readValue()

  override suspend fun invoke(
    endpoint: ChargeCardRepaymentApi.CreateStatementBalancePayment,
  ): Unit = httpClient.request(endpoint).readValue()

  override suspend fun invoke(endpoint: ChargeCardRepaymentApi.Get): List<ChargeCardRepaymentRep> =
    httpClient.request(endpoint).readValue()

  override suspend fun invoke(endpoint: ChargeCardRepaymentApi.GetCsv): String =
    checkNotNull(httpClient.request(endpoint).readText())
}
