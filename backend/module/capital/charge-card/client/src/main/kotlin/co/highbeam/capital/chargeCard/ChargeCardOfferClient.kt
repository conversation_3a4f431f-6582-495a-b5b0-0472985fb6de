package co.highbeam.capital.chargeCard

import co.highbeam.capital.chargeCard.api.ChargeCardOfferApi
import co.highbeam.capital.chargeCard.rep.ChargeCardOfferRep
import com.google.inject.ImplementedBy

@ImplementedBy(HttpChargeCardOfferClient::class)
interface ChargeCardOfferClient {
  suspend operator fun invoke(
    endpoint: ChargeCardOfferApi.Create,
  ): ChargeCardOfferRep

  suspend operator fun invoke(
    endpoint: ChargeCardOfferApi.Get,
  ): ChargeCardOfferRep?
}
