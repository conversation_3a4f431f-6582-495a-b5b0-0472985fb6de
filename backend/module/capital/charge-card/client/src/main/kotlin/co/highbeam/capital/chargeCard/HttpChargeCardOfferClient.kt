package co.highbeam.capital.chargeCard

import co.highbeam.capital.chargeCard.api.ChargeCardOfferApi
import co.highbeam.capital.chargeCard.feature.CHARGE_CARD_FEATURE
import co.highbeam.capital.chargeCard.rep.ChargeCardOfferRep
import co.highbeam.client.HttpClient
import com.google.inject.Inject
import com.google.inject.name.Named

class HttpChargeCardOfferClient @Inject constructor(
  @Named(CHARGE_CARD_FEATURE) private val httpClient: HttpClient,
) : ChargeCardOfferClient {
  override suspend fun invoke(
    endpoint: ChargeCardOfferApi.Create,
  ): ChargeCardOfferRep = httpClient.request(endpoint).readValue()

  override suspend fun invoke(
    endpoint: ChargeCardOfferApi.Get,
  ): ChargeCardOfferRep? = httpClient.request(endpoint).readValue()
}
