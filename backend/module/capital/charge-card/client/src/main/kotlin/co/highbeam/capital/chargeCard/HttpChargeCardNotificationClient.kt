package co.highbeam.capital.chargeCard

import co.highbeam.capital.chargeCard.api.ChargeCardNotificationApi
import co.highbeam.capital.chargeCard.feature.CHARGE_CARD_FEATURE
import co.highbeam.client.HttpClient
import com.google.inject.Inject
import com.google.inject.name.Named

class HttpChargeCardNotificationClient @Inject constructor(
  @Named(CHARGE_CARD_FEATURE) private val httpClient: HttpClient,
) : ChargeCardNotificationClient {
  override suspend fun invoke(
    endpoint: ChargeCardNotificationApi.SendStatementEnd,
  ): Unit = httpClient.request(endpoint).readValue()
}
