package co.highbeam.capital.chargeCard.service

import co.highbeam.capital.chargeCard.model.ChargeCardAccountHistoryModel
import co.highbeam.capital.chargeCard.rep.ChargeCardCreditTerm
import co.unit.rep.UnitCoCreditAccountRep
import com.google.inject.ImplementedBy
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.UUID

@ImplementedBy(ChargeCardAccountHistoryServiceImpl::class)
interface ChargeCardAccountHistoryService {
  fun create(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID?,
    capitalAccountGuid: UUID,
    effectiveAt: ZonedDateTime,
    data: ChargeCardAccountHistoryModel.Data,
  ): ChargeCardAccountHistoryModel

  fun getAll(businessGuid: UUID, chargeCardAccountGuid: UUID): List<ChargeCardAccountHistoryModel>

  fun getLatestOffer(businessGuid: UUID, capitalAccountGuid: UUID): ChargeCardAccountHistoryModel?

  fun getLatestAsOf(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID,
    dateTime: ZonedDateTime
  ): ChargeCardAccountHistoryModel?

  fun trackChange(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID,
    capitalAccountGuid: UUID,
    chargeCardAccountStatus: UnitCoCreditAccountRep.Status? = null,
    minCashback: BigDecimal? = null,
    maxCashback: BigDecimal? = null,
    creditTerms: ChargeCardCreditTerm?,
    latePaymentFees: BigDecimal? = null,
  )
}
