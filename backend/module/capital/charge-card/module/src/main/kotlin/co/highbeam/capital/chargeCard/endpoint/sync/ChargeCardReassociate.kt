package co.highbeam.capital.chargeCard.endpoint.sync

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.chargeCard.service.ChargeCardSyncService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardSyncApi as Api

internal class ChargeCardReassociate @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: ChargeCardSyncService,
) : EndpointHandler<Api.Reassociate, Unit>(
  template = Api.Reassociate::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Reassociate = Api.Reassociate(
    businessGuid = call.getParam("businessGuid"),
    chargeCardAccountGuid = call.getParam("chargeCardAccountGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(endpoint: Api.Reassociate) {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.reassociate(
      businessGuid = endpoint.businessGuid,
      chargeCardAccountGuid = endpoint.chargeCardAccountGuid,
      rep = endpoint.rep,
    )
  }
}
