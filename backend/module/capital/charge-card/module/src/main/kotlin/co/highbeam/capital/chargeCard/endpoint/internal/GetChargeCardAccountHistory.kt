package co.highbeam.capital.chargeCard.endpoint.internal

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.chargeCard.mapper.ChargeCardAccountHistoryMapper
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountHistoryRep
import co.highbeam.capital.chargeCard.service.ChargeCardAccountHistoryService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardInternalApi as Api

internal class GetChargeCardAccountHistory @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: ChargeCardAccountHistoryService,
  private val mapper: ChargeCardAccountHistoryMapper,
) : EndpointHandler<Api.GetHistory, List<ChargeCardAccountHistoryRep>>(
  template = Api.GetHistory::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetHistory = Api.GetHistory(
    businessGuid = call.getParam("businessGuid"),
    chargeCardAccountGuid = call.getParam("chargeCardAccountGuid"),
  )

  override fun loggingContext(endpoint: Api.GetHistory) = super.loggingContext(endpoint) +
    mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "chargeCardAccountGuid" to endpoint.chargeCardAccountGuid.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.GetHistory): List<ChargeCardAccountHistoryRep> {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.getAll(
      businessGuid = endpoint.businessGuid,
      chargeCardAccountGuid = endpoint.chargeCardAccountGuid,
    ).map { mapper.toRep(it) }
  }
}

