package co.highbeam.capital.chargeCard.endpoint.account

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthCapitalAccount
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.capital.chargeCard.service.ChargeCardAccountService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi as Api

internal class CreateChargeCardAccount @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val service: ChargeCardAccountService,
) : EndpointHandler<Api.Create, ChargeCardAccountRep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create = Api.Create(
    businessGuid = call.getParam("businessGuid"),
    capitalAccountGuid = call.getParam("capitalAccountGuid"),
  )

  override suspend fun Handler.handle(endpoint: Api.Create): ChargeCardAccountRep {
    authSome(
      authCapitalAccount(
        permission = Permission.BankAccount_Create,
        capitalAccountGuid = endpoint.capitalAccountGuid,
        businessGuid = endpoint.businessGuid,
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return service.create(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
    )
  }
}
