package co.highbeam.capital.chargeCard.listener

import co.highbeam.capital.chargeCard.store.ChargeCardAccountStore
import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.rep.UUIDSelection
import com.google.inject.Inject
import com.google.inject.Singleton
import com.google.inject.name.Named
import mu.KotlinLogging
import java.util.UUID

internal const val CHARGE_CARD_REPAYMENT_REMINDER_JOB_TOPIC_NAME =
  "charge-card-repayment-reminder-job"

@Singleton
internal class ChargeCardRepaymentReminderJobListener @Inject constructor(
  factory: EventListenerFactory,
  private val chargeCardAccountStore: ChargeCardAccountStore,
  @Named(CHARGE_CARD_REPAYMENT_REMINDER_TOPIC_NAME)
  private val publisher: EventPublisher<UUID>,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig(CHARGE_CARD_REPAYMENT_REMINDER_JOB_TOPIC_NAME),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "charge-card",
      ),
      clazz = UUIDSelection::class.java,
      listener = ::onReceive
    )
  }

  fun onReceive(selection: UUIDSelection) {
    logger.info { "Received reminder job selection=$selection" }

    when (selection) {
      is UUIDSelection.All -> {
        chargeCardAccountStore.getAllOpen().map { publisher.publishEvent(it.businessGuid) }
      }
      is UUIDSelection.Explicit -> {
        selection.guids.map { publisher.publishEvent(it) }
      }
      else -> error("Unsupported UUIDSelection: $selection")
    }
  }
}
