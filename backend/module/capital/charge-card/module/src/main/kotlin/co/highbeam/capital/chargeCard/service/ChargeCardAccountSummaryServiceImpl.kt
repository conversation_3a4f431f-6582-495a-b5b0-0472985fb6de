package co.highbeam.capital.chargeCard.service

import co.highbeam.capital.chargeCard.rep.ChargeCardSummaryRep
import co.highbeam.money.Balance
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import java.time.Clock
import java.time.LocalDate
import java.util.UUID

class ChargeCardAccountSummaryServiceImpl @Inject constructor(
  private val chargeCardAccountService: ChargeCardAccountService,
  private val unitCoClient: UnitCoClient,
  private val clock: Clock,
) : ChargeCardAccountSummaryService {
  override suspend fun get(businessGuid: UUID, capitalAccountGuid: UUID): ChargeCardSummaryRep {
    val chargeCardAccount = chargeCardAccountService.getByCapitalAccountGuid(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    ) ?: return ChargeCardSummaryRep(
      balance = Balance.ZERO,
      pending = Balance.ZERO,
      date = LocalDate.now(clock),
    )
    val unitCoCreditAccount =
      unitCoClient.creditAccount.get(chargeCardAccount.unitCoCreditAccountId)

    return ChargeCardSummaryRep(
      balance = unitCoCreditAccount.balance,
      pending = unitCoCreditAccount.hold,
      date = LocalDate.now(clock),
    )
  }
}
