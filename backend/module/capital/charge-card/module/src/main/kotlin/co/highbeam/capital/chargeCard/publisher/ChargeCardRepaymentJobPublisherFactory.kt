package co.highbeam.capital.chargeCard.publisher

import co.highbeam.capital.chargeCard.listener.CHARGE_CARD_REPAYMENT_JOB_TOPIC_NAME
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.rep.UUIDSelection
import com.google.inject.Inject

internal class ChargeCardRepaymentJobPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<UUIDSelection>>() {
  override fun get(): EventPublisher<UUIDSelection> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<UUIDSelection>(
    topic = TopicConfig(CHARGE_CARD_REPAYMENT_JOB_TOPIC_NAME),
    typeLiteral = typeLiteral(),
    provider = ChargeCardRepaymentJobPublisherFactory::class,
  )
}
