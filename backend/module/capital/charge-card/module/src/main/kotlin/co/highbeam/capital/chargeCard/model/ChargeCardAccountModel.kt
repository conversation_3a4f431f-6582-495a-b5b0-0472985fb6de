package co.highbeam.capital.chargeCard.model

import co.highbeam.capital.chargeCard.rep.ChargeCardCreditTerm
import co.unit.rep.UnitCoCreditAccountRep
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.UUID

data class ChargeCardAccountModel(
  val guid: UUID,
  val businessGuid: UUID,
  val capitalAccountGuid: UUID,
  val name: String,
  val unitCoCreditAccountId: String,
  val creditTerms: ChargeCardCreditTerm,
  val minCashback: BigDecimal?,
  val maxCashback: BigDecimal?,
  val type: String,
  val status: UnitCoCreditAccountRep.Status,
  val nextRepaymentAt: ZonedDateTime,
  val latePaymentFees: BigDecimal?,
) {
  data class Updater(
    val status: UnitCoCreditAccountRep.Status? = null,
    val minCashback: BigDecimal? = null,
    val maxCashback: BigDecimal? = null,
    val nextRepaymentAt: ZonedDateTime? = null,
    val capitalAccountGuid: UUID? = null,
    val latePaymentFees: BigDecimal? = null,
  )
}
