package co.highbeam.capital.chargeCard.endpoint.sync

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.capital.chargeCard.service.ChargeCardSyncService
import co.highbeam.money.Money
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardSyncApi as Api

internal class ChargeCardSync @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: ChargeCardSyncService,
) : EndpointHandler<Api.Sync, Unit>(
  template = Api.Sync::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Sync = Api.Sync(
    businessGuid = call.getParam("businessGuid"),
    capitalAccountGuid = call.getParam("capitalAccountGuid"),
    rep = call.body(),
  )

  override suspend fun Handler.handle(endpoint: Api.Sync) {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return service.syncWithHoldAmount(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
      hold = endpoint.rep.hold ?: Money.ZERO,
    )
  }
}
