package co.highbeam.capital.chargeCard.endpoint.account

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthCapitalAccount
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.capital.chargeCard.service.ChargeCardAccountService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi as Api

internal class GetChargeCardAccount @Inject constructor(
  private val authCapitalAccount: AuthCapitalAccount.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: ChargeCardAccountService,
) : EndpointHandler<Api.Get, ChargeCardAccountRep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get(
    businessGuid = call.getParam("businessGuid"),
    capitalAccountGuid = call.getParam("capitalAccountGuid"),
  )

  override fun loggingContext(endpoint: Api.Get) = super.loggingContext(endpoint) +
    mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "capitalAccountGuid" to endpoint.capitalAccountGuid.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.Get): ChargeCardAccountRep {
    authSome(
      authCapitalAccount(
        permission = Permission.BankAccount_Read,
        capitalAccountGuid = endpoint.capitalAccountGuid,
        businessGuid = endpoint.businessGuid,
      ),
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )

    return service.getByCapitalAccountGuid(
      businessGuid = endpoint.businessGuid,
      capitalAccountGuid = endpoint.capitalAccountGuid,
    ) ?: throw ChargeCardAccountNotFound()
  }
}
