package co.highbeam.capital.chargeCard.service

import co.highbeam.capital.chargeCard.exception.ChargeCardOfferNotFound
import co.highbeam.capital.chargeCard.mapper.ChargeCardCreditTermMapper.maxCashback
import co.highbeam.capital.chargeCard.mapper.ChargeCardCreditTermMapper.maxRepaymentDays
import co.highbeam.capital.chargeCard.mapper.ChargeCardCreditTermMapper.minCashback
import co.highbeam.capital.chargeCard.model.ChargeCardAccountHistoryModel
import co.highbeam.capital.chargeCard.rep.ChargeCardOfferRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

class ChargeCardOfferServiceImpl @Inject constructor(
  private val service: ChargeCardAccountHistoryService,
  private val clock: Clock,
) : ChargeCardOfferService {
  private val logger = KotlinLogging.logger {}

  override suspend fun create(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    rep: ChargeCardOfferRep.Creator
  ): ChargeCardOfferRep {
    logger.info { "Creating initial charge card offer for businessGuid=$businessGuid, data=$rep" }

    val offer = service.create(
      businessGuid = businessGuid,
      chargeCardAccountGuid = null,
      capitalAccountGuid = capitalAccountGuid,
      effectiveAt = ZonedDateTime.now(clock),
      data = ChargeCardAccountHistoryModel.Data(
        creditTerms = rep.creditTerms,
        minCashback = rep.minCashback,
        maxCashback = rep.maxCashback,
        latePaymentFees = rep.latePaymentFees,
      ),
    )

    return ChargeCardOfferRep(
      businessGuid = offer.businessGuid,
      minCashback = offer.data.minCashback ?: offer.data.creditTerms.minCashback(),
      maxCashback = offer.data.maxCashback ?: offer.data.creditTerms.maxCashback(),
      creditTerms = offer.data.creditTerms,
      latePaymentFees = offer.data.latePaymentFees,
      maxRepaymentDays = offer.data.creditTerms.maxRepaymentDays(),
    )
  }

  override suspend fun get(businessGuid: UUID, capitalAccountGuid: UUID): ChargeCardOfferRep {
    val offer = service.getLatestOffer(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    ) ?: throw ChargeCardOfferNotFound()

    return ChargeCardOfferRep(
      businessGuid = offer.businessGuid,
      creditTerms = offer.data.creditTerms,
      minCashback = offer.data.minCashback ?: offer.data.creditTerms.minCashback(),
      maxCashback = offer.data.maxCashback ?: offer.data.creditTerms.maxCashback(),
      latePaymentFees = offer.data.latePaymentFees,
      maxRepaymentDays = offer.data.creditTerms.maxRepaymentDays(),
    )
  }
}
