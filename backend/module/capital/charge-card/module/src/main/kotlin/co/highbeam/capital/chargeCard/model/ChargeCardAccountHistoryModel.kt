package co.highbeam.capital.chargeCard.model

import co.highbeam.capital.chargeCard.rep.ChargeCardAccountHistoryRep
import co.highbeam.capital.chargeCard.rep.ChargeCardCreditTerm
import org.jdbi.v3.json.Json
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.UUID

data class ChargeCardAccountHistoryModel(
  val guid: UUID,
  val businessGuid: UUID,
  val chargeCardAccountGuid: UUID?,
  val capitalAccountGuid: UUID?,
  val effectiveAt: ZonedDateTime,
  val state: ChargeCardAccountHistoryRep.State,
  @Json val data: Data,
) {
  data class Data(
    val minCashback: BigDecimal?,
    val maxCashback: BigDecimal?,
    val creditTerms: ChargeCardCreditTerm,
    val latePaymentFees: BigDecimal?,
  )
}
