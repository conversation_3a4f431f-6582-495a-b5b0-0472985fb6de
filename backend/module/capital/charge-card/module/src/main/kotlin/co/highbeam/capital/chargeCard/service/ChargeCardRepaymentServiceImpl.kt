package co.highbeam.capital.chargeCard.service

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.api.CapitalAccountApi
import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.chargeCard.config.ChargeCardConfig
import co.highbeam.capital.chargeCard.exception.BankAccountCounterpartyIdNotSet
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.exception.ChargeCardUnableToDrawdown
import co.highbeam.capital.chargeCard.mapper.ChargeCardCreditTermMapper.rollToNextRepaymentDateUTC
import co.highbeam.capital.chargeCard.mapper.ChargeCardRepaymentMapper
import co.highbeam.capital.chargeCard.model.ChargeCardAccountModel
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentInfoRep
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.capital.chargeCard.rep.ChargeCardStatementRep
import co.highbeam.capital.chargeCard.store.ChargeCardAccountStore
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.Creator
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.Status
import co.highbeam.capital.transaction.rep.CapitalDrawdownTransactionRep.TransactionInitiator
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.business.BusinessIsNotOnboardedWithUnitYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.unit.client.UnitCoClient
import co.unit.rep.AchCounterpartyRep
import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoAchRepaymentRep
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.unitCoObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import com.slack.client.SlackMessageClient
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging
import java.time.Clock
import java.time.LocalDate
import java.time.YearMonth
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID

internal class ChargeCardRepaymentServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val notificationService: NotificationService,
  private val clock: Clock,
  private val capitalAccountClient: CapitalAccountClient,
  private val chargeCardAccountStore: ChargeCardAccountStore,
  private val chargeCardAccountService: ChargeCardAccountService,
  private val chargeCardConfig: ChargeCardConfig,
  private val lineOfCreditTransactionClient: LineOfCreditTransactionsClient,
  private val mapper: ChargeCardRepaymentMapper,
  private val unitCoClient: UnitCoClient,
  private val slackMessageClient: SlackMessageClient,
  private val featureFlagService: FeatureFlagService,
) : ChargeCardRepaymentService {
  private val logger = KotlinLogging.logger {}

  override suspend fun createRepayment(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID,
    creator: ChargeCardRepaymentRep.Creator,
    repaymentSource: ChargeCardRepaymentService.RepaymentSource,
  ): ChargeCardRepaymentRep {
    val chargeCardAccount = chargeCardAccountService.get(
      guid = chargeCardAccountGuid,
      businessGuid = businessGuid,
    ) ?: throw unprocessable(ChargeCardAccountNotFound())

    val customerAccount = bankAccountClient.request(BankAccountApi.Get(creator.bankAccountGuid))
      ?: throw unprocessable(BankAccountNotFound())

    logger.info {
      "Repaying charge card account [" +
        "businessGuid=$businessGuid, " +
        "chargeCardAccount=$chargeCardAccount," +
        "chargeCardRepaymentRep=$creator}"
    }

    val bankAccountForPayment = createCounterpartyForBankAccount(customerAccount)
    val capitalAccount = capitalAccountClient(
      CapitalAccountApi.Get(
        businessGuid = businessGuid,
        guid = chargeCardAccount.capitalAccountGuid,
      )
    ) ?: throw unprocessable(CapitalAccountNotFound())

    val repaymentAccount = getRepaymentAccount(
      chargeCardAccount = chargeCardAccount,
      repaymentSource = repaymentSource
    )

    val achResponse = unitCoClient.repayment.createAch(
      UnitCoAchRepaymentRep.Creator(
        amount = creator.amount,
        unitCounterpartyId = bankAccountForPayment.unitCoCounterpartyId
          ?: throw BankAccountCounterpartyIdNotSet(),
        description = "Repayment",
        addenda = "${capitalAccount.name} repayment",
        unitDepositAccountId = repaymentAccount,
        unitCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
        repaymentDate = ZonedDateTime.now(clock),
        idempotencyKey = creator.idempotencyKey,
      )
    )

    return mapper.toRep(achResponse)
  }

  private fun getRepaymentAccount(
    chargeCardAccount: ChargeCardAccountRep,
    repaymentSource: ChargeCardRepaymentService.RepaymentSource
  ): String {
    if (!featureFlagService.isEnabled(
        BusinessFlag.CapitalSpvFlowOfFunds,
        chargeCardAccount.businessGuid,
      )
    ) {
      return chargeCardConfig.highbeamThreadChargeCardRepaymentUnitAccountId
    }
    when (repaymentSource) {
      ChargeCardRepaymentService.RepaymentSource.Rollover ->
        return chargeCardConfig.highbeamThreadChargeCardRepaymentUnitAccountId
      ChargeCardRepaymentService.RepaymentSource.Customer -> {}
    }

    return if (chargeCardAccount.creditTerms.isCashTerm) {
      chargeCardConfig.highbeamThreadChargeCardRepaymentUnitAccountId
    } else {
      chargeCardConfig.highbeamSpvCollectionUnitAccountId
    }
  }

  private suspend fun createCounterpartyForBankAccount(
    bankAccount: BankAccountRep.Complete,
  ): BankAccountRep.Complete {
    if (bankAccount.unitCoCounterpartyId != null) {
      return bankAccount
    }

    logger.info {
      "Creating counterparty for charge card repayment with bankAccountGuid=${bankAccount.guid}"
    }

    val business = businessClient.request(BusinessApi.Get(bankAccount.businessGuid))
      ?: throw BusinessNotFound()
    val unitCoCustomerId = business.unitCoCustomerId ?: throw BusinessIsNotOnboardedWithUnitYet()

    val unitCoCounterparty = unitCoClient.payment.createAchCounterparty(
      AchCounterpartyRep.Creator(
        name = checkNotNull(business.name),
        routingNumber = bankAccount.routingNumber,
        accountNumber = bankAccount.accountNumber,
        accountType = AchCounterpartyRep.AccountType.Checking,
        customerId = unitCoCustomerId,
        permission = AchCounterpartyRep.Permissions.CreditAndDebit,
      )
    )

    return bankAccountClient.request(
      BankAccountApi.Patch(
        accountGuid = bankAccount.guid,
        rep = BankAccountRep.Updater(unitCoCounterpartyId = unitCoCounterparty.id)
      )
    ) ?: throw unprocessable(BankAccountNotFound())
  }

  override suspend fun createLastStatementBalanceRepayment(businessGuid: UUID) {
    chargeCardAccountService.getAll(businessGuid).forEach { chargeCardAccount ->
      logger.info { "Repayment for chargeCardAccount=$chargeCardAccount" }

      if (chargeCardAccount.creditTerms.isCashTerm) {
        return@forEach
      }

      if (!isTimeToRepay(chargeCardAccount)) {
        logger.info {
          "Next repayment date is in the future for charge card account [" +
            "chargeCardAccount=$chargeCardAccount]"
        }
        return@forEach
      }

      val repaymentInfo = getRepaymentInfo(chargeCardAccount.unitCoCreditAccountId)
      if (repaymentInfo.remainingAmountDue < repaymentInfo.initiatedRepayments) {
        logger.info {
          "Negative balance to repay for charge card [chargeCardAccount=$chargeCardAccount," +
            "repaymentInfo=$repaymentInfo]"
        }
        notifyNegativeBalance(chargeCardAccount = chargeCardAccount, repaymentInfo = repaymentInfo)
        return@forEach
      }

      val repaymentAmountDue = repaymentInfo.remainingAmountDue - repaymentInfo.initiatedRepayments
      if (repaymentAmountDue == Money.ZERO) {
        updateNextRepaymentDate(chargeCardAccount)
        return@forEach
      }

      val idempotencyKey = UUID.nameUUIDFromBytes(
        "charge-card-repayment-${businessGuid}-${chargeCardAccount.nextRepaymentAt}".toByteArray()
      )

      val capitalAccount = capitalAccountClient(
        CapitalAccountApi.Get(
          businessGuid = businessGuid,
          guid = chargeCardAccount.capitalAccountGuid,
        )
      ) ?: throw unprocessable(CapitalAccountNotFound())

      val repaymentAccount = getAutoPayAccount(capitalAccount, chargeCardAccount)
      val source = if (repaymentAccount.availableBalance < Balance(repaymentAmountDue.rawCents)) {
        drawdownRepaymentAmount(
          businessGuid = businessGuid,
          repaymentAmountDue = repaymentAmountDue,
          idempotencyKey = idempotencyKey,
          capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
          repaymentAccountGuid = repaymentAccount.guid,
        )
        ChargeCardRepaymentService.RepaymentSource.Rollover
      } else {
        ChargeCardRepaymentService.RepaymentSource.Customer
      }

      createRepayment(
        businessGuid = businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
        creator = ChargeCardRepaymentRep.Creator(
          amount = repaymentAmountDue,
          bankAccountGuid = repaymentAccount.guid,
          idempotencyKey = idempotencyKey,
        ),
        repaymentSource = source,
      )

      notificationService.sendAutomaticStatementBalancePaidEmail(
        bankAccount = repaymentAccount,
        businessGuid = businessGuid,
        repaymentInfo = repaymentInfo,
        capitalAccountName = capitalAccount.name,
      )

      updateNextRepaymentDate(chargeCardAccount)
    }
  }

  private suspend fun getAutoPayAccount(
    capitalAccount: CapitalAccountRep,
    chargeCardAccount: ChargeCardAccountModel
  ) = capitalAccount.details.repayment.bankAccountGuid?.let {
    bankAccountClient.request(BankAccountApi.Get(it))
  } ?: bankAccountClient.request(
    BankAccountApi.GetPrimaryBankAccountByBusinessGuid(chargeCardAccount.businessGuid)
  ) ?: throw unprocessable(BankAccountNotFound())

  @SuppressWarnings("TooGenericExceptionCaught")
  private suspend fun notifyNegativeBalance(
    chargeCardAccount: ChargeCardAccountModel,
    repaymentInfo: ChargeCardRepaymentInfoRep,
  ) {
    try {
      slackMessageClient.sendMessage(
        key = null,
        webhookPath = chargeCardConfig.chargeCardWebhookPath,
        body = mapOf(
          "text" to "NEGATIVE BALANCE ALERT: ${chargeCardAccount.businessGuid} has a negative " +
            "balance of ${repaymentInfo.initiatedRepayments - repaymentInfo.remainingAmountDue} " +
            "on account ${chargeCardAccount.unitCoCreditAccountId}"
        )
      )
    } catch (e: Exception) {
      logger.warn(e) { "Failed to send slack message for drawdown" }
    }
  }

  private fun isTimeToRepay(chargeCardAccount: ChargeCardAccountModel): Boolean {
    // Don't collect payments on the first of the month
    val now = ZonedDateTime.now(clock)
    if (now.dayOfMonth == 1) {
      return false
    }

    // Allow us to have some buffer so that we can collect it anytime during the due date
    return chargeCardAccount.nextRepaymentAt.minusHours(24) < now
  }

  private fun updateNextRepaymentDate(chargeCardAccount: ChargeCardAccountModel) {
    val nextRepaymentAt = chargeCardAccount.creditTerms.rollToNextRepaymentDateUTC(
      chargeCardAccount.nextRepaymentAt
    )

    logger.info {
      "Updating next repayment date [" +
        "chargeCardAccount=$chargeCardAccount," +
        "nextRepaymentAt=$nextRepaymentAt]"
    }
    chargeCardAccountStore.update(
      businessGuid = chargeCardAccount.businessGuid,
      guid = chargeCardAccount.guid,
      updater = ChargeCardAccountModel.Updater(
        nextRepaymentAt = nextRepaymentAt
      ),
    )
  }

  private suspend fun drawdownRepaymentAmount(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    repaymentAmountDue: Money,
    idempotencyKey: UUID,
    repaymentAccountGuid: UUID,
  ) {
    logger.info {
      "Insufficient funds while repaying charge card. Creating a drawdown for [" +
        "businessGuid=$businessGuid, " +
        "capitalAccountGuid=$capitalAccountGuid, " +
        "repaymentAmountDue=$repaymentAmountDue]"
    }

    val res = lineOfCreditTransactionClient.request(
      LineOfCreditTransactionsApi.ForceDrawdown(
        businessGuid = businessGuid,
        creditAccountGuid = capitalAccountGuid,
        rep = Creator(
          amount = repaymentAmountDue,
          bankAccountGuid = repaymentAccountGuid,
          idempotencyKey = idempotencyKey,
        ),
        transactionInitiator = TransactionInitiator.ChargeCardRepayment,
      )
    )
    if (res.status != Status.Sent) {
      logger.error { "Failed to drawdown repayment amount for businessGuid=$businessGuid" }
      throw ChargeCardUnableToDrawdown()
    }
  }

  override suspend fun getRepayments(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID,
  ): List<ChargeCardRepaymentRep> {
    val chargeCardAccount = chargeCardAccountService.get(
      guid = chargeCardAccountGuid,
      businessGuid = businessGuid,
    ) ?: throw unprocessable(ChargeCardAccountNotFound())

    return unitCoClient.transaction.list(
      customerId = null,
      accountId = chargeCardAccount.unitCoCreditAccountId,
      types = listOf("CustomerRepayment"),
      from = null,
      to = null,
      query = null,
    ).toList()
      .flatMap { unitCoObjectMapper.convertValue<DataWrapper<List<UnitCoTransactionRep>>>(it).data }
      .map { mapper.toRep(it) }
      .sortedByDescending { it.createdAt }
  }

  override suspend fun getRepaymentInfo(unitCoCreditAccountId: String): ChargeCardRepaymentInfoRep {
    val unitCoRepaymentInfo = unitCoClient.creditAccount.getRepaymentInformation(
      unitCoCreditAccountId
    )

    val chargeCardAccount = chargeCardAccountStore.getByUnitCoCreditAccountId(unitCoCreditAccountId)
      ?: throw ChargeCardAccountNotFound()

    /**
     * The unit's statement period doesn't align with the repayment date.
     * Consider the scenario: If today's date is Sept 1st and the next repayment date is Sept 15th,
     * the statement period will be Sept 1st to Sept 30th, rather than Aug 1st to Aug 31st.
     * To address this discrepancy, we compute the statement periods from the repayment date.
     */
    val today = LocalDate.now(clock)
    val firstOfThisMonth = today
      // NB(justin): We subtract here because the Unit statement closes on the first of the month
      .minusDays(1)
      .withDayOfMonth(1)
    val lastOfThisMonth = firstOfThisMonth.plusMonths(1).minusDays(1)
    val lastOfLastMonth = firstOfThisMonth.minusDays(1)
    val firstOfLastMonth = lastOfLastMonth.withDayOfMonth(1)
    val repaymentInfo = mapper.toRep(
      unitCoRepaymentInfo = unitCoRepaymentInfo,
      currentStatementPeriodStart = firstOfThisMonth,
      currentStatementPeriodEnd = lastOfThisMonth,
      previousPeriodStartDate = firstOfLastMonth,
      previousPeriodEndDate = lastOfLastMonth,
      nextRepaymentDueDate = chargeCardAccount.nextRepaymentAt
        .withZoneSameInstant(ZoneId.of("America/New_York"))
        .toLocalDate(),
    )

    logger.info {
      "Repayment info for charge card statement: [" +
        "unitCoCreditAccountId=$unitCoCreditAccountId," +
        "repaymentInfo=$repaymentInfo]"
    }
    return repaymentInfo
  }

  override suspend fun getChargeCardStatement(
    unitCoCreditAccountId: String,
    yearMonth: YearMonth,
  ): ChargeCardStatementRep {
    val threadBankZone = ZoneId.of("America/Chicago")
    val startOfMonth = yearMonth.atDay(1)
    val endOfMonth = yearMonth.atEndOfMonth()
    // NB(justin): Thread Bank's end-of-day for statements is 4:00 PM CT.
    val statementPeriodStart = yearMonth.atDay(1)
      .minusDays(1)
      .atTime(16, 0)
      .atZone(threadBankZone)

    val statementPeriodEnd = yearMonth.atEndOfMonth()
      .atTime(16, 0)
      .atZone(threadBankZone)

    val lastTransaction = unitCoClient.transaction.list(
      accountId = unitCoCreditAccountId,
      customerId = null,
      from = statementPeriodStart,
      to = statementPeriodEnd,
      sort = "-createdAt",
      limit = 1,
    ).firstOrNull()
    return ChargeCardStatementRep(
      statementAmount = lastTransaction?.balance?.takeIf { it > Balance.ZERO }?.let {
        Money.fromBalance(it)
      } ?: Money.ZERO,
      statementPeriodStart = startOfMonth,
      statementPeriodEnd = endOfMonth,
    )
  }
}
