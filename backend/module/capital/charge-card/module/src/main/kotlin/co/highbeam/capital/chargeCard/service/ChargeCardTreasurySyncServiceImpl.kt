package co.highbeam.capital.chargeCard.service

import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.capital.chargeCard.model.ChargeCardAccountModel
import co.highbeam.client.treasury.TreasuryRuleClient
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.CardAuthorizationTriggerCondition
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.unit.rep.UnitCoCreditAccountRep
import com.google.inject.Inject
import java.util.UUID

class ChargeCardTreasurySyncServiceImpl @Inject constructor(
  private val treasuryRuleClient: TreasuryRuleClient,
) : ChargeCardTreasurySyncService {
  override suspend fun create(chargeCardAccount: ChargeCardAccountModel) {
    if (!chargeCardAccount.creditTerms.isCashTerm) {
      return
    }

    setupAutoPaymentRule(chargeCardAccount)
    setupTransactionAutoPaymentRule(chargeCardAccount)
    setupTransactionBasedSyncRule(chargeCardAccount)
    setupBankCardAuthBasedSyncRule(chargeCardAccount)
  }

  private suspend fun setupAutoPaymentRule(chargeCardAccount: ChargeCardAccountModel) {
    val expectedRule = Rule.CashCardRepayment(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
    )

    val expectedTrigger = RuleTrigger.CardAuthorization(
      conditions = listOf(
        CardAuthorizationTriggerCondition.CapitalAccounts(
          capitalAccounts = listOf(chargeCardAccount.capitalAccountGuid),
        ),
      ),
      dryRun = false,
      state = TreasuryRuleRep.State.Active,
    )

    treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = paymentRuleName(chargeCardAccount.guid),
          businessGuid = chargeCardAccount.businessGuid,
          rule = expectedRule,
          trigger = expectedTrigger,
        )
      )
    )
  }

  private suspend fun setupTransactionAutoPaymentRule(chargeCardAccount: ChargeCardAccountModel) {
    val expectedRule = Rule.CashCardRepayment(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
    )

    val expectedTrigger = RuleTrigger.Transaction(
      conditions = listOf(
        TransactionTriggerCondition.CapitalAccounts(
          capitalAccounts = listOf(chargeCardAccount.capitalAccountGuid),
        ),
      ),
      dryRun = false,
      state = TreasuryRuleRep.State.Active,
    )

    treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = transactionPaymentRuleName(chargeCardAccount.guid),
          businessGuid = chargeCardAccount.businessGuid,
          rule = expectedRule,
          trigger = expectedTrigger,
        )
      )
    )
  }

  private suspend fun setupTransactionBasedSyncRule(chargeCardAccount: ChargeCardAccountModel) {
    val expectedRule = Rule.ChargeCardLimitSync(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
    )

    val expectedTrigger = RuleTrigger.Transaction(
      conditions = listOf(),
      dryRun = false,
      state = TreasuryRuleRep.State.Active,
    )

    treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = transactionLimitSyncRuleName(chargeCardAccount.guid),
          businessGuid = chargeCardAccount.businessGuid,
          rule = expectedRule,
          trigger = expectedTrigger,
        )
      )
    )
  }

  private suspend fun setupBankCardAuthBasedSyncRule(chargeCardAccount: ChargeCardAccountModel) {
    val expectedRule = Rule.ChargeCardLimitSync(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
    )

    val expectedTrigger = RuleTrigger.CardAuthorization(
      conditions = listOf(),
      dryRun = false,
      state = TreasuryRuleRep.State.Active,
    )

    treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = cardAuthorizationLimitSyncRuleName(chargeCardAccount.guid),
          businessGuid = chargeCardAccount.businessGuid,
          rule = expectedRule,
          trigger = expectedTrigger,
        )
      )
    )
  }

  override suspend fun updateStatus(chargeCardAccount: ChargeCardAccountModel) {
    if (!chargeCardAccount.creditTerms.isCashTerm) {
      return
    }

    val rules = getRules(
      businessGuid = chargeCardAccount.businessGuid,
      chargeCardAccountGuid = chargeCardAccount.guid
    )

    rules.forEach { rule ->
      val expectedState = when (chargeCardAccount.status) {
        UnitCoCreditAccountRep.Status.Frozen -> TreasuryRuleRep.State.Paused
        UnitCoCreditAccountRep.Status.Open -> TreasuryRuleRep.State.Active
        UnitCoCreditAccountRep.Status.Closed -> TreasuryRuleRep.State.Terminated
      }

      if (rule.state == expectedState) return@forEach

      treasuryRuleClient(
        TreasuryRuleApi.Update(
          ruleGuid = rule.guid,
          updater = TreasuryRuleRep.Updater(
            state = expectedState,
          ),
        )
      )
    }
  }

  private suspend fun getRules(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID
  ): List<TreasuryRuleRep> = treasuryRuleClient(TreasuryRuleApi.GetForBusiness(businessGuid))
    .filter {
      it.name in listOf(
        paymentRuleName(chargeCardAccountGuid),
        transactionPaymentRuleName(chargeCardAccountGuid),
        transactionLimitSyncRuleName(chargeCardAccountGuid),
        cardAuthorizationLimitSyncRuleName(chargeCardAccountGuid),
      )
    }

  private fun paymentRuleName(chargeCardAccountGuid: UUID): String =
    "autoManagedChargeCardRule_${chargeCardAccountGuid}"

  private fun transactionPaymentRuleName(chargeCardAccountGuid: UUID): String =
    "autoManagedChargeCardTransactionRule_${chargeCardAccountGuid}"

  private fun transactionLimitSyncRuleName(chargeCardAccountGuid: UUID): String =
    "autoManagedChargeCardTransactionSyncRule_${chargeCardAccountGuid}"

  private fun cardAuthorizationLimitSyncRuleName(chargeCardAccountGuid: UUID): String =
    "autoManagedChargeCardAuthorizationSyncRule_${chargeCardAccountGuid}"
}
