package co.highbeam.job.chargeCardCreditAccount

import co.highbeam.capital.chargeCard.service.ChargeCardRepaymentService
import co.highbeam.job.HighbeamJob
import co.highbeam.rep.UUIDSelection
import com.google.inject.Inject
import mu.KotlinLogging
import co.highbeam.job.chargeCardCreditAccount.ChargeCardAutomaticRepaymentJob as Job

class ChargeCardAutomaticRepaymentJob @Inject constructor(
  private val chargeCardRepaymentService: ChargeCardRepaymentService
) : HighbeamJob<Job.Params>() {
  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  private val logger = KotlinLogging.logger {}

  data class Params(
    val businessGuids: UUIDSelection,
  ) : HighbeamJob.Params()

  override suspend fun execute(params: Params) {
    logger.info {
      "Running automatic charge card payment job for " +
        "businessGuids=${params.businessGuids}"
    }

    when (params.businessGuids) {
      is UUIDSelection.All -> TODO("Implement fetch all charge card accounts first.")
      is UUIDSelection.Explicit -> {
        params.businessGuids.guids.forEach {
          chargeCardRepaymentService.createLastStatementBalanceRepayment(it)
        }
      }
      else -> error("Unsupported UUIDSelection: ${params.businessGuids}")
    }
  }
}
