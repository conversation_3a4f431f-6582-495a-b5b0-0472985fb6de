package co.highbeam.capital.chargeCard.endpoint.repayment

import co.highbeam.auth.permissions.Permission
import co.highbeam.capital.chargeCard.auth.AuthChargeCardAccount
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentInfoRep
import co.highbeam.capital.chargeCard.service.ChargeCardAccountService
import co.highbeam.capital.chargeCard.service.ChargeCardRepaymentService
import co.highbeam.exception.unprocessable
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi as Api

internal class GetChargeCardRepaymentInfo @Inject constructor(
  private val authChargeCardAccount: AuthChargeCardAccount.Provider,
  private val accountService: ChargeCardAccountService,
  private val service: ChargeCardRepaymentService,
) : EndpointHandler<Api.GetInfo, ChargeCardRepaymentInfoRep>(
  template = Api.GetInfo::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetInfo = Api.GetInfo(
    businessGuid = call.getParam("businessGuid"),
    chargeCardAccountGuid = call.getParam("chargeCardAccountGuid"),
  )

  override fun loggingContext(endpoint: Api.GetInfo) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.businessGuid.toString(),
    "chargeCardAccountGuid" to endpoint.chargeCardAccountGuid.toString(),
  )

  override suspend fun Handler.handle(endpoint: Api.GetInfo): ChargeCardRepaymentInfoRep {
    auth(
      authChargeCardAccount(
        permission = Permission.BankAccount_Read,
        businessGuid = endpoint.businessGuid,
        chargeCardAccountGuid = endpoint.chargeCardAccountGuid,
      )
    )
    val account = accountService.get(
      guid = endpoint.chargeCardAccountGuid,
      businessGuid = endpoint.businessGuid,
    ) ?: throw unprocessable(ChargeCardAccountNotFound())

    return service.getRepaymentInfo(account.unitCoCreditAccountId)
  }
}
