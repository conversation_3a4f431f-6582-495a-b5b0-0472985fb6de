package co.highbeam.chargeCard.listener

import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.chargeCard.listener.ChargeCardApproachingLimitListener
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.capital.chargeCard.rep.ChargeCardCreditTerm
import co.highbeam.capital.transaction.CapitalTransactionSummaryClient
import co.highbeam.capital.transaction.FakeCapitalTransactionSummaryClient
import co.highbeam.capital.transaction.rep.CapitalTransactionRep
import co.highbeam.chargeCard.testing.ChargeCardFeatureIntegrationTest
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.email.EmailService
import co.highbeam.money.Balance
import co.highbeam.server.Server
import co.highbeam.util.time.inUTC
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import io.mockk.verify
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

internal class ChargeCardApproachingLimitListenerTest(
  server: Server<*>,
) : ChargeCardFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private val unitCreditAccountId = "unitCreditAccountId"
  private val transactionSummaryClient = get<CapitalTransactionSummaryClient>()
    as FakeCapitalTransactionSummaryClient

  @Test
  fun `card auth with 80% utilization`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-80_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    val key = emailIdempotencyKey(chargeCardAccount, ZonedDateTime.now(clock).inUTC())
    coEvery {
      get<EmailService>().sync(key = key, block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 1) {
      get<EmailService>().sync(key = key, block = any())
    }
  }

  @Test
  fun `card auth with 79% utilization`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-79_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    coEvery {
      get<EmailService>().sync(key = any(), block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 0) {
      get<EmailService>().sync(key = any(), block = any())
    }
  }

  @Test
  fun `card auth with 95% utilization and Cash Card`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
      creditTerms = ChargeCardCreditTerm.SandboxCash,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-95_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    val key = emailIdempotencyKey(chargeCardAccount, ZonedDateTime.now(clock).inUTC())
    coEvery {
      get<EmailService>().sync(key = key, block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 1) {
      get<EmailService>().sync(key = key, block = any())
    }
  }

  @Test
  fun `card auth with 94% utilization and Cash Card`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
      creditTerms = ChargeCardCreditTerm.SandboxCash,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-79_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    coEvery {
      get<EmailService>().sync(key = any(), block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 0) {
      get<EmailService>().sync(key = any(), block = any())
    }
  }

  @Test
  fun `two card auths`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-80_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    val key = emailIdempotencyKey(chargeCardAccount, ZonedDateTime.now(clock).inUTC())
    coEvery {
      get<EmailService>().sync(key = key, block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 2) {
      get<EmailService>().sync(key = key, block = any())
    }
  }

  @Test
  fun `two card auths with repayment in-between`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
      unitCreditAccountId = unitCreditAccountId,
    )

    transactionSummaryClient.set(
      capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = Balance.fromCents(-80_000_00),
    )

    mockUnitCoTransactionClient("transaction/charge_card_repayment_transaction.json")
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf()
    val key = emailIdempotencyKey(chargeCardAccount, ZonedDateTime.now(clock).inUTC())
    coEvery {
      get<EmailService>().sync(key = any(), block = any())
    } returns Unit

    val unitCardAuthWebhook = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitWebhooks/cardAuthorization.json"),
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.GetTransactions(
          businessGuid = businessGuid,
          since = LocalDate.now(clock).withDayOfMonth(1),
          until = LocalDate.now(clock),
        )
      )
    } returns listOf(
      CapitalTransactionRep(
        capitalAccountGuid = chargeCardAccount.capitalAccountGuid,
        businessGuid = businessGuid,
        unitCoId = unitCreditAccountId,
        amount = Balance.fromCents(10_000_00),
        summary = "Some summary",
        date = ZonedDateTime.now(clock).plusDays(1).inUTC(),
        unitCoDepositAccountId = "123124",
        unitCoCounterpartyDepositAccountId = null,
        type = null,
      )
    )
    get<ChargeCardApproachingLimitListener>().onReceive(unitCardAuthWebhook)

    verify(exactly = 1) {
      get<EmailService>().sync(key = key, block = any())
    }
    verify(exactly = 1) {
      get<EmailService>().sync(
        key = emailIdempotencyKey(
          chargeCardAccount = chargeCardAccount,
          date = ZonedDateTime.now(clock).plusDays(1).inUTC(),
        ),
        block = any()
      )
    }
  }

  private fun emailIdempotencyKey(
    chargeCardAccount: ChargeCardAccountRep,
    date: ZonedDateTime,
  ) = "ChargeCardApproachingLimit|${chargeCardAccount.capitalAccountGuid}-$date"
}
