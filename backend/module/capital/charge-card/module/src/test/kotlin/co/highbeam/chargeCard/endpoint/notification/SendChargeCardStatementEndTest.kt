package co.highbeam.chargeCard.endpoint.notification

import co.highbeam.capital.chargeCard.api.ChargeCardNotificationApi
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.chargeCard.testing.ChargeCardFeatureIntegrationTest
import co.highbeam.email.EmailService
import co.highbeam.server.Server
import io.mockk.coEvery
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.YearMonth
import java.util.UUID
import java.util.concurrent.TimeUnit

internal class SendChargeCardStatementEndTest(
  server: Server<*>,
) : ChargeCardFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @BeforeEach
  fun beforeEach() {
    coEvery {
      get<EmailService>().sync(key = any(), block = any())
    } returns Unit
    mockUnitCoTransactionsList("transaction/charge_card_single_transaction.json")
  }

  @Test
  fun `statement created sends an email`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
    )

    chargeCardNotificationClient(
      ChargeCardNotificationApi.SendStatementEnd(
        businessGuid = businessGuid,
        yearMonth = YearMonth.now(clock).minusMonths(1),
      )
    )

    verify(exactly = 1) {
      get<EmailService>().sync(
        key = emailIdempotencyKey(chargeCardAccount, YearMonth.now(clock).minusMonths(1)),
        block = any()
      )
    }
  }

  @Test
  fun `statement created sends an email idempotent`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(
      businessGuid = businessGuid,
    )

    chargeCardNotificationClient(
      ChargeCardNotificationApi.SendStatementEnd(
        businessGuid = businessGuid,
        yearMonth = YearMonth.now(clock).minusMonths(1),
      )
    )

    verify(exactly = 1) {
      get<EmailService>().sync(
        key = emailIdempotencyKey(chargeCardAccount, YearMonth.now(clock).minusMonths(1)),
        block = any()
      )
    }

    // Send a duplicate
    chargeCardNotificationClient(
      ChargeCardNotificationApi.SendStatementEnd(
        businessGuid = businessGuid,
        yearMonth = YearMonth.now(clock).minusMonths(1),
      )
    )

    // Go to next month
    clock.add(45, TimeUnit.DAYS)

    chargeCardNotificationClient(
      ChargeCardNotificationApi.SendStatementEnd(
        businessGuid = businessGuid,
        yearMonth = YearMonth.now(clock).minusMonths(1),
      )
    )

    verify(exactly = 1) {
      get<EmailService>().sync(
        key = emailIdempotencyKey(chargeCardAccount, YearMonth.now(clock).minusMonths(1)),
        block = any()
      )
    }

    verify(exactly = 2) {
      get<EmailService>().sync(
        key = emailIdempotencyKey(chargeCardAccount, YearMonth.now(clock).minusMonths(2)),
        block = any()
      )
    }
  }

  private fun emailIdempotencyKey(
    chargeCardAccount: ChargeCardAccountRep,
    yearMonth: YearMonth,
  ) = "ChargeCardStatementEnd|${chargeCardAccount.capitalAccountGuid}-$yearMonth"
}
