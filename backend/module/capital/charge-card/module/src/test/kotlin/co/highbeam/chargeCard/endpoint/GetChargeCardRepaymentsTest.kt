package co.highbeam.chargeCard.endpoint

import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.chargeCard.testing.ChargeCardFeatureIntegrationTest
import co.highbeam.exception.unprocessable
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.util.time.inUTC
import co.unit.client.UnitCoClient
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import kotlinx.coroutines.flow.flowOf
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class GetChargeCardRepaymentsTest(
  server: Server<*>,
) : ChargeCardFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @Test
  fun `missing credit account returns unprocessable`() = integrationTest {
    assertHighbeamException {
      chargeCardTransactionClient(
        ChargeCardRepaymentApi.Get(
          businessGuid = businessGuid,
          chargeCardAccountGuid = UUID.randomUUID(),
        )
      )
    }.isHighbeamException(unprocessable(ChargeCardAccountNotFound()))
  }

  @Test
  fun `returns repayment reps`() = integrationTest {
    val primaryBankAccount = bankAccountRep(
      businessGuid = businessGuid,
      unitCoDepositAccountId = "1111",
      isPrimary = true,
      name = "Primary",
      highbeamType = BankAccountRep.Type.DepositAccount,
      availableBalance = Balance(100000),
      unitCoCounterpartyId = "67890",
    )
    mockGetPrimaryBankAccountByBusinessGuid(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount
    )
    val chargeCardCreditAccount = setupChargeCardCreditAccount(businessGuid)

    mockUnitTransactions(chargeCardCreditAccount.unitCoCreditAccountId)

    assertThat(
      chargeCardTransactionClient(
        ChargeCardRepaymentApi.Get(
          businessGuid = businessGuid,
          chargeCardAccountGuid = chargeCardCreditAccount.guid,
        )
      )
    ).containsExactlyInAnyOrder(
      ChargeCardRepaymentRep(
        unitCoCreditAccountId = "1668166",
        unitCoTransactionId = "5065800",
        amount = Money(1234),
        balance = Balance(101545),
        createdAt = ZonedDateTime.now(clock).inUTC(),
      )
    )
  }

  private fun mockUnitTransactions(unitCreditAccountId: String) {
    coEvery {
      get<UnitCoClient>().transaction.list(
        customerId = null,
        accountId = unitCreditAccountId,
        query = null,
        from = any(),
        to = any(),
        tags = null,
        types = listOf("CustomerRepayment"),
        fromAmount = null,
        toAmount = null,
      )
    } returns flowOf(
      objectMapper.readValue(
        Resources.getResource("transaction/charge_card_repayment_transaction.json")
      )
    )
  }
}
