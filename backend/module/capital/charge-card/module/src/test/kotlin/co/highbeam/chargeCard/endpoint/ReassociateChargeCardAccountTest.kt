package co.highbeam.chargeCard.endpoint

import co.highbeam.capital.account.exception.CapitalAccountNotFound
import co.highbeam.capital.chargeCard.api.ChargeCardSyncApi
import co.highbeam.capital.chargeCard.exception.ChargeCardAccountNotFound
import co.highbeam.capital.chargeCard.rep.ChargeCardSyncRep
import co.highbeam.chargeCard.testing.ChargeCardFeatureIntegrationTest
import co.highbeam.exception.unprocessable
import co.highbeam.money.Money
import co.highbeam.server.Server
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoCreditAccountRep
import io.mockk.coVerify
import org.junit.jupiter.api.Test
import java.util.UUID

internal class ReassociateChargeCardAccountTest(
  server: Server<*>,
) : ChargeCardFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @Test
  fun `update limit`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(businessGuid)
    mockUnitUpdateCreditAccount(
      businessGuid = businessGuid,
      updatedCreditLimit = Money(1_00L),
      unitCoCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
    )

    val anotherCapitalAccount = mockLineOfCreditGet(businessGuid, limit = 1_00L)
    chargeCardSyncClient(
      ChargeCardSyncApi.Reassociate(
        businessGuid = businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
        rep = ChargeCardSyncRep.Reassociate(
          capitalAccountGuid = anotherCapitalAccount,
        )
      )
    )

    coVerify {
      get<UnitCoClient>().creditAccount.update(
        UnitCoCreditAccountRep.Updater(
          unitCoCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
          creditLimit = Money(1_00L),
        )
      )
    }
  }

  @Test
  fun `change idempotent`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(businessGuid)
    mockUnitUpdateCreditAccount(
      businessGuid = businessGuid,
      updatedCreditLimit = Money(1_00L),
      unitCoCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
    )

    val anotherCapitalAccount = mockLineOfCreditGet(businessGuid, limit = 1_00L)
    chargeCardSyncClient(
      ChargeCardSyncApi.Reassociate(
        businessGuid = businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
        rep = ChargeCardSyncRep.Reassociate(
          capitalAccountGuid = anotherCapitalAccount,
        )
      )
    )

    coVerify {
      get<UnitCoClient>().creditAccount.update(
        UnitCoCreditAccountRep.Updater(
          unitCoCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
          creditLimit = Money(1_00L),
        )
      )
    }

    chargeCardSyncClient(
      ChargeCardSyncApi.Reassociate(
        businessGuid = businessGuid,
        chargeCardAccountGuid = chargeCardAccount.guid,
        rep = ChargeCardSyncRep.Reassociate(
          capitalAccountGuid = anotherCapitalAccount,
        )
      )
    )
  }

  @Test
  fun `charge card account doesn't exist`() = integrationTest {
    val anotherCapitalAccount = mockLineOfCreditGet(businessGuid, limit = 1_00L)
    assertHighbeamException {
      chargeCardSyncClient(
        ChargeCardSyncApi.Reassociate(
          businessGuid = businessGuid,
          chargeCardAccountGuid = UUID.randomUUID(),
          rep = ChargeCardSyncRep.Reassociate(
            capitalAccountGuid = anotherCapitalAccount,
          )
        )
      )
    }.isHighbeamException(unprocessable(ChargeCardAccountNotFound()))
  }

  @Test
  fun `capital account doesnt exist`() = integrationTest {
    val chargeCardAccount = setupChargeCardCreditAccount(businessGuid)
    mockUnitUpdateCreditAccount(
      businessGuid = businessGuid,
      updatedCreditLimit = Money(1_00L),
      unitCoCreditAccountId = chargeCardAccount.unitCoCreditAccountId,
    )

    assertHighbeamException {
      chargeCardSyncClient(
        ChargeCardSyncApi.Reassociate(
          businessGuid = businessGuid,
          chargeCardAccountGuid = chargeCardAccount.guid,
          rep = ChargeCardSyncRep.Reassociate(
            capitalAccountGuid = UUID.randomUUID(),
          )
        )
      )
    }.isHighbeamException(unprocessable(CapitalAccountNotFound()))
  }
}

