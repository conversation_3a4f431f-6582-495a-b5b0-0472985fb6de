package co.highbeam.client.user

import co.highbeam.api.user.UserApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.users.USER_FEATURE
import co.highbeam.rep.user.UserRep
import com.google.inject.Inject
import com.google.inject.name.Named

class UserClient @Inject constructor(
  @Named(USER_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: UserApi.Create): UserRep.Complete =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: UserApi.BatchGet): List<UserRep.Complete> =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: UserApi.Get): UserRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: UserApi.GetByEmailAddress): UserRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: User<PERSON><PERSON>.UpdateByEmailAddress): UserRep.Complete? =
    httpClient.request(endpoint).readValue()
}
