package co.highbeam.api.user

import co.highbeam.rep.user.UserRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object UserApi {
  data class Create(val creator: UserRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/users",
    body = creator,
  )

  data class BatchGet(val rep: UserRep.BatchGet) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/batch-get-users",
    body = rep,
  )

  data class Get(val userGuid: UUID) : Endpoint("/users/$userGuid")

  data class GetByEmailAddress(val emailAddress: String) : Endpoint(
    path = "/users",
    qp = mapOf("emailAddress" to listOf(emailAddress)),
  )

  data class UpdateByEmailAddress(val emailAddress: String, val update: UserRep.Updater) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/users",
    qp = mapOf("emailAddress" to listOf(emailAddress)),
    body = update,
  )
}
