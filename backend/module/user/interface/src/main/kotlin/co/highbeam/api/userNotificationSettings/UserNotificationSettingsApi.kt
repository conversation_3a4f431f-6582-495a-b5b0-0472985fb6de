package co.highbeam.api.userNotificationSettings

import co.highbeam.rep.userNotificationSettings.UserNotificationSettingsRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object UserNotificationSettingsApi {
  data class Get(val userGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/users/$userGuid/notification-settings",
  )

  data class Update(val userGuid: UUID, val update: UserNotificationSettingsRep.Updater) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/users/$userGuid/notification-settings",
    body = update,
  )
}
