package co.highbeam.rep.user

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class UserRepTest {
  @Test
  fun `initials - no name`() {
    val user = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = null,
      lastName = null,
      profilePhotoUrl = null,
    )
    assertThat(user.initials).isEqualTo("J")
  }

  @Test
  fun `initials - with name`() {
    val user = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    assertThat(user.initials).isEqualTo("JH")
  }
}
