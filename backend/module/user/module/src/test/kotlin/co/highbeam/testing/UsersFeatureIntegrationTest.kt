package co.highbeam.testing

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.user.UserClient
import co.highbeam.client.user.UserExistenceClient
import co.highbeam.client.userNotificationSettings.UserNotificationSettingsClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.UserFeatureTestConfig
import co.highbeam.event.FakeEventFeature
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.feature.users.UserFeature
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.business.BusinessRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.unit.client.UnitCoClient
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import java.util.UUID

@ExtendWith(UsersFeatureIntegrationTest.Extension::class)
internal abstract class UsersFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  internal class Extension : AbstractIntegrationTestExtension() {
    private object MockFeature : AbstractMockFeature() {
      override fun bind() {
        mock(BusinessClient::class)
        mock(BusinessMemberClient::class)
        mock(FeatureFlagService::class)
        mock(UnitCoClient::class)
      }
    }

    private companion object {
      val sharedState = SharedState()
      val config: UserFeatureTestConfig = ConfigLoader.load("users/test")
      val eventFeature = FakeEventFeature
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "users",
      )
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<UserFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            eventFeature,
            sqlFeature,
            MockFeature,
            UserFeature())
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val userExistenceClient: UserExistenceClient by lazy {
    UserExistenceClient(httpClient)
  }

  val userClient: UserClient by lazy {
    UserClient(httpClient)
  }

  val userNotificationSettingsClient: UserNotificationSettingsClient by lazy {
    UserNotificationSettingsClient(httpClient)
  }

  protected fun mockBusiness(
    businessGuid: UUID,
    unitCoCustomerId: String = UUID.randomUUID().toString(),
    ownerUserGuid: UUID = UUID.randomUUID(),
  ) {
    mockBusiness(
      businessGuid = businessGuid,
      business = mockk {
        every { <EMAIL> } returns unitCoCustomerId
        every { <EMAIL> } returns ownerUserGuid
      },
    )
  }

  private fun mockBusiness(businessGuid: UUID, business: BusinessRep.Complete?) {
    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns business
  }
}
