package co.highbeam.listener.users

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.exception.user.UserNotFound
import co.highbeam.model.unitCoEvent.UnitCoCustomerCreatedEventModel
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.service.user.UserService
import co.unit.client.UnitCoClient
import co.unit.rep.ApplicationRep
import co.unit.rep.PhoneRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.common.annotations.VisibleForTesting
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging
import java.time.LocalDate
import java.util.UUID

@Singleton
internal class UnitCoCustomerCreatedWebhookListener @Inject constructor(
  factory: EventListenerFactory,
  private val objectMapper: ObjectMapper,
  private val businessClient: BusinessClient,
  private val businessMemberClient: BusinessMemberClient,
  private val unitCoClient: UnitCoClient,
  private val userService: UserService,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig("unit-co-webhook"),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "users",
        filter = "attributes.type = \"customer.created\"",
      ),
      clazz = JsonNode::class.java,
      listener = ::onReceive
    )
  }

  @VisibleForTesting
  suspend fun onReceive(json: JsonNode) {
    logger.info { "Received event: $json." }
    val event = readEvent(json)
    updateUserAndBusinessMember(event)
  }

  @Suppress("LongMethod")
  private suspend fun updateUserAndBusinessMember(event: UnitCoCustomerCreatedEventModel) {
    when (val application = getApplication(event)) {
      is ApplicationRep.Complete.Business -> {
        // By combining all user updates into a map before running updates rather than just looping
        // through them all, we avoid duplicate updates to any users that show up multiple times
        // (such as a beneficial owner and also as the officer).
        val usersToUpdate = buildMap<String, UpdateUserAndBusinessMember> {
          application.beneficialOwners.map { beneficialOwner ->
            put(beneficialOwner.emailAddress, UpdateUserAndBusinessMember(
              businessGuid = application.businessGuid,
              emailAddress = beneficialOwner.emailAddress,
              firstName = beneficialOwner.firstName,
              lastName = beneficialOwner.lastName,
              phoneNumber = beneficialOwner.phoneNumber,
              dateOfBirth = beneficialOwner.dateOfBirth
            ))
          }
          application.contact.let { contact ->
            put(contact.email, UpdateUserAndBusinessMember(
              businessGuid = application.businessGuid,
              emailAddress = contact.email,
              firstName = contact.fullName?.first,
              lastName = contact.fullName?.last,
              phoneNumber = contact.phone,
              dateOfBirth = null
            ))
          }
          application.officer.let { officer ->
            put(officer.emailAddress, UpdateUserAndBusinessMember(
              businessGuid = application.businessGuid,
              emailAddress = officer.emailAddress,
              firstName = officer.firstName,
              lastName = officer.lastName,
              phoneNumber = officer.phoneNumber,
              dateOfBirth = officer.dateOfBirth
            ))
          }
        }
        val hasUpdated = usersToUpdate.map { update ->
          update(update.value)
        }.any { it == true }

        if (!hasUpdated) {
          val ownerUser = getOwnerUser(event.businessGuid)
          logger.info {
            "Email mismatch, fallback to updating owner User: ${ownerUser.guid} with contact info"
          }
          application.contact.let { contact ->
            update(
              UpdateUserAndBusinessMember(
                businessGuid = application.businessGuid,
                emailAddress = ownerUser.emailAddress,
                firstName = contact.fullName?.first,
                lastName = contact.fullName?.last,
                phoneNumber = contact.phone,
                dateOfBirth = null
              )
            )
          }
        }
      }

      is ApplicationRep.Complete.Individual -> {
        val hasUpdated = update(
          UpdateUserAndBusinessMember(
            businessGuid = application.businessGuid,
            emailAddress = application.emailAddress,
            firstName = application.firstName,
            lastName = application.lastName,
            phoneNumber = application.phone,
            dateOfBirth = application.dateOfBirth
          )
        )

        if (!hasUpdated) {
          val ownerUser = getOwnerUser(event.businessGuid)
          logger.info {
            "Email mismatch, fallback to updating owner User: ${ownerUser.guid}."
          }
          update(
            UpdateUserAndBusinessMember(
              businessGuid = application.businessGuid,
              emailAddress = ownerUser.emailAddress,
              firstName = application.firstName,
              lastName = application.lastName,
              phoneNumber = application.phone,
              dateOfBirth = application.dateOfBirth
            )
          )
        }
      }
    }
  }

  data class UpdateUserAndBusinessMember(
    val emailAddress: String,
    val businessGuid: UUID,
    val firstName: String?,
    val lastName: String?,
    val phoneNumber: PhoneRep?,
    val dateOfBirth: LocalDate?,
  )

  @SuppressWarnings("SwallowedException")
  private suspend fun update(
    update: UpdateUserAndBusinessMember,
  ): Boolean {
    try {
      val user = userService.updateByEmailAddress(
        update.emailAddress,
        UserRep.Updater(
          firstName = update.firstName,
          lastName = update.lastName,
        )
      )
      val businessMember = businessMemberClient.request(
        BusinessMemberApi.UpdateByUserGuid(
          businessGuid = update.businessGuid,
          userGuid = user.guid,
          BusinessMemberRep.Updater(
            firstName = update.firstName,
            lastName = update.lastName,
            phoneNumber = update.phoneNumber?.toE164(),
            dateOfBirth = update.dateOfBirth,
          )
        )
      )
      if (businessMember == null) {
        logger.info {
          "Tried to update business member with email address $update.emailAddress," +
            " but the business member did not exist."
        }
        return false
      }
      return user != null && businessMember != null
    } catch (e: UserNotFound) {
      // This is fine and should be suppressed because not all email addresses associated with the
      // company will have corresponding Users in Highbeam.
      logger.info {
        "Tried to update user with email address $update.emailAddress, but the user did not exist."
      }
      return false
    }
  }

  private fun readEvent(eventJson: JsonNode): UnitCoCustomerCreatedEventModel =
    objectMapper.convertValue(eventJson)

  private suspend fun getApplication(event: UnitCoCustomerCreatedEventModel) =
    checkNotNull(
      value = unitCoClient.application.get(event.applicationId),
      lazyMessage = {
        "When a customer created event is received, the application should never be missing."
      }
    )

  private suspend fun getOwnerUser(businessGuid: UUID): UserRep.Complete {
    val business = checkNotNull(businessClient.request(BusinessApi.Get(businessGuid)))
    return checkNotNull(userService.get(business.ownerUserGuid))
  }
}
