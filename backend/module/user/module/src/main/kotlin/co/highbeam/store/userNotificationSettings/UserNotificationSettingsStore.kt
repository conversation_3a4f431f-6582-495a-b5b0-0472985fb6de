package co.highbeam.store.userNotificationSettings

import co.highbeam.rep.userNotificationSettings.UserNotificationSettingsRep
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
internal class UserNotificationSettingsStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(settings: UserNotificationSettingsRep): UserNotificationSettingsRep =
    transaction { handle ->
      val query = handle.createQuery(sqlResource(
        "store/userNotificationSettings/create.sql"))
      query.bindKotlin(settings)
      return@transaction query.mapTo(UserNotificationSettingsRep::class.java).single()
    }

  fun get(userGuid: UUID): UserNotificationSettingsRep? =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        "store/userNotificationSettings/getByUser.sql"))
      query.bind("userGuid", userGuid)
      return@handle query.mapTo(UserNotificationSettingsRep::class.java).singleNullOrThrow()
    }

  fun update(
    userGuid: UUID,
    update: UserNotificationSettingsRep.Updater,
  ): UserNotificationSettingsRep? =
    transaction { handle ->
      val query = handle.createQuery(sqlResource(
        "store/userNotificationSettings/updateByUser.sql"))
      query.bind("userGuid", userGuid)
      query.bindKotlin(update)
      return@transaction query.mapTo(UserNotificationSettingsRep::class.java).singleNullOrThrow()
    }
}
