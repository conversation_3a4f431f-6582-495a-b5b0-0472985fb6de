package co.highbeam.endpoint.user

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.user.UserService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.user.UserApi as Api
import co.highbeam.rep.user.UserRep as Rep

internal class BatchGetUsers @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val userService: UserService,
) : EndpointHandler<Api.BatchGet, List<Rep.Complete>>(
  template = Api.BatchGet::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.BatchGet =
    Api.BatchGet(rep = call.body())

  override suspend fun Handler.handle(endpoint: Api.BatchGet): List<Rep.Complete> {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    return userService.batchGet(endpoint.rep.guids)
  }
}
