package co.highbeam.endpoint.userNotificationSettings

import co.highbeam.auth.auth.AuthUser
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userNotificationSettings.UserNotificationSettingsService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userNotificationSettings.UserNotificationSettingsApi as Api
import co.highbeam.rep.userNotificationSettings.UserNotificationSettingsRep as Rep

internal class UpdateUserNotificationSettingsByUser @Inject constructor(
  private val authUser: AuthUser.Provider,
  private val notificationSettingsService: UserNotificationSettingsService,
) : EndpointHandler<Api.Update, Rep>(
  template = Api.Update::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Update =
    Api.Update(userGuid = call.getParam("userGuid"), update = call.body())

  override fun loggingContext(endpoint: Api.Update) =
    super.loggingContext(endpoint) + mapOf("userGuid" to endpoint.userGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Update): Rep {
    auth(authUser(endpoint.userGuid))
    return notificationSettingsService.update(
      userGuid = endpoint.userGuid,
      update = endpoint.update,
    )
  }
}
