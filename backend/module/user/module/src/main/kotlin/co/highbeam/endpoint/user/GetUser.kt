package co.highbeam.endpoint.user

import co.highbeam.auth.auth.AuthUser
import co.highbeam.exception.user.UserNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.user.UserService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.user.UserApi as Api
import co.highbeam.rep.user.UserRep as Rep

internal class GetUser @Inject constructor(
  private val authUser: AuthUser.Provider,
  private val userService: UserService,
) : EndpointHandler<Api.Get, Rep.Complete>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(userGuid = call.getParam("userGuid"))

  override fun loggingContext(endpoint: Api.Get) = super.loggingContext(endpoint) +
    mapOf("userGuid" to endpoint.userGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Get): Rep.Complete {
    auth(authUser(endpoint.userGuid))
    return userService.get(endpoint.userGuid) ?: throw UserNotFound()
  }
}
