package co.highbeam.endpoint.user

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.user.UserService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import mu.KotlinLogging
import co.highbeam.api.user.UserApi as Api
import co.highbeam.rep.user.UserRep as Rep

internal class UpdateUserByEmailAddress @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val userService: UserService,
) : EndpointHandler<Api.UpdateByEmailAddress, Rep.Complete>(
  template = Api.UpdateByEmailAddress::class.template(),
) {
  private val logger = KotlinLogging.logger {}

  override suspend fun endpoint(call: ApplicationCall): Api.UpdateByEmailAddress =
    Api.UpdateByEmailAddress(
      emailAddress = call.getParam("emailAddress"),
      update = call.body(),
    )

  override fun loggingContext(endpoint: Api.UpdateByEmailAddress) = super.loggingContext(endpoint) +
    mapOf("userEmailAddress" to endpoint.emailAddress)

  override suspend fun Handler.handle(endpoint: Api.UpdateByEmailAddress): Rep.Complete {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    logger.info { "Updating user with email address: ${endpoint.emailAddress} " +
      "Update: ${endpoint.update}" }
    return userService.updateByEmailAddress(
      emailAddress = endpoint.emailAddress,
      update = endpoint.update,
    )
  }
}
