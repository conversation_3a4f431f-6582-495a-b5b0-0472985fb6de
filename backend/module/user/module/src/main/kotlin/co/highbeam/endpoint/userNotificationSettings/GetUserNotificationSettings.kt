package co.highbeam.endpoint.userNotificationSettings

import co.highbeam.auth.auth.AuthUser
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userNotificationSettings.UserNotificationSettingsService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userNotificationSettings.UserNotificationSettingsApi as Api
import co.highbeam.rep.userNotificationSettings.UserNotificationSettingsRep as Rep

internal class GetUserNotificationSettings @Inject constructor(
  private val authUser: AuthUser.Provider,
  private val notificationSettingsService: UserNotificationSettingsService,
) : EndpointHandler<Api.Get, Rep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(userGuid = call.getParam("userGuid"))

  override fun loggingContext(endpoint: Api.Get) =
    super.loggingContext(endpoint) + mapOf("userGuid" to endpoint.userGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Get): Rep {
    auth(authUser(endpoint.userGuid))
    return notificationSettingsService.get(endpoint.userGuid)
  }
}
