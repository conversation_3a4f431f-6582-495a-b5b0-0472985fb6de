package co.highbeam.client.treasury

import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.treasury.TREASURY_FEATURE
import com.google.inject.Inject
import com.google.inject.name.Named

class TreasuryTaskClient @Inject constructor(
  @Named(TREASURY_FEATURE) private val httpClient: HttpClient,
) {
  suspend operator fun invoke(endpoint: TreasuryTaskApi.Execute) =
    httpClient.request(endpoint)
}
