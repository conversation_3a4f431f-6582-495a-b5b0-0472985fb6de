package co.highbeam.client.treasury

import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.treasury.TREASURY_FEATURE
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import com.google.inject.Inject
import com.google.inject.name.Named

class TreasuryTransactionClient @Inject constructor(
  @Named(TREASURY_FEATURE) private val httpClient: HttpClient,
) {
  suspend operator fun invoke(
    endpoint: TreasuryTransactionApi.GetForRule,
  ): List<TreasuryTransactionRep> = httpClient.request(endpoint).readValue()

  suspend operator fun invoke(
    endpoint: TreasuryTransactionApi.GetForBusiness,
  ): List<TreasuryTransactionRep> = httpClient.request(endpoint).readValue()
}
