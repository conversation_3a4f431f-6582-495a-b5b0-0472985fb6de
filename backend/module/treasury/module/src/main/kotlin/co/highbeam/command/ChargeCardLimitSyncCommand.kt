package co.highbeam.command

import co.highbeam.capital.chargeCard.ChargeCardSyncClient
import co.highbeam.capital.chargeCard.api.ChargeCardSyncApi
import co.highbeam.capital.chargeCard.rep.ChargeCardSyncRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.ChargeCardLimitSync as Config

class ChargeCardLimitSyncCommand private constructor(
  private val chargeCardSyncClient: ChargeCardSyncClient,
  private val capitalAccountGuid: UUID,
) : TreasuryCommand {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(
    ruleGuid: UUID,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ): TreasuryRep.Complete<*> {
    logger.info {
      "Charge card limit sync command for $businessGuid: " +
        "executionId=$executionGuid, dryRun=$dryRun"
    }

    if (dryRun) {
      return TreasuryRep.dryRan(ChargeCardLimitSyncData)
    }

    chargeCardSyncClient(
      ChargeCardSyncApi.Sync(
        businessGuid = businessGuid,
        capitalAccountGuid = capitalAccountGuid,
        rep = ChargeCardSyncRep.Sync()
      ),
    )

    return TreasuryRep.completed(ChargeCardLimitSyncData)
  }

  class Builder @Inject constructor(
    private val chargeCardSyncClient: ChargeCardSyncClient,
  ) : TreasuryCommand.Builder<Config>() {
    override fun build(config: Config): TreasuryCommand {
      return ChargeCardLimitSyncCommand(
        chargeCardSyncClient = chargeCardSyncClient,
        capitalAccountGuid = config.capitalAccountGuid,
      )
    }
  }

  object ChargeCardLimitSyncData
}
