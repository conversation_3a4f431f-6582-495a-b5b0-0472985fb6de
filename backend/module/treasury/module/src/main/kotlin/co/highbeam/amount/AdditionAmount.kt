package co.highbeam.amount

import co.highbeam.money.Money
import co.highbeam.rep.treasury.amount.AmountSelector
import com.google.inject.Inject
import com.google.inject.Provider
import java.util.UUID
import co.highbeam.rep.treasury.amount.AmountSelector.Addition as Config

class AdditionAmount private constructor(
  private val amountSelectors: List<AmountSelector>,
  private val amountConfigMapper: AmountConfigMapper,
) : AmountSelection {
  override suspend fun get(businessGuid: UUID): Money {
    return amountSelectors.mapNotNull {
      amountConfigMapper.getAmountSelection(it).get(businessGuid)
    }.sumOf { it }
  }

  class Builder @Inject constructor(
    private val amountConfigMapper: Provider<AmountConfigMapper>,
  ) : AmountSelection.Builder<Config>() {
    override fun build(config: Config): AmountSelection {
      return AdditionAmount(
        amountSelectors = config.amountSelectors,
        amountConfigMapper = amountConfigMapper.get(),
      )
    }
  }
}
