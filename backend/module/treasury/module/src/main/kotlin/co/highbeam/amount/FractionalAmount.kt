package co.highbeam.amount

import co.highbeam.money.Money
import co.highbeam.rep.treasury.amount.AmountSelector
import com.google.inject.Inject
import com.google.inject.Provider
import java.math.BigDecimal
import java.math.MathContext
import java.util.UUID
import co.highbeam.rep.treasury.amount.AmountSelector.Fractional as Config

class FractionalAmount private constructor(
  private val amountSelector: AmountSelector,
  private val amountConfigMapper: AmountConfigMapper,
  private val fraction: BigDecimal,
) : AmountSelection {
  override suspend fun get(businessGuid: UUID): Money? {
    val amountSelection = amountConfigMapper.getAmountSelection(amountSelector)
    val amount = amountSelection.get(businessGuid) ?: return null

    val result = BigDecimal.valueOf(amount.rawCents).multiply(fraction, MathContext.DECIMAL128)

    return Money.fromCents(result)
  }

  class Builder @Inject constructor(
    private val amountConfigMapper: Provider<AmountConfigMapper>,
  ) : AmountSelection.Builder<Config>() {
    override fun build(config: Config): AmountSelection {
      return FractionalAmount(
        amountSelector = config.amountSelector,
        amountConfigMapper = amountConfigMapper.get(),
        fraction = config.fraction,
      )
    }
  }
}
