package co.highbeam.mapper

import co.highbeam.rep.job.JobScheduleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import com.google.inject.Inject
import java.util.UUID

class TreasuryRuleTriggerMapper @Inject constructor(
) {
  fun toJobName(ruleGuid: UUID): String = "treasury_rule_$ruleGuid"

  fun toJobScheduleRep(trigger: RuleTrigger): JobScheduleRep = when (trigger) {
    is RuleTrigger.Cron -> JobScheduleRep.Cron(trigger.value)
    is RuleTrigger.Interval -> JobScheduleRep.Interval(trigger.value)
    is RuleTrigger.CardAuthorization ->
      throw IllegalArgumentException("CardAuthorization trigger is not a job")
    is RuleTrigger.Transaction ->
      throw IllegalArgumentException("Transaction trigger is not a job")
    is RuleTrigger.ReceivedPayments ->
      throw IllegalArgumentException("ReceivedPayments trigger is not a job")
  }
}
