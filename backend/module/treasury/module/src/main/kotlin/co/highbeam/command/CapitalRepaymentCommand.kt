package co.highbeam.command

import co.highbeam.amount.AmountConfigMapper
import co.highbeam.amount.AmountSelection
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.bankAccount.BankAccount
import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Money
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.CapitalRepayment.RetryPolicy
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.service.treasury.TreasuryRuleService
import com.google.inject.Inject
import java.util.UUID
import mu.KotlinLogging
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.CapitalRepayment as Config

class CapitalRepaymentCommand private constructor(
  private val fromAccountSelection: BankAccountSelection,
  private val amountSelection: AmountSelection,
  private val capitalAccountGuid: UUID,
  private val pullPartialPayments: Boolean,
  private val retryPolicy: RetryPolicy,

  private val lineOfCreditTransactionsClient: LineOfCreditTransactionsClient,
  private val treasuryRuleService: TreasuryRuleService,
) : TreasuryCommand {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(
    ruleGuid: UUID,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ): TreasuryRep.Complete<*> {
    val metadata = CapitalRepaymentData(
      fromAccount = fromAccountSelection.get(businessGuid),
      amount = amountSelection.get(businessGuid),
    )
    logger.info {
      "Capital repayment command for $businessGuid: " +
        "executionId=$executionGuid, dryRun=$dryRun, data=$metadata"
    }

    metadata.fromAccount ?: return TreasuryRep.accountInvalid(metadata)
    metadata.amount ?: return TreasuryRep.amountInvalid(metadata)

    if (metadata.amount == Money.ZERO) {
      return TreasuryRep.zeroAmount(metadata)
    }

    val availableBalance = Money.fromBalance(metadata.fromAccount.availableBalance)

    if (availableBalance <= Money.ZERO) {
      createRetryRule(
        amount = metadata.amount,
        businessGuid = businessGuid,
        executionGuid = executionGuid,
        dryRun = dryRun,
      )
      return TreasuryRep.insufficientFunds(metadata)
    }

    if (!pullPartialPayments && availableBalance < metadata.amount) {
      createRetryRule(
        amount = metadata.amount,
        businessGuid = businessGuid,
        executionGuid = executionGuid,
        dryRun = dryRun,
      )
      return TreasuryRep.onlyPartialAmountAvailable(metadata)
    }

    // we can pull something, and create a retry the leftover amount
    val amountToTransfer = Money.min(metadata.amount, availableBalance)
    val result = createRepayment(
      amount = amountToTransfer,
      fromAccount = metadata.fromAccount,
      businessGuid = businessGuid,
      executionGuid = executionGuid,
      metadata = metadata,
      dryRun = dryRun,
    )

    val remainingAmount = when (result.state) {
      TreasuryTransactionRep.State.Failed ->
        metadata.amount
      TreasuryTransactionRep.State.Completed, TreasuryTransactionRep.State.DryRunCompleted ->
        metadata.amount - amountToTransfer
      TreasuryTransactionRep.State.Pending ->
        throw AssertionError("Unexpected treasury transaction rep state: ${result.state}")
    }

    if (remainingAmount > Money.ZERO) {
      createRetryRule(
        amount = remainingAmount,
        businessGuid = businessGuid,
        executionGuid = executionGuid,
        dryRun = dryRun,
      )
    }

    return result
  }

  private suspend fun createRepayment(
    amount: Money,
    businessGuid: UUID,
    executionGuid: UUID,
    metadata: CapitalRepaymentData,
    fromAccount: BankAccount,
    dryRun: Boolean,
  ): TreasuryRep.Complete<CapitalRepaymentData> {
    if (dryRun) {
      return TreasuryRep.dryRan(metadata)
    }

    val repaymentCreator = CapitalRepaymentTransactionRep.Creator(
      amount = amount,
      idempotencyKey = executionGuid,
      bankAccountGuid = fromAccount.guid,
    )

    logger.info {
      "Capital repayment command with " +
        "repaymentCreator:$repaymentCreator, businessGuid:$businessGuid"
    }
    val response = lineOfCreditTransactionsClient.request(
      LineOfCreditTransactionsApi.CreateRepayment(
        businessGuid = businessGuid,
        creditAccountGuid = capitalAccountGuid,
        rep = repaymentCreator,
      )
    )
    logger.info { "Capital repayment command with create repayment response:$response" }

    val updatedMetadata = metadata.copy(
      unitCoId = response.unitCoId,
    )

    if (response.status == CapitalRepaymentTransactionRep.Status.Rejected) {
      return TreasuryRep.failed(null, updatedMetadata)
    }

    return TreasuryRep.completed(updatedMetadata)
  }

  private suspend fun createRetryRule(
    amount: Money,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ) {
    when (retryPolicy) {
      RetryPolicy.None ->
        return
      RetryPolicy.PartialAllowed,
      RetryPolicy.AllOrNothing,
        ->
        treasuryRuleService.create(

          TreasuryRuleRep.Creator(
            name = "autoManagedCreditRule_${capitalAccountGuid}_retry_$executionGuid",
            businessGuid = businessGuid,
            rule = Rule.CapitalInstallmentRetry(
              bankAccountGuid = null,
              capitalAccountGuid = capitalAccountGuid,
              amount = amount,
              pullPartialPayments = retryPolicy == RetryPolicy.PartialAllowed,
            ),
            trigger = RuleTrigger.Transaction(
              conditions = listOf(TransactionTriggerCondition.Incoming),
              dryRun = dryRun,
              state = TreasuryRuleRep.State.Active
            )
          )
        )
    }
  }

  class Builder @Inject constructor(
    private val bankAccountConfigMapper: BankAccountConfigMapper,
    private val amountConfigMapper: AmountConfigMapper,
    private val lineOfCreditTransactionsClient: LineOfCreditTransactionsClient,
    private val treasuryRuleService: TreasuryRuleService,
  ) : TreasuryCommand.Builder<Config>() {
    override fun build(config: Config): TreasuryCommand {
      return CapitalRepaymentCommand(
        fromAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.fromAccount),
        amountSelection = amountConfigMapper.getAmountSelection(config.amount),
        capitalAccountGuid = config.capitalAccountGuid,
        pullPartialPayments = config.pullPartialPayments,
        lineOfCreditTransactionsClient = lineOfCreditTransactionsClient,
        treasuryRuleService = treasuryRuleService,
        retryPolicy = config.retryPolicy,
      )
    }
  }

  data class CapitalRepaymentData(
    val fromAccount: BankAccount?,
    val amount: Money?,
    val unitCoId: String? = null,
  )
}
