package co.highbeam.command

import co.highbeam.rep.treasury.command.TreasuryCommandConfig
import com.google.inject.Inject
import com.google.inject.Injector

class TreasuryCommandConfigMapper @Inject constructor(
  private val injector: Injector,
) {
  private val treasuryCommandBuilders = TreasuryCommand.Builder::class.sealedSubclasses.map {
    injector.getInstance(it.java)
  }.associateBy { it.configClass }

  fun getTreasuryCommand(config: TreasuryCommandConfig): TreasuryCommand {
    val treasuryCommandConfigBuilder = treasuryCommandBuilders[config::class]
      ?: throw IllegalArgumentException("No builder found for config: $config")
    return treasuryCommandConfigBuilder::class.java.getMethod("build", config::class.java)
      .invoke(treasuryCommandConfigBuilder, config) as TreasuryCommand
  }
}
