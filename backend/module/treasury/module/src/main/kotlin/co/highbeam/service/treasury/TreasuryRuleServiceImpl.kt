package co.highbeam.service.treasury

import co.highbeam.exception.treasury.TreasuryRuleNotFound
import co.highbeam.exception.treasury.TreasuryRuleUpdateMismatchException
import co.highbeam.mapper.TreasuryRuleMapper
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.store.TreasuryRuleStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class TreasuryRuleServiceImpl @Inject constructor(
  private val treasuryRuleMapper: TreasuryRuleMapper,
  private val treasuryTriggerService: TreasuryRuleTriggerService,
  private val treasuryRuleStore: TreasuryRuleStore,
) : TreasuryRuleService {
  private val logger = KotlinLogging.logger {}

  override suspend fun create(creator: TreasuryRuleRep.Creator): TreasuryRuleRep {
    logger.info { "Creating rule for ${creator.businessGuid}: $creator" }
    val model = treasuryRuleStore.create(treasuryRuleMapper.toModel(creator))
    creator.trigger?.let { treasuryTriggerService.create(ruleGuid = model.guid, trigger = it) }
    return treasuryRuleMapper.toCompleteRep(model)
  }

  override fun get(ruleGuid: UUID): TreasuryRuleRep? =
    treasuryRuleStore.get(ruleGuid)?.let { treasuryRuleMapper.toCompleteRep(it) }

  override fun getForBusiness(businessGuid: UUID): List<TreasuryRuleRep> =
    treasuryRuleStore.getForBusiness(businessGuid).map { treasuryRuleMapper.toCompleteRep(it) }

  override suspend fun update(ruleGuid: UUID, updater: TreasuryRuleRep.Updater): TreasuryRuleRep {
    val model = treasuryRuleStore.get(ruleGuid) ?: throw TreasuryRuleNotFound()

    updater.rule?.let {
      if (it::class != model.rule::class) {
        throw TreasuryRuleUpdateMismatchException(
          current = model.rule,
          update = it,
        )
      }
    }
    logger.info { "Updating rule $model with $updater" }

    val updatedModel =
      treasuryRuleStore.update(ruleGuid, treasuryRuleMapper.toUpdateModel(updater))

    if (model.trigger != updatedModel.trigger || model.state != updatedModel.state) {
      treasuryTriggerService.update(
        ruleGuid = ruleGuid,
        trigger = updatedModel.trigger
          ?.takeIf { updatedModel.state != TreasuryRuleRep.State.Terminated }
      )
    }

    return treasuryRuleMapper.toCompleteRep(updatedModel)
  }
}
