package co.highbeam.command

import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.bankAccount.BankAccount
import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.email.EmailService
import co.highbeam.email.template.DepositAccountLowBalanceEmailTemplate
import co.highbeam.email.template.EmailTemplate
import co.highbeam.money.Balance
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import java.time.Clock
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.AccountLowBalanceEmail as Config

class DepositAccountLowBalanceEmailCommand private constructor(
  private val accountSelection: BankAccountSelection,
  private val businessMemberClient: BusinessMemberClient,
  private val clock: Clock,
  private val emailService: EmailService,
  private val lowBalanceAlert: Balance,
  private val unitCoClient: UnitCoClient,
) : TreasuryCommand {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(
    ruleGuid: UUID,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ): TreasuryRep.Complete<*> {
    val metadata = LowBalanceData(
      account = accountSelection.get(businessGuid),
      lowBalanceAmount = lowBalanceAlert,
    )

    logger.info {
      "Low balance email command for $businessGuid: " +
        "executionId=$executionGuid, dryRun=$dryRun, data=$metadata"
    }

    if (metadata.account == null) {
      return TreasuryRep.accountInvalid(metadata)
    }

    if (dryRun) {
      return TreasuryRep.dryRan(metadata)
    }

    if (metadata.account.availableBalance >= metadata.lowBalanceAmount) {
      return TreasuryRep.completed(metadata)
    }

    val trailingBalance = yesterdaysBalance(
      unitCoAccountId = metadata.account.unitCoDepositAccountId
    )

    // Account does not have enough history to determine if it qualifies for the alert
    if (trailingBalance.isEmpty()) {
      return TreasuryRep.completed(metadata)
    } else if (
    // Prevents alerting every day if the account is consistently below the threshold
      trailingBalance[0] < metadata.lowBalanceAmount) {
      return TreasuryRep.completed(metadata)
    }

    val key = "DepositAccountLowBalanceEmailCommand|" +
      "$businessGuid|${metadata.account.guid}|$executionGuid"
    emailService.sync(key) { sendEmail ->
      sendEmail(
        DepositAccountLowBalanceEmailTemplate(
          accountBalance = metadata.account.availableBalance,
          accountLast4Digits = metadata.account.accountNumber.takeLast(4),
          accountName = metadata.account.name,
          accountsLink = "https://app.highbeam.co/accounts",
          lowBalanceAmount = metadata.lowBalanceAmount,
          recipients = businessAdminRecipients(businessGuid)
        )
      )
    }

    return TreasuryRep.completed(metadata)
  }

  private suspend fun businessAdminRecipients(businessGuid: UUID): List<EmailTemplate.Recipient> {
    val businessAdminMembers = businessMemberClient.request(
      BusinessMemberApi.GetAdminsByBusiness(businessGuid)
    )
    return businessAdminMembers.mapNotNull {
      it.emailAddress?.let { it1 ->
        EmailTemplate.Recipient(it1, it.fullName)
      }
    }
  }

  private suspend fun yesterdaysBalance(
    unitCoAccountId: String,
  ): List<Balance> {
    val yesterday = LocalDate.now(clock).minusDays(1)
    return unitCoClient.accountClient.getDailyBalances(
      customerId = null,
      accountId = unitCoAccountId,
      from = yesterday,
      to = LocalDate.now(clock),
      query = null,
    ).toList().flatten().map { it.attributes.balance }
  }

  class Builder @Inject constructor(
    private val bankAccountConfigMapper: BankAccountConfigMapper,
    private val businessMemberClient: BusinessMemberClient,
    private val clock: Clock,
    private val emailService: EmailService,
    private val unitCoClient: UnitCoClient,
  ) : TreasuryCommand.Builder<Config>() {
    override fun build(config: Config): TreasuryCommand {
      return DepositAccountLowBalanceEmailCommand(
        accountSelection = bankAccountConfigMapper.getBankAccountSelection(config.account),
        businessMemberClient = businessMemberClient,
        clock = clock,
        emailService = emailService,
        lowBalanceAlert = config.lowBalanceAlert,
        unitCoClient = unitCoClient,
      )
    }
  }

  data class LowBalanceData(
    val account: BankAccount?,
    val lowBalanceAmount: Balance,
  )
}
