package co.highbeam.service.treasury

import co.highbeam.command.TreasuryCommand
import co.highbeam.exception.treasury.TreasuryRuleNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.mapper.TreasuryTransactionMapper
import co.highbeam.model.TreasuryTransactionModel
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.TriggerData
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.rulebuilder.RuleCommandMapper
import co.highbeam.store.TreasuryTransactionStore
import com.google.inject.Inject
import java.util.UUID
import mu.KotlinLogging

class TreasuryTransactionServiceImpl @Inject constructor(
  private val treasuryRuleService: TreasuryRuleService,
  private val treasuryTransactionStore: TreasuryTransactionStore,
  private val treasuryTransactionMapper: TreasuryTransactionMapper,
  private val ruleCommandMapper: RuleCommandMapper,
) : TreasuryTransactionService {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(ruleGuid: UUID, dryRun: Boolean) {
    val treasuryRule = treasuryRuleService.get(ruleGuid)
      .takeIf { it?.state != TreasuryRuleRep.State.Terminated }
      ?: throw unprocessable(TreasuryRuleNotFound())

    if (treasuryRule.state == TreasuryRuleRep.State.Paused) {
      return
    }

    execute(treasuryRule, dryRun)
  }

  override suspend fun execute(treasuryRule: TreasuryRuleRep, dryRun: Boolean) {
    logger.info { "Executing rule for ${treasuryRule.businessGuid}: $treasuryRule" }

    val commands = ruleCommandMapper.getCommand(treasuryRule.rule)
    if (commands.isEmpty()) throw unprocessable(TreasuryRuleNotFound())

    for (command in commands) {
      wrapInTransaction(
        command = command,
        rule = treasuryRule,
        dryRun = dryRun,
      )
    }
  }

  override suspend fun execute(
    treasuryRule: TreasuryRuleRep,
    dryRun: Boolean,
    triggerData: TriggerData,
  ) {
    logger.info {
      "Executing rule for ${treasuryRule.businessGuid}: $treasuryRule with $triggerData"
    }

    val commands = ruleCommandMapper.getCommand(
      rule = treasuryRule.rule,
      triggerData = triggerData,
    )
    if (commands.isEmpty()) throw unprocessable(TreasuryRuleNotFound())

    for (command in commands) {
      wrapInTransaction(
        command = command,
        rule = treasuryRule,
        dryRun = dryRun,
      )
    }
  }

  override fun getTransactionsForRule(ruleGuid: UUID): List<TreasuryTransactionRep> {
    return treasuryTransactionStore.getForRule(ruleGuid)
      .map { treasuryTransactionMapper.toTransactionRep(it) }
  }

  override fun getTransactionsForBusiness(businessGuid: UUID): List<TreasuryTransactionRep> {
    return treasuryTransactionStore.getForBusiness(businessGuid)
      .map { treasuryTransactionMapper.toTransactionRep(it) }
  }

  @SuppressWarnings("TooGenericExceptionCaught")
  private suspend fun wrapInTransaction(
    command: TreasuryCommand,
    rule: TreasuryRuleRep,
    dryRun: Boolean,
  ) {
    val transaction = treasuryTransactionStore.create(treasuryTransactionMapper.toCreator(rule))
    try {
      val response = command.execute(
        ruleGuid = rule.guid,
        businessGuid = rule.businessGuid,
        executionGuid = transaction.guid,
        dryRun = dryRun,
      )
      treasuryTransactionStore.update(
        treasuryTransactionMapper.toUpdater(
          transaction = transaction,
          response = response
        )
      )
    } catch (e: Exception) {
      treasuryTransactionStore.update(
        TreasuryTransactionModel.Updater(
          guid = transaction.guid,
          state = TreasuryTransactionRep.State.Failed,
        )
      )
      throw e
    }
  }
}
