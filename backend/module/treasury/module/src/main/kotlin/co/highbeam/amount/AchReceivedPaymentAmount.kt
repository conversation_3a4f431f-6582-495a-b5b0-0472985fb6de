package co.highbeam.amount

import co.highbeam.api.business.BusinessApi
import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.client.business.BusinessClient
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import kotlinx.coroutines.flow.fold
import kotlinx.coroutines.flow.map
import java.util.UUID
import co.highbeam.rep.treasury.amount.AmountSelector.AchReceivedPayment as Config

class AchReceivedPaymentAmount private constructor(
  private val businessClient: BusinessClient,
  private val bankAccountSelection: BankAccountSelection,
  private val unitCoClient: UnitCoClient,
  private val statuses: List<String>?,
  private val direction: MoneyDirection,
) : AmountSelection {
  override suspend fun get(businessGuid: UUID): Money? {
    val business = businessClient.request(BusinessApi.Get(businessGuid)) ?: return null
    val bankAccount = bankAccountSelection.get(businessGuid) ?: return null

    val amount = unitCoClient.receivedPayment.list(
      customerId = business.unitCoCustomerId,
      accountId = bankAccount.unitCoDepositAccountId,
      statuses = statuses,
    ).map { payments ->
      payments.filter { MoneyDirection.valueOf(it.direction) == direction }
        .sumOf { it.amount }
    }.fold(Money.ZERO) { acc, amount -> acc + amount }

    return amount
  }

  class Builder @Inject constructor(
    private val businessClient: BusinessClient,
    private val bankAccountConfigMapper: BankAccountConfigMapper,
    private val unitCoClient: UnitCoClient,
  ) : AmountSelection.Builder<Config>() {
    override fun build(config: Config): AmountSelection {
      return AchReceivedPaymentAmount(
        businessClient = businessClient,
        unitCoClient = unitCoClient,
        bankAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.bankAccount),
        statuses = config.statuses,
        direction = config.direction,
      )
    }
  }
}
