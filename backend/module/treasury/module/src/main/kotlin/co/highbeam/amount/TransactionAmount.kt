package co.highbeam.amount

import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.money.Money
import co.unit.client.UnitCoClient
import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoTransactionRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.toList
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.util.UUID
import co.highbeam.rep.treasury.amount.AmountSelector.TransactionAmount as Config

class TransactionAmount private constructor(
  val bankAccountSelection: BankAccountSelection,
  val multiplier: BigDecimal,
  val tags: Map<String, String>? = null,
  private val objectMapper: ObjectMapper,
  private val unitCoClient: UnitCoClient
) : AmountSelection {
  @OptIn(FlowPreview::class)
  override suspend fun get(businessGuid: UUID): Money? {
    val bankAccount = bankAccountSelection.get(businessGuid) ?: return null

    val transactions = unitCoClient.transaction.list(
      customerId = null,
      accountId = bankAccount.unitCoDepositAccountId,
      query = null,
      from = null,
      to = null,
      tags = tags,
    ).flatMapConcat { toUnitCoTransactionReps(it).asFlow() }
      .toList()

    if (transactions.isEmpty()) {
      return null
    }

    val totalAmount = transactions.sumOf { it.amount }
    val result = BigDecimal.valueOf(totalAmount.rawCents)
      .multiply(multiplier, MathContext(0, RoundingMode.HALF_EVEN))
    if (result > BigDecimal.ZERO) {
      return Money.fromCents(result)
    }
    return Money.ZERO
  }

  private fun toUnitCoTransactionReps(it: JsonNode): List<UnitCoTransactionRep> =
    objectMapper.convertValue<DataWrapper<List<UnitCoTransactionRep>>>(it).data

  class Builder @Inject constructor(
    private val unitCoClient: UnitCoClient,
    private val objectMapper: ObjectMapper,
    private val bankAccountConfigMapper: BankAccountConfigMapper
  ) : AmountSelection.Builder<Config>() {
    override fun build(config: Config): AmountSelection {
      return TransactionAmount(
        bankAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.bankAccount),
        multiplier = config.multiplier,
        tags = config.tags,
        unitCoClient = unitCoClient,
        objectMapper = objectMapper,
      )
    }
  }
}
