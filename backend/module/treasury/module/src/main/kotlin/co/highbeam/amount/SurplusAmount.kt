package co.highbeam.amount

import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.money.Money
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.amount.AmountSelector.Surplus as Config

class SurplusAmount private constructor(
  private val bankAccountSelection: BankAccountSelection,
  private val threshold: Money,
) : AmountSelection {
  override suspend fun get(businessGuid: UUID): Money? {
    val bankAccount = bankAccountSelection.get(businessGuid) ?: return null

    val hasSurplus = threshold < bankAccount.availableBalance

    if (!hasSurplus) return Money.ZERO
    return Money.fromBalance(bankAccount.availableBalance - threshold)
  }

  class Builder @Inject constructor(
    private val bankAccountConfigMapper: BankAccountConfigMapper,
  ) : AmountSelection.Builder<Config>() {
    override fun build(config: Config): AmountSelection {
      return SurplusAmount(
        threshold = config.threshold,
        bankAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.bankAccount),
      )
    }
  }
}
