package co.highbeam.endpoint.treasury.task

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.treasury.TreasuryTransactionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.treasury.TreasuryTaskApi as Api

internal class ExecuteTreasuryTask @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val service: TreasuryTransactionService,
) : EndpointHandler<Api.Execute, Unit>(
  template = Api.Execute::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Execute =
    Api.Execute(call.body())

  override suspend fun Handler.handle(endpoint: Api.Execute) {
    auth(authPlatformRole(PlatformRole.CLOUD_TASKS))
    service.execute(endpoint.rep.ruleGuid, endpoint.rep.dryRun)
  }
}
