package co.highbeam.listener.treasury

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.model.unitCoEvent.UnitCoAuthorizationAmountChangedModel
import co.highbeam.model.unitCoEvent.UnitCoAuthorizationCreatedModel
import co.highbeam.model.unitCoEvent.UnitCoEventModel
import co.highbeam.model.unitCoEvent.UnitCoReceivedPaymentCreatedEventModel
import co.highbeam.model.unitCoEvent.UnitCoTransactionCreatedEventModel
import co.highbeam.service.treasury.TreasuryWebhookService
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.common.annotations.VisibleForTesting
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging

@Singleton
class TreasuryUnitCoWebhookListener @Inject constructor(
  factory: EventListenerFactory,
  private val objectMapper: ObjectMapper,
  private val webhookService: TreasuryWebhookService,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig("unit-co-webhook"),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "treasury",
        filter = "attributes.type = \"transaction.created\"" +
          " OR attributes.type = \"receivedPayment.created\"" +
          " OR attributes.type = \"authorization.created\"" +
          " OR attributes.type = \"authorization.amountChanged\""
      ),
      clazz = JsonNode::class.java,
      listener = ::onReceive
    )
  }

  @VisibleForTesting
  suspend fun onReceive(json: JsonNode) {
    logger.info { "Received event in treasury: $json." }
    when (val event = readEvent(json)) {
      is UnitCoTransactionCreatedEventModel -> webhookService.handle(event)
      is UnitCoReceivedPaymentCreatedEventModel -> webhookService.handle(event)
      is UnitCoAuthorizationCreatedModel -> webhookService.handle(event)
      is UnitCoAuthorizationAmountChangedModel -> webhookService.handle(event)
      else -> logger.error { "Event type mismatch in treasury: $json." }
    }
  }

  private fun readEvent(eventJson: JsonNode): UnitCoEventModel =
    objectMapper.convertValue(eventJson)
}
