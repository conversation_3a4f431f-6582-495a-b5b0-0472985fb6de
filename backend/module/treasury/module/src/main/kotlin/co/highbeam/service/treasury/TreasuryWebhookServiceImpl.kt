package co.highbeam.service.treasury

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi
import co.highbeam.capital.chargeCard.rep.ChargeCardAccountRep
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.model.unitCoEvent.UnitCoAuthorizationAmountChangedModel
import co.highbeam.model.unitCoEvent.UnitCoAuthorizationCreatedModel
import co.highbeam.model.unitCoEvent.UnitCoReceivedPaymentCreatedEventModel
import co.highbeam.model.unitCoEvent.UnitCoTransactionCreatedEventModel
import co.highbeam.money.Balance
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.CardAuthorizationTriggerData
import co.highbeam.rep.treasury.ruleTrigger.ReceivedPaymentsTriggerData
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerData
import co.unit.client.UnitCoClient
import co.unit.rep.UnitAchReceivedPaymentRep
import co.unit.rep.UnitCoCardPurchaseTransactionRep
import co.unit.rep.UnitCoReceivedAchTransactionRep
import co.unit.rep.UnitCoTransactionRep
import co.unit.rep.UnitCoWireTransactionRep
import com.google.inject.Inject
import mu.KotlinLogging

class TreasuryWebhookServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val chargeCardAccountClient: ChargeCardAccountClient,
  private val unitCoClient: UnitCoClient,
  private val treasuryRuleService: TreasuryRuleService,
  private val transactionService: TreasuryTransactionService,
) : TreasuryWebhookService {
  private val logger = KotlinLogging.logger {}

  override suspend fun handle(event: UnitCoTransactionCreatedEventModel) {
    val (bankAccount, chargeCardAccount) = getAccounts(event.accountId)
    if (bankAccount == null && chargeCardAccount == null) {
      logger.info { "Bank/Capital account not found for incoming transaction. $event" }
      return
    }

    val businessGuid = bankAccount?.businessGuid ?: chargeCardAccount?.businessGuid ?: return

    val transaction = getUnitCoTransaction(
      accountId = event.accountId,
      event.transactionId
    )

    val rules = treasuryRuleService.getForBusiness(businessGuid)
      .filter { it.state == TreasuryRuleRep.State.Active }

    val triggerData = generateTransactionTriggerData(
      bankAccount = bankAccount,
      chargeCardAccount = chargeCardAccount,
      transaction = transaction,
      event = event,
    )

    rules.forEach {
      val ruleTrigger = it.trigger ?: return@forEach
      if (ruleTrigger !is RuleTrigger.Transaction) return@forEach
      if (ruleTrigger.state != TreasuryRuleRep.State.Active) return@forEach

      if (ruleTrigger.conditions.all { condition -> condition.shouldTrigger(triggerData) }) {
        logger.info { "Running execution for rule - $it" }
        transactionService.execute(
          treasuryRule = it,
          dryRun = ruleTrigger.dryRun,
          triggerData = triggerData,
        )
      }
    }
  }

  override suspend fun handle(event: UnitCoReceivedPaymentCreatedEventModel) {
    if (event.direction != MoneyDirection.Debit) return

    val bankAccount =
      bankAccountClient.request(BankAccountApi.GetByUnitCoDepositAccountId(event.unitCoAccountId))
    if (bankAccount == null) {
      logger.info { "Bank account not found for incoming received payment webhook. $event" }
      return
    }

    val receivedPayment = getUnitCoReceivedPayments(event.unitCoReceivedPaymentId)
    if (receivedPayment.status !in listOf("Pending", "MarkedForReturn")) {
      logger.info { "Received payment not processed. $receivedPayment" }
      return
    }

    val rules = treasuryRuleService.getForBusiness(bankAccount.businessGuid)
      .filter { it.state == TreasuryRuleRep.State.Active }

    val triggerData = generateReceivedPaymentTriggerData(
      bankAccount = bankAccount,
      receivedPayment = receivedPayment,
    )

    rules.forEach {
      val trigger = it.trigger ?: return@forEach
      if (trigger !is RuleTrigger.ReceivedPayments) return@forEach
      if (trigger.state != TreasuryRuleRep.State.Active) return@forEach

      if (trigger.conditions.all { condition -> condition.shouldTrigger(triggerData) }) {
        logger.info { "Running execution for rule - $it" }
        transactionService.execute(
          treasuryRule = it,
          dryRun = trigger.dryRun,
          triggerData = triggerData,
        )
        if (trigger.reprocess && !trigger.dryRun && receivedPayment.status == "MarkedForReturn") {
          reprocessReceivedPayment(event)
        }
      }
    }
  }

  override suspend fun handle(event: UnitCoAuthorizationCreatedModel) {
    val (bankAccount, chargeCardAccount) = getAccounts(event.unitCoAccountId)
    if (bankAccount == null && chargeCardAccount == null) {
      logger.info { "Bank/Capital account not found for incoming transaction. $event" }
      return
    }

    val businessGuid = bankAccount?.businessGuid ?: chargeCardAccount?.businessGuid ?: return


    val rules = treasuryRuleService.getForBusiness(businessGuid)
      .filter { it.state == TreasuryRuleRep.State.Active }

    val data = generateCardAuthorizationTriggerData(
      bankAccount = bankAccount,
      chargeCardAccount = chargeCardAccount,
      event = event,
    )

    rules.forEach {
      val ruleTrigger = it.trigger ?: return@forEach
      if (ruleTrigger !is RuleTrigger.CardAuthorization) return@forEach
      if (ruleTrigger.state != TreasuryRuleRep.State.Active) return@forEach

      if (ruleTrigger.conditions.all { condition -> condition.shouldTrigger(data) }) {
        logger.info { "Running execution for rule - $it" }
        transactionService.execute(
          treasuryRule = it,
          dryRun = ruleTrigger.dryRun,
          triggerData = data,
        )
      }
    }
  }

  override suspend fun handle(event: UnitCoAuthorizationAmountChangedModel) {
    val (bankAccount, chargeCardAccount) = getAccounts(event.unitCoAccountId)
    if (bankAccount == null && chargeCardAccount == null) {
      logger.info { "Bank/Capital account not found for incoming transaction. $event" }
      return
    }

    val businessGuid = bankAccount?.businessGuid ?: chargeCardAccount?.businessGuid ?: return

    val rules = treasuryRuleService.getForBusiness(businessGuid)
      .filter { it.state == TreasuryRuleRep.State.Active }

    val data = generateCardAuthorizationTriggerData(
      bankAccount = bankAccount,
      chargeCardAccount = chargeCardAccount,
      event = event,
    )

    rules.forEach {
      val ruleTrigger = it.trigger ?: return@forEach
      if (ruleTrigger !is RuleTrigger.CardAuthorization) return@forEach
      if (ruleTrigger.state != TreasuryRuleRep.State.Active) return@forEach

      if (ruleTrigger.conditions.all { condition -> condition.shouldTrigger(data) }) {
        logger.info { "Running execution for rule - $it" }
        transactionService.execute(
          treasuryRule = it,
          dryRun = ruleTrigger.dryRun,
          triggerData = data,
        )
      }
    }
  }

  private fun generateCardAuthorizationTriggerData(
    bankAccount: BankAccountRep.Complete?,
    chargeCardAccount: ChargeCardAccountRep?,
    event: UnitCoAuthorizationCreatedModel
  ): CardAuthorizationTriggerData = CardAuthorizationTriggerData(
    bankAccountGuid = bankAccount?.guid,
    capitalAccountGuid = chargeCardAccount?.capitalAccountGuid,
    amount = event.amount,
    cardId = event.unitCoCardId,
  )

  private fun generateCardAuthorizationTriggerData(
    bankAccount: BankAccountRep.Complete?,
    chargeCardAccount: ChargeCardAccountRep?,
    event: UnitCoAuthorizationAmountChangedModel
  ): CardAuthorizationTriggerData = CardAuthorizationTriggerData(
    bankAccountGuid = bankAccount?.guid,
    capitalAccountGuid = chargeCardAccount?.capitalAccountGuid,
    amount = event.newAmount - event.oldAmount,
    cardId = event.unitCoCardId,
  )

  private suspend fun reprocessReceivedPayment(event: UnitCoReceivedPaymentCreatedEventModel) {
    unitCoClient.receivedPayment.reprocess(event.unitCoReceivedPaymentId)
  }

  private fun generateTransactionTriggerData(
    bankAccount: BankAccountRep.Complete?,
    chargeCardAccount: ChargeCardAccountRep?,
    transaction: UnitCoTransactionRep,
    event: UnitCoTransactionCreatedEventModel,
  ): TransactionTriggerData {
    val senderName = when (transaction) {
      is UnitCoReceivedAchTransactionRep -> transaction.companyName
      is UnitCoWireTransactionRep -> transaction.counterparty.name
      is UnitCoCardPurchaseTransactionRep -> transaction.merchantName
      else -> null
    }

    val type = UnitCoTransactionRep.UnitTransactionType.fromValue(event.transactionType)

    return TransactionTriggerData(
      senderName = senderName,
      bankAccountGuid = bankAccount?.guid,
      capitalAccountGuid = chargeCardAccount?.capitalAccountGuid,
      isIncomingTransaction = event.amount > Balance.ZERO,
      type = type.displayName,
    )
  }

  private fun generateReceivedPaymentTriggerData(
    bankAccount: BankAccountRep.Complete,
    receivedPayment: UnitAchReceivedPaymentRep,
  ): ReceivedPaymentsTriggerData = ReceivedPaymentsTriggerData(
    bankAccountGuid = bankAccount.guid,
    amount = receivedPayment.amount,
    counterpartyName = receivedPayment.companyName,
    description = receivedPayment.description,
  )

  private suspend fun getUnitCoTransaction(
    accountId: String,
    transactionId: String,
  ): UnitCoTransactionRep =
    unitCoClient.transaction.get(
      accountId = accountId,
      transactionId = transactionId
    ).let(::checkNotNull)

  private suspend fun getUnitCoReceivedPayments(
    receivedPaymentId: String,
  ): UnitAchReceivedPaymentRep =
    unitCoClient.receivedPayment.getReceivedPayment(
      receivedPaymentId = receivedPaymentId
    ).let(::checkNotNull)

  private suspend fun getAccounts(
    unitCoAccountId: String,
  ): Pair<BankAccountRep.Complete?, ChargeCardAccountRep?> {
    val bankAccount =
      bankAccountClient.request(BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    if (bankAccount != null) {
      return Pair(bankAccount, null)
    }

    val chargeCardAccount =
      chargeCardAccountClient(ChargeCardAccountApi.GetByUnitCoCreditAccountId(unitCoAccountId))
    return Pair(null, chargeCardAccount)
  }
}
