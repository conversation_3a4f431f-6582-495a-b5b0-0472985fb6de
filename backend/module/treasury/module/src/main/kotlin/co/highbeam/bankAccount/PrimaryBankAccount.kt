package co.highbeam.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.rep.treasury.bankAccount.BankAccountSelector.Primary as Config

internal class PrimaryBankAccount(
  private val bankAccountClient: BankAccountClient,
) : BankAccountSelection {
  override suspend fun get(businessGuid: UUID): BankAccount? {
    val bankAccountRep = bankAccountClient.request(
      BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
    ) ?: return null

    return BankAccount(
      guid = bankAccountRep.guid,
      accountNumber = bankAccountRep.accountNumber,
      routingNumber = bankAccountRep.routingNumber,
      availableBalance = bankAccountRep.availableBalance,
      name = bankAccountRep.name,
      unitCoDepositAccountId = bankAccountRep.unitCoDepositAccountId,
    )
  }

  class Builder @Inject constructor(
    private val bankAccountClient: BankAccountClient,
  ) : BankAccountSelection.Builder<Config>() {
    override fun build(config: Config): BankAccountSelection {
      return PrimaryBankAccount(
        bankAccountClient = bankAccountClient,
      )
    }
  }
}
