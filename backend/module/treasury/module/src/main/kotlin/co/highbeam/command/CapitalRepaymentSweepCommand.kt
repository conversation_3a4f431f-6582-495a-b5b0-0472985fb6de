package co.highbeam.command

import co.highbeam.amount.AmountConfigMapper
import co.highbeam.amount.AmountSelection
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.bankAccount.BankAccount
import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Money
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.service.treasury.TreasuryRuleService
import com.google.inject.Inject
import java.util.UUID
import mu.KotlinLogging
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.CapitalRepaymentSweep as Config

class CapitalRepaymentSweepCommand private constructor(
  private val fromAccountSelection: BankAccountSelection,
  private val amountSelection: AmountSelection,
  private val capitalAccountGuid: UUID,
  private val pullPartialPayments: Boolean,

  private val lineOfCreditTransactionsClient: LineOfCreditTransactionsClient,
  private val treasuryRuleService: TreasuryRuleService,
) : TreasuryCommand {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(
    ruleGuid: UUID,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ): TreasuryRep.Complete<*> {
    val metadata = CapitalRepaymentSweepData(
      fromAccount = fromAccountSelection.get(businessGuid),
      amount = amountSelection.get(businessGuid),
    )

    logger.info {
      "Capital repayment sweep command for $businessGuid: " +
        "executionId=$executionGuid, dryRun=$dryRun, data=$metadata"
    }

    metadata.fromAccount ?: return TreasuryRep.accountInvalid(metadata)
    metadata.amount ?: return TreasuryRep.amountInvalid(metadata)

    if (metadata.amount <= Money.ZERO) {
      terminateRetryRule(ruleGuid)
      return TreasuryRep.zeroAmount(metadata)
    }

    val availableBalance = Money.fromBalance(metadata.fromAccount.availableBalance)

    if (availableBalance <= Money.ZERO) {
      return TreasuryRep.insufficientFunds(metadata)
    }

    if (!pullPartialPayments && availableBalance < metadata.amount) {
      return TreasuryRep.onlyPartialAmountAvailable(metadata)
    }

    val amountToTransfer = Money.min(availableBalance, metadata.amount)
    val result = createRepayment(
      amount = amountToTransfer,
      businessGuid = businessGuid,
      executionGuid = executionGuid,
      metadata = metadata,
      fromAccount = metadata.fromAccount,
      dryRun = dryRun,
    )

    val remainingAmount = when (result.state) {
      TreasuryTransactionRep.State.Failed ->
        metadata.amount
      TreasuryTransactionRep.State.Completed, TreasuryTransactionRep.State.DryRunCompleted ->
        metadata.amount - amountToTransfer
      TreasuryTransactionRep.State.Pending ->
        throw AssertionError("Unexpected treasury transaction rep state: ${result.state}")
    }

    if (remainingAmount > Money.ZERO) {
      updateRetryRuleAmount(ruleGuid, newAmount = remainingAmount)
    } else {
      terminateRetryRule(ruleGuid)
    }

    return result
  }

  private suspend fun createRepayment(
    amount: Money,
    businessGuid: UUID,
    executionGuid: UUID,
    metadata: CapitalRepaymentSweepData,
    fromAccount: BankAccount,
    dryRun: Boolean,
  ): TreasuryRep.Complete<CapitalRepaymentSweepData> {
    if (dryRun) {
      return TreasuryRep.dryRan(metadata)
    }

    val repaymentCreator = CapitalRepaymentTransactionRep.Creator(
      amount = amount,
      idempotencyKey = executionGuid,
      bankAccountGuid = fromAccount.guid,
    )

    logger.info {
      "Capital repayment sweep command with " +
        "repaymentCreator:$repaymentCreator, businessGuid:$businessGuid"
    }
    val response = lineOfCreditTransactionsClient.request(
      LineOfCreditTransactionsApi.CreateRepayment(
        businessGuid = businessGuid,
        creditAccountGuid = capitalAccountGuid,
        rep = repaymentCreator,
      )
    )
    logger.info { "Capital repayment sweep command with create repayment response:$response" }

    val updatedMetadata = metadata.copy(
      unitCoId = response.unitCoId,
    )

    return when (response.status) {
      CapitalRepaymentTransactionRep.Status.Rejected -> TreasuryRep.failed(null, updatedMetadata)
      CapitalRepaymentTransactionRep.Status.Sent -> TreasuryRep.completed(updatedMetadata)
    }
  }

  private suspend fun updateRetryRuleAmount(
    ruleGuid: UUID,
    newAmount: Money,
  ) {
    val existingRuleRep = treasuryRuleService.get(ruleGuid)
      ?: throw AssertionError("Rule with guid $ruleGuid not found")

    when (val existingRule = existingRuleRep.rule) {
      is Rule.CapitalInstallmentRetry ->
        treasuryRuleService.update(
          ruleGuid = ruleGuid,
          updater = TreasuryRuleRep.Updater(
            rule = existingRule.copy(amount = newAmount),
          )
        )
      else ->
        return
    }
  }

  private suspend fun terminateRetryRule(ruleGuid: UUID) {
    treasuryRuleService.update(
      ruleGuid = ruleGuid,
      updater = TreasuryRuleRep.Updater(
        state = TreasuryRuleRep.State.Terminated
      )
    )
  }

  class Builder @Inject constructor(
    private val bankAccountConfigMapper: BankAccountConfigMapper,
    private val amountConfigMapper: AmountConfigMapper,
    private val lineOfCreditTransactionsClient: LineOfCreditTransactionsClient,
    private val treasuryRuleService: TreasuryRuleService,
  ) : TreasuryCommand.Builder<Config>() {
    override fun build(config: Config): TreasuryCommand {
      return CapitalRepaymentSweepCommand(
        fromAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.fromAccount),
        amountSelection = amountConfigMapper.getAmountSelection(config.amount),
        pullPartialPayments = config.pullPartialPayments,
        capitalAccountGuid = config.capitalAccountGuid,
        lineOfCreditTransactionsClient = lineOfCreditTransactionsClient,
        treasuryRuleService = treasuryRuleService,
      )
    }
  }

  data class CapitalRepaymentSweepData(
    val fromAccount: BankAccount?,
    val amount: Money?,
    val unitCoId: String? = null,
  )
}
