package co.highbeam.command

import co.highbeam.amount.AmountConfigMapper
import co.highbeam.amount.AmountSelection
import co.highbeam.api.paymentV2.PaymentApi
import co.highbeam.bankAccount.BankAccount
import co.highbeam.bankAccount.BankAccountConfigMapper
import co.highbeam.bankAccount.BankAccountSelection
import co.highbeam.client.paymentV2.PaymentClient
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID
import co.highbeam.rep.treasury.command.TreasuryCommandConfig.AchTransfer as Config

class AchTransferCommand private constructor(
  private val paymentClient: PaymentClient,
  private val fromAccountSelection: BankAccountSelection,
  private val toAccountSelection: BankAccountSelection,
  private val amountSelection: AmountSelection,
  private val description: String,
  private val send: Boolean,
  private val direction: MoneyDirection,
  private val sameDay: Boolean,
  private val tags: Map<String, String>?,
) : TreasuryCommand {
  private val logger = KotlinLogging.logger {}

  override suspend fun execute(
    ruleGuid: UUID,
    businessGuid: UUID,
    executionGuid: UUID,
    dryRun: Boolean,
  ): TreasuryRep.Complete<AchTransferData> {
    val metadata = AchTransferData(
      fromAccount = fromAccountSelection.get(businessGuid),
      toAccount = toAccountSelection.get(businessGuid),
      amount = amountSelection.get(businessGuid)
    )
    logger.info {
      "Ach transfer command for $businessGuid: " +
        "executionId=$executionGuid, ruleGuid=$ruleGuid, dryRun=$dryRun, data=$metadata"
    }

    metadata.fromAccount ?: return TreasuryRep.accountInvalid(metadata)
    metadata.toAccount ?: return TreasuryRep.accountInvalid(metadata)
    metadata.amount ?: return TreasuryRep.amountInvalid(metadata)

    if (metadata.amount == Money.ZERO) {
      return TreasuryRep.zeroAmount(metadata)
    }

    if (dryRun) {
      return TreasuryRep.dryRan(metadata)
    }

    return makeTransfer(
      amount = metadata.amount,
      executionGuid = executionGuid,
      fromAccount = metadata.fromAccount,
      toAccount = metadata.toAccount,
      metadata = metadata
    )
  }

  private suspend fun makeTransfer(
    amount: Money,
    executionGuid: UUID,
    fromAccount: BankAccount,
    toAccount: BankAccount,
    metadata: AchTransferData,
  ): TreasuryRep.Complete<AchTransferData> {
    val achPaymentCreator = PaymentRep.Creator(
      send = send,
      detail = PaymentDetailCreatorRep.Ach(
        amount = amount,
        accountNumber = toAccount.accountNumber,
        routingNumber = toAccount.routingNumber,
        payeeName = toAccount.name,
        description = description.take(10),
        direction = direction,
        addenda = description.take(80),
        fromBankAccountGuid = fromAccount.guid,
        idempotencyKey = executionGuid.toString(),
        sameDay = sameDay,
        tags = buildMap {
          put("transactionType", "TreasuryTransfer")
        } + tags.orEmpty(),
      )
    )
    logger.info { "Ach transfer command with payment request:$achPaymentCreator" }
    val response = paymentClient.request(PaymentApi.Create(achPaymentCreator))
    logger.info { "Ach transfer command with payment response:$response" }

    val updatedMetadata = metadata.copy(transactionGuid = response.guid)
    if (response.status == PaymentRep.Status.Failed) {
      return TreasuryRep.failed(response.reason, updatedMetadata)
    }

    return TreasuryRep.completed(updatedMetadata)
  }

  class Builder @Inject constructor(
    private val bankAccountConfigMapper: BankAccountConfigMapper,
    private val amountConfigMapper: AmountConfigMapper,
    private val paymentClient: PaymentClient,
  ) : TreasuryCommand.Builder<Config>() {
    override fun build(config: Config): TreasuryCommand {
      return AchTransferCommand(
        fromAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.fromAccount),
        toAccountSelection = bankAccountConfigMapper.getBankAccountSelection(config.toAccount),
        amountSelection = amountConfigMapper.getAmountSelection(config.amount),
        description = config.description,
        paymentClient = paymentClient,
        send = config.send,
        direction = config.direction,
        sameDay = config.sameDay,
        tags = config.tags,
      )
    }
  }

  data class AchTransferData(
    val fromAccount: BankAccount?,
    val toAccount: BankAccount?,
    val amount: Money?,
    val transactionGuid: UUID? = null,
  )
}
