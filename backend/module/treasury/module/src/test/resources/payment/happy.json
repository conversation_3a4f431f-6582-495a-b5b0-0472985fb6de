{"data": [{"type": "achPayment", "id": "1095037", "attributes": {"createdAt": "2022-11-02T21:48:01.652Z", "amount": 100, "direction": "Debit", "description": "Transfer", "counterparty": {"name": "<PERSON> <PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "****************", "accountType": "Checking"}, "status": "Clearing", "settlementDate": "2023-02-03", "counterpartyVerificationMethod": "Plaid", "sameDay": false, "secCode": "WEB", "traceNumber": "***************"}, "relationships": {"account": {"data": {"type": "account", "id": "723197"}}, "customer": {"data": {"type": "customer", "id": "519608"}}, "customers": {"data": [{"type": "customer", "id": "519608"}]}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "achPayment", "id": "1281235", "attributes": {"createdAt": "2022-11-16T22:21:34.443Z", "amount": 100, "direction": "Debit", "description": "Transfer", "counterparty": {"name": "<PERSON> <PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "****************", "accountType": "Checking"}, "status": "Clearing", "settlementDate": "2023-02-03", "counterpartyVerificationMethod": "Plaid", "sameDay": false, "secCode": "WEB", "traceNumber": "***************"}, "relationships": {"account": {"data": {"type": "account", "id": "723197"}}, "customer": {"data": {"type": "customer", "id": "519608"}}, "customers": {"data": [{"type": "customer", "id": "519608"}]}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "achPayment", "id": "1313587", "attributes": {"createdAt": "2022-11-29T22:12:56.644Z", "amount": 100, "direction": "Debit", "description": "Transfer", "counterparty": {"name": "<PERSON> <PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "****************", "accountType": "Checking"}, "status": "Clearing", "settlementDate": "2023-02-03", "counterpartyVerificationMethod": "Plaid", "sameDay": false, "secCode": "WEB", "traceNumber": "***************"}, "relationships": {"account": {"data": {"type": "account", "id": "723197"}}, "customer": {"data": {"type": "customer", "id": "519608"}}, "customers": {"data": [{"type": "customer", "id": "519608"}]}, "org": {"data": {"type": "org", "id": "553"}}}}, {"type": "achPayment", "id": "1371477", "attributes": {"createdAt": "2022-12-14T21:59:48.045Z", "amount": 100, "direction": "Debit", "description": "External t", "addenda": "External transfer to Highbeam", "counterparty": {"name": "<PERSON> <PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "****************", "accountType": "Checking"}, "status": "Clearing", "settlementDate": "2023-02-03", "counterpartyVerificationMethod": "Plaid", "sameDay": false, "secCode": "WEB", "traceNumber": "***************"}, "relationships": {"account": {"data": {"type": "account", "id": "723197"}}, "customer": {"data": {"type": "customer", "id": "519608"}}, "customers": {"data": [{"type": "customer", "id": "519608"}]}, "org": {"data": {"type": "org", "id": "553"}}}}], "meta": {"pagination": {"total": 4, "limit": 100, "offset": 0}}}