package co.highbeam.testing

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.job.JobApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.CapitalAccountSummaryClient
import co.highbeam.capital.account.FakeCapitalAccountSummaryClient
import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.chargeCard.ChargeCardAccountClient
import co.highbeam.capital.chargeCard.ChargeCardRepaymentClient
import co.highbeam.capital.chargeCard.ChargeCardSummaryClient
import co.highbeam.capital.chargeCard.ChargeCardSyncClient
import co.highbeam.capital.chargeCard.api.ChargeCardAccountApi
import co.highbeam.capital.chargeCard.api.ChargeCardRepaymentApi
import co.highbeam.capital.chargeCard.rep.ChargeCardRepaymentRep
import co.highbeam.capital.repayment.CapitalInstallmentClient
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.capital.transaction.CapitalTransactionSummaryClient
import co.highbeam.capital.transaction.CapitalTransactionsClient
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.job.JobClient
import co.highbeam.client.lineOfCredit.reporting.LineOfCreditBusinessReportingClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.client.paymentV2.PaymentClient
import co.highbeam.client.plaid.PlaidClient
import co.highbeam.client.treasury.TreasuryRuleClient
import co.highbeam.client.treasury.TreasuryTaskClient
import co.highbeam.client.treasury.TreasuryTransactionClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.TreasuryFeatureTestConfig
import co.highbeam.email.EmailService
import co.highbeam.event.FakeEventFeature
import co.highbeam.exception.JobNotFound
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.feature.treasury.TreasuryFeature
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.highbeam.util.time.inUTC
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCompleteRep
import highbeam.feature.task.TestTaskFeature
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.ListAssert
import org.assertj.core.api.RecursiveComparisonAssert
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

@ExtendWith(TreasuryFeatureIntegrationTest.Extension::class)
internal abstract class TreasuryFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private class MockFeature(
    val fakeCapitalAccountSummaryClient: FakeCapitalAccountSummaryClient,
  ) : AbstractMockFeature() {
    override fun bind() {
      mock(BankAccountClient::class)
      mock(BusinessClient::class)
      mock(BusinessMemberClient::class)
      mock(CapitalAccountSummaryClient::class, fakeCapitalAccountSummaryClient)
      mock(ChargeCardAccountClient::class)
      mock(ChargeCardRepaymentClient::class)
      mock(ChargeCardSyncClient::class)
      mock(ChargeCardSummaryClient::class, fakeCapitalAccountSummaryClient.chargeCardSummaryClient)
      mock(CapitalAccountClient::class, fakeCapitalAccountSummaryClient.capitalAccountClient)
      mock(EmailService::class, mockk(relaxed = true))
      mock(JobClient::class)
      mock(PaymentClient::class)
      mock(PlaidClient::class)
      mock(LineOfCreditBusinessReportingClient::class)
      mock(LineOfCreditTransactionsClient::class)
      mock(CapitalTransactionsClient::class)
      mock(CapitalInstallmentClient::class)
      mock(
        CapitalTransactionSummaryClient::class,
        fakeCapitalAccountSummaryClient.transactionSummaryClient
      )
      mock(UnitCoClient::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val fakeCapitalAccountSummaryClient = FakeCapitalAccountSummaryClient()
      val config: TreasuryFeatureTestConfig = ConfigLoader.load("test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "treasury",
      )
      val eventFeature = FakeEventFeature
      val taskFeature = TestTaskFeature(config.task)
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<TreasuryFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            sqlFeature,
            eventFeature,
            MockFeature(
              fakeCapitalAccountSummaryClient,
            ),
            TreasuryFeature(),
            taskFeature,
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
    }

    override fun stop() {
      sharedState.stop()
      fakeCapitalAccountSummaryClient.reset()
    }
  }

  val treasuryRuleClient: TreasuryRuleClient by lazy {
    TreasuryRuleClient(httpClient)
  }

  val treasuryTaskClient: TreasuryTaskClient by lazy {
    TreasuryTaskClient(httpClient)
  }

  val treasuryTransactionClient: TreasuryTransactionClient by lazy {
    TreasuryTransactionClient(httpClient)
  }

  protected fun mockUnitBookTransfer(
    status: UnitCoPayment.Status,
    reason: UnitCoPayment.Reason? = null,
  ) {
    coEvery {
      get<UnitCoClient>().payment.createBook(any())
    } answers {
      val creator = firstArg<BookPaymentRep.Creator>()
      return@answers UnitCompleteRep(
        id = "1231",
        type = UnitCoPayment.PaymentType.BookPayment.value,
        attributes = BookPaymentRep(
          createdAt = ZonedDateTime.now(clock),
          amount = creator.amount,
          description = creator.description,
          status = status,
          reason = reason,
        ),
        relationships = emptyMap(),
        json = objectMapper.createObjectNode(),
      )
    }
  }

  protected fun mockBusiness(businessGuid: UUID, businessName: String) {
    coEvery {
      get<BusinessClient>().request(BusinessApi.Get(businessGuid))
    } returns mockk {
      every { <EMAIL> } returns businessGuid
      every { <EMAIL> } returns businessName
    }
  }

  protected fun mockBusinessAccounts(
    businessGuid: UUID,
    bankAccounts: List<BankAccountRep.Complete>,
  ) {
    coEvery {
      get<BankAccountClient>().request(BankAccountApi.GetAllByBusinessGuid(businessGuid))
    } returns bankAccounts
  }

  protected fun mockPrimaryBusinessAccount(
    businessGuid: UUID,
    bankAccount: BankAccountRep.Complete?,
  ) {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
      )
    } returns bankAccount
  }

  protected fun mockChargeCardAccount(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    chargeCardAccountGuid: UUID,
  ) {
    coEvery {
      get<ChargeCardAccountClient>()(
        ChargeCardAccountApi.Get(
          businessGuid = businessGuid,
          capitalAccountGuid = capitalAccountGuid
        )
      )
    } returns mockk {
      every { guid } returns chargeCardAccountGuid
    }
  }

  protected fun mockGetBankAccountByBusiness(
    bankAccount: BankAccountRep.Complete,
    businessGuid: UUID,
  ) {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.Get(
          accountGuid = bankAccount.guid,
        )
      )
    } returns bankAccount
  }

  protected fun bankAccountRep(
    bankAccountGuid: UUID,
    unitCoId: String,
    isPrimary: Boolean,
    balance: Balance,
    type: BankAccountRep.Type,
    depositProduct: DepositAccountRep.DepositProduct,
    status: BankAccountRep.Status,
    businessGuid: UUID = UUID.randomUUID(),
  ): BankAccountRep.Complete = BankAccountRep.Complete(
    guid = bankAccountGuid,
    unitCoDepositAccountId = unitCoId,
    businessGuid = businessGuid,
    name = "Highbeam Bank",
    status = status,
    isPrimary = isPrimary,
    availableBalance = balance,
    routingNumber = "*********",
    accountNumber = "142683",
    type = "depositAccount",
    highbeamType = type,
    depositProduct = depositProduct,
    minimumRequiredBalance = Balance.ZERO,
  )

  fun mockCapitalAccountGet(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    limit: Money = Money.fromDollarsAndCents(100_000, 0),
    runningBalance: Balance = Balance.fromDollarsAndCents(-90_000, 0),
    bankAccountGuid: UUID? = null,
  ) {
    val capitalAccountSummaryClient =
      get<CapitalAccountSummaryClient>() as FakeCapitalAccountSummaryClient
    capitalAccountSummaryClient.capitalAccountClient.add(
      CapitalAccountRep(
        name = "Test Account",
        type = CapitalAccountRep.Type.CashAccessOnly,
        guid = capitalAccountGuid,
        businessGuid = businessGuid,
        details = CapitalAccountDetailsRep(
          limit = limit,
          apr = BigDecimal("0.1500"),
          repayment = CapitalAccountDetailsRep.Repayment(
            option = CapitalRepaymentOption.PayoutPercentage(BigDecimal("0.5000")),
            bankAccountGuid = bankAccountGuid,
          ),
          lineType = CapitalAccountDetailsRep.LineType.Revolving,
          targetRepaymentDays = 120,
          securedStatus = CapitalAccountDetailsRep.SecuredStatus.Unsecured,
        ),
        state = CapitalAccountRep.State.Active,
        controls = CapitalAccountControlsRep(
          drawdownEnabled = true,
        ),
        activatedAt = clock.date().inUTC(),
        lender = CapitalLender.Highbeam,
        terminatedAt = null,
      )
    )
    capitalAccountSummaryClient.transactionSummaryClient.set(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
      date = LocalDate.now(clock),
      balance = runningBalance,
    )
  }

  protected fun mockChargeCardRepayment(
    businessGuid: UUID,
    chargeCardAccountGuid: UUID,
    expectedAmount: Money,
    idempotencyKey: UUID,
    unitCoId: String = "1231",
    bankAccountGuid: UUID,
  ) {
    coEvery {
      get<ChargeCardRepaymentClient>()(
        ChargeCardRepaymentApi.Create(
          businessGuid = businessGuid,
          chargeCardAccountGuid = chargeCardAccountGuid,
          rep = ChargeCardRepaymentRep.Creator(
            amount = expectedAmount,
            idempotencyKey = idempotencyKey,
            bankAccountGuid = bankAccountGuid,
          )
        )
      )
    } returns mockk {
      every { unitCoTransactionId } returns unitCoId
    }
  }

  fun mockCapitalRepayment(
    businessGuid: UUID,
    capitalAccountGuid: UUID,
    expectedAmount: Money,
    idempotencyKey: UUID,
    status: CapitalRepaymentTransactionRep.Status? = null,
    unitCoId: String = "1231",
    bankAccountGuid: UUID,
  ) {
    coEvery {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = expectedAmount,
            idempotencyKey = idempotencyKey,
            bankAccountGuid = bankAccountGuid,
          )
        )
      )
    } returns CapitalRepaymentTransactionRep(
      status = status ?: CapitalRepaymentTransactionRep.Status.Sent,
      unitCoId = unitCoId,
    )
  }

  protected fun mockRemoveJob(exists: Boolean) {
    coEvery {
      get<JobClient>().request(any<JobApi.Delete>())
    } answers {
      if (exists) {
        mockk {
          every { <EMAIL> } returns firstArg<JobApi.Delete>().jobName
        }
      } else {
        throw JobNotFound()
      }
    }
  }

  protected fun ListAssert<TreasuryTransactionRep>.hasSameTransactionsAs(
    expected: List<TreasuryTransactionRep>,
  ): RecursiveComparisonAssert<*>? = usingRecursiveComparison()
    .ignoringFields("createdAt", "updatedAt")
    .isEqualTo(expected)

  protected object TreasuryRepReason {
    const val AccountInvalid: String = "AccountInvalid"
    const val AmountInvalid: String = "AmountInvalid"
    const val InsufficientFunds: String = "InsufficientFunds"
    const val OnlyPartialAmountAvailable: String = "OnlyPartialAmountAvailable"
    const val ZeroAmount: String = "ZeroAmount"
  }
}
