package co.highbeam.rule

import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.rep.DepositAccountRep
import io.mockk.coVerify
import java.math.BigDecimal
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CapitalInstallmentTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val capitalAccountGuid: UUID = UUID.randomUUID()
  private lateinit var ruleGuid: UUID
  private val primaryBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(12000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )

  @BeforeEach
  fun setup() = integrationTest {
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
  }

  @Test
  fun `happy case`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(6250, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 625000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - partial pull`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )

    assertThat(rulesForBusiness).contains(
      TreasuryRuleRep(
        guid = uuidGenerator[2],
        businessGuid = businessGuid,
        state = TreasuryRuleRep.State.Active,
        name = "autoManagedCreditRule_${capitalAccountGuid}_retry_${uuidGenerator[1]}",
        rule = Rule.CapitalInstallmentRetry(
          bankAccountGuid = null,
          capitalAccountGuid = capitalAccountGuid,
          amount = Money.fromDollarsAndCents(5250, 0), // 6250 - 1000
          pullPartialPayments = true
        ),
        trigger = RuleTrigger.Transaction(
          conditions = listOf(TransactionTriggerCondition.Incoming),
          dryRun = false,
          state = TreasuryRuleRep.State.Active
        )
      )
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 625000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - dry run`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, true)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "amount" to 625000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `partial pull attempt`() = integrationTest {
    addRule(BigDecimal("0.0625"), pullPartialPayments = false)
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )

    assertThat(rulesForBusiness).contains(
      TreasuryRuleRep(
        guid = uuidGenerator[2],
        businessGuid = businessGuid,
        state = TreasuryRuleRep.State.Active,
        name = "autoManagedCreditRule_${capitalAccountGuid}_retry_${uuidGenerator[1]}",
        rule = Rule.CapitalInstallmentRetry(
          bankAccountGuid = null,
          capitalAccountGuid = capitalAccountGuid,
          amount = Money.fromDollarsAndCents(6250, 0), // full amount, no partial
          pullPartialPayments = true
        ),
        trigger = RuleTrigger.Transaction(
          conditions = listOf(TransactionTriggerCondition.Incoming),
          dryRun = false,
          state = TreasuryRuleRep.State.Active
        )
      )
    )

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.OnlyPartialAmountAvailable,
          metadata = mapOf(
            "amount" to 625000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `rejected transaction`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockCapitalRepayment(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
      expectedAmount = Money.fromDollarsAndCents(6250, 0),
      idempotencyKey = uuidGenerator[1],
      status = CapitalRepaymentTransactionRep.Status.Rejected,
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = null,
          metadata = mapOf(
            "amount" to 625000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `limit - zero`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      limit = Money.ZERO,
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(0, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `runningBalance - zero`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      runningBalance = Balance.ZERO,
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(0, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `runningBalance - partial`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      runningBalance = Balance.fromDollarsAndCents(-6000, 0),
    )
    mockCapitalRepayment(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
      expectedAmount = Money.fromDollarsAndCents(6000, 0),
      idempotencyKey = uuidGenerator[1],
      status = CapitalRepaymentTransactionRep.Status.Sent,
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 600000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `no line of credit`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.AmountInvalid,
          metadata = mapOf(
            "amount" to null,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  private suspend fun addRule(multiplier: BigDecimal, pullPartialPayments: Boolean = true) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "Line of credit installments",
          businessGuid = businessGuid,
          rule = Rule.CreditInstallment(
            amountStrategy = Rule.CreditInstallment.AmountStrategy.LimitMultiplier(multiplier),
            creditAccountGuid = capitalAccountGuid,
            pullPartialPayments = pullPartialPayments,
            retryPolicy = Rule.CreditInstallment.RetryPolicy.PartialAllowed,
          ),
        )
      )
    ).guid
  }
}
