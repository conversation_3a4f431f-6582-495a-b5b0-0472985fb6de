package co.highbeam.rule

import co.highbeam.api.lineOfCredit.reporting.LineOfCreditBusinessReportingApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.reporting.LineOfCreditBusinessReportingClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.lineOfCredit.reporting.LineOfCreditAgingRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.rep.DepositAccountRep
import io.mockk.coEvery
import io.mockk.coVerify
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CapitalUnpaidDrawdownsInstallmentAnyAccountTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val capitalAccountGuid: UUID = UUID.randomUUID()
  private lateinit var ruleGuid: UUID
  private val bankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = false,
    balance = Balance.fromDollarsAndCents(12000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )

  @BeforeEach
  fun setup() = integrationTest {
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount,
      businessGuid = businessGuid,
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
  }

  @Test
  fun `happy case`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1250, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = bankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - partial`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
      businessGuid = businessGuid,
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = bankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    }

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )

    assertThat(rulesForBusiness).contains(
      TreasuryRuleRep(
        guid = uuidGenerator[2],
        businessGuid = businessGuid,
        state = TreasuryRuleRep.State.Active,
        name = "autoManagedCreditRule_${capitalAccountGuid}_retry_${uuidGenerator[1]}",
        rule = Rule.CapitalInstallmentRetry(
          bankAccountGuid = null,
          capitalAccountGuid = capitalAccountGuid,
          amount = Money.fromDollarsAndCents(250, 0), // 1250 - 1000
          pullPartialPayments = false
        ),
        trigger = RuleTrigger.Transaction(
          conditions = listOf(TransactionTriggerCondition.Incoming),
          dryRun = false,
          state = TreasuryRuleRep.State.Active
        )
      )
    )

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `fully paid`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
      )
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `no aging`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(listOf())
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `almost paid`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(9999, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = bankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 100,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - dry run`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, true)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `partial pull attempt`() = integrationTest {
    addRule(BigDecimal("0.0625"), pullPartialPayments = false)
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
      businessGuid = businessGuid,
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = bankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    }

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )

    assertThat(rulesForBusiness).contains(
      TreasuryRuleRep(
        guid = uuidGenerator[2],
        businessGuid = businessGuid,
        state = TreasuryRuleRep.State.Active,
        name = "autoManagedCreditRule_${capitalAccountGuid}_retry_${uuidGenerator[1]}",
        rule = Rule.CapitalInstallmentRetry(
          bankAccountGuid = null,
          capitalAccountGuid = capitalAccountGuid,
          amount = Money.fromDollarsAndCents(1250, 0), // 1250 - 1000
          pullPartialPayments = false
        ),
        trigger = RuleTrigger.Transaction(
          conditions = listOf(TransactionTriggerCondition.Incoming),
          dryRun = false,
          state = TreasuryRuleRep.State.Active
        )
      )
    )

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.OnlyPartialAmountAvailable,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `rejected transaction`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalRepayment(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
      expectedAmount = Money.fromDollarsAndCents(1250, 0),
      idempotencyKey = uuidGenerator[1],
      status = CapitalRepaymentTransactionRep.Status.Rejected,
      bankAccountGuid = bankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  private suspend fun addRule(multiplier: BigDecimal, pullPartialPayments: Boolean = true) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "Line of credit installments",
          businessGuid = businessGuid,
          rule = Rule.CreditInstallment(
            amountStrategy = Rule.CreditInstallment.AmountStrategy.UnpaidDrawdown(multiplier),
            creditAccountGuid = capitalAccountGuid,
            bankAccountGuid = bankAccount.guid,
            pullPartialPayments = pullPartialPayments,
            retryPolicy = Rule.CreditInstallment.RetryPolicy.AllOrNothing
          ),
        )
      )
    ).guid
  }

  private suspend fun mockAgingReport(amounts: List<Pair<Money, Money>>) {
    coEvery {
      get<LineOfCreditBusinessReportingClient>().request(
        LineOfCreditBusinessReportingApi.GetCreditAging(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
        )
      )
    } returns amounts.map {
      LineOfCreditAgingRep(
        drawdownDate = LocalDate.now(clock),
        drawdownId = UUID.randomUUID().toString(),
        drawdownAmount = it.first,
        repaymentAmount = it.second,
        apr = BigDecimal.ONE,
        interestCollected = Money.ZERO,
        loanAgeInWeeks = 0,
        noticeAddress = null,
        activatedAt = null,
        repaymentDate = null,
      )
    }
  }
}
