package co.highbeam.rule

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.BookPaymentRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoPayment
import co.unit.rep.UnitCoTransactionRep
import io.mockk.coEvery
import io.mockk.coVerify
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class MaximumBalanceTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val bankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(2000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )
  private val highYieldBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "321",
    isPrimary = false,
    balance = Balance.fromDollarsAndCents(500, 0),
    type = BankAccountRep.Type.HighYield,
    depositProduct = DepositAccountRep.DepositProduct.HighYield,
    status = BankAccountRep.Status.OPEN,
  )
  private lateinit var ruleGuid: UUID

  @BeforeEach
  fun setup() = integrationTest {
    addRule(Money.fromDollarsAndCents(0, 0))
    mockUnitBookTransfer(UnitCoPayment.Status.Sent)
    mockBusinessAccounts(
      businessGuid = businessGuid,
      bankAccounts = listOf(bankAccount, highYieldBankAccount)
    )
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount,
      businessGuid = businessGuid,
    )
  }

  @Test
  fun `money - sweep`() = integrationTest {
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money.fromDollarsAndCents(2000, 0),
          description = "Smart Yield - Maintain target balance",
          idempotencyKey = uuidGenerator[1],
          fromAccountId = "123",
          toAccountId = "321",
          tags = BookPaymentRep.Tags(UnitCoTransactionRep.TransactionType.TreasuryTransfer),
        )
      )
    }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 200000,
            "unitCoTransactionId" to "1231",
          ),
        )
      )
    )
  }

  @Test
  fun `money - sweep - dry run`() = integrationTest {
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, true)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 200000,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `money - just equal`() = integrationTest {
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(0, 0)
      ),
      businessGuid = businessGuid
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 0,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 0,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `money - less`() = integrationTest {
    mockGetBankAccountByBusiness(
      bankAccount = bankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(-500, 0)
      ),
      businessGuid = businessGuid
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to -50000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 0,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `bank selection - no account`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.Get(
          accountGuid = bankAccount.guid,
        )
      )
    } returns null
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.AccountInvalid,
          metadata = mapOf(
            "fromAccount" to null,
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to null,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `bank selection - no high yield`() = integrationTest {
    mockBusinessAccounts(
      businessGuid = businessGuid,
      bankAccounts = listOf(bankAccount)
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.AccountInvalid,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to null,
            "amount" to 200000,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `bank selection - closed high yield`() = integrationTest {
    mockBusinessAccounts(
      businessGuid = businessGuid,
      bankAccounts = listOf(
        bankAccount, highYieldBankAccount.copy(
          status = BankAccountRep.Status.CLOSED
        )
      )
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.AccountInvalid,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to null,
            "amount" to 200000,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `bank selection - separate bank high yield`() = integrationTest {
    mockBusinessAccounts(
      businessGuid = businessGuid,
      bankAccounts = listOf(
        bankAccount, highYieldBankAccount.copy(
          depositProduct = DepositAccountRep.DepositProduct.HighYieldThread
        )
      )
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) { get<UnitCoClient>().payment.createBook(any()) }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.AccountInvalid,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to null,
            "amount" to 200000,
            "unitCoTransactionId" to null,
          ),
        )
      )
    )
  }

  @Test
  fun `money - sweep - unit rejection`() = integrationTest {
    mockUnitBookTransfer(
      status = UnitCoPayment.Status.Rejected,
      reason = UnitCoPayment.Reason.DailyACHCreditLimitExceeded,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createBook(
        BookPaymentRep.Creator(
          amount = Money.fromDollarsAndCents(2000, 0),
          description = "Smart Yield - Maintain target balance",
          idempotencyKey = uuidGenerator[1],
          fromAccountId = "123",
          toAccountId = "321",
          tags = BookPaymentRep.Tags(UnitCoTransactionRep.TransactionType.TreasuryTransfer),
        )
      )
    }
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = "Daily ACH credit limit exceeded",
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to bankAccount.guid.toString(),
              "accountNumber" to bankAccount.accountNumber,
              "routingNumber" to bankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to bankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 200000,
            "unitCoTransactionId" to "1231",
          ),
        )
      )
    )
  }

  private suspend fun addRule(maximumBalance: Money) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "TestAutoSweep",
          businessGuid = businessGuid,
          rule = Rule.MaximumBalance(
            maximumBalance = maximumBalance,
            bankAccountGuid = bankAccount.guid,
          ),
        )
      )
    ).guid
  }
}
