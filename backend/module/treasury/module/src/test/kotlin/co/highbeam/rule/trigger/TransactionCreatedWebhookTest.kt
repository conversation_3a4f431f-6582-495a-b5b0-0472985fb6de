package co.highbeam.rule.trigger

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.listener.treasury.TreasuryUnitCoWebhookListener
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import io.mockk.coVerify
import java.math.BigDecimal
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class TransactionCreatedWebhookTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val capitalAccountGuid: UUID = UUID.randomUUID()
  private lateinit var ruleGuid: UUID
  private val primaryBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(120_000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
    businessGuid = businessGuid,
  )

  @BeforeEach
  fun setup() = integrationTest {
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockGetBankAccountByBusiness(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
    mockBankAccount(primaryBankAccount.unitCoDepositAccountId)
  }

  @Test
  fun `happy case`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(90_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 90_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 120_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `outgoing transaction`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/outgoing-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/outgoing-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).isEmpty()
  }

  @Test
  fun `happy case - sender match`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
          TransactionTriggerCondition.Senders(listOf("AFFIRM INC")),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(90_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 90_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 120_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - sender doesnt match`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
          TransactionTriggerCondition.Senders(listOf("Highbema")),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(12000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).isEmpty()
  }

  @Test
  fun `happy case - bank account match`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
          TransactionTriggerCondition.BankAccounts(listOf(primaryBankAccount.guid)),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(90_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 90_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 120_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - bank account doesnt match`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
          TransactionTriggerCondition.BankAccounts(listOf(UUID.randomUUID())),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).isEmpty()
  }

  @Test
  fun `happy case -type doesnt match`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Types(listOf("Wire")),
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).isEmpty()
  }

  @Test
  fun `dry run`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
          TransactionTriggerCondition.Types(listOf("ACH", "Wire")),
        ),
        dryRun = true,
        state = TreasuryRuleRep.State.Active,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "amount" to 90_000_00,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 120_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `not active trigger`() = integrationTest {
    addRule(
      RuleTrigger.Transaction(
        conditions = listOf(
          TransactionTriggerCondition.Incoming,
        ),
        dryRun = false,
        state = TreasuryRuleRep.State.Terminated,
      )
    )
    mockUnitTransactionGet("event/incoming-transaction.json")

    get<TreasuryUnitCoWebhookListener>().onReceive(
      objectMapper.readValue<JsonNode>(
        Resources.getResource("event/incoming-transaction-created-event.json"),
      )
    )

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(90_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).isEmpty()
  }

  private suspend fun addRule(ruleTrigger: RuleTrigger) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "Capital installments",
          businessGuid = businessGuid,
          rule = Rule.CreditInstallment(
            amountStrategy = Rule.CreditInstallment.AmountStrategy.LimitMultiplier(BigDecimal.ONE),
            creditAccountGuid = capitalAccountGuid,
          ),
          trigger = ruleTrigger,
        )
      )
    ).guid
  }

  private fun mockBankAccount(unitCoDepositAccountId: String) {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoDepositAccountId)
      )
    } returns primaryBankAccount
  }

  private fun mockUnitTransactionGet(fileName: String = "event/incoming-transaction.json") {
    val transactionJson = objectMapper.readValue<JsonNode>(
      Resources.getResource(fileName),
    )
    coEvery {
      get<UnitCoClient>().transaction.get(
        primaryBankAccount.unitCoDepositAccountId,
        transactionJson.get("id").asText(),
      )
    } returns objectMapper.readValue(transactionJson.toString())
  }
}
