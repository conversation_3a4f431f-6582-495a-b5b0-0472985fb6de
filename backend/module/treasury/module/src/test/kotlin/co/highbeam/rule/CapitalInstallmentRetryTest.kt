package co.highbeam.rule

import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.rep.treasury.ruleTrigger.TransactionTriggerCondition
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.rep.DepositAccountRep
import io.mockk.coVerify
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CapitalInstallmentRetryTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val capitalAccountGuid: UUID = UUID.randomUUID()
  private lateinit var ruleGuid: UUID
  private val primaryBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(12_000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )

  @BeforeEach
  fun setup() = integrationTest {
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
  }

  @Test
  fun `happy case`() = integrationTest {
    mockRemoveJob(exists = true)
    addRule(repaymentAmount = Money.fromDollarsAndCents(10_000, 0))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(10_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(10_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 10_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 12_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )

    val updatedRule = treasuryRuleClient(TreasuryRuleApi.Get(ruleGuid))
    assertThat(updatedRule?.state).isEqualTo(TreasuryRuleRep.State.Terminated)
  }

  @Test
  fun `happy case - partial pull`() = integrationTest {
    addRule(repaymentAmount = Money.fromDollarsAndCents(10_000, 0))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1_000, 0),
      ),
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 10_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )

    val updatedRule = treasuryRuleClient(TreasuryRuleApi.Get(ruleGuid))
    assertThat(updatedRule?.state).isEqualTo(TreasuryRuleRep.State.Active)
    val ruleRep = updatedRule!!.rule as Rule.CapitalInstallmentRetry
    assertThat(ruleRep.amount).isEqualTo(Money.fromDollarsAndCents(9_000, 0))
  }

  @Test
  fun `happy case - dry run`() = integrationTest {
    mockRemoveJob(exists = true)
    addRule(repaymentAmount = Money.fromDollarsAndCents(10_000, 0), dryRun = true)
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, true)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(10_000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "amount" to 10_000_00,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 12_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )

    val updatedRule = treasuryRuleClient(TreasuryRuleApi.Get(ruleGuid))
    assertThat(updatedRule?.state).isEqualTo(TreasuryRuleRep.State.Terminated)
  }

  @Test
  fun `partial pull attempt`() = integrationTest {
    addRule(
      repaymentAmount = Money.fromDollarsAndCents(10_000, 0),
      pullPartialPayments = false
    )
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
    )
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1_000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )
    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.OnlyPartialAmountAvailable,
          metadata = mapOf(
            "amount" to 10_000_00,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `runningBalance - zero`() = integrationTest {
    mockRemoveJob(exists = true)
    addRule(repaymentAmount = Money.fromDollarsAndCents(10_000, 0))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      runningBalance = Balance.ZERO,
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(0, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 12_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )

    val updatedRule = treasuryRuleClient(TreasuryRuleApi.Get(ruleGuid))
    assertThat(updatedRule?.state).isEqualTo(TreasuryRuleRep.State.Terminated)
  }

  @Test
  fun `runningBalance - partial`() = integrationTest {
    mockRemoveJob(exists = true)
    addRule(repaymentAmount = Money.fromDollarsAndCents(10_000, 0))
    mockCapitalAccountGet(
      businessGuid = businessGuid,
      capitalAccountGuid = capitalAccountGuid,
      runningBalance = Balance.fromDollarsAndCents(-6000, 0),
    )
    mockCapitalRepayment(
      capitalAccountGuid = capitalAccountGuid,
      businessGuid = businessGuid,
      expectedAmount = Money.fromDollarsAndCents(6000, 0),
      idempotencyKey = uuidGenerator[1],
      status = CapitalRepaymentTransactionRep.Status.Sent,
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = capitalAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(6000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 6_000_00,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 12_000_00,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )

    val updatedRule = treasuryRuleClient(TreasuryRuleApi.Get(ruleGuid))
    assertThat(updatedRule?.state).isEqualTo(TreasuryRuleRep.State.Terminated)
  }

  private suspend fun addRule(
    repaymentAmount: Money,
    dryRun: Boolean = false,
    pullPartialPayments: Boolean = true,
  ) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "Retry capital payment",
          businessGuid = businessGuid,
          rule = Rule.CapitalInstallmentRetry(
            amount = repaymentAmount,
            pullPartialPayments = pullPartialPayments,
            capitalAccountGuid = capitalAccountGuid,
          ),
          trigger = RuleTrigger.Transaction(
            conditions = listOf(TransactionTriggerCondition.Incoming),
            dryRun = dryRun,
            state = TreasuryRuleRep.State.Active
          )
        )
      )
    ).guid
  }
}
