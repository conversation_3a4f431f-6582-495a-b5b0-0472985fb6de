package co.highbeam.rule

import co.highbeam.api.lineOfCredit.reporting.LineOfCreditBusinessReportingApi
import co.highbeam.api.lineOfCredit.transaction.LineOfCreditTransactionsApi
import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.capital.transaction.rep.CapitalRepaymentTransactionRep
import co.highbeam.client.lineOfCredit.reporting.LineOfCreditBusinessReportingClient
import co.highbeam.client.lineOfCredit.transaction.LineOfCreditTransactionsClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.lineOfCredit.reporting.LineOfCreditAgingRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.rep.DepositAccountRep
import io.mockk.coEvery
import io.mockk.coVerify
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CapitalUnpaidDrawdownsInstallmentTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val creditAccountGuid: UUID = UUID.randomUUID()
  private lateinit var ruleGuid: UUID
  private val primaryBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(12000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )

  @BeforeEach
  fun setup() = integrationTest {
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
  }

  @Test
  fun `happy case`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = creditAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1250, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `fully paid`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
      )
    )
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `no aging`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(listOf())
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = TreasuryRepReason.ZeroAmount,
          metadata = mapOf(
            "amount" to 0,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `almost paid`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(9999, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = creditAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 100,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - partial`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = creditAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )
    assertThat(rulesForBusiness).hasSize(1) // retry disabled

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `happy case - dry run`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, true)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.DryRunCompleted,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `partial pull attempt`() = integrationTest {
    addRule(BigDecimal("0.0625"), pullPartialPayments = false)
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount.copy(
        availableBalance = Balance.fromDollarsAndCents(1000, 0),
      ),
    )
    mockAgingReport(
      listOf(
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(10000, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(3750, 0),
        Money.fromDollarsAndCents(10000, 0) to Money.fromDollarsAndCents(0, 0),
      )
    )
    mockCapitalRepayment(
      businessGuid = businessGuid,
      capitalAccountGuid = creditAccountGuid,
      expectedAmount = Money.fromDollarsAndCents(1000, 0),
      idempotencyKey = uuidGenerator[1],
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 0) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1000, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val rulesForBusiness = treasuryRuleClient(
      TreasuryRuleApi.GetForBusiness(businessGuid)
    )
    assertThat(rulesForBusiness).hasSize(1) // retry disabled

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = TreasuryRepReason.OnlyPartialAmountAvailable,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to null,
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 100000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  @Test
  fun `rejected transaction`() = integrationTest {
    addRule(BigDecimal("0.0625"))
    mockCapitalRepayment(
      capitalAccountGuid = creditAccountGuid,
      businessGuid = businessGuid,
      expectedAmount = Money.fromDollarsAndCents(1250, 0),
      idempotencyKey = uuidGenerator[1],
      status = CapitalRepaymentTransactionRep.Status.Rejected,
      bankAccountGuid = primaryBankAccount.guid,
    )

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid, false)))

    coVerify(exactly = 1) {
      get<LineOfCreditTransactionsClient>().request(
        LineOfCreditTransactionsApi.CreateRepayment(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
          rep = CapitalRepaymentTransactionRep.Creator(
            amount = Money.fromDollarsAndCents(1250, 0),
            idempotencyKey = uuidGenerator[1],
            bankAccountGuid = primaryBankAccount.guid,
          ),
        )
      )
    }

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForRule(ruleGuid)
    )

    assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[1],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid,
          state = TreasuryTransactionRep.State.Failed,
          reason = null,
          metadata = mapOf(
            "amount" to 125000,
            "unitCoId" to "1231",
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 1200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
          ),
        )
      )
    )
  }

  private suspend fun addRule(multiplier: BigDecimal, pullPartialPayments: Boolean = true) {
    ruleGuid = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "Line of credit installments",
          businessGuid = businessGuid,
          rule = Rule.CreditInstallment(
            amountStrategy = Rule.CreditInstallment.AmountStrategy.UnpaidDrawdown(multiplier),
            creditAccountGuid = creditAccountGuid,
            pullPartialPayments = pullPartialPayments,
            retryPolicy = Rule.CreditInstallment.RetryPolicy.None
          ),
        )
      )
    ).guid
  }

  private suspend fun mockAgingReport(amounts: List<Pair<Money, Money>>) {
    coEvery {
      get<LineOfCreditBusinessReportingClient>().request(
        LineOfCreditBusinessReportingApi.GetCreditAging(
          businessGuid = businessGuid,
          creditAccountGuid = creditAccountGuid,
        )
      )
    } returns amounts.map {
      LineOfCreditAgingRep(
        drawdownDate = LocalDate.now(clock),
        drawdownId = UUID.randomUUID().toString(),
        drawdownAmount = it.first,
        repaymentAmount = it.second,
        apr = BigDecimal.ONE,
        interestCollected = Money.ZERO,
        loanAgeInWeeks = 0,
        noticeAddress = null,
        activatedAt = null,
        repaymentDate = null,
      )
    }
  }
}
