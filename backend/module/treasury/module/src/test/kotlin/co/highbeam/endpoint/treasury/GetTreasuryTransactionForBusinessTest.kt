package co.highbeam.endpoint.treasury

import co.highbeam.api.treasury.TreasuryRuleApi
import co.highbeam.api.treasury.TreasuryTaskApi
import co.highbeam.api.treasury.TreasuryTransactionApi
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.treasury.rule.Rule
import co.highbeam.rep.treasury.rule.TreasuryRuleRep
import co.highbeam.rep.treasury.task.TreasuryTaskRep
import co.highbeam.rep.treasury.transaction.TreasuryTransactionRep
import co.highbeam.server.Server
import co.highbeam.testing.TreasuryFeatureIntegrationTest
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoPayment
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetTreasuryTransactionForBusinessTest(
  server: Server<*>,
) : TreasuryFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val primaryBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "123",
    isPrimary = true,
    balance = Balance.fromDollarsAndCents(2000, 0),
    type = BankAccountRep.Type.DepositAccount,
    depositProduct = DepositAccountRep.DepositProduct.Checking,
    status = BankAccountRep.Status.OPEN,
  )
  private val highYieldBankAccount = bankAccountRep(
    bankAccountGuid = UUID.randomUUID(),
    unitCoId = "321",
    isPrimary = false,
    balance = Balance.fromDollarsAndCents(500, 0),
    type = BankAccountRep.Type.HighYield,
    depositProduct = DepositAccountRep.DepositProduct.HighYield,
    status = BankAccountRep.Status.OPEN,
  )
  private lateinit var ruleGuid1: UUID
  private lateinit var ruleGuid2: UUID

  @BeforeEach
  fun setup() = integrationTest {
    addRule()
    mockUnitBookTransfer(UnitCoPayment.Status.Sent)
    mockBusinessAccounts(
      businessGuid = businessGuid,
      bankAccounts = listOf(primaryBankAccount, highYieldBankAccount)
    )
    mockPrimaryBusinessAccount(
      businessGuid = businessGuid,
      bankAccount = primaryBankAccount,
    )
  }

  @Test
  fun `money - sweep`() = integrationTest {
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid1, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForBusiness(businessGuid)
    )
    Assertions.assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[2],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid1,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 50000,
            "unitCoTransactionId" to "1231",
          ),
        )
      )
    )
  }

  @Test
  fun `no transactions`() = integrationTest {
    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForBusiness(businessGuid)
    )
    Assertions.assertThat(transactions).isEqualTo(emptyList<TreasuryTransactionRep>())
  }

  @Test
  fun `multiple transactions`() = integrationTest {
    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid1, false)))

    treasuryTaskClient(TreasuryTaskApi.Execute(TreasuryTaskRep(ruleGuid2, false)))

    val transactions = treasuryTransactionClient(
      TreasuryTransactionApi.GetForBusiness(businessGuid)
    )
    Assertions.assertThat(transactions).hasSameTransactionsAs(
      listOf(
        TreasuryTransactionRep(
          guid = uuidGenerator[2],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid1,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "amount" to 50000,
            "unitCoTransactionId" to "1231",
          ),
        ),
        TreasuryTransactionRep(
          guid = uuidGenerator[3],
          businessGuid = businessGuid,
          ruleGuid = ruleGuid2,
          state = TreasuryTransactionRep.State.Completed,
          reason = null,
          metadata = mapOf(
            "fromAccount" to mapOf(
              "guid" to highYieldBankAccount.guid.toString(),
              "accountNumber" to highYieldBankAccount.accountNumber,
              "routingNumber" to highYieldBankAccount.routingNumber,
              "availableBalance" to 50000,
              "name" to highYieldBankAccount.name,
              "unitCoDepositAccountId" to "321",
              "plaidProcessorToken" to null,
            ),
            "toAccount" to mapOf(
              "guid" to primaryBankAccount.guid.toString(),
              "accountNumber" to primaryBankAccount.accountNumber,
              "routingNumber" to primaryBankAccount.routingNumber,
              "availableBalance" to 200000,
              "name" to primaryBankAccount.name,
              "unitCoDepositAccountId" to "123",
              "plaidProcessorToken" to null,
            ),
            "amount" to 50000,
            "unitCoTransactionId" to "1231",
          ),
        ),
      )
    )
  }

  private suspend fun addRule() {
    ruleGuid1 = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "TestAutoSweep",
          businessGuid = businessGuid,
          rule = Rule.MaximumBalance(Money.fromDollarsAndCents(1500, 0)),
        )
      )
    ).guid
    ruleGuid2 = treasuryRuleClient(
      TreasuryRuleApi.Create(
        TreasuryRuleRep.Creator(
          name = "TestAutoSweep",
          businessGuid = businessGuid,
          rule = Rule.MinimumOperatingBalance(Money.fromDollarsAndCents(2500, 0)),
        )
      )
    ).guid
  }
}
