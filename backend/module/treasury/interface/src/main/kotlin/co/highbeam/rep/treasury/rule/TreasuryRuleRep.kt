package co.highbeam.rep.treasury.rule

import co.highbeam.rep.CreatorRep
import co.highbeam.rep.treasury.ruleTrigger.RuleTrigger
import co.highbeam.validation.RepValidation
import java.util.UUID

data class TreasuryRuleRep(
  val guid: UUID,
  val name: String,
  val state: State,
  val businessGuid: UUID,
  val rule: Rule,
  val trigger: RuleTrigger?,
) {
  data class Creator(
    val name: String,
    val businessGuid: UUID,
    val rule: Rule,
    val trigger: RuleTrigger? = null,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Updater(
    val name: String? = null,
    val rule: Rule? = null,
    val state: State? = null,
    val trigger: RuleTrigger? = null,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  enum class State { Active, Terminated, Paused }
}
