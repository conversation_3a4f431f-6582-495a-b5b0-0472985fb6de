package co.highbeam.rep.treasury.ruleTrigger

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import java.util.UUID

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes(
  JsonSubTypes.Type(TransactionTriggerCondition.BankAccounts::class, name = "BankAccounts"),
  JsonSubTypes.Type(TransactionTriggerCondition.Incoming::class, name = "Incoming"),
  JsonSubTypes.Type(
    TransactionTriggerCondition.CapitalAccounts::class,
    name = "CapitalAccounts"
  ),
  JsonSubTypes.Type(TransactionTriggerCondition.Senders::class, name = "Senders"),
  JsonSubTypes.Type(TransactionTriggerCondition.Types::class, name = "Types"),
)
sealed class TransactionTriggerCondition {
  data class BankAccounts(val bankAccounts: List<UUID>) : TransactionTriggerCondition() {
    override fun shouldTrigger(data: TransactionTriggerData): Boolean =
      data.bankAccountGuid in bankAccounts
  }

  data class CapitalAccounts(val capitalAccounts: List<UUID>) : TransactionTriggerCondition() {
    override fun shouldTrigger(data: TransactionTriggerData): Boolean =
      data.capitalAccountGuid in capitalAccounts
  }

  data class Senders(val senders: List<String>) : TransactionTriggerCondition() {
    override fun shouldTrigger(data: TransactionTriggerData): Boolean =
      data.senderName in senders
  }

  object Incoming : TransactionTriggerCondition() {
    override fun shouldTrigger(data: TransactionTriggerData): Boolean =
      data.isIncomingTransaction
  }

  data class Types(val types: List<String>) : TransactionTriggerCondition() {
    override fun shouldTrigger(data: TransactionTriggerData): Boolean =
      data.type in types
  }

  abstract fun shouldTrigger(data: TransactionTriggerData): Boolean
}
