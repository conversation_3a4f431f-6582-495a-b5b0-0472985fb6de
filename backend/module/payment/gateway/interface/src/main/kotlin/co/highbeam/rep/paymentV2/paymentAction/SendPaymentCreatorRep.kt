package co.highbeam.rep.paymentV2.paymentAction

import co.highbeam.money.Money
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  include = JsonTypeInfo.As.WRAPPER_OBJECT,
  property = "type",
)
@JsonSubTypes(
  JsonSubTypes.Type(
    value = SendPaymentCreatorRep.InternationalWire::class,
    name = "InternationalWire",
  ),
  JsonSubTypes.Type(
    value = SendPaymentCreatorRep.UnitPayment::class,
    name = "UnitPayment",
  ),
  JsonSubTypes.Type(
    value = SendPaymentCreatorRep.UnitTransfer::class,
    name = "UnitTransfer",
  ),
  JsonSubTypes.Type(
    value = SendPaymentCreatorRep.Ach::class,
    name = "Ach",
  ),
  JsonSubTypes.Type(
    value = SendPaymentCreatorRep.DomesticWire::class,
    name = "DomesticWire",
  ),
)
sealed class SendPaymentCreatorRep : CreatorRep {
  data class InternationalWire(
    val receiveAmount: Money?,
    val sendAmount: Money?,
  ) : SendPaymentCreatorRep() {
    override fun validate(): RepValidation = RepValidation {
      validate amount@{
        /**
         * Only specify receiveAmount or sendAmount.
         * The existing value will be used for the fixed side.
         */
        if (receiveAmount == null && sendAmount == null) return@amount false
        if (receiveAmount != null && sendAmount != null) return@amount false
        return@amount true
      }
    }
  }

  object UnitPayment : SendPaymentCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }

  object UnitTransfer : SendPaymentCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }

  object Ach : SendPaymentCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }

  object DomesticWire : SendPaymentCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
