package co.highbeam.rep.paymentV2.payment

import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.JsonNode
import java.util.UUID

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  include = JsonTypeInfo.As.WRAPPER_OBJECT,
  property = "type",
)
@JsonSubTypes(
  JsonSubTypes.Type(
    value = PaymentDetailCreatorRep.Ach::class,
    name = "Ach",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailCreatorRep.DomesticWire::class,
    name = "DomesticWire",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailCreatorRep.InternationalWire::class,
    name = "InternationalWire",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailCreatorRep.UnitPayment::class,
    name = "UnitPayment",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailCreatorRep.UnitTransfer::class,
    name = "UnitTransfer",
  ),
)
sealed class PaymentDetailCreatorRep : CreatorRep {
  data class InternationalWire(
    val bankAccountGuid: UUID,
    val description: String,
    val fixedSide: ConversionSide,
    val generalPaymentMetadataGuid: UUID?,
    val idempotencyKey: String,
    val notificationEmailAddress: String?,
    val payeeGuid: UUID,
    val paymentType: String,
    val purposeCode: String?,
    val invoiceNumber: String?,
    val invoiceDate: String?,
    val reason: String,
    val receiveAmount: Money,
    val receiveCurrency: String,
    val sendAmount: Money,
    val tags: Map<String, String>,
  ) : PaymentDetailCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class UnitPayment(
    val unitPayload: JsonNode,
  ) : PaymentDetailCreatorRep() {
    val amount: Money
      @JsonIgnore get() =
        Money(unitPayload.get("attributes").get("amount").longValue())

    override fun validate(): RepValidation = RepValidation.none()
  }

  data class UnitTransfer(
    val unitPayload: JsonNode,
  ) : PaymentDetailCreatorRep() {
    val amount: Money
      @JsonIgnore get() =
        Money(unitPayload.get("attributes").get("amount").longValue())

    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Ach(
    val amount: Money,
    val accountNumber: String,
    val routingNumber: String,
    val payeeName: String,
    val description: String,
    val direction: MoneyDirection,
    val addenda: String,
    val fromBankAccountGuid: UUID,
    val idempotencyKey: String,
    val sameDay: Boolean,
    val tags: Map<String, String>,
  ) : PaymentDetailCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class DomesticWire(
    val amount: Money,
    val accountNumber: String,
    val routingNumber: String,
    val address: AddressRep,
    val payeeName: String,
    val description: String,
    val direction: MoneyDirection,
    val fromBankAccountGuid: UUID,
    val idempotencyKey: String,
    val tags: Map<String, String>,
  ) : PaymentDetailCreatorRep() {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
