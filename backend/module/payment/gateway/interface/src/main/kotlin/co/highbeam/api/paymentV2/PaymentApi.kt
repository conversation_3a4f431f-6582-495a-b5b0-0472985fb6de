package co.highbeam.api.paymentV2

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.ContentType
import io.ktor.http.HttpMethod
import java.util.UUID

object PaymentApi {
  data class Get(val paymentGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/v2/payments/$paymentGuid",
  )

  data class List(
    val businessGuid: UUID,
    val status: PaymentRep.Status,
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/v2/payments",
    qp = mapOf(
      "businessGuid" to listOf(businessGuid.toString()),
      "status" to listOf(status.name),
    ),
  )

  data class Create(val creator: PaymentRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments",
    body = creator,
  )

  data class CreateReceiptPdf(val paymentGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments/$paymentGuid/receipt",
    contentType = ContentType.Application.Pdf,
  )

  data class CreateReceiptXhtml(val paymentGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments/$paymentGuid/receipt",
    contentType = ContentType("text", "xhtml"),
  )
}
