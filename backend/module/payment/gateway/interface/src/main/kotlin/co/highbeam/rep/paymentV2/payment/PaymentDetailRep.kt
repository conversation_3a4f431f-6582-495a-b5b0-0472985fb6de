package co.highbeam.rep.paymentV2.payment

import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.JsonNode
import java.util.UUID

private val LARGE_PAYMENT_THRESHOLD = Money.fromDollarsAndCents(250_000, 0)

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  include = JsonTypeInfo.As.WRAPPER_OBJECT,
  property = "type",
)
@JsonSubTypes(
  JsonSubTypes.Type(
    value = PaymentDetailRep.Ach::class,
    name = "Ach",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailRep.DomesticWire::class,
    name = "DomesticWire",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailRep.InternationalWire::class,
    name = "InternationalWire",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailRep.UnitPayment::class,
    name = "UnitPayment",
  ),
  JsonSubTypes.Type(
    value = PaymentDetailRep.UnitTransfer::class,
    name = "UnitTransfer",
  ),
)
sealed class PaymentDetailRep {
  abstract fun requiresImmediateMfa(): Boolean

  data class InternationalWire(
    val fixedSide: ConversionSide,
    val paymentType: String,
    val purposeCode: String?,
    val invoiceNumber: String?,
    val invoiceDate: String?,
    val reason: String,
    val receiveAmount: Money,
    val receiveCurrency: String,
    val payeeGuid: UUID,
    val tags: Map<String, String>,
  ) : PaymentDetailRep() {
    override fun requiresImmediateMfa(): Boolean = false
  }

  data class UnitPayment(
    val unitPayload: JsonNode,
    val payeeGuid: UUID,
  ) : PaymentDetailRep() {
    enum class PaymentType(val value: String) {
      Ach("achPayment"),
      Wire("wirePayment");

      companion object {
        fun fromValue(value: String): PaymentType = PaymentType.values().find {
          it.value == value
        } ?: throw IllegalArgumentException("Invalid PaymentType: $value")
      }
    }

    override fun requiresImmediateMfa(): Boolean {
      val unitType = unitPayload.get("type").textValue()
      val amount = Money(unitPayload.get("attributes").get("amount").longValue())
      return unitType in setOf("achPayment", "wirePayment") && amount >= LARGE_PAYMENT_THRESHOLD
    }
  }

  data class UnitTransfer(
    val unitPayload: JsonNode,
  ) : PaymentDetailRep() {
    enum class PaymentType(val value: String) {
      Ach("achPayment"),
      Book("bookPayment"),
      Wire("wirePayment");

      companion object {
        fun fromValue(value: String): PaymentType = PaymentType.values().find {
          it.value == value
        } ?: throw IllegalArgumentException("Invalid PaymentType: $value")
      }
    }

    override fun requiresImmediateMfa(): Boolean {
      val unitType = unitPayload.get("type").textValue()
      val amount = Money(unitPayload.get("attributes").get("amount").longValue())
      return unitType in setOf("achPayment", "wirePayment") && amount >= LARGE_PAYMENT_THRESHOLD
    }
  }

  data class Ach(
    val amount: Money,
    val accountNumber: String,
    val routingNumber: String,
    val description: String,
    val addenda: String,
    val direction: MoneyDirection,
    val payeeName: String,
    val fromBankAccountGuid: UUID,
    val idempotencyKey: String,
    val sameDay: Boolean,
    val tags: Map<String, String>,
  ) : PaymentDetailRep() {
    override fun requiresImmediateMfa(): Boolean = amount >= LARGE_PAYMENT_THRESHOLD

    constructor(creator: PaymentDetailCreatorRep.Ach) : this(
      amount = creator.amount,
      accountNumber = creator.accountNumber,
      routingNumber = creator.routingNumber,
      description = creator.description,
      direction = creator.direction,
      addenda = creator.addenda,
      fromBankAccountGuid = creator.fromBankAccountGuid,
      idempotencyKey = creator.idempotencyKey,
      payeeName = creator.payeeName,
      sameDay = creator.sameDay,
      tags = creator.tags,
    )
  }

  data class DomesticWire(
    val amount: Money,
    val accountNumber: String,
    val routingNumber: String,
    val addressRep: AddressRep,
    val description: String,
    val direction: MoneyDirection,
    val fromBankAccountGuid: UUID,
    val payeeName: String,
    val idempotencyKey: String,
    val tags: Map<String, String>,
  ) : PaymentDetailRep() {
    override fun requiresImmediateMfa(): Boolean = amount >= LARGE_PAYMENT_THRESHOLD

    constructor(creator: PaymentDetailCreatorRep.DomesticWire) : this(
      amount = creator.amount,
      accountNumber = creator.accountNumber,
      routingNumber = creator.routingNumber,
      addressRep = creator.address,
      description = creator.description,
      direction = creator.direction,
      fromBankAccountGuid = creator.fromBankAccountGuid,
      payeeName = creator.payeeName,
      idempotencyKey = creator.idempotencyKey,
      tags = creator.tags,
    )
  }
}
