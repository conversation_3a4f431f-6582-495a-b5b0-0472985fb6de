package co.highbeam.api.paymentV2

import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object PaymentActionApi {
  data class Send(val paymentGuid: UUID, val sender: SendPaymentCreatorRep) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments/$paymentGuid/send",
    body = sender,
  )

  data class Reject(val paymentGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments/$paymentGuid/reject",
  )

  data class Cancel(val paymentGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/v2/payments/$paymentGuid/cancel",
  )
}
