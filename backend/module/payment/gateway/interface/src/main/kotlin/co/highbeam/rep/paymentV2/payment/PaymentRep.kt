package co.highbeam.rep.paymentV2.payment

import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import com.fasterxml.jackson.databind.JsonNode
import org.jdbi.v3.json.Json
import java.time.ZonedDateTime
import java.util.UUID

data class PaymentRep(
  val guid: UUID,

  val amount: Money,
  val bankAccountGuid: UUID,
  val businessGuid: UUID,
  val description: String,
  val generalPaymentMetadataGuid: UUID?,
  val idempotencyKey: String,
  val notificationEmailAddress: String?,
  val status: Status,
  val reason: String?,

  val createdByUserGuid: UUID?,
  val createdAt: ZonedDateTime,
  val sentByUserGuid: UUID?,
  val sentAt: ZonedDateTime?,
  val rejectedByUserGuid: UUID?,
  val rejectedAt: ZonedDateTime?,

  @Json val detail: PaymentDetailRep,
  @Json val paymentSenderResponse: JsonNode,
) : CompleteRep {
  enum class Status(val description: String) {
    Open("Payment has been drafted but has not been sent."),
    Pending("Payment is in the process of being sent."),
    Sent("Payment has been sent. It has not necessarily been received, and could be rejected."),
    Failed("Payment failed while sending."),
    Rejected("Payment was rejected before ever attempted to be sent."),
    Cancelled("Payment was cancelled by the user."),
  }

  data class Creator(
    val send: Boolean,
    val detail: PaymentDetailCreatorRep,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Creator::detail)
    }
  }
}
