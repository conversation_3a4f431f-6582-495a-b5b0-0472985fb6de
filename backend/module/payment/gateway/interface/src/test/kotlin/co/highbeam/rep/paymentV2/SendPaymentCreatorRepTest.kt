package co.highbeam.rep.paymentV2

import co.highbeam.money.Money
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class SendPaymentCreatorRepTest {
  private val objectMapper: ObjectMapper =
    HighbeamObjectMapper.json {
      prettyPrint(true)
      useTypeConverters(DEFAULT_TYPE_CONVERTERS)
    }.build()

  @Test
  fun unitPayment() {
    val json = getJson("unitPayment")
    val rep = SendPaymentCreatorRep.UnitPayment
    testSerialization(rep, json)
    testDeserialization(json, rep)
  }

  @Test
  fun internationalWire() {
    val json = getJson("internationalWire")
    val rep = SendPaymentCreatorRep.InternationalWire(
      receiveAmount = null,
      sendAmount = Money.fromDollarsAndCents(98, 89),
    )
    testSerialization(rep, json)
    testDeserialization(json, rep)
  }

  private fun testSerialization(rep: SendPaymentCreatorRep, json: String) {
    assertThat(objectMapper.readValue<JsonNode>(objectMapper.writeValueAsString(rep)))
      .isEqualTo(objectMapper.readValue<JsonNode>(json))
  }

  private fun testDeserialization(json: String, rep: SendPaymentCreatorRep) {
    assertThat(objectMapper.readValue<SendPaymentCreatorRep>(json))
      .isEqualTo(rep)
  }

  private fun getJson(name: String): String =
    Resources.getResource("send-payment-creator-rep/$name.json").readText()
}
