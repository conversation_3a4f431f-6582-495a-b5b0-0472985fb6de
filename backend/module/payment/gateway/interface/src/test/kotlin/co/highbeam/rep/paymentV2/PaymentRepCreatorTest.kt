package co.highbeam.rep.paymentV2

import co.highbeam.money.Money
import co.highbeam.rep.paymentV2.payment.ConversionSide
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class PaymentRepCreatorTest {
  private val objectMapper: ObjectMapper =
    HighbeamObjectMapper.json {
      prettyPrint(true)
      useTypeConverters(DEFAULT_TYPE_CONVERTERS)
    }.build()

  private val bankAccountGuid: UUID =
    UUID.fromString("50c4af08-1aac-4253-9a52-5215e0c31467")
  private val idempotencyKey: String =
    UUID.fromString("2d5362f5-1b29-4a90-92d6-c081c321866e").toString()
  private val payeeGuid: UUID =
    UUID.fromString("f02a8481-fdf7-43de-8978-28d1ae2ef572")

  @Test
  fun unitPayment() {
    val json = getJson("unitPayment")
    val rep = PaymentRep.Creator(
      send = false,
      detail = PaymentDetailCreatorRep.UnitPayment(
        unitPayload = objectMapper.convertValue<JsonNode>(
          mapOf(
            "type" to "achPayment",
            "attributes" to mapOf(
              "amount" to 12345,
              "direction" to "Credit",
              "counterparty" to mapOf(
                "routingNumber" to "*********",
                "accountNumber" to "*********",
                "accountType" to "Checking",
                "name" to "Jeff Hudson",
              ),
              "description" to "Jeff Hudso",
              "addenda" to "Payment to Jeff Hudson via highbeam.co",
              "idempotencyKey" to idempotencyKey,
              "tags" to mapOf(
                "recipientGuid" to payeeGuid,
              ),
            ),
            "relationships" to mapOf(
              "account" to mapOf(
                "data" to mapOf(
                  "type" to "depositAccount",
                  "id" to "494633",
                ),
              ),
            ),
          ),
        ),
      ),
    )
    testSerialization(rep, json)
    testDeserialization(json, rep)
  }

  @Test
  fun unitTransfer() {
    val json = getJson("unitTransfer")
    val rep = PaymentRep.Creator(
      send = false,
      detail = PaymentDetailCreatorRep.UnitTransfer(
        unitPayload = objectMapper.convertValue<JsonNode>(
          mapOf(
            "type" to "achPayment",
            "attributes" to mapOf(
              "amount" to 100,
              "direction" to "Debit",
              "plaidProcessorToken" to "processor-sandbox-a99d34e6-65b7-4865-8dca-fd26b4423513",
              "description" to "Internal transfer to Highbeam",
              "counterpartyName" to "Alberta Bobbeth Charleson",
              "idempotencyKey" to "366015d5-0430-419e-8468-51d8c9e5f29f"
            ),
            "relationships" to mapOf(
              "account" to mapOf(
                "data" to mapOf(
                  "type" to "depositAccount",
                  "id" to "1668145"
                )
              )
            )
          )
        ),
      )
    )

    testSerialization(rep, json)
    testDeserialization(json, rep)
  }

  @Test
  fun internationalWire() {
    val json = getJson("internationalWire")
    val rep = PaymentRep.Creator(
      send = false,
      detail = PaymentDetailCreatorRep.InternationalWire(
        bankAccountGuid = bankAccountGuid,
        description = "International wire to Jeff Hudson",
        fixedSide = ConversionSide.Receive,
        generalPaymentMetadataGuid = null,
        idempotencyKey = idempotencyKey,
        notificationEmailAddress = null,
        payeeGuid = payeeGuid,
        paymentType = "Regular",
        purposeCode = null,
        invoiceNumber = null,
        invoiceDate = null,
        reason = "Inventory",
        receiveAmount = Money.fromDollarsAndCents(130, 23),
        receiveCurrency = "CAD",
        sendAmount = Money.fromDollarsAndCents(99, 1),
        tags = emptyMap(),
      ),
    )
    testSerialization(rep, json)
    testDeserialization(json, rep)
  }

  private fun testSerialization(rep: PaymentRep.Creator, json: String) {
    assertThat(objectMapper.readValue<JsonNode>(objectMapper.writeValueAsString(rep)))
      .isEqualTo(objectMapper.readValue<JsonNode>(json))
  }

  private fun testDeserialization(json: String, rep: PaymentRep.Creator) {
    assertThat(objectMapper.readValue<PaymentRep.Creator>(json))
      .isEqualTo(rep)
  }

  private fun getJson(name: String): String =
    Resources.getResource("payment-rep-creator/$name.json").readText()
}
