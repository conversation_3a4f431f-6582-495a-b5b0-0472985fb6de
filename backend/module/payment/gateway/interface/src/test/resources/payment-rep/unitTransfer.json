{"amount": 12345, "bankAccountGuid": "50c4af08-1aac-4253-9a52-5215e0c31467", "businessGuid": "9c509576-1c40-49fc-a32b-01c60123ba60", "createdAt": "2007-12-03T10:15:30.789Z", "createdByUserGuid": "5cedd39a-74a9-4030-b56a-7b97f659dc45", "description": "Internal transfer to Highbeam", "detail": {"UnitTransfer": {"unitPayload": {"type": "achPayment", "attributes": {"amount": 12345, "direction": "Debit", "plaidProcessorToken": "processor-sandbox-a99d34e6-65b7-4865-8dca-fd26b4423513", "description": "Internal transfer to Highbeam", "counterpartyName": "<PERSON> <PERSON><PERSON>", "idempotencyKey": "366015d5-0430-419e-8468-51d8c9e5f29f"}, "relationships": {"account": {"data": {"type": "depositAccount", "id": "1668145"}}}}}}, "reason": null, "generalPaymentMetadataGuid": null, "guid": "f870a13a-17e6-4940-9c7d-03a208b6eb63", "idempotencyKey": "2d5362f5-1b29-4a90-92d6-c081c321866e", "notificationEmailAddress": null, "paymentSenderResponse": {"type": "achPayment", "id": "3581781", "attributes": {"createdAt": "2024-01-31T18:50:40.868Z", "amount": 12345, "direction": "Debit", "description": "Internal", "addenda": "Internal transfer to Highbeam", "counterparty": {"name": "<PERSON> <PERSON><PERSON>", "routingNumber": "*********", "accountNumber": "****************", "accountType": "Checking"}, "status": "Pending", "settlementDate": "2024-02-05", "counterpartyVerificationMethod": "Plaid", "sameDay": false, "secCode": "WEB"}, "relationships": {"account": {"data": {"type": "account", "id": "1668145"}}, "customer": {"data": {"type": "customer", "id": "1170882"}}, "customers": {"data": [{"type": "customer", "id": "1170882"}]}, "org": {"data": {"type": "org", "id": "553"}}}}, "rejectedAt": null, "rejectedByUserGuid": null, "sentAt": null, "sentByUserGuid": null, "status": "Open"}