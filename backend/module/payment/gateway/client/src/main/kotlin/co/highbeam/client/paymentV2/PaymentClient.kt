package co.highbeam.client.paymentV2

import co.highbeam.api.paymentV2.PaymentApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.paymentV2.PAYMENT_FEATURE
import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject
import com.google.inject.name.Named

class PaymentClient @Inject constructor(
  @Named(PAYMENT_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: PaymentApi.Get): PaymentRep? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: PaymentApi.List): List<PaymentRep> =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: PaymentApi.Create): PaymentRep =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: PaymentApi.CreateReceiptPdf): ByteArray? =
    httpClient.request(endpoint).readValueBytes()

  suspend fun request(endpoint: PaymentApi.CreateReceiptXhtml): String? =
    httpClient.request(endpoint).readText()
}
