package co.highbeam.service.paymentV2.payment

import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.service.paymentV2.email.PaymentEmailService
import co.highbeam.store.paymentV2.PaymentStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class PaymentServiceImpl @Inject constructor(
  private val featureFlagService: FeatureFlagService,
  private val paymentEmailService: PaymentEmailService,
  private val paymentStore: PaymentStore,
) : PaymentService {
  private val logger = KotlinLogging.logger {}

  override fun get(guid: UUID): PaymentRep? =
    paymentStore.get(guid)

  override fun list(businessGuid: UUID, status: PaymentRep.Status): List<PaymentRep> =
    paymentStore.list(businessGuid, status)

  override suspend fun create(creator: PaymentRep): PaymentRep {
    logger.info { "Creating payment (creator=$creator)." }
    val payment = paymentStore.create(creator)

    if (!isPaymentPoliciesEnabled(payment) && payment.status == PaymentRep.Status.Open) {
      paymentEmailService.drafted(payment.guid)
    }

    return payment
  }

  private fun isPaymentPoliciesEnabled(payment: PaymentRep) =
    featureFlagService.isEnabled(
      flag = BusinessFlag.PaymentPolicies,
      businessGuid = payment.businessGuid,
    )
}
