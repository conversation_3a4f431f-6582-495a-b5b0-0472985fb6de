package co.highbeam.auth.paymentV2

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject
import java.util.UUID

internal object AuthListPayments {
  internal class Provider @Inject constructor(
    private val authPermission: AuthPermission.Provider,
  ) {
    operator fun invoke(businessGuid: UUID, status: PaymentRep.Status): Auth =
      authPermission(
        permission = when (status) {
          PaymentRep.Status.Open -> Permission.PaymentApproval_Read
          PaymentRep.Status.Pending -> Permission.Payment_Read
          PaymentRep.Status.Sent -> Permission.Payment_Read
          PaymentRep.Status.Failed -> Permission.Payment_Read
          PaymentRep.Status.Rejected -> Permission.PaymentApproval_Read
          PaymentRep.Status.Cancelled -> Permission.Payment_Read
        },
        businessGuid = { businessGuid },
      )
  }
}
