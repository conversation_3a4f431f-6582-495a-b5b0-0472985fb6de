package co.highbeam.service.paymentV2.sender

import co.highbeam.money.Money
import co.highbeam.rep.payment.InternationalPaymentRep
import co.highbeam.rep.paymentV2.payment.ConversionSide
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import java.util.UUID
import co.highbeam.api.payment.PaymentApi as LegacyPaymentApi
import co.highbeam.client.payment.PaymentClient as LegacyPaymentClient

internal class InternationalWirePaymentSender @Inject constructor(
  private val objectMapper: ObjectMapper,
  private val paymentClient: LegacyPaymentClient
) : PaymentSender {
  @Suppress("TooGenericExceptionCaught")
  override suspend fun send(payment: PaymentRep, sender: SendPaymentCreatorRep): PaymentRep {
    val detail = payment.detail as PaymentDetailRep.InternationalWire

    val (sendAmount, receiveAmount) = calculateAmounts(
      payment = payment,
      detail = detail,
      sender = sender as SendPaymentCreatorRep.InternationalWire,
    )
    val buyRate = calculateBuyRate(receiveAmount, sendAmount)

    val paymentResponse = paymentClient.request(
      LegacyPaymentApi.CreateInternationalWire(
        creator = InternationalPaymentRep.Creator(
          amount = sendAmount,
          bankAccountGuid = payment.bankAccountGuid,
          businessGuid = payment.businessGuid,
          payeeGuid = detail.payeeGuid,
          description = payment.description,
          idempotencyKey = UUID.fromString(payment.idempotencyKey),
          paymentType = InternationalPaymentRep.PaymentType.valueOf(detail.paymentType),
          reason = detail.reason,
          payeeEmailToggle = payment.notificationEmailAddress != null,
          payeeEmail = payment.notificationEmailAddress,
          generalPaymentMetadataGuid = payment.generalPaymentMetadataGuid,
          receivedCurrency = detail.receiveCurrency,
          receivedAmount = receiveAmount,
          buyRate = buyRate,
          purposeCode = detail.purposeCode,
          paymentGuid = payment.guid,
          tags = detail.tags,
        ),
      ),
    )

    return payment.copy(
      status = PaymentRep.Status.Sent,
      amount = sendAmount,
      detail = detail.copy(receiveAmount = receiveAmount),
      paymentSenderResponse = objectMapper.convertValue(paymentResponse),
    )
  }

  private fun calculateAmounts(
    payment: PaymentRep,
    detail: PaymentDetailRep.InternationalWire,
    sender: SendPaymentCreatorRep.InternationalWire,
  ): Pair<Money, Money> =
    when (detail.fixedSide) {
      ConversionSide.Receive -> Pair(requireNotNull(sender.sendAmount), detail.receiveAmount)
      ConversionSide.Send -> Pair(payment.amount, requireNotNull(sender.receiveAmount))
    }

  private fun calculateBuyRate(receiveAmount: Money, sendAmount: Money): Double =
    receiveAmount.rawCents.toDouble() / sendAmount.rawCents.toDouble()
}
