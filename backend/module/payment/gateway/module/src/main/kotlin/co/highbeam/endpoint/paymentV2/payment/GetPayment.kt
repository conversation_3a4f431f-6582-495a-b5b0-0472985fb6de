package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.auth.paymentV2.AuthPayment
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.payment.PaymentService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentApi as Api
import co.highbeam.rep.paymentV2.payment.PaymentRep as Rep

internal class GetPayment @Inject constructor(
  private val authPayment: AuthPayment.Provider,
  private val paymentService: PaymentService,
) : EndpointHandler<Api.Get, Rep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(paymentGuid = call.getParam("paymentGuid"))

  override suspend fun Handler.handle(endpoint: Api.Get): Rep {
    auth(authPayment(
      paymentGuid = endpoint.paymentGuid,
      mfa = false,
      internalForPaymentEvaluation = false,
    ) { payment ->
      if (payment.status == PaymentRep.Status.Open) {
        return@authPayment Permission.PaymentApproval_Read
      }
      return@authPayment Permission.Payment_Read
    })
    return paymentService.get(endpoint.paymentGuid)
      ?: throw PaymentNotFound()
  }
}
