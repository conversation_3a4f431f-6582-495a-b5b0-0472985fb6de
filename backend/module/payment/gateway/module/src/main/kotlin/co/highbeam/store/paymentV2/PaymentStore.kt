package co.highbeam.store.paymentV2

import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

@Singleton
internal class PaymentStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun get(guid: UUID): PaymentRep? =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/paymentV2/get.sql"))
      query.bind("paymentGuid", guid)
      return@handle query.mapTo(PaymentRep::class.java).singleNullOrThrow()
    }

  fun list(businessGuid: UUID, status: PaymentRep.Status): List<PaymentRep> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/paymentV2/list.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("status", status)
      return@handle query.mapTo(PaymentRep::class.java).toList()
    }

  fun create(payment: PaymentRep): PaymentRep =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/paymentV2/create.sql"))
      query.bindKotlin(payment)
      return@transaction query.mapTo(PaymentRep::class.java).single()
    }

  fun update(payment: PaymentRep): PaymentRep =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/paymentV2/update.sql"))
      query.bindKotlin(payment)
      return@transaction query.mapTo(PaymentRep::class.java).singleNullOrThrow()
        ?: throw unprocessable(PaymentNotFound())
    }

  fun setStatus(guid: UUID, status: PaymentRep.Status): PaymentRep =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/paymentV2/setStatus.sql"))
      query.bind("paymentGuid", guid)
      query.bind("status", status)
      return@transaction query.mapTo(PaymentRep::class.java).singleNullOrThrow()
        ?: throw unprocessable(PaymentNotFound())
    }
}
