package co.highbeam.service.paymentV2.email

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.payee.PayeeApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.payee.PayeeClient
import co.highbeam.config.AppConfig
import co.highbeam.email.EmailService
import co.highbeam.email.template.EmailTemplate
import co.highbeam.email.template.PaymentGatewayApprovedEmailTemplate
import co.highbeam.email.template.PaymentGatewayDraftedEmailTemplate
import co.highbeam.email.template.PaymentGatewayRejectedEmailTemplate
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.service.paymentV2.payment.PaymentService
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class PaymentEmailServiceImpl @Inject constructor(
  private val appConfig: AppConfig,
  private val bankAccountClient: BankAccountClient,
  private val businessMemberClient: BusinessMemberClient,
  private val emailService: EmailService,
  private val payeeClient: PayeeClient,
  private val paymentService: PaymentService,
) : PaymentEmailService {
  private val logger = KotlinLogging.logger {}

  override suspend fun approved(paymentGuid: UUID) {
    logger.info { "Sending payment approved email." }
    emailService.async { sendEmail ->
      val payment = getPayment(paymentGuid)
      val payeeName = getPayeeName(payment) ?: return@async
      val bankAccount = getBankAccount(payment.bankAccountGuid)

      val template = PaymentGatewayApprovedEmailTemplate(
        recipient = payment.createdByUserGuid?.let { userRecipient(payment.businessGuid, it) }
          ?: return@async,
        amount = payment.amount,
        to = payeeName,
        from = bankAccount.nameWithMask,
        date = payment.sentAt ?: return@async,
        ctaLink = "${appConfig.appBaseUrl}/accounts/transactions",
      )
      logger.info { "Sending payment approved email: $template." }
      sendEmail(template)
    }
  }

  override suspend fun drafted(paymentGuid: UUID) {
    logger.info { "Sending payment drafted email." }
    emailService.async { sendEmail ->
      val payment = getPayment(paymentGuid)
      val createdBy = payment.createdByUserGuid?.let { getUser(payment.businessGuid, it) }
      val payeeName = getPayeeName(payment) ?: return@async
      val bankAccount = getBankAccount(payment.bankAccountGuid)

      val template = PaymentGatewayDraftedEmailTemplate(
        recipients = businessAdminRecipients(payment.businessGuid),
        requestedBy = createdBy?.fullName ?: "",
        amount = payment.amount,
        to = payeeName,
        from = bankAccount.nameWithMask,
        submitted = payment.createdAt,
        ctaLink = "${appConfig.appBaseUrl}/payments",
        description = payment.description,
      )
      logger.info { "Sending payment drafted email: $template." }
      sendEmail(template)
    }
  }

  override suspend fun rejected(paymentGuid: UUID) {
    logger.info { "Sending payment rejected email." }
    emailService.async { sendEmail ->
      val payment = getPayment(paymentGuid)
      val payeeName = getPayeeName(payment) ?: return@async
      val bankAccount = getBankAccount(payment.bankAccountGuid)

      val template = PaymentGatewayRejectedEmailTemplate(
        recipient = payment.createdByUserGuid?.let { userRecipient(payment.businessGuid, it) }
          ?: return@async,
        amount = payment.amount,
        to = payeeName,
        from = bankAccount.nameWithMask,
        submitted = payment.createdAt,
        ctaLink = "${appConfig.appBaseUrl}/send-money",
      )
      logger.info { "Sending payment rejected email: $template." }
      sendEmail(template)
    }
  }

  private fun getPayment(paymentGuid: UUID): PaymentRep =
    checkNotNull(paymentService.get(paymentGuid))

  private suspend fun getUser(businessGuid: UUID, userGuid: UUID): BusinessMemberRep.Complete? =
    businessMemberClient.request(BusinessMemberApi.GetByUser(businessGuid, userGuid))

  private suspend fun getPayeeName(payment: PaymentRep): String? {
    return when (val detail = payment.detail) {
      is PaymentDetailRep.Ach -> detail.payeeName
      is PaymentDetailRep.DomesticWire -> detail.payeeName
      is PaymentDetailRep.InternationalWire -> getPayeeNameFromPayeeGuid(
        payment.businessGuid,
        detail.payeeGuid
      )
      is PaymentDetailRep.UnitPayment -> getPayeeNameFromPayeeGuid(
        payment.businessGuid,
        detail.payeeGuid
      )
      is PaymentDetailRep.UnitTransfer -> null
    }
  }

  private suspend fun getPayeeNameFromPayeeGuid(businessGuid: UUID, payeeGuid: UUID): String? {
    val payee = payeeClient.request(PayeeApi.Get(businessGuid, payeeGuid)) ?: return null
    return payee.name
  }

  private suspend fun getBankAccount(bankAccountGuid: UUID): BankAccountRep.Complete =
    checkNotNull(bankAccountClient.request(BankAccountApi.Get(bankAccountGuid)))

  private suspend fun userRecipient(businessGuid: UUID, userGuid: UUID): EmailTemplate.Recipient? {
    val user = getUser(businessGuid, userGuid)
    return user?.emailAddress?.let { EmailTemplate.Recipient(it, user.fullName) }
  }

  private suspend fun businessAdminRecipients(businessGuid: UUID): List<EmailTemplate.Recipient> {
    val businessAdminMembers = businessMemberClient.request(
      BusinessMemberApi.GetAdminsByBusiness(businessGuid)
    )
    return businessAdminMembers.mapNotNull { user ->
      user.emailAddress?.let { EmailTemplate.Recipient(it, user.fullName) }
    }
  }
}
