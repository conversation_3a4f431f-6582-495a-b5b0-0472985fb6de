package co.highbeam.endpoint.paymentV2.paymentReceipt

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.payment.PaymentService
import co.highbeam.service.paymentV2.paymentReceipt.XHtmlGenerationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentApi as Api

internal class CreatePaymentReceiptXhtml @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val templateGenerator: XHtmlGenerationService,
  private val paymentService: PaymentService,
) : EndpointHandler<Api.CreateReceiptXhtml, String>(
  template = Api.CreateReceiptXhtml::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.CreateReceiptXhtml =
    Api.CreateReceiptXhtml(paymentGuid = call.getParam("paymentGuid"))

  override suspend fun Handler.handle(endpoint: Api.CreateReceiptXhtml): String {
    val payment = paymentService.get(endpoint.paymentGuid) ?: throw PaymentNotFound()
    auth(authPermission(Permission.Payment_Read) { payment.businessGuid })

    return templateGenerator.generateReceipt(payment)
  }
}
