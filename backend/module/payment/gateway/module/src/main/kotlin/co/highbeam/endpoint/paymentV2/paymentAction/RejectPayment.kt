package co.highbeam.endpoint.paymentV2.paymentAction

import co.highbeam.auth.paymentV2.AuthPayment
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.action.PaymentActionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentActionApi as Api

internal class RejectPayment @Inject constructor(
  private val authPayment: AuthPayment.Provider,
  private val paymentActionService: PaymentActionService,
) : EndpointHandler<Api.Reject, PaymentRep>(
  template = Api.Reject::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Reject =
    Api.Reject(paymentGuid = call.getParam("paymentGuid"))

  override suspend fun Handler.handle(endpoint: Api.Reject): PaymentRep {
    auth(authPayment(
      paymentGuid = endpoint.paymentGuid,
      mfa = false,
      internalForPaymentEvaluation = true,
    ) { Permission.PaymentApproval_Delete })
    return paymentActionService.reject(endpoint.paymentGuid)
  }
}
