package co.highbeam.service.paymentV2.canceller

import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.unit.client.UnitCoClient
import com.fasterxml.jackson.databind.JsonNode
import com.google.inject.Inject

internal class UnitPaymentPaymentCanceller @Inject constructor(
  private val unitCoClient: UnitCoClient,
) : PaymentCanceller {
  override suspend fun cancel(payment: PaymentRep): PaymentRep {
    val detail = payment.detail as PaymentDetailRep.UnitPayment

    when (getPaymentType(detail.unitPayload)) {
      PaymentDetailRep.UnitPayment.PaymentType.Ach -> {
        cancelAch(payment)
        return payment
      }
      PaymentDetailRep.UnitPayment.PaymentType.Wire -> {
        cancelWire(payment)
        return payment
      }
    }
  }

  private suspend fun cancelAch(payment: PaymentRep) {
    val unitPaymentId = payment.paymentSenderResponse.get("id")?.textValue()
      ?: error("unitPaymentId is not found in payment sender response.")

    unitCoClient.payment.cancelAch(unitPaymentId)
  }

  private suspend fun cancelWire(payment: PaymentRep) {
    val unitPaymentId = payment.paymentSenderResponse.get("id")?.textValue()
      ?: error("unitPaymentId is not found in payment sender response.")

    unitCoClient.payment.cancelWire(unitPaymentId)
  }

  private fun getPaymentType(unitPayload: JsonNode): PaymentDetailRep.UnitPayment.PaymentType {
    val unitType = unitPayload.get("type").textValue()
    return PaymentDetailRep.UnitPayment.PaymentType.fromValue(unitType)
  }
}
