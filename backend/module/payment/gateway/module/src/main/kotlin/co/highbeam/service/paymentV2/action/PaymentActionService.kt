package co.highbeam.service.paymentV2.action

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(PaymentActionServiceImpl::class)
internal interface PaymentActionService {
  suspend fun send(paymentGuid: UUID, sender: SendPaymentCreatorRep): PaymentRep

  suspend fun approve(paymentGuid: UUID, sender: SendPaymentCreatorRep): PaymentRep

  suspend fun reject(paymentGuid: UUID): PaymentRep

  suspend fun cancel(paymentGuid: UUID): PaymentRep
}
