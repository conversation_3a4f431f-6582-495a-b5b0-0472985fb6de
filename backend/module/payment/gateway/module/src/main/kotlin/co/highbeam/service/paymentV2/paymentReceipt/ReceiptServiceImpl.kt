package co.highbeam.service.paymentV2.paymentReceipt

import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject
import com.openhtmltopdf.pdfboxout.PdfRendererBuilder
import mu.KotlinLogging
import java.io.ByteArrayOutputStream

internal class ReceiptServiceImpl @Inject constructor(
  private val templateGenerator: XHtmlGenerationService,
) : ReceiptService {
  private val logger = KotlinLogging.logger {}

  override suspend fun generateReceipt(payment: PaymentRep): ByteArray {
    logger.info { "Generating receipt for payment ${payment.guid}" }
    val template = templateGenerator.generateReceipt(payment)
    return convertXhtmlToPdfByteArray(template)
  }

  private fun convertXhtmlToPdfByteArray(xhtml: String): ByteArray {
    return ByteArrayOutputStream().use { outputStream ->
      PdfRendererBuilder().apply {
        useFastMode()
        withHtmlContent(xhtml, null)
        toStream(outputStream)
      }.run()
      return@use outputStream.toByteArray()
    }
  }
}
