package co.highbeam.feature.paymentV2

import co.highbeam.endpoint.paymentV2.payment.CreatePayment
import co.highbeam.endpoint.paymentV2.payment.GetPayment
import co.highbeam.endpoint.paymentV2.payment.ListPayments
import co.highbeam.endpoint.paymentV2.paymentAction.CancelPayment
import co.highbeam.endpoint.paymentV2.paymentAction.RejectPayment
import co.highbeam.endpoint.paymentV2.paymentAction.SendPayment
import co.highbeam.endpoint.paymentV2.paymentReceipt.CreatePaymentReceiptPdf
import co.highbeam.endpoint.paymentV2.paymentReceipt.CreatePaymentReceiptXhtml
import co.highbeam.feature.Feature

class PaymentFeature : Feature() {
  override fun bind() {
    bindApiEndpoints()
  }

  private fun bindApiEndpoints() {
    bind(GetPayment::class.java).asEagerSingleton()
    bind(ListPayments::class.java).asEagerSingleton()
    bind(CreatePayment::class.java).asEagerSingleton()

    bind(CancelPayment::class.java).asEagerSingleton()
    bind(SendPayment::class.java).asEagerSingleton()
    bind(RejectPayment::class.java).asEagerSingleton()

    bind(CreatePaymentReceiptPdf::class.java).asEagerSingleton()
    bind(CreatePaymentReceiptXhtml::class.java).asEagerSingleton()
  }
}
