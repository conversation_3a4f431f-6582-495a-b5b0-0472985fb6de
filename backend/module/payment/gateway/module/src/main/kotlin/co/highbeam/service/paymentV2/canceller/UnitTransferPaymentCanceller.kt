package co.highbeam.service.paymentV2.canceller

import co.highbeam.exception.paymentV2.PaymentCannotBeCancelled
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.unit.client.UnitCoClient
import com.fasterxml.jackson.databind.JsonNode
import com.google.inject.Inject

internal class UnitTransferPaymentCanceller @Inject constructor(
  private val unitCoClient: UnitCoClient,
) : PaymentCanceller {
  override suspend fun cancel(payment: PaymentRep): PaymentRep {
    val detail = payment.detail as PaymentDetailRep.UnitTransfer

    when (getPaymentType(detail.unitPayload)) {
      PaymentDetailRep.UnitTransfer.PaymentType.Ach -> {
        cancelAch(payment)
        return payment
      }
      PaymentDetailRep.UnitTransfer.PaymentType.Book -> {
        throw PaymentCannotBeCancelled()
      }
      PaymentDetailRep.UnitTransfer.PaymentType.Wire -> {
        throw PaymentCannotBeCancelled()
      }
    }
  }

  private suspend fun cancelAch(payment: PaymentRep) {
    val unitPaymentId = payment.paymentSenderResponse.get("id")?.textValue()
      ?: error("unitPaymentId is not found in payment sender response.")

    unitCoClient.payment.cancelAch(unitPaymentId)
  }

  private fun getPaymentType(unitPayload: JsonNode): PaymentDetailRep.UnitTransfer.PaymentType {
    val unitType = unitPayload.get("type").textValue()
    return PaymentDetailRep.UnitTransfer.PaymentType.fromValue(unitType)
  }
}
