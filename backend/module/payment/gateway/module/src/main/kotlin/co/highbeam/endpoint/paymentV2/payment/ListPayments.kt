package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.auth.paymentV2.AuthListPayments
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.payment.PaymentService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentApi as Api
import co.highbeam.rep.paymentV2.payment.PaymentRep as Rep

internal class ListPayments @Inject constructor(
  private val authListPayments: AuthListPayments.Provider,
  private val paymentService: PaymentService,
) : EndpointHandler<Api.List, List<Rep>>(
  template = Api.List::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.List =
    Api.List(
      businessGuid = call.getParam("businessGuid"),
      status = call.getParam("status"),
    )

  override suspend fun Handler.handle(endpoint: Api.List): List<Rep> {
    auth(authListPayments(endpoint.businessGuid, endpoint.status))
    return paymentService.list(endpoint.businessGuid, endpoint.status)
  }
}
