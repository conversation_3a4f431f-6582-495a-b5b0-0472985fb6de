package co.highbeam.service.paymentV2.payment

import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(PaymentServiceImpl::class)
internal interface PaymentService {
  fun get(guid: UUID): PaymentRep?

  fun list(businessGuid: UUID, status: PaymentRep.Status): List<PaymentRep>

  suspend fun create(creator: PaymentRep): PaymentRep
}
