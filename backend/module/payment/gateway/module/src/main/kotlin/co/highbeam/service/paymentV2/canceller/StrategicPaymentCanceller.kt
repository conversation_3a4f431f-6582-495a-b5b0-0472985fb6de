package co.highbeam.service.paymentV2.canceller

import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import com.google.inject.Inject

internal class StrategicPaymentCanceller @Inject constructor(
  private val achPayment: AchPaymentCanceller,
  private val unitPayment: UnitPaymentPaymentCanceller,
  private val unitTransfer: UnitTransferPaymentCanceller,
  private val domesticWirePayment: DomesticWirePaymentCanceller,
) : PaymentCanceller {
  override suspend fun cancel(payment: PaymentRep): PaymentRep =
    when (payment.detail) {
      is PaymentDetailRep.Ach -> achPayment.cancel(payment)
      is PaymentDetailRep.DomesticWire -> domesticWirePayment.cancel(payment)
      is PaymentDetailRep.InternationalWire ->
        error("International wire payment cancellation is not allowed.")
      is PaymentDetailRep.UnitPayment -> unitPayment.cancel(payment)
      is PaymentDetailRep.UnitTransfer -> unitTransfer.cancel(payment)
    }
}
