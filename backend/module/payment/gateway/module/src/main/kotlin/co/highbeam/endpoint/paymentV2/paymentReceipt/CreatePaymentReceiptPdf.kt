package co.highbeam.endpoint.paymentV2.paymentReceipt

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.payment.PaymentService
import co.highbeam.service.paymentV2.paymentReceipt.ReceiptService
import com.google.inject.Inject
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.server.application.ApplicationCall
import io.ktor.server.response.header
import co.highbeam.api.paymentV2.PaymentApi as Api

internal class CreatePaymentReceiptPdf @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val paymentService: PaymentService,
  private val receiptService: ReceiptService,
) : EndpointHandler<Api.CreateReceiptPdf, ByteArray>(
  template = Api.CreateReceiptPdf::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.CreateReceiptPdf =
    Api.CreateReceiptPdf(paymentGuid = call.getParam("paymentGuid"))

  override suspend fun Handler.handle(endpoint: Api.CreateReceiptPdf): ByteArray {
    val payment = paymentService.get(endpoint.paymentGuid) ?: throw PaymentNotFound()
    auth(authPermission(Permission.Payment_Read) { payment.businessGuid })

    call.response.header(HttpHeaders.ContentType, ContentType.Application.Pdf.toString())

    return receiptService.generateReceipt(payment)
  }
}
