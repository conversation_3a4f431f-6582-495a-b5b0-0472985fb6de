package co.highbeam.auth.paymentV2

import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.service.paymentV2.payment.PaymentService
import com.google.inject.Inject
import java.util.UUID

internal object AuthPayment {
  internal class Provider @Inject constructor(
    private val authMfa: AuthMfa.Provider,
    private val authPermission: AuthPermission.Provider,
    private val authPlatformRole: AuthPlatformRole.Provider,
    private val featureFlagService: FeatureFlagService,
    private val paymentService: PaymentService,
  ) {
    operator fun invoke(
      paymentGuid: UUID,
      mfa: Boolean,
      internalForPaymentEvaluation: <PERSON><PERSON>an,
      permission: (payment: PaymentRep) -> Permission,
    ): Auth {
      val payment = paymentService.get(paymentGuid) ?: return Auth.Deny
      val businessGuid = payment.businessGuid

      // Temporary shim:
      // If payment policies are enabled for this business, then certain actions should only be
      // performed internally (i.e., via the Highbeam Server platform role) during the payment
      // evaluation lifecycle. For example, end users are not allowed to send or reject payments
      // directly via the payment gateway, if the payments are subject to evaluation against a
      // payment policy.
      // Callers of AuthPayment must opt in to this behavior by passing the param
      // internalForPaymentEvaluation = true. Canceling a payment, in contrast to sending or
      // rejecting, *is* allowed for end users, regardless of if payment policies are enabled.
      val requireHighbeamServer = internalForPaymentEvaluation && featureFlagService.isEnabled(
        flag = BusinessFlag.PaymentPolicies,
        businessGuid = businessGuid,
      )

      val auths = buildList {
        if (mfa) add(authMfa())
        add(authPermission(permission(payment)) { businessGuid })
        if (requireHighbeamServer) {
          add(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
        }
      }
      return Auth.All(auths)
    }
  }
}
