package co.highbeam.service.paymentV2.sender

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.business.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.metrics.Metrics
import co.highbeam.rep.paymentV2.payment.AddressRep
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoWirePaymentRep
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.HttpRequestTimeoutException
import mu.KotlinLogging
import java.util.UUID

internal class DomesticWirePaymentSender @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val unitCoClient: UnitCoClient,
  private val objectMapper: ObjectMapper,
  private val metrics: Metrics,
) : PaymentSender {
  private val logger = KotlinLogging.logger {}

  override suspend fun send(payment: PaymentRep, sender: SendPaymentCreatorRep): PaymentRep {
    val detail = payment.detail as PaymentDetailRep.DomesticWire

    val bankAccount = bankAccountClient.request(BankAccountApi.Get(detail.fromBankAccountGuid))
      ?: throw unprocessable(BankAccountNotFound())

    @Suppress("TooGenericExceptionCaught")
    try {
      val unitPaymentResponse = unitCoClient.payment.createWire(
        creator = UnitCoWirePaymentRep.Creator(
          amount = detail.amount,
          inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
            accountNumber = detail.accountNumber,
            routingNumber = detail.routingNumber,
            name = detail.payeeName,
            address = toUnitCoAddress(detail.addressRep),
          ),
          description = detail.description,
          idempotencyKey = UUID.fromString(detail.idempotencyKey),
          payeeEmail = detail.tags["payeeEmail"],

          // The underlying Unit payment client is too coupled with our models.
          // Even though we're moving towards the payment gateway knowing being agnostic to our
          // systems, we currently still need to pass the payeeGuid
          // and fromAccountId to payment client.
          payeeGuid = UUID.fromString(checkNotNull(detail.tags["payeeGuid"])),
          fromAccountType = "depositAccount",
          fromAccountId = bankAccount.unitCoDepositAccountId,
          tags = detail.tags + mapOf(
            "paymentGuid" to payment.guid
          )
        )

      )
      val (status, errorReason) = getStatusAndReason(unitPaymentResponse)

      return payment.copy(
        status = status,
        reason = errorReason,
        paymentSenderResponse = objectMapper.convertValue(unitPaymentResponse),
      )
    } catch (e: Exception) {
      return when (e) {
        is HttpRequestTimeoutException -> handleHighbeamTimeoutException(payment)
        is ConnectTimeoutException -> handleHighbeamTimeoutException(payment)
        else -> throw e
      }
    }
  }

  private fun toUnitCoAddress(addressRep: AddressRep): co.unit.rep.AddressRep {
    return co.unit.rep.AddressRep(
      street = addressRep.street,
      street2 = addressRep.street2,
      city = addressRep.city,
      state = addressRep.state,
      postalCode = addressRep.postalCode,
      country = addressRep.country,
    )
  }

  private fun getStatusAndReason(
    unitPaymentResponse: UnitCoWirePaymentRep.Complete
  ): Pair<PaymentRep.Status, String?> {

    return when (unitPaymentResponse.status) {
      UnitCoWirePaymentRep.Status.Rejected, UnitCoWirePaymentRep.Status.Canceled -> {
        Pair(PaymentRep.Status.Failed, unitErrorToHighbeamErrorMessage(unitPaymentResponse.reason))
      }
      else -> Pair(PaymentRep.Status.Sent, unitPaymentResponse.reason)
    }
  }

  private fun unitErrorToHighbeamErrorMessage(unitErrorMessage: String?): String? {
    if (unitErrorMessage == null) return null

    return when (unitErrorMessage) {
      "AccountFrozen" -> "The account is frozen."
      "InsufficientFunds" -> "The account does not have sufficient balance."
      "DailyACHCreditLimitExceeded" -> "The daily ACH credit limit has been exceeded."
      "DailyACHDebitLimitExceeded" -> "The daily ACH debit limit has been exceeded."
      "MonthlyACHCreditLimitExceeded" -> "The monthly ACH credit limit has been exceeded."
      "MonthlyACHDebitLimitExceeded" -> "The monthly ACH debit limit has been exceeded."
      "Invalid Routing Number" -> "The counterparty routing number is not valid."
      "Wire Rejected" -> "The counterparty rejected the wire payment."
      "ClientRequest" -> "The client has requested the wire payment to be rejected."
      else -> "Please contact support for more information."
    }
  }


  private fun handleHighbeamTimeoutException(payment: PaymentRep): PaymentRep {
    logger.error(
      "Payment request timed out. Payment: $payment. We have marked " +
        "it as sent to the user. Please verify that it is actually sent."
    )

    metrics.counter(
      "payment_timeout",
      "sender", "DomesticWirePayment",
      "marked_as", "sent"
    ) {}.increment()

    return payment.copy(
      status = PaymentRep.Status.Sent,
    )
  }
}
