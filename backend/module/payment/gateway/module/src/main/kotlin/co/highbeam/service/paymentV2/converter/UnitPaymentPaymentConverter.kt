package co.highbeam.service.paymentV2.converter

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class UnitPaymentPaymentConverter @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val clock: Clock,
  private val objectMapper: ObjectMapper,
) : PaymentConverterStrategy {
  override suspend fun fromCreator(
    creator: PaymentRep.Creator,
    guid: UUID,
    status: PaymentRep.Status,
    createdByUserGuid: UUID?,
  ): Pair<PaymentRep, SendPaymentCreatorRep?> {
    val detail = creator.detail as PaymentDetailCreatorRep.UnitPayment

    val unitPayload = detail.unitPayload
    val unitAttributes = unitPayload.get("attributes")
    val unitRelationships = unitPayload.get("relationships")
    val unitTags = unitAttributes.get("tags")
    val unitDepositAccountId = unitRelationships.get("account").get("data").get("id").textValue()

    val bankAccount = bankAccountClient.request(
      endpoint = BankAccountApi.GetByUnitCoDepositAccountId(unitDepositAccountId),
    ) ?: throw unprocessable(BankAccountNotFound())

    val payment = PaymentRep(
      guid = guid,

      amount = detail.amount,
      bankAccountGuid = bankAccount.guid,
      businessGuid = bankAccount.businessGuid,
      description = unitAttributes.get("addenda")?.textValue()
        ?: unitAttributes.get("description").textValue(),
      generalPaymentMetadataGuid = unitTags.get("generalPaymentMetadataGuid")?.textValue()
        ?.let { UUID.fromString(it) },
      idempotencyKey = unitAttributes.get("idempotencyKey").textValue(),
      notificationEmailAddress = unitTags.get("payeeEmail")?.textValue(),
      status = status,
      reason = null,

      createdByUserGuid = createdByUserGuid,
      createdAt = ZonedDateTime.now(clock),
      sentByUserGuid = null,
      sentAt = null,
      rejectedByUserGuid = null,
      rejectedAt = null,

      detail = PaymentDetailRep.UnitPayment(
        unitPayload = unitPayloadWithTags(unitPayload, guid),
        payeeGuid = UUID.fromString(unitTags.get("recipientGuid").textValue()),
      ),
      paymentSenderResponse = NullNode.instance,
    )

    val sender = if (creator.send) SendPaymentCreatorRep.UnitPayment else null

    return Pair(payment, sender)
  }

  private fun unitPayloadWithTags(unitPayload: JsonNode, paymentGuid: UUID): JsonNode {
    val attributes = unitPayload.get("attributes") as ObjectNode
    val tags = attributes.get("tags")?.let { it as ObjectNode }

    if (tags == null || tags.isMissingNode) {
      attributes.set<JsonNode>("tags", objectMapper.convertValue(mapOf(
        "paymentGuid" to paymentGuid,
      )))
    } else {
      tags.putIfAbsent("paymentGuid", objectMapper.convertValue(paymentGuid))
    }

    return unitPayload
  }
}
