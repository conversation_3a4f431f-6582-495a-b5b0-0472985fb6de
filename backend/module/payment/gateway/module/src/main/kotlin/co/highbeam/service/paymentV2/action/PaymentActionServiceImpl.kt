package co.highbeam.service.paymentV2.action

import co.highbeam.auth.getRestContext
import co.highbeam.auth.principal.JwtAccess
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.client.exception.propagate
import co.highbeam.exception.paymentV2.PaymentCannotBeCancelled
import co.highbeam.exception.paymentV2.PaymentCannotBeRejected
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.service.paymentV2.canceller.PaymentCanceller
import co.highbeam.service.paymentV2.email.PaymentEmailService
import co.highbeam.service.paymentV2.payment.PaymentService
import co.highbeam.service.paymentV2.sender.PaymentSender
import co.highbeam.store.paymentV2.PaymentStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class PaymentActionServiceImpl @Inject constructor(
  private val clock: Clock,
  private val featureFlagService: FeatureFlagService,
  private val paymentEmailService: PaymentEmailService,
  private val paymentCanceller: PaymentCanceller,
  private val paymentService: PaymentService,
  private val paymentSender: PaymentSender,
  private val paymentStore: PaymentStore,
) : PaymentActionService {
  private val logger = KotlinLogging.logger {}

  override suspend fun send(paymentGuid: UUID, sender: SendPaymentCreatorRep): PaymentRep {
    logger.info { "Sending payment (paymentGuid=$paymentGuid)." }
    var payment = paymentService.get(paymentGuid)
      ?: throw unprocessable(PaymentNotFound())
    if (!canSendPayment(payment)) throw PaymentCannotBeSent()
    payment = sendImmediately(payment, sender)
    return payment
  }

  override suspend fun approve(paymentGuid: UUID, sender: SendPaymentCreatorRep): PaymentRep {
    logger.info { "Approving payment (paymentGuid=$paymentGuid)." }
    val payment = paymentService.get(paymentGuid)
      ?: throw unprocessable(PaymentNotFound())
    if (!canSendPayment(payment)) throw PaymentCannotBeSent()

    return sendApproval(payment, sender).also {
      if (!isPaymentPoliciesEnabled(it)) {
        paymentEmailService.approved(it.guid)
      }
    }
  }

  private fun canSendPayment(payment: PaymentRep): Boolean {
    val canSendPayment = payment.status in setOf(PaymentRep.Status.Open, PaymentRep.Status.Pending)
    logger.debug { "Can send payment: $canSendPayment." }
    return canSendPayment
  }

  @OptIn(JwtAccess::class)
  private suspend fun sendImmediately(
    payment: PaymentRep,
    sender: SendPaymentCreatorRep
  ): PaymentRep {
    logger.info { "Sending payment (payment=$payment)." }

    paymentStore.setStatus(payment.guid, PaymentRep.Status.Pending)

    @Suppress("NAME_SHADOWING", "TooGenericExceptionCaught")
    val payment = try {
      paymentSender.send(payment, sender)
    } catch (e: Exception) {
      logger.error(e) { "Failed to send payment." }

      return paymentStore.update(payment.copy(
        status = PaymentRep.Status.Failed,
        reason = "Payment failed. Please contact support for more information.",
        sentByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
        sentAt = ZonedDateTime.now(clock),
      ))
    }

    logger.info { "Payment sent with status=${payment.status}." }
    return paymentStore.update(payment.copy(
      sentByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
      sentAt = ZonedDateTime.now(clock),
    ))
  }

  @OptIn(JwtAccess::class)
  private suspend fun sendApproval(payment: PaymentRep, sender: SendPaymentCreatorRep): PaymentRep {
    logger.info { "Sending payment approval (payment=$payment)." }

    paymentStore.setStatus(payment.guid, PaymentRep.Status.Pending)

    @Suppress("NAME_SHADOWING", "TooGenericExceptionCaught")
    val payment = try {
      paymentSender.send(payment, sender)
    } catch (e: Exception) {
      logger.error(e) { "Failed to send payment." }
      paymentStore.update(payment.copy(
        status = PaymentRep.Status.Open,
        reason = "Payment failed. Please contact support for more information.",
        sentByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
        sentAt = null,
      ))
      @Suppress("InstanceOfCheckForException")
      if (e is HighbeamHttpClientException) e.propagate()
      throw e
    }

    if (payment.status == PaymentRep.Status.Failed) {
      logger.error { "Failed to send payment. reason=${payment.reason} " }

      return paymentStore.update(payment.copy(
        status = PaymentRep.Status.Open,
        reason = payment.reason,
        sentByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
        sentAt = null,
      ))
    }

    logger.info { "Payment sent with status=${payment.status}." }
    return paymentStore.update(payment.copy(
      sentByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
      sentAt = ZonedDateTime.now(clock),
    ))
  }

  override suspend fun reject(paymentGuid: UUID): PaymentRep {
    logger.info { "Rejecting payment (paymentGuid=$paymentGuid)." }
    var payment = paymentService.get(paymentGuid)
      ?: throw unprocessable(PaymentNotFound())
    if (!canRejectPayment(payment)) throw PaymentCannotBeRejected()
    payment = reject(payment)

    if (!isPaymentPoliciesEnabled(payment)) {
      paymentEmailService.rejected(payment.guid)
    }

    return payment
  }

  private fun canRejectPayment(payment: PaymentRep): Boolean {
    val canRejectPayment = payment.status in setOf(PaymentRep.Status.Open)
    logger.debug { "Can reject payment: $canRejectPayment." }
    return canRejectPayment
  }

  @OptIn(JwtAccess::class)
  private suspend fun reject(payment: PaymentRep): PaymentRep {
    logger.info { "Rejecting payment (payment=$payment)." }
    return paymentStore.update(payment.copy(
      status = PaymentRep.Status.Rejected,
      rejectedByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
      rejectedAt = ZonedDateTime.now(clock),
    ))
  }

  override suspend fun cancel(paymentGuid: UUID): PaymentRep {
    logger.info { "Cancelling payment (paymentGuid=$paymentGuid)." }
    val payment = paymentService.get(paymentGuid)
      ?: throw unprocessable(PaymentNotFound())
    if (!canCancelPayment(payment)) throw PaymentCannotBeCancelled()
    return cancel(payment)
  }

  private fun canCancelPayment(payment: PaymentRep): Boolean {
    val canCancelPayment = payment.status in setOf(PaymentRep.Status.Sent)
    logger.debug { "Can cancel payment: $canCancelPayment." }
    return canCancelPayment
  }

  private suspend fun cancel(payment: PaymentRep): PaymentRep {
    logger.info { "Cancelling payment (payment=$payment)." }

    paymentCanceller.cancel(payment)

    return paymentStore.update(payment.copy(
      status = PaymentRep.Status.Cancelled,
    ))
  }

  private fun isPaymentPoliciesEnabled(payment: PaymentRep) =
    featureFlagService.isEnabled(
      flag = BusinessFlag.PaymentPolicies,
      businessGuid = payment.businessGuid,
    )
}
