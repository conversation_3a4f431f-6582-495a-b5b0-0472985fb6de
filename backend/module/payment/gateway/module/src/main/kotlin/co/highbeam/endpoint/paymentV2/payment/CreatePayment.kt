package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.api.backendV2.payments.PaymentEvaluationApi
import co.highbeam.auth.paymentV2.AuthCreatePayment
import co.highbeam.client.backendV2.payments.PaymentEvaluationClient
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.backendV2.payments.PaymentEvaluationSubmissionRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.action.PaymentActionService
import co.highbeam.service.paymentV2.converter.PaymentConverter
import co.highbeam.service.paymentV2.payment.PaymentService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import mu.KotlinLogging
import co.highbeam.api.paymentV2.PaymentApi as Api
import co.highbeam.rep.paymentV2.payment.PaymentRep as Rep

private val logger = KotlinLogging.logger {}

internal class CreatePayment @Inject constructor(
  private val authCreatePayment: AuthCreatePayment.Provider,
  private val featureFlagService: FeatureFlagService,
  private val paymentActionService: PaymentActionService,
  private val paymentConverter: PaymentConverter,
  private val paymentEvaluationClient: PaymentEvaluationClient,
  private val paymentService: PaymentService,
) : EndpointHandler<Api.Create, Rep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(creator = call.body())

  override suspend fun Handler.handle(endpoint: Api.Create): Rep {
    var (creator, sender) = paymentConverter.fromCreator(endpoint.creator)

    // Temporary shim:
    // If payment policies are enabled for this business, then the payment *must* be created with an
    // Open status and may not be sent immediately. It should always be submitted for evaluation.
    // The payment is evaluated against a payment policy asynchronously and will be sent (or
    // rejected) according to the policy, its approval rules, etc.
    // For now, while we continue to support the existing "create payment" API, we just disregard
    // the caller-provider "send" parameter and *never* send the payment immediately.
    val isPaymentPoliciesEnabled = featureFlagService.isEnabled(
      flag = BusinessFlag.PaymentPolicies,
      businessGuid = creator.businessGuid,
    )
    if (isPaymentPoliciesEnabled && creator.status != Rep.Status.Open) {
      creator = creator.copy(
        status = Rep.Status.Open,
      )
    }

    auth(authCreatePayment(creator))
    val payment = paymentService.create(creator)

    if (isPaymentPoliciesEnabled) {
      submitPaymentForEvaluation(payment)
      return payment
    }

    if (sender == null) return payment
    return paymentActionService.send(payment.guid, sender)
  }

  private suspend fun submitPaymentForEvaluation(payment: Rep) {
    logger.info { "Submitting payment for evaluation: $payment." }
    paymentEvaluationClient.request(
      PaymentEvaluationApi.SubmitPaymentForEvaluation(
        PaymentEvaluationSubmissionRep.Submitter(
          businessGuid = payment.businessGuid,
          paymentGuid = payment.guid,
        )
      )
    )
  }
}
