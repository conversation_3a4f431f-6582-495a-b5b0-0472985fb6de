package co.highbeam.service.paymentV2.sender

import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.google.inject.Inject

internal class StrategicPaymentSender @Inject constructor(
  private val achPayment: AchPaymentSender,
  private val domesticWire: DomesticWirePaymentSender,
  private val internationalWire: InternationalWirePaymentSender,
  private val unitPayment: UnitPaymentPaymentSender,
  private val unitTransfer: UnitTransferPaymentSender,
) : PaymentSender {
  override suspend fun send(payment: PaymentRep, sender: SendPaymentCreatorRep): PaymentRep =
    when (payment.detail) {
      is PaymentDetailRep.Ach -> achPayment.send(payment, sender)
      is PaymentDetailRep.DomesticWire -> domesticWire.send(payment, sender)
      is PaymentDetailRep.InternationalWire -> internationalWire.send(payment, sender)
      is PaymentDetailRep.UnitPayment -> unitPayment.send(payment, sender)
      is PaymentDetailRep.UnitTransfer -> unitTransfer.send(payment, sender)
    }
}
