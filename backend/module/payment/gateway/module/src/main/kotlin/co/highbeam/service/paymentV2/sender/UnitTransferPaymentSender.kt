package co.highbeam.service.paymentV2.sender

import co.highbeam.metrics.Metrics
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.google.inject.Inject
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.HttpRequestTimeoutException
import mu.KotlinLogging
import co.highbeam.api.payment.PaymentApi as LegacyPaymentApi
import co.highbeam.client.payment.PaymentClient as LegacyPaymentClient
import co.highbeam.rep.payment.PaymentRep as LegacyPaymentRep

internal class UnitTransferPaymentSender @Inject constructor(
  private val paymentClient: LegacyPaymentClient,
  private val metrics: Metrics,
) : PaymentSender {
  private val logger = KotlinLogging.logger {}
  override suspend fun send(payment: PaymentRep, sender: SendPaymentCreatorRep): PaymentRep {
    val detail = payment.detail as PaymentDetailRep.UnitTransfer

    @Suppress("TooGenericExceptionCaught")
    try {
      val unitPaymentResponse = paymentClient.request(
        LegacyPaymentApi.CreatePayment(
          creator = LegacyPaymentRep.Creator(
            unitPayload = detail.unitPayload,
          ),
        ),
      )
      val (status, errorReason) = getStatusAndReason(unitPaymentResponse)

      return payment.copy(
        status = status,
        reason = errorReason,
        paymentSenderResponse = unitPaymentResponse.unitResponse,
      )
    } catch (e: Exception) {
      return when (e) {
        is HttpRequestTimeoutException -> handleHighbeamTimeoutException(payment)
        is ConnectTimeoutException -> handleHighbeamTimeoutException(payment)
        else -> throw e
      }
    }
  }

  private fun getStatusAndReason(
    unitPaymentResponse: LegacyPaymentRep
  ): Pair<PaymentRep.Status, String?> {
    val attributes = unitPaymentResponse.unitResponse.get("attributes")
    val status = attributes?.get("status")?.textValue()
    val reason = attributes?.get("reason")?.textValue()

    return when (status) {
      "Rejected", "Canceled" -> {
        Pair(PaymentRep.Status.Failed, unitErrorToHighbeamErrorMessage(reason))
      }
      else -> Pair(PaymentRep.Status.Sent, reason)
    }
  }

  private fun unitErrorToHighbeamErrorMessage(unitErrorMessage: String?): String? {
    if (unitErrorMessage == null) return null

    return when (unitErrorMessage) {
      "AccountFrozen" -> "The account is frozen."
      "InsufficientFunds" -> "The account does not have sufficient balance."
      "DailyACHCreditLimitExceeded" -> "The daily ACH credit limit has been exceeded."
      "DailyACHDebitLimitExceeded" -> "The daily ACH debit limit has been exceeded."
      "MonthlyACHCreditLimitExceeded" -> "The monthly ACH credit limit has been exceeded."
      "MonthlyACHDebitLimitExceeded" -> "The monthly ACH debit limit has been exceeded."
      "Invalid Routing Number" -> "The counterparty routing number is not valid."
      "Wire Rejected" -> "The counterparty rejected the wire payment."
      "ClientRequest" -> "The client has requested the wire payment to be rejected."
      else -> "Please contact support for more information."
    }
  }

  private fun handleHighbeamTimeoutException(payment: PaymentRep): PaymentRep {
    logger.error("Payment request timed out. Payment: $payment. We have marked " +
      "it as sent to the user please verify that it is actually sent.")

    metrics.counter(
      "payment_timeout",
      "sender", "UnitTransfer",
      "marked_as", "sent"
    ) {}.increment()

    return payment.copy(
      status = PaymentRep.Status.Sent,
    )
  }
}
