package co.highbeam.endpoint.paymentV2.paymentAction

import co.highbeam.auth.paymentV2.AuthPayment
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.action.PaymentActionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentActionApi as Api

internal class SendPayment @Inject constructor(
  private val authPayment: AuthPayment.Provider,
  private val paymentActionService: PaymentActionService,
) : EndpointHandler<Api.Send, PaymentRep>(
  template = Api.Send::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Send =
    Api.Send(
      paymentGuid = call.getParam("paymentGuid"),
      sender = call.body(),
    )

  override suspend fun Handler.handle(endpoint: Api.Send): PaymentRep {
    auth(authPayment(
      paymentGuid = endpoint.paymentGuid,
      mfa = true,
      internalForPaymentEvaluation = true,
    ) { Permission.Payment_Create })
    return paymentActionService.approve(endpoint.paymentGuid, endpoint.sender)
  }
}
