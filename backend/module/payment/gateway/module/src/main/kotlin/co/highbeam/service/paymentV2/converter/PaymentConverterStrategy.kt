package co.highbeam.service.paymentV2.converter

import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.google.inject.Inject
import java.util.UUID

internal interface PaymentConverterStrategy {
  class Factory @Inject constructor(
    private val achPayment: AchPaymentConverter,
    private val internationalWire: InternationalWirePaymentConverter,
    private val unitPayment: UnitPaymentPaymentConverter,
    private val unitTransfer: UnitTransferPaymentConverter,
    private val domesticWire: DomesticWirePaymentConverter,
  ) {
    fun forType(detail: PaymentDetailCreatorRep): PaymentConverterStrategy =
      when (detail) {
        is PaymentDetailCreatorRep.InternationalWire -> internationalWire
        is PaymentDetailCreatorRep.UnitPayment -> unitPayment
        is PaymentDetailCreatorRep.UnitTransfer -> unitTransfer
        is PaymentDetailCreatorRep.Ach -> achPayment
        is PaymentDetailCreatorRep.DomesticWire -> domesticWire
      }
  }

  suspend fun fromCreator(
    creator: PaymentRep.Creator,
    guid: UUID,
    status: PaymentRep.Status,
    createdByUserGuid: UUID?,
  ): Pair<PaymentRep, SendPaymentCreatorRep?>
}
