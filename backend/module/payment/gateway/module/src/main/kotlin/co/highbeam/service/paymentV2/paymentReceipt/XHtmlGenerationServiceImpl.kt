package co.highbeam.service.paymentV2.paymentReceipt

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.unit.client.UnitCoClient
import co.unit.rep.UnitPaymentRep
import com.fasterxml.jackson.databind.node.NullNode
import com.github.mustachejava.DefaultMustacheFactory
import com.google.inject.Inject
import java.io.StringWriter
import java.time.Clock
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale
import java.util.UUID

internal class XHtmlGenerationServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val clock: Clock,
  private val unitCoClient: UnitCoClient,
) : XHtmlGenerationService {
  override suspend fun generateReceipt(payment: PaymentRep): String {
    val (templateName, templateData) = getTemplate(payment)
    StringWriter().use {
      DefaultMustacheFactory("templates").compile(
        templateName
      ).execute(
        it,
        templateData
      )
      return it.toString()
    }
  }

  private suspend fun getTemplate(payment: PaymentRep): Pair<String, Map<String, String>> {
    val paymentDetail = payment.detail
    if (paymentDetail is PaymentDetailRep.UnitPayment) {
      val businessName = getBusiness(payment.businessGuid).displayName ?: "Highbeam"
      val unitCoPaymentId = getUnitCoPaymentId(payment)
      val unitCoPayment = getUnitCoPayment(unitCoPaymentId)

      when (unitCoPayment) {
        is UnitPaymentRep.Complete.BookPayment ->
          throw IllegalArgumentException("Unsupported payment type: $unitCoPayment")
        is UnitPaymentRep.Complete.OriginatedAch -> {
          return Pair(
            "paymentReceipt/UnitPaymentReceipt.hbs",
            mapOf(
              "businessName" to businessName,
              "currentDate" to LocalDate.now(clock)
                .format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.US)),

              // Payment details
              "formattedAmount" to payment.amount.formatString(
                withDollarSign = true,
                withComma = true
              ),
              "paymentType" to getPaymentTypeFromUnitCoPayment(unitCoPayment),
              "status" to unitCoPayment.status,

              // Recipient details
              "payeeName" to unitCoPayment.counterparty.name,
              "accountNumber" to unitCoPayment.counterparty.accountNumber,
              "routingNumber" to unitCoPayment.counterparty.routingNumber,
            )
          )
        }
        is UnitPaymentRep.Complete.Wire -> {
          return Pair(
            "paymentReceipt/UnitPaymentReceipt.hbs",
            mapOf(
              "businessName" to businessName,
              "currentDate" to LocalDate.now(clock)
                .format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.US)),

              // Payment details
              "formattedAmount" to payment.amount.formatString(
                withDollarSign = true,
                withComma = true
              ),
              "paymentType" to getPaymentTypeFromUnitCoPayment(unitCoPayment),
              "status" to unitCoPayment.status,

              // Recipient details
              "payeeName" to unitCoPayment.counterparty.name,
              "accountNumber" to unitCoPayment.counterparty.accountNumber,
              "routingNumber" to unitCoPayment.counterparty.routingNumber,
            )
          )
        }
      }
    }

    throw IllegalArgumentException("Unsupported payment type: $paymentDetail")
  }

  private suspend fun getBusiness(businessGuid: UUID) =
    businessClient.request(BusinessApi.Get(businessGuid))
      .let(::checkNotNull)

  private fun getUnitCoPaymentId(payment: PaymentRep): String {
    if (payment.detail is PaymentDetailRep.UnitPayment) {
      if (payment.paymentSenderResponse != NullNode.instance) {
        return payment.paymentSenderResponse.get("id").asText()
      }
    }

    throw IllegalArgumentException("Unsupported payment type: $payment")
  }

  private suspend fun getUnitCoPayment(unitCoPaymentId: String) =
    unitCoClient.payment.getPayment(unitCoPaymentId)
      .let(::checkNotNull)

  private fun getPaymentTypeFromUnitCoPayment(unitCoPayment: UnitPaymentRep.Complete): String {
    return when (unitCoPayment) {
      is UnitPaymentRep.Complete.OriginatedAch -> "ACH"
      is UnitPaymentRep.Complete.Wire -> "Wire"
      else -> throw IllegalArgumentException("Unsupported payment type: $unitCoPayment")
    }
  }
}
