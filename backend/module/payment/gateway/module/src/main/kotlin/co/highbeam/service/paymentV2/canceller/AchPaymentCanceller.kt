package co.highbeam.service.paymentV2.canceller

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.unit.client.UnitCoClient
import com.google.inject.Inject

internal class AchPaymentCanceller @Inject constructor(
  private val unitCoClient: UnitCoClient,
) : PaymentCanceller {
  override suspend fun cancel(payment: PaymentRep): PaymentRep {
    cancelAch(payment)

    return payment
  }

  private suspend fun cancelAch(payment: PaymentRep) {
    val unitPaymentId = payment.paymentSenderResponse.get("id")?.textValue()
      ?: error("unitPaymentId is not found in payment sender response.")

    unitCoClient.payment.cancelAch(unitPaymentId)
  }
}
