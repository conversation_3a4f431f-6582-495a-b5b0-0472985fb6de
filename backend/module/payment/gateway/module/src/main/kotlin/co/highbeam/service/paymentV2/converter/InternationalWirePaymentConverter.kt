package co.highbeam.service.paymentV2.converter

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.fasterxml.jackson.databind.node.NullNode
import com.google.inject.Inject
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class InternationalWirePaymentConverter @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val clock: Clock,
) : PaymentConverterStrategy {
  override suspend fun fromCreator(
    creator: PaymentRep.Creator,
    guid: UUID,
    status: PaymentRep.Status,
    createdByUserGuid: UUID?,
  ): Pair<PaymentRep, SendPaymentCreatorRep?> {
    val detail = creator.detail as PaymentDetailCreatorRep.InternationalWire

    val bankAccount = bankAccountClient.request(
      endpoint = BankAccountApi.Get(detail.bankAccountGuid),
    ) ?: throw unprocessable(BankAccountNotFound())

    val payment = PaymentRep(
      guid = guid,

      amount = detail.sendAmount,
      bankAccountGuid = bankAccount.guid,
      businessGuid = bankAccount.businessGuid,
      description = detail.description,
      generalPaymentMetadataGuid = detail.generalPaymentMetadataGuid,
      idempotencyKey = detail.idempotencyKey,
      notificationEmailAddress = detail.notificationEmailAddress,
      status = status,
      reason = null,

      createdByUserGuid = createdByUserGuid,
      createdAt = ZonedDateTime.now(clock),
      sentByUserGuid = null,
      sentAt = null,
      rejectedByUserGuid = null,
      rejectedAt = null,

      detail = PaymentDetailRep.InternationalWire(
        fixedSide = detail.fixedSide,
        payeeGuid = detail.payeeGuid,
        paymentType = detail.paymentType,
        purposeCode = detail.purposeCode,
        invoiceNumber = detail.invoiceNumber,
        invoiceDate = detail.invoiceDate,
        reason = detail.reason,
        receiveAmount = detail.receiveAmount,
        receiveCurrency = detail.receiveCurrency,
        tags = detail.tags,
      ),
      paymentSenderResponse = NullNode.instance,
    )

    val sender = run {
      if (!creator.send) return@run null
      return@run SendPaymentCreatorRep.InternationalWire(
        receiveAmount = detail.receiveAmount,
        sendAmount = detail.sendAmount,
      )
    }

    return Pair(payment, sender)
  }
}
