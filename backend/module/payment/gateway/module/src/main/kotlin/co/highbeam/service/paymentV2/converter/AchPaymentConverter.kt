package co.highbeam.service.paymentV2.converter

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import com.fasterxml.jackson.databind.node.NullNode
import com.google.inject.Inject
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class AchPaymentConverter @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val clock: Clock,
) : PaymentConverterStrategy {
  override suspend fun fromCreator(
    creator: PaymentRep.Creator,
    guid: UUID,
    status: PaymentRep.Status,
    createdByUserGuid: UUID?,
  ): Pair<PaymentRep, SendPaymentCreatorRep?> {
    val detail = creator.detail as PaymentDetailCreatorRep.Ach

    val bankAccount = bankAccountClient.request(
      endpoint = BankAccountApi.Get(detail.fromBankAccountGuid),
    ) ?: throw unprocessable(BankAccountNotFound())

    val payeeEmail = detail.tags["payeeEmail"]

    val payment = PaymentRep(
      guid = guid,

      amount = detail.amount,
      bankAccountGuid = bankAccount.guid,
      businessGuid = bankAccount.businessGuid,
      description = detail.description,
      generalPaymentMetadataGuid = null,
      idempotencyKey = detail.idempotencyKey,
      notificationEmailAddress = payeeEmail,
      status = status,
      reason = null,

      createdByUserGuid = createdByUserGuid,
      createdAt = ZonedDateTime.now(clock),
      sentByUserGuid = null,
      sentAt = null,
      rejectedByUserGuid = null,
      rejectedAt = null,

      detail = PaymentDetailRep.Ach(detail),
      paymentSenderResponse = NullNode.instance,
    )

    val sender = if (creator.send) SendPaymentCreatorRep.Ach else null

    return Pair(payment, sender)
  }
}
