package co.highbeam.service.paymentV2.converter

import co.highbeam.auth.getRestContext
import co.highbeam.auth.principal.JwtAccess
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import mu.KotlinLogging

internal class PaymentConverter @Inject constructor(
  private val strategyFactory: PaymentConverterStrategy.Factory,
  private val uuidGenerator: UuidGenerator,
) {
  private val logger = KotlinLogging.logger {}

  @OptIn(JwtAccess::class)
  suspend fun fromCreator(creator: PaymentRep.Creator): Pair<PaymentRep, SendPaymentCreatorRep?> {
    logger.info { "Converting payment: $creator." }

    val strategy = strategyFactory.forType(creator.detail)
    logger.info { "Using strategy: $strategy." }

    return strategy.fromCreator(
      creator = creator,
      guid = uuidGenerator.generate(),
      status = if (creator.send) PaymentRep.Status.Pending else PaymentRep.Status.Open,
      createdByUserGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid,
    )
  }
}
