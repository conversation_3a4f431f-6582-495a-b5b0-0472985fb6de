package co.highbeam.endpoint.paymentV2.paymentAction

import co.highbeam.auth.paymentV2.AuthPayment
import co.highbeam.auth.permissions.Permission
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentV2.action.PaymentActionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentV2.PaymentActionApi as Api

internal class CancelPayment @Inject constructor(
  private val authPayment: AuthPayment.Provider,
  private val paymentActionService: PaymentActionService,
) : EndpointHandler<Api.Cancel, PaymentRep>(
  template = Api.Cancel::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Cancel =
    Api.Cancel(
      paymentGuid = call.getParam("paymentGuid"),
    )

  override suspend fun Handler.handle(endpoint: Api.Cancel): PaymentRep {
    auth(authPayment(
      paymentGuid = endpoint.paymentGuid,
      mfa = true,
      internalForPaymentEvaluation = false,
    ) { Permission.Payment_Cancel })

    return paymentActionService.cancel(endpoint.paymentGuid)
  }
}
