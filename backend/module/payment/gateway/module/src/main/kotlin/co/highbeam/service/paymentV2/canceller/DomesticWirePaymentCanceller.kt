package co.highbeam.service.paymentV2.canceller

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.unit.client.UnitCoClient
import com.google.inject.Inject

internal class DomesticWirePaymentCanceller @Inject constructor(
  private val unitCoClient: UnitCoClient,
) : PaymentCanceller {
  override suspend fun cancel(payment: PaymentRep): PaymentRep {
    cancelDomesticWire(payment)

    return payment
  }

  private suspend fun cancelDomesticWire(payment: PaymentRep) {
    val unitPaymentId = payment.paymentSenderResponse.get("id")?.textValue()
      ?: error("unitPaymentId is not found in payment sender response.")

    unitCoClient.payment.cancelWire(unitPaymentId)
  }
}
