insert into payment_gateway.payment (guid,
                                     amount, bank_account_guid, business_guid,
                                     description, general_payment_metadata_guid, idempotency_key,
                                     notification_email_address, status, reason,
                                     created_by_user_guid, created_at,
                                     sent_by_user_guid, sent_at,
                                     rejected_by_user_guid, rejected_at,
                                     detail, payment_sender_response)
values (:guid,
        :amount, :bankAccountGuid, :businessGuid,
        :description, :generalPaymentMetadataGuid, :idempotencyKey,
        :notificationEmailAddress, :status, :reason,
        :createdByUserGuid, :createdAt,
        :sentByUserGuid, :sentAt,
        :rejectedByUserGuid, :rejectedAt,
        :detail,
        :paymentSenderResponse)
returning *
