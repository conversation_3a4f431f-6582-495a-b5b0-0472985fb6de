update payment_gateway.payment
set amount                        = :amount,
    bank_account_guid             = :bankAccountGuid,
    business_guid                 = :businessGuid,
    description                   = :description,
    general_payment_metadata_guid = :generalPaymentMetadataGuid,
    idempotency_key               = :idempotencyKey,
    notification_email_address    = :notification<PERSON><PERSON>Address,
    reason                        = :reason,
    status                        = :status,
    created_by_user_guid          = :createdByUserGuid,
    created_at                    = :createdAt,
    sent_by_user_guid             = :sentByUserGuid,
    sent_at                       = :sentAt,
    rejected_by_user_guid         = :rejectedByUserGuid,
    rejected_at                   = :rejectedAt,
    detail                        = :detail,
    payment_sender_response       = :paymentSenderResponse
where guid = :guid
returning *
