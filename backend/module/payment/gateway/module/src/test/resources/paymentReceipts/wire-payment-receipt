<!DOCTYPE html>
<html lang="en">
<head>
  <title>Highbeam payment receipt</title>
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <meta charset="UTF-8" />
  <style>
    body {
      max-width: 816px;
      font-family: custom-font, "Open Sans", sans-serif;
      line-height: 1.25;
    }
    .row {
      display: flex;
      flex-direction: row;
      margin-top: 24px;
      max-width: 50%;
      break-inside: avoid;
    }
    table {
      border-collapse: collapse;
      margin: 0;
      padding: 0;
      width: 100%;
      table-layout: fixed;
    }
    table caption {
      font-size: 1.2em;
      margin: .5em 0 .75em;
      text-align: left;
    }
    table tr {
      background-color: white;
      border-bottom: 1px solid #ddd;
      padding: .35em;
    }
    table th,
    table td {
      padding: .625em;
      text-align: left;
    }
    table th {
      color: black;
      font-size: .85em;
      letter-spacing: .1em;
      text-transform: uppercase;
    }
    /* Clear floats after the columns */
    .row:after {
      content: "";
      display: table;
      clear: both;
    }
    @media screen and (max-width: 900px) {
      .row {
        max-width: none;
      }
      table {
        border: 0;
      }
      table caption {
        font-size: 1.3em;
      }
      table tr {
        border-bottom: 3px solid #ddd;
        display: block;
        margin-bottom: .625em;
      }
      table td {
        border-bottom: 1px solid #ddd;
        display: block;
        font-size: .8em;
        text-align: right;
        min-height: 1em;
      }
      table td::before {
        /*
        * aria-label has no advantage, it won't be read inside a table
        content: attr(aria-label);
        */
        content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
      }
      table td:last-child {
        border-bottom: 0;
      }
    }
    .customer-name {
      margin-top: 16px;
      font-size: 1.2em;
      font-weight: 600;
      display: flex;
      align-items: start;
    }
    .account-number {
      border-left: 1px solid #C4C4C4;
      margin-left: 1rem;
      padding-left: 1rem;
      font-size: 1rem;
      font-weight: 700;
    }
    .account-number>div {
      font-weight: 400;
      font-size: 0.75em;
    }
    .details-row {
      display: flex;
      flex-wrap: wrap;
      border-top: 1px solid #ddd;
    }
    .header-row {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: center;
    }
    .payment{
      width: 18rem;
    }
    @media screen and (max-width: 600px) {
      .payment{
        width: initial;
      }
    }
    .payment-title {
      font-size: 1em;
      font-weight: 700;
    }
    .disclaimer {
      font-size: 0.8em;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
<div class="header-row">
  <img alt="logo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAu4AAADGCAYAAACNfQW4AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAACWYSURBVHgB7d0LchRXsvDxrGo9sL+5YXkFLlaAuBETAZI9bq0AWIHFCsArQKwAWAFiBYYVqD22BBETcZFX4GIFV44YfwOSus7N7OrGQujRlaeq+qH/z6EAC0mt6q6uypMnTx4RAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADql8iMW89ePxdMXCrh1S/52ksBAABAIxZk5oVNwcT1JbzTPwjcAQAAGpIKAAAAgKlH4I66rAgAAAAaQ+COWiSSfiUAAABoDIE7ahEkfC0AAABoDIE7apGIfCMAAABoDIE76pIJAAAAGkPgjrqsdLMdFqgCAAA0hMAdtflTljIBAABAIwjcUZtFkVUBAABAIwjcUScCdwAAgIYQuKM2iSTfCwAAABqxIFdSsr2b374vDrezvW0d7fwgFS3Kh697+caBOKxlb35KpLgrFYXkWrb3+3+/E4f1bPd/9Xmqutg0swWq3uMEAADA+a5o4O6XSMgHXcsrOky+sp1FnQFt/53nMUP49039wxW46+PlUr30ZeVYFux7egK0xAaLh7J0N5HODT3rB4PNQvq/BTl6+SbfyAVjW89e7+hz2JVKkp4mQjZkxqxpWiOp3MbWn/QBgDoQuLckDX9eF3cQLfvioIFMV/94KQ5Bws+JJJVr1gtJbWagJ3Bby3Y3E0krz+oEKX7ey9e3ZAL+nu2uLkn6RCrS8+xgL1+7Jw5lwH7twZEUD5PB7FDx8d/SQRXg8hMNRLf78v4xATwAYB7MQeAeDqqWdGiw8LX45eKgAW0mfs7APb0hfpN4TJSy6lnPwXOfy4R0pND3YNKVihLn++lWtpMdyfJPOgO2evFsVNjsyHJXB0P3dFDjOqcBAJgWM784NUhSufxEs3NfiVNH+q5yl1C9XvyjJTnMxSW4u7zoQKMnLqHLRkxomgbjP8n4pVyZvud3LNgXAABm2BXtKhMycTqSRVfWLpXEnYm2xZ7Bl5lc+Tb7pSsOb/LbeTmbUd0HWd4UoCFWSiSO9RcdufZcAACYYTMfuCflIsrWXJP3zgWm/uz34LtFfhaHvnQisu7JK3HQQcodARqTPBKX0F27/j/fCAAAM+pKlspI5U4CfylbHYZWH7NUeDP97iA6kdATl9D1ZvqBi9zS8yqJeC+F8N61EBYAgGkwB4F78Yc4xGXeXFn+lZjH1JmFnrj4a86X5NDVkcYUknYFqFkat8g7qmQNAIBJm4NSGeurXt2wx7lLIfKbOMQ8ZtkRw1dzbj2uxaGcXfANGHSg8YBFqgAAAPWZh8WpubgsZOJWOFslDvqquwXp9MTB0xN8pJC+q85drbBIFXXzdnUa8c7QAQAwDeYhcHcG0f7OMh0JE+lxHuTYtUA1plxmWY62xUlPrgcC1EgD955ECJHfDwDAJM184F5I6srAxQTRC3Ls3Mglrse5ZtzdNeeHsvRQHGLKZVQ2bN0H1CLyfMxf5/9wv4cAAJi0mQ/c/f3G/e0ZI/qqy4eIcpnyWN015+7uMkGKF+KUuFv3AWdL5PixOOgsm/s8BgBgGszJBky+Li9/z3YjeqsHV+13fJ174SyXkVVvi8ayu4xvYayUWfctAWrya/5dT4PwSsG7DrRf/pqvbwkAADNsLgL3IMEVzC5I0hU/Z5174l4oalIpeuIUZMGV/R7OMDwTJzrMoG4WhI8fvBcvluSL+wIAwIybl8A9F4eYns4RPc5XYjYnsmyjRPR09z52Iem2+K0cyTIlM6iVBe99Sa7rX88rgdHMfH9jN/92s5ffjOpGAwDANJiLwD1icyJXf3MTU+euQbD7cU1EuYxl3V0Z/5j6+qGH7KaKutl5uZuvbepHYkH66GNRvvhaP7dRDnQBAJgPcxG4R2xOtBIXTHrr3JMfYkpHNNv/VNzC5pqztt+7KPDjI0vnOSUzaIoF6aMPMuwAgHk0J4tTTeqqOdfsd1ecUinc5TIx3WUiW+JZK8wn4hBXpjOQUTIDAADgMzeBu7d8JGaxaBnI+rqtpLIYtTlRXPbbX+sem3UXSmYAAABc5iZwj+i2ksUEkoUkrnKZmODZDGt3nRtB+TvM1JB1p2QGAADAYW4C95jsd0y5TEf62+LkXSg6UkRsjGQDh9vZnms3VZ3d+FHiZN6dXAEAAK6qOapx92e/rc+4OMUMGGyh6K1sJxOnZTnajtgYyV78R57Ht8XAiQR3X3djz3nMsQMAAFw1CzJHNJjsia9mfdBdxts6Loi8SERcwX9HrlnJimtzGFukupbtPtPH9i74XNHHf65/bkhFC3K4dSRL+lwn3pIX92MD085Kwd7Lwmoqaab/myWSfnPy33XW6l3QQbd+7F+T4/1ywfn8O/28nPy3MEhCpPmxhPxfg05hs892514UsS5en50Deu08KOT4nR1zkA/7b/KNXGbErey1Hk9Y1Q+7/mdnfEmuM8J5X9KDWXot7fz8U5Yye830fFxJzr6/zeSxXWR0np51zEGS/Sbfk3YupVJ0z3m+B8+1yHE+S++Pps1V4G6bImkw+cQTTA5rvnviYN1lgnScWftB1v2x96S01pB6zA/8AXRZMvM6X6vUYtICjfVszxaqujrUjB47ZsAETJNbei6nsvC9DqS7R3pudz751/DJ/yXD/8yRDt/1vbRvpW9Bjl7O2w3KnpeOLN7RwcrdIw3yOud83ej5WNI/9fmwz/T0e14UctiblefEAr8PsriZSueOzgGvfnpdDqe+Oui9YzTpvWzHnNsx9+X4xZspuyaOjisZnN/9rpQBu8jwNTtLZ/Cf6HHt2oBMz+/+q2k7v8sBSP/u6LiO9PVaGv5bMuPHdpHzztOzjtk+U74ndzXR0OmJHqvOum9LhPKaYKXCxd3yXErOfb7LZ7rz8f1RSHil8Yq3o99cSGTOrGevdywgFAfbuMUbRMY8rl7OX+7la/fESbPuW3rSx7RZPFiUD9c9Wb+Y4y4lvd38Nln3E/yvZ7Ktz6Vr9iZWeSHu7Eh1toHS9YrfYxfxUPV7NKPzWG84W1Kz4U3oUdz74KRkuy/vzxzMr2VvfkoGN7uxVXp+fe/ns9/Deh5roJf+UM/zcv5z4rWW7f2enJ0tvvD3OOs9ZmV/HflCEyj9zYhZyJPy4fm6LRNU/7ltygHZpI7t06C1zuMyzR1b1fNVz59n+nt8spbMjv1Qrj3Qa8jDyPPUdX6uZa8f1fDYw8dPtpfk/bOrMlt50lzVuJsQsWCziGjRmMqRu+Zb34x3YzrMWNbdu4vr0Iq3v3p/UOYTIt44cd11gEmxoMYC3XLAUmcAEDY7svy7Dk6enO6+lHzMdE6v8nnZe6uDz+f1PS/lc2I3fpki9vrY72S/m9QTkIxk9vzp8/i7DYCkZc2d2yZ0J3VsOrv8QGeof08He5nUfVzmr2O7nf2zygC7AUl28v/sNT2UZX1fhq0aztOP5+c4a9XsscuBRy2PPXz8sKVxy9tJvD8mbe4CdyuX8QaSlsnyLpj8Jf/Hy5gA1tue0ZQjzjCR/uq25bw+c5E7qi5M1c0YuIgFaxZUNxPUfOKh3ZhOXpM0y5XJFCuD2MHMy6o0wG7WNiiYhoXta9nO6l+BUGNGAdKTNlrotnhum0rBXwx7rey80YDnaY2Dq4tkqSz81NbrdhZNCN4Y/X30vqw+w3QpnWla3rloN3YbLDX02IPHt3No2gb0TZu7wN2CWFssKk7DxaIu+rgRnVb87RlNOWUV21/dF0DvDurjYx6brDtmgwUYFqzpX93v1YoGN8ZRYNPQza8W69nu84aD2JHVk8/JJGg2+odElnZafD0+G8TVbTQQkfbO7RE7xxvLnFrgmJTH1chg8hKNv24XyEYzQi0MLs8M3u2xy8FSs8oB/e5zuSLmLnA3tlhU3MKmN4i0kpVJtGccid/V1B9Ax5bMkHXHtLPAJtWAcQLB8zCrtTOJwGMs5U0z2ZT22HPy0ySymRa06xVru6XM7UlZUwOWCQxETltpInPaVuB4iWxSA00diLU1mLbX75NjbGHAcEqyeVWC97kM3GN39/QGkcNsf0x/81GLRBc77tj+6t5jt5KZ2BkHsu6YVnZDmnBgM3h8mUpWUtFq0D6y6l2bEyErg/aJqT0InOBA5DMW6NUVvLcfOF5oIgNNWz8n7Vmxc9OOsbxeTuK5TzZjKhdmxVy1gzzJFqnqCLArLv42hZNqzzhi/dV1lH3HH2CErk1ZelbF27HrY//gfexCUrvI9AROYdU60sgElD2qKzd6mQl2E7JMu/51woHN5AOrKWRrc16111K28brvcQyCQP3zpkT6Ltu7W0x2IPIZC/j0HviH9x5ohse1JdNlNNCM3Xl8mg12RU99++nUYli5MHdtdU+au3aQJ+m0yf9G3OzyRflw09NqqIb2jBLTJabsPBFzk/e3aCxbwCXeWQN3W8p5Usf5M0Omvh2kr23g1GihHeTE7esxVg5iZ/x1NU/1uN1BYNnCcunttA4I9f16c8+x6c9ooD2tr6237fQcnK8tmlxr5DbMZanMSGTZSuadho2tdTfJYGrc9xF/IfaXrUQukl35IMubAkyJcrqdm+WUW72iZXYPY467nEWa3lkcTV64SkuswcQ0v2dZz9WGsDmpbj5tmOvAvYYA2nVhHNa6z/R0WMzFJWaRrE6x3RFgCkyuThNVXdVgyHvc9Q1Iw0E5O2zJmtGHVM6Sn2NQdlHlG8ra/7ApbhYvJNuFFD+mcnzPsuMnP+zztmEie5dMv6rnziyZ2xp3YwH0Wrb7TC9QES0eO8915Fa5ZMYyz8NFP12ZSf46f/sePfae79j9jwvUSTOST6Q++3rD/1nP70FQ05H+QV86K0HCSiLp9/qp1fnP7FuQlxzocVqwt1Lv8YauZdimrMxOX+vkYxBbvtaDY66xO1D162X8gDTYa6iz2UXvose130vvn5v6Wn/vfa016/5Af9/tceuVU1naFJ+eBuaPx3ge7d+fdrO3K8fy/x/qsbnWsw0HXD2ZuCbfk2M8ug769DXOT3zGnsta3h9JWWe/JXNorgN3E79Y9OOof0sqssyzXri6MqNiFovaTrKFLHTFQR+3KyxSxQTZTn/xHRnKAOdY0u1yo7JzDRbhjQId/evEFnY1oKeZy2cd+a9eL7/5WVBd5zEPy+wm3fpvEAAuyN/2zzreke+yf97V66OdX9HHXTUILPcq8S0k14Bfj+3Lpxcd28gwCO7dyl5nnXKg4DnWlbS8/46ZPXUtivxxt+JC2OHxb+mxbacSHPX0kxxoltelQmcOzlpD0Px1qHz8884jO19SKbr6WsbOCmVr1//nm73f//udzJm5LpUx5RsjbmfP4ag/k4rqaM84STZi9daJxewkm0xwRTpgOrIQew7u9yW9+Wu+vnVJ0P6RXS80gNjsS3I9ZnH6lOjZcejxbNi14LxAr85jTiW5IZPTs8WUdrx2TJcFtvac1Pdaj1964S8lsWDLFlWub40TtJ9k578dqzjLR8e9D5UzCZUDvcpB+0l2bKn0XYsgJ7Gey+KRRfnyur2O5y38bfI6dPLxzzuP7Dm1ioVCko2yLClC+M+GzKG5D9yNvTEjT0B3f3VrzzjDN+GoxaIRi4MzagAxKfF1ssUL63IybsB+mn3fknywLil11Qq3yrKyFsBWOf56jnkyZYmj4/V0QCmDlLXr9jMkQhhzoOndGTyR4l5s+eIwQPYE72PdhxalU7HEwrqn+YP2Ee++MW0PNO0c04D54bgDLzs3h8FzLjXwPL6+N+5FNLswNZalTY8rEbgb76j4L74FJZbxj3/syYlZLFouDvYZlssArdOp+a44WYZoN/92UyLZdWNRPli2aKaC9+HNeUsc7Jg1y3cvYuFf1v4GN/7jPcl+RlzwHu6Od+xF5fKv8hjrWXM0TKJVzqKOcx/qVwzcba8XqYmvIUObA81k23Oexswo1PH4ZlHeR1wTkkzm0JUJ3GN3UzXeFfyzXTITulYnJg7DMqWeOAwX7AGt03PPWyZjGarauknVEMi2rRcbxMbuwPynLGXSErum1xG0j5Q/y99K91gWLgxcra7esdYrr/MYje89UtaEX/JFlY7tqMZB8YIce35WWwPNvC/iHhTWETvFPH7MjvSJiCt2mXZXJnA3evLcn1QbJyuZmaEb8KfCf9wjbn9WY6wLNVCr8pzzZcJsgydvecx5YgPZNukgo5aZxZg2vgvSz6Qd+YJ8uSU1i7lHDZsJXPDvi5VnT0NkCc9ZyvdI9UDwUJYuPL5QcVDyL0dp03mGwWUuFR0mX30ljUt6sdelmBbPlm2PffyI2fu5jCGuVOA+vGDE1hK6su51LJKdlJjst77h3ItUP8xwRx7MpveXZC0vkJebj9Wvjg3dmqZBy6u6Bi1lENTpiUMinVZu1BbQVl2kOY6YgVoiyYXX6eAYkOpgoCcNKKT/Sqq78L3Zkf62HuP9cT+kZtZSUSpKw5/XpWGHUkQP/J0zCgOFHHle6094B0bzau7bQZ5mNXbr2es7/voyf59xe+y1bO/B7PVr9reusu/R53vf83wnZeBeuR4S8Eol7YpDE5nJkeF+FC+SQc/oaRVqfZ8GOf45uSSDPEGNDdJM2cLYtWt3dt4/+DquxGdqz/3Jjoz7ZQOTtvb+sHvhe7m20pdiZUES64k/2I9h2Ae9Eh0YZdKsvI6ZhfIatJd7Ypdl6fekBrYPxuzFTs24coG7senIjoS33t7uMZsn2EIPzSbtyIyJ6ZFs5TJ60e1KRZddqPGZPLYW0UtvXpleWLsy8zo37HZaVVOZyRHNJvYKWZjawL3u49fr876v03gbmn2PDZMdPUeyY+W8vtXWcaXqWW0b89xqrrtXJtVl0hDrHa7XsNWk3ABI/975SgbBeLIyDMwz+zoLHI9kcH7qxyhMTwb/TaeTmxtF/qTBzyqfhwoO6upVn5S72gquaOBuWYS4HVUntavoILN3P+Ym2RF57st+D4IyV+Bu5TKaQfK001ydwt0Qp5i1N7s9kQ5GdoPvzEFp0/DGXVXeVGZy5EgW9zsytaHsQd3HfywL+bQebyLHtXUjOY+Vknhmf0L4t7XU/Cxwt44rVUOeRIq7+p6eplmPlTruB/YzNBHVTQcloLbRj5XgjM610bNUDP8vfPLZGZTLZOVSH+KAoSsZuBubjjyU5R+8Uy8xWfeYHVWt3lxvktvidDv71XVD0OfJnf0uM0h7Nl1XuX542CmhJ0ALLLNW/SZdX1brPBYYr2e7B+LfAbpBSW2L/EauyfsDHezLNIqp9x2Xf8ZhITvrs5o5zmY4+PxouJjTFcBZciGVzoMjTVylg0WL1WfWZo3Odr+TiUoIthtwpRannhTfX93f9cSy7v6FFuP26z3bshxti8/K37Nd78I9C4h+Fod+5U01AD/nQD6XFoQrdBOc4lm2vI3fzWYcxOG8GaNUkrloi3cU/vO1VGQBu85y7+jswY6+v+9O5+AXGN+VDdxNbH/SQ1l6KG7BO926cllbrIvE9FZfcNSpn+DKUk14G3PgUprV+kNakLSQ2cdl2nkNvKVHOiM7l32rR5alGDvotgSXzvQ+6QzWlE1mV12gCVc6cDcx/Un1RupeLBbT4i12cyINNFzZ75gg2l+XH8i4A7iCZnTfjylgXXQOZfmt/jUiuQZMpysfuEdm3Ve8GzIN+5J6NyeKWjCUStETF3/WoswguW5EmQBTTAfSLWyiUtbfC4ALWdCeyvIOrQMxr6584G5isu5FRK9hDaBfio97wGDKwYoviI7bzTT1lMsMWpwJ0ALn2pNMWuDseIOZRS12VXZ/ImjHvCNwl7jFookkP4hTRAAdNWAw3p0JY3YzDVL8Jg6eBUlAe5ov51obLAwnkJu8dmY9vAmSttZbTCvbuIqgHfPuyraD/JwtFk08fd1XvD3dB48q4uonr9PzUYs2/TsTnt1ubLzHDLlno4rFso1k4y3YgHJWqMikmnM3vqkRaz2mQ9bG3hLvZWG1Iy5n/l6FhHdVs3SjZNY0LYrWhNW5z7uVyEjtNe3hwLo5DZ+DfPAZKd7pvcw+d9CR/kFfOrn9Xh0JPwnvU7SAwH2o3GZ66YEnqzXMfvfEwerNNfvtGDCU7Si9N5AFKexiI1XFLFD1PmYg04jW9N+5tlsJ/7HWslvSmM6dq9B3eha0sbeEBoWuAPC8lqGeXSePJNz7V74+MwmTjlx7JPGbdvWSQeviorcgf9vv5TfHvr+uZ68Panh84FKUygyVi0V95SN6kb0jTjHlMsMbiIttoS4u/rIA2/1RHBIW5aE9znPU32HqMpZJtF0sBVNBkw9daVgiC67OYXpdP+/8rXxeR7b/bVWZbQ+b4qTh9stE+hu7+drGr/n6lt2XqwTtQJsI3E9I5eiZ+GQxCygLSV6JQ8zmRGWm3jVgcGe/bTdEcWirawfgb1sqK7ezvZqn6UtlJhHTImZd0zhiBmofzi8lqRy4z9IeGqksdcUpSLi/l6/d85a7Am0jcD8hJvsdwvt74qQZ5Z446MRnV6K4ahczcfKW9eiFlcWpaEVE21K7mD6K67r0udhMIhqRxXT1ukxEEHpwXmnLkhzm1c/ruLbDLXMlsfTe+3gvX9+WWhTUt6MV1LifYr3VNSCuPO0duzlRx1Ebp79nVEakEPktdVzwYhbi2YKnqqv+NcNFxh2t8V4D1MqRXLMFahtSAxsEHMryjmDqBOk80T9uSs3KgVrinGFJzs2qW9JkPXu9X3EvjqjGC+PQ3+l5la9PJbz6JV97efrzZbOG6vfQY0m3pQbD1421WGgFGfdT/L3V/dkJy/J5+0fHZPg025CLA+0ZMc8i9lcQC4zWs91KwchZ7H19RD/qaba6nu09kZpZWZT3NS80qL343/uVSzKDLDRWpvVd9s+75WzS+B/HGmqf/Xt61kElvXKGLV5MqQ5QFYH7KQtyvO+t/Y6pc9fA/Wdx+FOWMvHLxWFB+pk4Jee0K7sYi1PRnmGGMaKbRrKpQd3bYXu6ym5plnO4XTtT79PtoWaMa6t3X8teP4pbYJlcOOBclqNtqf5Tu02VBRWyWHlWKzgbHDQvaWyAA5xG4H5KWYed+i4O4T8RU+SF6zEXI27u1oNWHBLpREwJJqzUx9QrpHghcVY7svy7ZWXHDeAtYLfSgY50yLTPjLBdx6JkC9p1BnRL/PYvyx6X97akJxUF6Tz3DkLPo8/Zg4plOyavK0Neqmcn4rVsd5P3K9pEjfsZghQ/J75WWO4g2tvjPOYxjwYbSFRHX3XMO8tOatb7QQ035IcawFtmtmfXFX3v7IfhgNkGwBqs6Ufnhg4VujJ4L9MHetboVfuJvr43+vL+8Zt8I6/yvcN1DM+TyIWgQcJYHdESOX4cqu9+nek5vKPB+0bV4ztLOUsRnkpF4ZJSIIfV2M204tYkAD5k3M9gmyKJS5KJk7/HeUyrxIVcHJKaMhXAtLKbeUeOf5TahK4mAx7pBfcny6jbh/1dP/dcg3bL2FIWM9PCZjnD8vr5rTFKS8rZlTdPjmTpdx0cxnZvycdtY2plYN71VMPgPZMIw3KgbXHQY7wg2PfNkh/Kknu2xJ6LlHUomAAy7mewOvcjqZ6LjunyYlOA69nuQfWV6YEbPtCAX/J/vLRMuWNKH1eWBfCdzfJaPggmc51pGXTg0iTLNzZbmUi/W17nC3Ht0nuKJlJevMnX8nG/PpX+fc26e7oVZcPByXZfjvUxx+s2Y1ntD7K4qcf/wL+ZXrJ9cZmMb8dj2zhtLdt9uVdxh1gbeKXSeU7QjkkgcD9D2Tprz97IVYPiLGbqzbarTqpvcET2G2hIX+R+R8JbWr2hGjtfygFf8jGgDBZkSx3B+gl51ZaGlnWPG5COBid7OiixwUn/NzmVxQ86K5sOSsDC6pHeR2On9vV9+PiSL/EuWtWBVPK2HIxcXOpk9/b3srDaGXTaYTCPySFwP4e3x/mwy4vzImIXwSKTajIB0AjL8n2b/XLPmaEEGqUB8uMq2faRmgaktsNrpj/js1KfcrBS13qNy7Ltgw2mXh7JckQb1kGpkw1G9nUwYnuN6KCk/0cina/KRaw2AAk64zAYkAkwSdS4n6v9Li86pfqHONS9WyOAvwzbQ9ZY7w7UIdn27vo53DtkFs7pfFGuXfp7ejvmnGFVByN3bd1JMlh0autPBi06V5l1w7QgcD+HdXkRh5iOK4lzq/XD5Ct2FgUatJuvPbXt0WWycgFK+RjlIxeyoH8KzukLhIO+JBu9/OZY90XrmCPAFUDgfg5/l5eozYLocQ5MqV/z9a1Uju85N2iL9WOQENtbHvF6Mnm5BbR19DS3c3pag3ebEahyjOXMWC1Zd2CqEbif45q8d25OFNOe0ZdRS8Of1wVA46zTTF/Sm6G97HeeSH/DMv6CiTuU8ONkg8NwoAO4e3VuRDSNwbse431PGZDV7k9oYC32PhWgBQTu5yhr5lwXgEwAzC0LmvbytesWXDQZwGsw9WxRvrj565ht99C8/yeH+aK811kXdxeTGLkNGqu2LhzHFAXvg4FqZO1+6wG0XQusjbQALSBwv4C1Z5TK2JwIuAosuCgk2ag7gNef9dKCFw2mHo5b34t2WELHPhblw4a9TtKeng3i6sy0n2bBe1+S62Fyayl6VgIUO1C1gU17JW3hQIOoe3YtiEj2AZUQuF8gkSSX6gjcgSuizL6vb1sGfjhV/qJ64DO42fcKKX7U4Oxr/Vn3yLJPpXz0FwvS7HVqPks9ODd+3M3XNtoYxI1mk1rOvucW/Nox1jUwaamkrWeP8Uu+dmIA54oZgEro4w4ANRgG2/Yh3eztyrH8e1UziKvJYLfM9JvR12mG/o9hQJbrx34TpQ8nkAGszedBmWWpb2WvtzsStvR/f5DaBNuM79mCfPl0ErMuJ49Lg9/vG9ohtGcLrr1lMZcZDgKuf5vt6jFYa8fa9HSg8ezTgL3k3f8FqILA/QKFhHeOKYlMgAgdSfYLSbaloiDFzzIxx7leTralMu/UcvXnp9NiXfIw2OrJxLuQVCv3K6T/KpVOXuV7mstqVn+NU+nn4391eCWVmgmE38767DBA3NRAdysu0LX3QrJvweySfPly0mVSo+Oyv69lu/pncicZ7Bga1c98Pxk870WvrVml0SAklaKrv/sj/2uTvkzk+MVFv7ceW8/aRciYmj1f673mTfra4LsvzmfpUq17L8+b29mevtmrZ1F0ys/1vNrFUbNzlXd/K+thq18E9WKW6Y3md6nIduvTLMmWOKxnr3cc20Xn+pzSOQdXml4fnur14UGV79Eb56u9fO2uoFX6WlnW1TbtsRmXG7b2KZwqoxyWYuYaEP2m1+H9Bfnb/iysafg2+6VbziSl2XnHZk4en+2L0pH/6k3D8XleG0rXME3IuAPA0LfZ60ehYkZOZ+Z+e91Cu0YNlG5U32495ILWDcuf5rLLyMmSsFk0z68NrgYC9waUWWUP3+ZNQTpP9DFb64Cj2Ygf9PG+F5dilYkeTKu+hOtVZ9nScjq48cDd894JLJYDgLlC4N6IyqUgsVarZ+KiZOLeIZagHdMrGWSoK5+jWTfbWSnbwTXjVvZL11Nb3JE+mUUAmCO0gwSAIW+geyhLjdaRd2TB1bGE2lwAmC8E7gAwpIF7TxwSSWtsBfipW9lOpjNcm1JZ0hMAwFwhcAeAISt38bUwC93vsr1Gsu6pLD8Rh2LQPg4AME8I3AHgE76AtxB5XmbH67OWvba+064BQZDkpQAA5gqBOwCckErhDXhXOrK8U1fwXgbtgx05Pfbr2j4eADA9CNwB4IRyQae7Pjyz4L3cadLHAn9rKRsRtNsmac8EADB3aAcJAKcEKV4kknTFx3aUfL6e7T2yXYYLOey9yTfyy77JWj6W3WNsIWpUe9d8L1/fFgDA3CFwB4BTLPBd08A7qbiL6imDAF4z8KJB/H6QNBfp/6bB/IF+/qD8985XYfBnv1v2aY/fj8EGCwIAmEsE7gBwhlT694N0nLsgf2Y1KXc+vZt8ssFTMfy/2jYmI9sOAHOMGncAOIPVumv+e6Y6s/Ql2RAAwNwi436BRKe0gyS5YKISXgNMyJJ8uH8oy6uRJTOt0OvV4zf5Wi4AgLlV2/wsAMyjtWzXAvedsgZ9OmnQ/uzXfP2hAADmGoE7AFxiyoP3/d187aYAAOYeNe4AcIm9fH0/iGyIhAOZKsWLRfmCunYAuCLIuAPAmG5lr7NUws401LxTHgMAVw8ZdwAY05v8dr4kH24mk92ZNE+kv0HQDgBXDxl3AHCwune9hP7UXvbdNm6SZwvy5dNefnPKSnYAAG0gcAeACN9l/7xbyOIDDay70ggCdgBAicAdAGpg9e+J9O+m0rkTH8TbItj0ZSrhVUe+6BGwAwAMgTsA1KybvV05ln+v9iVZTSTVgD65ocH4ShD5rJ2kXoQP9PO5/vWdfs2+/rlvXWwEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgzvwf55WoNc2uSR0AAAAASUVORK5CYII=" height="60"/>
  <div class="payment">
    <div class="payment-title">December 3, 2007</div>
  </div>
</div>
<div class="details-row">
  <div class="customer-column">
    <div class="customer-name">
      Payment receipt from Business name dba
    </div>
  </div>
</div>
<div class="row">
  <table>
    <caption>Payment details</caption>
    <thead>
    </thead>
    <tbody>
    <tr>
      <td data-label="Amount">Amount</td>
      <td data-label="Amount sent">$123.45</td>
    </tr>
    <tr>
      <td data-label="Payment method">Payment method</td>
      <td data-label="Payment type">ACH</td>
    </tr>
    <tr>
      <td data-label="Payment status">Payment status</td>
      <td data-label="Current payment status">Pending</td>
    </tr>
    </tbody>
  </table>
</div>
<div class="row">
  <table>
    <caption>Recipient details</caption>
    <tbody>
    <tr>
      <td data-label="Recipient">Recipient</td>
      <td data-label="Recipient name">Jane Doe</td>
    </tr>
    <tr>
      <td data-label="Account number">Account number</td>
      <td data-label="Account number">********</td>
    </tr>
    <tr>
      <td data-label="Routing number">Routing number</td>
      <td data-label="Routing number">*********</td>
    </tr>
    </tbody>
  </table>
</div>
<div class="row">
  <div class="disclaimer">
    <p>If you have any questions about this payment, <NAME_EMAIL></p>
  </div>
</div>
</body>
</html>
