package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.createInternationalWire
import co.highbeam.testing.utils.getPayment
import co.highbeam.testing.utils.internationalWire
import co.highbeam.testing.utils.mockBankAccount
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetPaymentInternationalWireTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    val payment = getPayment(UUID.randomUUID())
    assertThat(payment)
      .isNull()
  }

  @Test
  fun `payment exists`() = integrationTest {
    val (paymentGuid) = createInternationalWire(send = false)
    val payment = getPayment(paymentGuid)
    assertThat(payment)
      .isEqualTo(internationalWire(guid = uuidGenerator[0], status = PaymentRep.Status.Open))
  }
}
