package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.createInternationalWire
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.internationalWire
import co.highbeam.testing.utils.listPayments
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.unitPayment
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class ListPaymentsTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `no payments`() = integrationTest {
    val payment = listPayments()
    assertThat(payment).isEmpty()
  }

  @Test
  fun `payments exist`() = integrationTest {
    mockLegacyPaymentEndpoints()
    createUnitPayment(send = false)
    createUnitPayment(send = true)
    createInternationalWire(send = false)
    createInternationalWire(send = true)
    val payment = listPayments()
    assertThat(payment).containsExactlyInAnyOrder(
      unitPayment(guid = uuidGenerator[0], status = PaymentRep.Status.Open),
      internationalWire(guid = uuidGenerator[2], status = PaymentRep.Status.Open),
    )
  }
}
