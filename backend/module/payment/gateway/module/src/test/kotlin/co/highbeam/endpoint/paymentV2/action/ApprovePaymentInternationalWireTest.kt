package co.highbeam.endpoint.paymentV2.action

import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.client.payment.PaymentClient
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.money.Money
import co.highbeam.rep.paymentV2.payment.ConversionSide
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createInternationalWire
import co.highbeam.testing.utils.getPayment
import co.highbeam.testing.utils.internationalWire
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.rejectPayment
import co.highbeam.testing.utils.sendInternationalWire
import co.highbeam.testing.utils.sendUnitPayment
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertFailsWith

internal class ApprovePaymentInternationalWireTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    assertHighbeamException {
      sendUnitPayment(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `payment exists, fixed side send, wrong side provided`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false, fixedSide = ConversionSide.Send)
    assertFailsWith<HighbeamHttpClientException> {
      sendInternationalWire(
        paymentGuid = paymentGuid,
        receiveAmount = null,
        sendAmount = Money.fromDollarsAndCents(98, 89),
      )
    }.let {
      assertThat(it.statusCode).isEqualTo(HttpStatusCode.InternalServerError)
    }
    val payment = getPayment(paymentGuid)
    assertThat(payment?.status).isEqualTo(PaymentRep.Status.Open)
  }

  @Test
  fun `payment exists, fixed side send, happy path`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false, fixedSide = ConversionSide.Send)
    val payment = sendInternationalWire(
      paymentGuid = paymentGuid,
      receiveAmount = Money.fromDollarsAndCents(131, 5),
      sendAmount = null,
    )
    assertThat(payment)
      .isEqualTo(
        internationalWire(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          fixedSide = ConversionSide.Send,
          paymentSenderResponse = objectMapper.readValue(
            Resources.getResource(
              "sampleInternationalPaymentResponses/successful-international-payment.json"
            )
          ),
        ).let {
          it.copy(
            detail = (it.detail as PaymentDetailRep.InternationalWire).copy(
              receiveAmount = Money.fromDollarsAndCents(131, 5),
            ),
          )
        }
      )
  }

  @Test
  fun `payment exists, fixed side receive, wrong side provided`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false, fixedSide = ConversionSide.Receive)
    assertFailsWith<HighbeamHttpClientException> {
      sendInternationalWire(
        paymentGuid = paymentGuid,
        receiveAmount = Money.fromDollarsAndCents(131, 5),
        sendAmount = null,
      )
    }.let {
      assertThat(it.statusCode).isEqualTo(HttpStatusCode.InternalServerError)
    }
    val payment = getPayment(paymentGuid)
    assertThat(payment?.status).isEqualTo(PaymentRep.Status.Open)
  }

  @Test
  fun `payment exists, fixed side receive, happy path`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false, fixedSide = ConversionSide.Receive)
    val payment = sendInternationalWire(
      paymentGuid = paymentGuid,
      receiveAmount = null,
      sendAmount = Money.fromDollarsAndCents(98, 89),
    )
    assertThat(payment)
      .isEqualTo(
        internationalWire(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          fixedSide = ConversionSide.Receive,
          paymentSenderResponse = objectMapper.readValue(
            Resources.getResource(
              "sampleInternationalPaymentResponses/successful-international-payment.json"
            )
          ),
        ).copy(amount = Money.fromDollarsAndCents(98, 89))
      )
  }

  @Test
  fun `payment exists, international wire call fails`() = integrationTest {
    coEvery {
      get<PaymentClient>().request(any<PaymentApi.CreateInternationalWire>())
    } throws HighbeamHttpClientException(
      statusCode = HttpStatusCode.InternalServerError,
      errorMessage = "Invalid payment",
    )

    val (paymentGuid) = createInternationalWire(send = false, fixedSide = ConversionSide.Receive)
    assertFailsWith<HighbeamHttpClientException> {
      sendInternationalWire(
        paymentGuid = paymentGuid,
        receiveAmount = null,
        sendAmount = Money.fromDollarsAndCents(98, 89),
      )
    }
    val payment = getPayment(paymentGuid)
    assertThat(payment?.status).isEqualTo(PaymentRep.Status.Open)
  }

  @Test
  fun `payment was already sent`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = true)
    assertHighbeamException {
      sendUnitPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }

  @Test
  fun `payment was already rejected`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false)
    rejectPayment(paymentGuid)
    assertHighbeamException {
      sendUnitPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }
}
