package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.rep.backendV2.payments.PaymentEvaluationSubmissionRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.Constants
import co.highbeam.testing.utils.createInternationalWire
import co.highbeam.testing.utils.internationalWire
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreatePaymentInternationalWireTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `happy path, send false`() = integrationTest {
    val payment = createInternationalWire(send = false)
    assertThat(payment)
      .isEqualTo(internationalWire(guid = uuidGenerator[0], status = PaymentRep.Status.Open))
  }

  @Test
  fun `happy path, send true`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val payment = createInternationalWire(send = true)
    assertThat(payment)
      .isEqualTo(
        internationalWire(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = objectMapper.readValue(
            Resources.getResource(
              "sampleInternationalPaymentResponses/successful-international-payment.json"
            )
          )
        ),
      )
  }

  @Test
  fun `payment policies enabled, send true ignored`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.PaymentPolicies] = true
    val paymentEvaluationSubmitter = PaymentEvaluationSubmissionRep.Submitter(
      businessGuid = Constants.businessGuid,
      paymentGuid = uuidGenerator[0],
    )

    mockSubmitPaymentForEvaluation(paymentEvaluationSubmitter)

    val payment = createInternationalWire(send = true)
    assertThat(payment)
      .isEqualTo(internationalWire(guid = uuidGenerator[0], status = PaymentRep.Status.Open))

    verifySubmitPaymentForEvaluation(paymentEvaluationSubmitter)
  }
}
