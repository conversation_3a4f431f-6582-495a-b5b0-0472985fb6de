package co.highbeam.testing.utils

import java.util.UUID

@Suppress("MayBeConst")
internal object Constants {
  val guid: UUID =
    UUID.fromString("f870a13a-17e6-4940-9c7d-03a208b6eb63")
  val bankAccountGuid: UUID =
    UUID.fromString("50c4af08-1aac-4253-9a52-5215e0c31467")
  val unitCoDepositAccountId: String =
    UUID.randomUUID().toString()
  val businessGuid: UUID =
    UUID.fromString("9c509576-1c40-49fc-a32b-01c60123ba60")
  val createdByUserGuid: UUID =
    UUID.fromString("5cedd39a-74a9-4030-b56a-7b97f659dc45")
  val depositAccountId: String =
    "494633"
  val idempotencyKey: String =
    UUID.fromString("2d5362f5-1b29-4a90-92d6-c081c321866e").toString()
  val payeeGuid: UUID =
    UUID.fromString("f02a8481-fdf7-43de-8978-28d1ae2ef572")
}
