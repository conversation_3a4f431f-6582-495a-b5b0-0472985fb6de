package co.highbeam.endpoint.paymentV2.action

import co.highbeam.exception.paymentV2.PaymentCannotBeRejected
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createInternationalWire
import co.highbeam.testing.utils.internationalWire
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.rejectPayment
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class RejectPaymentInternationalWireTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    assertHighbeamException {
      rejectPayment(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `payment exists`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false)
    val payment = rejectPayment(paymentGuid)
    assertThat(payment)
      .isEqualTo(internationalWire(guid = uuidGenerator[0], status = PaymentRep.Status.Rejected))
  }

  @Test
  fun `payment was already sent`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = true)
    assertHighbeamException {
      rejectPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeRejected())
  }

  @Test
  fun `payment was already rejected`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createInternationalWire(send = false)
    rejectPayment(paymentGuid)
    assertHighbeamException {
      rejectPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeRejected())
  }
}
