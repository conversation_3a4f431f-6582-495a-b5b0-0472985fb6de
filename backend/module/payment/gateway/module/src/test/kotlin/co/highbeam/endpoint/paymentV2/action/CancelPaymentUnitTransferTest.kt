package co.highbeam.endpoint.paymentV2.action

import co.highbeam.exception.paymentV2.PaymentCannotBeCancelled
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.cancelPayment
import co.highbeam.testing.utils.createUnitAchTransfer
import co.highbeam.testing.utils.createUnitBookTransfer
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCancelUnitAchPayment
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.unitAchTransfer
import com.fasterxml.jackson.module.kotlin.convertValue
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class CancelPaymentUnitTransferTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    assertHighbeamException {
      cancelPayment(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `payment exists - cannot cancel book transfers`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val sentPayment = createUnitBookTransfer(send = true)

    assertHighbeamException {
      cancelPayment(sentPayment.guid)
    }.isHighbeamException(PaymentCannotBeCancelled())
  }

  @Test
  fun `payment exists - cancel external ach transfers`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      "sampleUnitResponses/successful-external-ach-transfer.json"
    )
    val sentPayment = createUnitAchTransfer(send = true)

    mockCancelUnitAchPayment()

    val cancelledPayment = cancelPayment(sentPayment.guid)

    assertThat(cancelledPayment)
      .isEqualTo(
        unitAchTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Cancelled,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }

  @Test
  fun `payment cannot be cancelled if not in Sent state`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val sentPayment = createUnitBookTransfer(send = false)

    assertHighbeamException {
      cancelPayment(sentPayment.guid)
    }.isHighbeamException(PaymentCannotBeCancelled())
  }
}
