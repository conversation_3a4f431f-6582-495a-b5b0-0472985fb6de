package co.highbeam.testing.utils

import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.paymentV2.payment.AddressRep
import co.highbeam.rep.paymentV2.payment.ConversionSide
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.util.time.inUTC
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import java.time.ZonedDateTime
import java.util.UUID


internal fun PaymentFeatureIntegrationTest.achPayment(
  guid: UUID,
  status: PaymentRep.Status,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,
    amount = Money.fromDollarsAndCents(123, 45),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "construction",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = null,
    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),
    detail = PaymentDetailRep.Ach(
      amount = Money.fromDollarsAndCents(123, 45),
      accountNumber = "some account number",
      routingNumber = "some routing number",
      payeeName = "Bob the builder",
      description = "construction",
      direction = MoneyDirection.Credit,
      addenda = "fixing leaking air conditioning",
      fromBankAccountGuid = Constants.bankAccountGuid,
      idempotencyKey = Constants.idempotencyKey,
      sameDay = true,
      tags = mapOf(
        "payeeGuid" to Constants.payeeGuid.toString(),
      )
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

internal fun PaymentFeatureIntegrationTest.domesticWirePayment(
  guid: UUID,
  status: PaymentRep.Status,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,
    amount = Money.fromDollarsAndCents(123, 45),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "construction",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = null,
    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),
    detail = PaymentDetailRep.DomesticWire(
      amount = Money.fromDollarsAndCents(123, 45),
      accountNumber = "some account number",
      routingNumber = "some routing number",
      addressRep = AddressRep(
        street = "123 Main St",
        city = "San Francisco",
        state = "CA",
        postalCode = "94105",
        country = "US",
      ),
      payeeName = "Bob the builder",
      description = "construction",
      direction = MoneyDirection.Credit,
      fromBankAccountGuid = Constants.bankAccountGuid,
      idempotencyKey = Constants.idempotencyKey,
      tags = mapOf(
        "payeeGuid" to Constants.payeeGuid.toString(),
      )
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

internal fun PaymentFeatureIntegrationTest.internationalWire(
  guid: UUID,
  status: PaymentRep.Status,
  reason: String? = null,
  fixedSide: ConversionSide = ConversionSide.Send,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,

    amount = Money.fromDollarsAndCents(99, 1),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "International wire to Jeff Hudson",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = reason,

    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),

    detail = PaymentDetailRep.InternationalWire(
      fixedSide = fixedSide,
      payeeGuid = Constants.payeeGuid,
      paymentType = "Regular",
      purposeCode = null,
      invoiceNumber = null,
      invoiceDate = null,
      reason = "Inventory",
      receiveAmount = Money.fromDollarsAndCents(130, 23),
      receiveCurrency = "CAD",
      tags = emptyMap(),
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

internal fun PaymentFeatureIntegrationTest.unitPayment(
  guid: UUID,
  status: PaymentRep.Status,
  reason: String? = null,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,

    amount = Money.fromDollarsAndCents(123, 45),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "Payment to Jeff Hudson via highbeam.co",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = reason,

    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),

    detail = PaymentDetailRep.UnitPayment(
      payeeGuid = Constants.payeeGuid,
      unitPayload = objectMapper.convertValue<JsonNode>(
        mapOf(
          "type" to "achPayment",
          "attributes" to mapOf(
            "amount" to 123_45,
            "direction" to "Credit",
            "counterparty" to mapOf(
              "routingNumber" to "*********",
              "accountNumber" to "*********",
              "accountType" to "Checking",
              "name" to "Jeff Hudson",
            ),
            "description" to "Jeff Hudso",
            "addenda" to "Payment to Jeff Hudson via highbeam.co",
            "idempotencyKey" to Constants.idempotencyKey,
            "tags" to mapOf(
              "paymentGuid" to guid,
              "recipientGuid" to Constants.payeeGuid,
            ),
          ),
          "relationships" to mapOf(
            "account" to mapOf(
              "data" to mapOf(
                "type" to "depositAccount",
                "id" to "494633",
              ),
            ),
          ),
        ),
      ),
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

internal fun PaymentFeatureIntegrationTest.unitBookTransfer(
  guid: UUID,
  status: PaymentRep.Status,
  reason: String? = null,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,

    amount = Money.fromDollarsAndCents(123, 45),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "Internal transfer",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = reason,

    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),

    detail = PaymentDetailRep.UnitTransfer(
      unitPayload = objectMapper.convertValue<JsonNode>(
        mapOf(
          "type" to "bookPayment",
          "attributes" to mapOf(
            "amount" to 12345,
            "idempotencyKey" to Constants.idempotencyKey,
            "direction" to "Credit",
            "description" to "Internal transfer",
            "tags" to mapOf(
              "paymentGuid" to guid,
            ),
          ),
          "relationships" to mapOf(
            "account" to mapOf(
              "data" to mapOf(
                "type" to "depositAccount",
                "id" to Constants.depositAccountId,
              )
            ),
            "counterpartyAccount" to mapOf(
              "data" to mapOf(
                "type" to "depositAccount",
                "id" to "2386656"
              )
            )
          )
        )
      )
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

internal fun PaymentFeatureIntegrationTest.unitAchTransfer(
  guid: UUID,
  status: PaymentRep.Status,
  reason: String? = null,
  paymentSenderResponse: JsonNode = NullNode.instance,
): PaymentRep =
  PaymentRep(
    guid = guid,

    amount = Money.fromDollarsAndCents(123, 45),
    bankAccountGuid = Constants.bankAccountGuid,
    businessGuid = Constants.businessGuid,
    description = "Internal transfer to Highbeam",
    generalPaymentMetadataGuid = null,
    idempotencyKey = Constants.idempotencyKey,
    notificationEmailAddress = null,
    status = status,
    reason = reason,

    createdByUserGuid = null,
    createdAt = ZonedDateTime.now(clock).inUTC(),
    sentByUserGuid = null,
    sentAt = sentAt(status),
    rejectedByUserGuid = null,
    rejectedAt = rejectedAt(status),

    detail = PaymentDetailRep.UnitTransfer(
      unitPayload = objectMapper.convertValue<JsonNode>(
        mapOf(
          "type" to "achPayment",
          "attributes" to mapOf(
            "amount" to 12345,
            "direction" to "Debit",
            "plaidProcessorToken" to "processor-sandbox-a99d34e6-65b7-4865-8dca-fd26b4423513",
            "description" to "Internal transfer to Highbeam",
            "counterpartyName" to "Alberta Bobbeth Charleson",
            "idempotencyKey" to Constants.idempotencyKey,
            "tags" to mapOf(
              "paymentGuid" to guid,
            ),
          ),
          "relationships" to mapOf(
            "account" to mapOf(
              "data" to mapOf(
                "type" to "depositAccount",
                "id" to Constants.depositAccountId,
              )
            )
          )
        )
      )
    ),
    paymentSenderResponse = paymentSenderResponse,
  )

private fun PaymentFeatureIntegrationTest.sentAt(status: PaymentRep.Status): ZonedDateTime? =
  when (status) {
    PaymentRep.Status.Open -> null
    PaymentRep.Status.Pending -> null
    PaymentRep.Status.Sent -> ZonedDateTime.now(clock).inUTC()
    PaymentRep.Status.Failed -> ZonedDateTime.now(clock).inUTC()
    PaymentRep.Status.Rejected -> null
    PaymentRep.Status.Cancelled -> ZonedDateTime.now(clock).inUTC()
  }

private fun PaymentFeatureIntegrationTest.rejectedAt(status: PaymentRep.Status): ZonedDateTime? =
  when (status) {
    PaymentRep.Status.Open -> null
    PaymentRep.Status.Pending -> null
    PaymentRep.Status.Sent -> null
    PaymentRep.Status.Failed -> null
    PaymentRep.Status.Rejected -> ZonedDateTime.now(clock).inUTC()
    PaymentRep.Status.Cancelled -> null
  }
