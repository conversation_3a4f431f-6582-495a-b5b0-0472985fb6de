package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.rep.backendV2.payments.PaymentEvaluationSubmissionRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.Constants
import co.highbeam.testing.utils.createDomesticWirePayment
import co.highbeam.testing.utils.domesticWirePayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockUnitDomesticWirePayment
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreatePaymentDomesticWireTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `happy path, send false`() = integrationTest {
    val payment = createDomesticWirePayment(send = false)
    assertThat(payment)
      .isEqualTo(domesticWirePayment(guid = uuidGenerator[0], status = PaymentRep.Status.Open))
  }

  @Test
  fun `happy path, send true`() = integrationTest {
    mockUnitDomesticWirePayment(uuidGenerator[0])

    val payment = createDomesticWirePayment(send = true)
    assertThat(payment)
      .isEqualTo(
        domesticWirePayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = objectMapper.readValue<JsonNode>(
            Resources.getResource(
              "sampleUnitClientResponse/successful-domestic-wire.json"
            )
          )
        )
      )
  }

  @Test
  fun `payment policies enabled, send true ignored`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.PaymentPolicies] = true
    val paymentEvaluationSubmitter = PaymentEvaluationSubmissionRep.Submitter(
      businessGuid = Constants.businessGuid,
      paymentGuid = uuidGenerator[0],
    )

    mockSubmitPaymentForEvaluation(paymentEvaluationSubmitter)

    val payment = createDomesticWirePayment(send = true)
    assertThat(payment)
      .isEqualTo(domesticWirePayment(guid = uuidGenerator[0], status = PaymentRep.Status.Open))

    verifySubmitPaymentForEvaluation(paymentEvaluationSubmitter)
  }
}
