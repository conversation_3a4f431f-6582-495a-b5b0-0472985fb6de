package co.highbeam.testing.utils

import co.highbeam.api.paymentV2.PaymentActionApi
import co.highbeam.money.Money
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.rep.paymentV2.paymentAction.SendPaymentCreatorRep
import co.highbeam.testing.PaymentFeatureIntegrationTest
import java.util.UUID


internal suspend fun PaymentFeatureIntegrationTest.cancelPayment(
  paymentGuid: UUID,
): PaymentRep {
  return paymentActionClient.request(
    PaymentActionApi.Cancel(
      paymentGuid = paymentGuid,
    ),
  )
}

internal suspend fun PaymentFeatureIntegrationTest.sendInternationalWire(
  paymentGuid: UUID,
  receiveAmount: Money?,
  sendAmount: Money?
): PaymentRep {
  return paymentActionClient.request(
    PaymentActionApi.Send(
      paymentGuid = paymentGuid,
      sender = SendPaymentCreatorRep.InternationalWire(
        receiveAmount = receiveAmount,
        sendAmount = sendAmount,
      ),
    ),
  )
}

internal suspend fun PaymentFeatureIntegrationTest.sendUnitPayment(
  paymentGuid: UUID,
): PaymentRep {
  return paymentActionClient.request(
    PaymentActionApi.Send(
      paymentGuid = paymentGuid,
      sender = SendPaymentCreatorRep.UnitPayment,
    ),
  )
}

internal suspend fun PaymentFeatureIntegrationTest.sendUnitTransfer(
  paymentGuid: UUID,
): PaymentRep {
  return paymentActionClient.request(
    PaymentActionApi.Send(
      paymentGuid = paymentGuid,
      sender = SendPaymentCreatorRep.UnitTransfer
    ),
  )
}

internal suspend fun PaymentFeatureIntegrationTest.rejectPayment(
  paymentGuid: UUID,
): PaymentRep {
  return paymentActionClient.request(PaymentActionApi.Reject(paymentGuid))
}
