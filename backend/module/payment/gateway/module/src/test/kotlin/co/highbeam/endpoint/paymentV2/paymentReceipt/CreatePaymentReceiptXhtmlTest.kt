package co.highbeam.endpoint.paymentV2.paymentReceipt

import co.highbeam.api.paymentV2.PaymentApi
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockGetBusiness
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.unit.client.UnitCoClient
import co.unit.rep.UnitPaymentRep
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreatePaymentReceiptXhtmlTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
    mockGetBusiness()
  }

  @Test
  fun `happy path, generates an ACH receipt`() = integrationTest {
    mockLegacyPaymentEndpoints()
    mockGetUnitCoPayment()
    val payment = createUnitPayment(send = true)

    val expectedReceipt =
      Resources.getResource("paymentReceipts/ach-payment-receipt").readText()
    val xhtmlReceipt = checkNotNull(createReceipt(payment))

    assertThat(xhtmlReceipt).isEqualTo(expectedReceipt)
  }

  private suspend fun createReceipt(payment: PaymentRep) =
    paymentClient.request(
      PaymentApi.CreateReceiptXhtml(
        paymentGuid = payment.guid,
      )
    )

  private fun mockGetUnitCoPayment() {
    coEvery {
      get<UnitCoClient>().payment.getPayment(any())
    } returns objectMapper.readValue<UnitPaymentRep.Complete.OriginatedAch>(
      Resources.getResource("sampleUnitResponses/successful-ach.json")
    )
  }
}
