package co.highbeam.testing.utils

import co.highbeam.api.paymentV2.PaymentApi
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.paymentV2.payment.AddressRep
import co.highbeam.rep.paymentV2.payment.ConversionSide
import co.highbeam.rep.paymentV2.payment.PaymentDetailCreatorRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.testing.PaymentFeatureIntegrationTest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue

internal suspend fun PaymentFeatureIntegrationTest.createAchPayment(
  send: Boolean,
): PaymentRep = paymentClient.request(
  PaymentApi.Create(
    creator = PaymentRep.Creator(
      send = send,
      detail = PaymentDetailCreatorRep.Ach(
        amount = Money.fromDollarsAndCents(123, 45),
        accountNumber = "some account number",
        routingNumber = "some routing number",
        payeeName = "Bob the builder",
        description = "construction",
        direction = MoneyDirection.Credit,
        addenda = "fixing leaking air conditioning",
        fromBankAccountGuid = Constants.bankAccountGuid,
        idempotencyKey = Constants.idempotencyKey,
        sameDay = true,
        tags = mapOf(
          "payeeGuid" to Constants.payeeGuid.toString(),
        )
      )
    )
  )
)

internal suspend fun PaymentFeatureIntegrationTest.createDomesticWirePayment(
  send: Boolean,
): PaymentRep = paymentClient.request(
  PaymentApi.Create(
    creator = PaymentRep.Creator(
      send = send,
      detail = PaymentDetailCreatorRep.DomesticWire(
        amount = Money.fromDollarsAndCents(123, 45),
        accountNumber = "some account number",
        routingNumber = "some routing number",
        address = AddressRep(
          street = "123 Main St",
          city = "San Francisco",
          state = "CA",
          postalCode = "94105",
          country = "US",
        ),
        payeeName = "Bob the builder",
        description = "construction",
        direction = MoneyDirection.Credit,
        fromBankAccountGuid = Constants.bankAccountGuid,
        idempotencyKey = Constants.idempotencyKey,
        tags = mapOf(
          "payeeGuid" to Constants.payeeGuid.toString(),
        )
      )
    )
  )
)

internal suspend fun PaymentFeatureIntegrationTest.createInternationalWire(
  send: Boolean,
  fixedSide: ConversionSide = ConversionSide.Send,
): PaymentRep =
  paymentClient.request(
    PaymentApi.Create(
      creator = PaymentRep.Creator(
        send = send,
        detail = PaymentDetailCreatorRep.InternationalWire(
          bankAccountGuid = Constants.bankAccountGuid,
          description = "International wire to Jeff Hudson",
          fixedSide = fixedSide,
          generalPaymentMetadataGuid = null,
          idempotencyKey = Constants.idempotencyKey,
          notificationEmailAddress = null,
          payeeGuid = Constants.payeeGuid,
          paymentType = "Regular",
          purposeCode = null,
          invoiceNumber = null,
          invoiceDate = null,
          reason = "Inventory",
          receiveAmount = Money.fromDollarsAndCents(130, 23),
          receiveCurrency = "CAD",
          sendAmount = Money.fromDollarsAndCents(99, 1),
          tags = emptyMap(),
        ),
      ),
    ),
  )

internal suspend fun PaymentFeatureIntegrationTest.createUnitPayment(
  send: Boolean,
): PaymentRep =
  paymentClient.request(
    PaymentApi.Create(
      creator = PaymentRep.Creator(
        send = send,
        detail = PaymentDetailCreatorRep.UnitPayment(
          unitPayload = objectMapper.convertValue<JsonNode>(
            mapOf(
              "type" to "achPayment",
              "attributes" to mapOf(
                "amount" to 123_45,
                "direction" to "Credit",
                "counterparty" to mapOf(
                  "routingNumber" to "*********",
                  "accountNumber" to "*********",
                  "accountType" to "Checking",
                  "name" to "Jeff Hudson",
                ),
                "description" to "Jeff Hudso",
                "addenda" to "Payment to Jeff Hudson via highbeam.co",
                "idempotencyKey" to Constants.idempotencyKey,
                "tags" to mapOf(
                  "recipientGuid" to Constants.payeeGuid,
                ),
              ),
              "relationships" to mapOf(
                "account" to mapOf(
                  "data" to mapOf(
                    "type" to "depositAccount",
                    "id" to "494633",
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  )

internal suspend fun PaymentFeatureIntegrationTest.createUnitBookTransfer(
  send: Boolean,
): PaymentRep =
  paymentClient.request(
    PaymentApi.Create(
      creator = PaymentRep.Creator(
        send = send,
        detail = PaymentDetailCreatorRep.UnitTransfer(
          unitPayload = objectMapper.convertValue<JsonNode>(
            mapOf(
              "type" to "bookPayment",
              "attributes" to mapOf(
                "amount" to 12345,
                "idempotencyKey" to Constants.idempotencyKey,
                "direction" to "Credit",
                "description" to "Internal transfer"
              ),
              "relationships" to mapOf(
                "account" to mapOf(
                  "data" to mapOf(
                    "type" to "depositAccount",
                    "id" to Constants.depositAccountId,
                  )
                ),
                "counterpartyAccount" to mapOf(
                  "data" to mapOf(
                    "type" to "depositAccount",
                    "id" to "2386656"
                  )
                )
              )
            )
          )
        ),
      ),
    ),
  )

internal suspend fun PaymentFeatureIntegrationTest.createUnitAchTransfer(

  send: Boolean,
): PaymentRep =
  paymentClient.request(
    PaymentApi.Create(
      creator = PaymentRep.Creator(
        send = send,
        detail = PaymentDetailCreatorRep.UnitTransfer(
          unitPayload = objectMapper.convertValue<JsonNode>(
            mapOf(
              "type" to "achPayment",
              "attributes" to mapOf(
                "amount" to 12345,
                "direction" to "Debit",
                "plaidProcessorToken" to "processor-sandbox-a99d34e6-65b7-4865-8dca-fd26b4423513",
                "description" to "Internal transfer to Highbeam",
                "counterpartyName" to "Alberta Bobbeth Charleson",
                "idempotencyKey" to Constants.idempotencyKey
              ),
              "relationships" to mapOf(
                "account" to mapOf(
                  "data" to mapOf(
                    "type" to "depositAccount",
                    "id" to Constants.depositAccountId,
                  )
                )
              )
            )
          ),
        )
      )
    ),
  )
