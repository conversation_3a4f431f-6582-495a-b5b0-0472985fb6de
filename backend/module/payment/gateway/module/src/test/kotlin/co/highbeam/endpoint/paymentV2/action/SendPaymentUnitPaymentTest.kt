package co.highbeam.endpoint.paymentV2.action

import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.payment.PaymentClient
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamConnectTimeout
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamTimeout
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.sendUnitPayment
import co.highbeam.testing.utils.unitPayment
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class SendPaymentUnitPaymentTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `happy path - payment immediately sent`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints()
    val payment = createUnitPayment(send = true)
    assertThat(payment)
      .isEqualTo(
        unitPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }

  @Test
  fun `payment was already sent`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitPayment(send = true)
    assertHighbeamException {
      sendUnitPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }

  @Test
  fun `immediate payment failed by Unit`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/failed-ach.json"
    )
    val payment = createUnitPayment(send = true)
    assertThat(payment)
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Failed,
        reason = "The account does not have sufficient balance.",
        paymentSenderResponse = objectMapper.convertValue(paymentResponse.unitPaymentResponse),
      ))
  }

  @Test
  fun `immediate payment timed out by HB infra - mark as sent and investigate`() = integrationTest {
    mockCreateUnitPaymentHighbeamTimeout()

    val payment = createUnitPayment(send = true)
    assertThat(payment)
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Sent,
        paymentSenderResponse = NullNode.instance,
      ))
  }

  @Test
  fun `immediate payment connect timed out by HB infra - mark as sent and investigate`() =
    integrationTest {
      mockCreateUnitPaymentHighbeamConnectTimeout()

      val payment = createUnitPayment(send = true)
      assertThat(payment)
        .isEqualTo(unitPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = NullNode.instance,
        ))
    }

  @Test
  fun `immediate payment failed due to exception - mark as failed`() = integrationTest {
    coEvery {
      get<PaymentClient>().request(any<PaymentApi.CreatePayment>())
    } throws Exception("Something went wrong")

    val payment = createUnitPayment(send = true)
    assertThat(payment)
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        reason = "Payment failed. Please contact support for more information.",
        status = PaymentRep.Status.Failed,
        paymentSenderResponse = NullNode.instance,
      ))
  }
}
