package co.highbeam.endpoint.paymentV2.action

import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.payment.PaymentClient
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createUnitAchTransfer
import co.highbeam.testing.utils.createUnitBookTransfer
import co.highbeam.testing.utils.getPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamConnectTimeout
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamTimeout
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.rejectPayment
import co.highbeam.testing.utils.sendUnitTransfer
import co.highbeam.testing.utils.unitAchTransfer
import co.highbeam.testing.utils.unitBookTransfer
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class ApprovePaymentUnitTransferTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment approval does not exist`() = integrationTest {
    assertHighbeamException {
      sendUnitTransfer(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `payment approved`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/successful-book-transfer.json"
    )
    val (paymentGuid) = createUnitBookTransfer(send = false)
    val payment = sendUnitTransfer(paymentGuid)
    assertThat(payment)
      .isEqualTo(
        unitBookTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }

  @Test
  fun `happy path - payment approved - external ach`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/successful-external-ach-transfer.json"
    )
    val (paymentGuid) = createUnitAchTransfer(send = false)
    val payment = sendUnitTransfer(paymentGuid)
    assertThat(payment)
      .isEqualTo(
        unitAchTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }


  @Test
  fun `payment approval was already approved`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitBookTransfer(send = false)
    sendUnitTransfer(paymentGuid)
    assertHighbeamException {
      sendUnitTransfer(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }

  @Test
  fun `payment approval failed by Unit`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/failed-ach.json"
    )
    val payment = createUnitBookTransfer(send = false)
    assertThat(sendUnitTransfer(payment.guid))
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Open,
        reason = "The account does not have sufficient balance.",
        paymentSenderResponse = objectMapper.convertValue(paymentResponse.unitPaymentResponse),
      ))
  }


  @Test
  fun `payment approval times out by HB infra - mark as sent and investigate`() = integrationTest {
    mockCreateUnitPaymentHighbeamTimeout()

    val payment = createUnitBookTransfer(send = false)

    assertThat(sendUnitTransfer(payment.guid))
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Sent,
        paymentSenderResponse = NullNode.instance,
      ))
  }

  @Test
  fun `approving payment faces connect times out by HB infra - mark as sent and investigate`() =
    integrationTest {
      mockCreateUnitPaymentHighbeamConnectTimeout()

      val payment = createUnitBookTransfer(send = false)

      assertThat(sendUnitTransfer(payment.guid))
        .isEqualTo(unitBookTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = NullNode.instance,
        ))
    }

  @Test
  fun `approving payment fails due to an exception - mark as failed`() = integrationTest {
    coEvery {
      get<PaymentClient>().request(any<PaymentApi.CreatePayment>())
    } throws Exception("Something went wrong")

    val (paymentGuid) = createUnitBookTransfer(send = false)
    assertThatThrownBy {
      runBlocking { sendUnitTransfer(paymentGuid) }
    }.isInstanceOf(Exception::class.java)
    assertThat(getPayment(paymentGuid))
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Open,
        reason = "Payment failed. Please contact support for more information.",
        paymentSenderResponse = NullNode.instance,
      ))
  }


  @Test
  fun `payment approval was already rejected`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitBookTransfer(send = false)
    rejectPayment(paymentGuid)
    assertHighbeamException {
      sendUnitTransfer(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }
}
