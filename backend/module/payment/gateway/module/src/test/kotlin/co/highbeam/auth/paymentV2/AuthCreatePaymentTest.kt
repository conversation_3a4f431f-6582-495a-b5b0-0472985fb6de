package co.highbeam.auth.paymentV2

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.money.Money
import co.highbeam.rep.paymentV2.payment.PaymentDetailRep
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.testing.utils.Constants
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Duration
import java.time.ZonedDateTime
import java.util.UUID

internal class AuthCreatePaymentTest {
  private class TestResult {
    val mfaTimeLimit = slot<Duration>()
    val permissionPermission = slot<Permission>()
    val permissionBusinessGuid = slot<suspend () -> UUID?>()

    suspend fun assert(
      mfaTimeLimit: Duration,
      permissionPermission: Permission,
      permissionBusinessGuid: UUID,
    ) {
      assertThat(this.mfaTimeLimit.captured).isEqualTo(mfaTimeLimit)
      assertThat(this.permissionPermission.captured).isEqualTo(permissionPermission)
      assertThat(this.permissionBusinessGuid.captured()).isEqualTo(permissionBusinessGuid)
    }
  }

  private val objectMapper: ObjectMapper =
    HighbeamObjectMapper.json {
      prettyPrint(true)
      useTypeConverters(DEFAULT_TYPE_CONVERTERS)
    }.build()

  private val authMfa: AuthMfa.Provider = mockk()
  private val authPermission: AuthPermission.Provider = mockk()

  private val featureFlagService = FakeFeatureFlagService()

  private val bankAccountClient: BankAccountClient = mockk()

  private val authCreatePayment: AuthCreatePayment.Provider =
    AuthCreatePayment.Provider(
      authMfa = authMfa,
      authPermission = authPermission,
      featureFlagService = featureFlagService,
    )

  @Test
  fun `small achPayment, send false`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small achPayment, send true`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small achPayment, send true (with payment policies enabled)`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Pending,
        isPaymentPoliciesEnabled = true,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large achPayment, send false`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofSeconds(150),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large achPayment, send true`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofSeconds(150),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large achPayment, send true (with payment policies enabled)`() =
    runBlocking {
      val result = test(
        type = "achPayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Pending,
        isPaymentPoliciesEnabled = true,
      )

      result.assert(
        mfaTimeLimit = Duration.ofSeconds(150),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small wirePayment, send false`() =
    runBlocking {
      val result = test(
        type = "wirePayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small wirePayment, send true`() =
    runBlocking {
      val result = test(
        type = "wirePayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large wirePayment, send false`() =
    runBlocking {
      val result = test(
        type = "wirePayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofSeconds(150),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large wirePayment, send true`() =
    runBlocking {
      val result = test(
        type = "wirePayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofSeconds(150),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small bookPayment, send false`() =
    runBlocking {
      val result = test(
        type = "bookPayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `small bookPayment, send true`() =
    runBlocking {
      val result = test(
        type = "bookPayment",
        amount = Money.fromDollarsAndCents(123, 45),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large bookPayment, send false`() =
    runBlocking {
      val result = test(
        type = "bookPayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Open,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.PaymentApproval_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `large bookPayment, send true`() =
    runBlocking {
      val result = test(
        type = "bookPayment",
        amount = Money.fromDollarsAndCents(1_234_567, 89),
        status = PaymentRep.Status.Pending,
      )

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.Payment_Create,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  private suspend fun test(
    type: String,
    amount: Money,
    status: PaymentRep.Status,
    isPaymentPoliciesEnabled: Boolean = false,
  ): TestResult {
    val result = TestResult()

    every {
      authMfa(
        timeLimit = capture(result.mfaTimeLimit),
      )
    } returns mockk {
      coEvery { authorize(any(), any()) } returns true
    }

    every {
      authPermission(
        permission = capture(result.permissionPermission),
        businessGuid = capture(result.permissionBusinessGuid),
      )
    } returns mockk {
      coEvery { authorize(any(), any()) } returns true
    }

    coEvery {
      bankAccountClient.request(BankAccountApi.GetByUnitCoDepositAccountId("494633"))
    } returns mockk {
      every { businessGuid } returns Constants.businessGuid
    }

    if (isPaymentPoliciesEnabled) {
      featureFlagService[BusinessFlag.PaymentPolicies] = true
    }

    authCreatePayment(
      creator = PaymentRep(
        guid = Constants.guid,

        amount = amount,
        bankAccountGuid = Constants.bankAccountGuid,
        businessGuid = Constants.businessGuid,
        description = "Payment to JEff Hudson via highbeam.co",
        generalPaymentMetadataGuid = null,
        idempotencyKey = Constants.idempotencyKey,
        notificationEmailAddress = null,
        status = status,
        reason = null,

        createdByUserGuid = Constants.createdByUserGuid,
        createdAt = ZonedDateTime.now(),
        sentByUserGuid = null,
        sentAt = null,
        rejectedByUserGuid = null,
        rejectedAt = null,

        detail = PaymentDetailRep.UnitPayment(
          unitPayload = objectMapper.convertValue<JsonNode>(
            mapOf("type" to type, "attributes" to mapOf("amount" to amount)),
          ),
          payeeGuid = Constants.payeeGuid,
        ),
        paymentSenderResponse = NullNode.instance,
      ),
    )

    featureFlagService.reset()

    return result
  }
}
