package co.highbeam.testing.utils

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.payment.PaymentApi
import co.highbeam.api.payment.PaymentApi.CreatePayment
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.client.payment.PaymentClient
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.payment.InternationalPaymentRep
import co.highbeam.rep.payment.PaymentRep
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.AddressRep
import co.unit.rep.UnitCoAchPaymentRep
import co.unit.rep.UnitCoWirePaymentRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import java.time.ZonedDateTime
import java.util.UUID

internal fun PaymentFeatureIntegrationTest.mockBankAccount() {
  val bankAccount = mockk<BankAccountRep.Complete> {
    every { guid } returns Constants.bankAccountGuid
    every { businessGuid } returns Constants.businessGuid
    every { unitCoDepositAccountId } returns Constants.unitCoDepositAccountId
  }
  coEvery {
    get<BankAccountClient>().request(
      BankAccountApi.Get(Constants.bankAccountGuid),
    )
  } returns bankAccount
  coEvery {
    get<BankAccountClient>().request(
      BankAccountApi.GetByUnitCoDepositAccountId(Constants.depositAccountId),
    )
  } returns bankAccount
}

internal fun PaymentFeatureIntegrationTest.mockGetBusiness() {
  coEvery {
    get<BusinessClient>().request(BusinessApi.Get(Constants.businessGuid))
  } returns BusinessRep.Complete(
    guid = Constants.businessGuid,
    dba = "Business name dba",
    name = "Business name",
    referralLinkGuid = null,
    ownerUserGuid = UUID.randomUUID(),
    status = BusinessRep.Complete.Status.Active,
    unitCoCustomerId = UUID.randomUUID().toString(),
    stateOfIncorporation = null,
    naics = null,
  )
}

internal fun PaymentFeatureIntegrationTest.mockCancelUnitAchPayment() {
  coEvery {
    get<UnitCoClient>().payment.cancelAch(any())
  } returns mockk()
}

internal fun PaymentFeatureIntegrationTest.mockCancelDomesticWirePayment() {
  coEvery {
    get<UnitCoClient>().payment.cancelWire(any())
  } returns mockk()
}

internal fun PaymentFeatureIntegrationTest.mockCreateUnitPaymentHighbeamTimeout() {
  coEvery {
    get<PaymentClient>().request(any<CreatePayment>())
  } throws mockk<HttpRequestTimeoutException>()
}

internal fun PaymentFeatureIntegrationTest.mockCreateUnitPaymentHighbeamConnectTimeout() {
  coEvery {
    get<PaymentClient>().request(any<CreatePayment>())
  } throws ConnectTimeoutException("Timeout")
}

internal fun PaymentFeatureIntegrationTest.mockCreateUnitPaymentUnitTimeout() {
  coEvery {
    get<PaymentClient>().request(any<CreatePayment>())
  } throws HighbeamHttpClientException(
    HttpStatusCode.RequestTimeout,
    "Timeout",
  )
}

internal fun PaymentFeatureIntegrationTest.mockUnitAchPayment(
  paymentGuid: UUID,
): UnitCoAchPaymentRep.Complete {
  val response = UnitCoAchPaymentRep.Complete(
    id = "unit-ach-payment-id",
    amount = Money.fromDollarsAndCents(123, 45),
    direction = MoneyDirection.Credit,
    counterparty = UnitCoAchPaymentRep.InlineCounterparty(
      accountNumber = "some account number",
      routingNumber = "some routing number",
      accountType = UnitCoAchPaymentRep.AccountType.Checking,
      name = "Bob the builder",
    ),
    reason = null,
    status = UnitCoAchPaymentRep.Status.Sent,
    createdAt = ZonedDateTime.now(clock),
    description = "construction",
    addenda = "fixing leaking air conditioning",
    payeeGuid = Constants.payeeGuid,
  )
  coEvery {
    get<UnitCoClient>().payment.createAch(
      UnitCoAchPaymentRep.Creator(
        amount = Money.fromDollarsAndCents(123, 45),
        direction = MoneyDirection.Credit,
        counterparty = UnitCoAchPaymentRep.InlineCounterparty(
          accountNumber = "some account number",
          routingNumber = "some routing number",
          accountType = UnitCoAchPaymentRep.AccountType.Checking,
          name = "Bob the builder",
        ),
        description = "construction",
        addenda = "fixing leaking air conditioning",
        idempotencyKey = UUID.fromString(Constants.idempotencyKey),
        payeeGuid = Constants.payeeGuid,
        fromAccountType = "depositAccount",
        fromAccountId = Constants.unitCoDepositAccountId,
        sameDay = true,
        tags = mapOf(
          "payeeGuid" to Constants.payeeGuid.toString(),
          "paymentGuid" to paymentGuid,
        ),
      )
    )
  } returns response

  return response
}

internal fun PaymentFeatureIntegrationTest.mockUnitDomesticWirePayment(
  paymentGuid: UUID,
):
  UnitCoWirePaymentRep.Complete {
  val response = UnitCoWirePaymentRep.Complete(
    id = "unit-ach-payment-id",
    amount = Money.fromDollarsAndCents(123, 45),
    inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
      accountNumber = "some account number",
      routingNumber = "some routing number",
      name = "Bob the builder",
      address = AddressRep(
        street = "123 Main St",
        city = "San Francisco",
        state = "CA",
        postalCode = "94105",
        country = "US",
      ),
    ),
    reason = null,
    status = UnitCoWirePaymentRep.Status.Sent,
    createdAt = ZonedDateTime.now(clock),
    description = "construction",
    payeeGuid = Constants.payeeGuid,
  )
  coEvery {
    get<UnitCoClient>().payment.createWire(
      UnitCoWirePaymentRep.Creator(
        amount = Money.fromDollarsAndCents(123, 45),
        inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
          accountNumber = "some account number",
          routingNumber = "some routing number",
          name = "Bob the builder",
          address = AddressRep(
            street = "123 Main St",
            city = "San Francisco",
            state = "CA",
            postalCode = "94105",
            country = "US",
          ),
        ),
        description = "construction",
        idempotencyKey = UUID.fromString(Constants.idempotencyKey),
        payeeGuid = Constants.payeeGuid,
        fromAccountType = "depositAccount",
        fromAccountId = Constants.unitCoDepositAccountId,
        tags = mapOf(
          "payeeGuid" to Constants.payeeGuid.toString(),
          "paymentGuid" to paymentGuid,
        ),
      )
    )
  } returns response

  return response
}


internal fun PaymentFeatureIntegrationTest.mockLegacyPaymentEndpoints(
  unitJsonResponseFixturePath: String = "sampleUnitResponses/successful-ach.json",
): PaymentResponse {
  val unitResponse = objectMapper.readValue<JsonNode>(
    Resources.getResource(unitJsonResponseFixturePath)
  )

  val internationalPaymentResponse = InternationalPaymentRep.Complete(
    amount = Money.ZERO,
    createdAt = ZonedDateTime.now(clock),
    currency = "USD",
    externalPaymentId = UUID.fromString("812cea6a-e232-4592-bf39-aa53e598d300"),
    paymentFee = Money.ZERO,
    paymentFeeCurrency = "USD",
    paymentMetadataGuid = UUID.fromString("90f7e3d8-dfd9-4055-8b4c-77ab1dbb9eba"),
    paymentType = InternationalPaymentRep.PaymentType.Priority,
    reason = "Inventory",
    status = "Sent",
  )

  coEvery {
    get<PaymentClient>().request(any<PaymentApi.CreateInternationalWire>())
  } returns internationalPaymentResponse
  coEvery {
    get<PaymentClient>().request(any<CreatePayment>())
  } returns PaymentRep(unitResponse = unitResponse)

  return PaymentResponse(
    internationalPaymentResponse = internationalPaymentResponse,
    unitPaymentResponse = unitResponse,
  )
}


