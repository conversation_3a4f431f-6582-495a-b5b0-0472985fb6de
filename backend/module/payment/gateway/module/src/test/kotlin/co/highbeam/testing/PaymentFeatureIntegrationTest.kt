package co.highbeam.testing

import co.highbeam.api.backendV2.payments.PaymentEvaluationApi
import co.highbeam.client.backendV2.payments.PaymentEvaluationClient
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.payee.PayeeClient
import co.highbeam.client.paymentV2.PaymentActionClient
import co.highbeam.client.paymentV2.PaymentClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.PaymentFeatureTestConfig
import co.highbeam.feature.email.EmailFeature
import co.highbeam.feature.paymentV2.PaymentFeature
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.rep.backendV2.payments.PaymentEvaluationSubmissionRep
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import co.unit.client.UnitCoClient
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext
import co.highbeam.client.payment.PaymentClient as LegacyPaymentClient

@ExtendWith(PaymentFeatureIntegrationTest.Extension::class)
internal abstract class PaymentFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private class MockFeature(
    val fakeFeatureFlagService: FakeFeatureFlagService,
  ) : AbstractMockFeature() {
    override fun bind() {
      mock(BankAccountClient::class)
      mock(BusinessClient::class)
      mock(BusinessMemberClient::class)
      mock(FeatureFlagService::class, fakeFeatureFlagService)
      mock(LegacyPaymentClient::class)
      mock(PayeeClient::class)
      mock(PaymentEvaluationClient::class)
      mock(UnitCoClient::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: PaymentFeatureTestConfig = ConfigLoader.load("test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "payment_gateway",
      )
      val fakeFeatureFlagService = FakeFeatureFlagService()
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<PaymentFeatureTestConfig>(config) {
          override val features = setOf(
            EmailFeature(config.email),
            TestRestFeature(),
            sqlFeature,
            MockFeature(fakeFeatureFlagService),
            PaymentFeature(),
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
      fakeFeatureFlagService.reset()
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val paymentClient: PaymentClient by lazy {
    PaymentClient(httpClient)
  }

  val paymentActionClient: PaymentActionClient by lazy {
    PaymentActionClient(httpClient)
  }

  val fakeFeatureFlagService: FakeFeatureFlagService by lazy {
    get<FeatureFlagService>() as FakeFeatureFlagService
  }

  val paymentEvaluationClient: PaymentEvaluationClient by lazy {
    get<PaymentEvaluationClient>()
  }

  fun mockSubmitPaymentForEvaluation(submitter: PaymentEvaluationSubmissionRep.Submitter) {
    coEvery {
      paymentEvaluationClient.request(
        PaymentEvaluationApi.SubmitPaymentForEvaluation(submitter)
      )
    } returns mockk()
  }

  fun verifySubmitPaymentForEvaluation(submitter: PaymentEvaluationSubmissionRep.Submitter) {
    coVerify(exactly = 1) {
      paymentEvaluationClient.request(
        PaymentEvaluationApi.SubmitPaymentForEvaluation(submitter)
      )
    }
  }
}
