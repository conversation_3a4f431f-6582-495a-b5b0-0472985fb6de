package co.highbeam.endpoint.paymentV2.action

import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.payment.PaymentClient
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.getPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamConnectTimeout
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamTimeout
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.rejectPayment
import co.highbeam.testing.utils.sendUnitPayment
import co.highbeam.testing.utils.unitPayment
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class ApprovePaymentUnitPaymentTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment approval does not exist`() = integrationTest {
    assertHighbeamException {
      sendUnitPayment(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `happy path - payment approved`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitPayment(send = false)
    val payment = sendUnitPayment(paymentGuid)
    assertThat(payment)
      .isEqualTo(
        unitPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }

  @Test
  fun `payment approval failed due to failure by Unit`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/failed-ach.json"
    )
    val (paymentGuid) = createUnitPayment(send = false)
    assertThat(sendUnitPayment(paymentGuid))
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Open,
        reason = "The account does not have sufficient balance.",
        paymentSenderResponse = objectMapper.convertValue(paymentResponse.unitPaymentResponse),
      ))
  }

  @Test
  fun `payment timed out by HB infra - mark as sent and investigate`() = integrationTest {
    mockCreateUnitPaymentHighbeamTimeout()

    val (paymentGuid) = createUnitPayment(send = false)
    assertThat(sendUnitPayment(paymentGuid))
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Sent,
        paymentSenderResponse = NullNode.instance,
      ))
  }

  @Test
  fun `payment connect timed out by HB infra - mark as sent and investigate`() =
    integrationTest {
      mockCreateUnitPaymentHighbeamConnectTimeout()

      val (paymentGuid) = createUnitPayment(send = false)
      assertThat(sendUnitPayment(paymentGuid))
        .isEqualTo(unitPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = NullNode.instance,
        ))
    }

  @Test
  fun `payment approval failed due to exception - mark as Open and throw`() = integrationTest {
    coEvery {
      get<PaymentClient>().request(any<PaymentApi.CreatePayment>())
    } throws Exception("Something went wrong")

    val (paymentGuid) = createUnitPayment(send = false)
    assertThatThrownBy {
      runBlocking { sendUnitPayment(paymentGuid) }
    }.isInstanceOf(Exception::class.java)
    assertThat(getPayment(paymentGuid))
      .isEqualTo(unitPayment(
        guid = uuidGenerator[0],
        reason = "Payment failed. Please contact support for more information.",
        status = PaymentRep.Status.Open,
        paymentSenderResponse = NullNode.instance,
      ))
  }

  @Test
  fun `payment was already approved`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitPayment(send = false)
    sendUnitPayment(paymentGuid)

    assertHighbeamException {
      sendUnitPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }

  @Test
  fun `payment approval was already rejected`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitPayment(send = false)
    rejectPayment(paymentGuid)
    assertHighbeamException {
      sendUnitPayment(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }
}
