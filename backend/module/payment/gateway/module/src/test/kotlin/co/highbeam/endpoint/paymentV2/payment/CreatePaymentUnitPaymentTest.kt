package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.unitPayment
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class CreatePaymentUnitPaymentTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `happy path, send false`() = integrationTest {
    val payment = createUnitPayment(send = false)
    assertThat(payment)
      .isEqualTo(unitPayment(guid = uuidGenerator[0], status = PaymentRep.Status.Open))
  }

  @Test
  fun `happy path, send true`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints()
    val payment = createUnitPayment(send = true)
    assertThat(payment)
      .isEqualTo(
        unitPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = paymentResponse.unitPaymentResponse,
        )
      )
  }
}
