package co.highbeam.endpoint.paymentV2.action

import co.highbeam.exception.paymentV2.PaymentCannotBeCancelled
import co.highbeam.exception.paymentV2.PaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.achPayment
import co.highbeam.testing.utils.cancelPayment
import co.highbeam.testing.utils.createAchPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCancelUnitAchPayment
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.mockUnitAchPayment
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class CancelAchPaymentTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    assertHighbeamException {
      cancelPayment(UUID.randomUUID())
    }.isHighbeamException(unprocessable(PaymentNotFound()))
  }

  @Test
  fun `payment exists`() = integrationTest {
    mockUnitAchPayment(uuidGenerator[0])
    val sentPayment = createAchPayment(send = true)

    mockCancelUnitAchPayment()

    val cancelledPayment = cancelPayment(sentPayment.guid)

    assertThat(cancelledPayment)
      .isEqualTo(
        achPayment(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Cancelled,
          paymentSenderResponse =
          objectMapper.readValue<JsonNode>(
            Resources.getResource("sampleUnitClientResponse/successful-ach.json")
          )
        )
      )
  }

  @Test
  fun `payment cannot be cancelled if not in Sent state`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val sentPayment = createAchPayment(send = false)

    mockCancelUnitAchPayment()

    assertHighbeamException {
      cancelPayment(sentPayment.guid)
    }.isHighbeamException(PaymentCannotBeCancelled())
  }
}
