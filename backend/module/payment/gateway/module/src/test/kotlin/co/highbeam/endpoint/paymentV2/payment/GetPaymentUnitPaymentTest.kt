package co.highbeam.endpoint.paymentV2.payment

import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.utils.createUnitPayment
import co.highbeam.testing.utils.getPayment
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.unitPayment
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetPaymentUnitPaymentTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `payment does not exist`() = integrationTest {
    val payment = getPayment(UUID.randomUUID())
    assertThat(payment)
      .isNull()
  }

  @Test
  fun `payment exists`() = integrationTest {
    val (paymentGuid) = createUnitPayment(send = false)
    val payment = getPayment(paymentGuid)
    assertThat(payment)
      .isEqualTo(unitPayment(guid = uuidGenerator[0], status = PaymentRep.Status.Open))
  }
}
