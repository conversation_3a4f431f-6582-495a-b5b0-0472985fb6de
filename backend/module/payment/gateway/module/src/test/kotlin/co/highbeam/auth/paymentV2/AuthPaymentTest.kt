package co.highbeam.auth.paymentV2

import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.service.paymentV2.payment.PaymentService
import co.highbeam.testing.utils.Constants
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.Duration
import java.util.UUID

internal class AuthPaymentTest {
  private class TestResult {
    val mfaTimeLimit = slot<Duration>()
    val permissionPermission = slot<Permission>()
    val permissionBusinessGuid = slot<suspend () -> UUID?>()
    val platformRolePlatformRole = slot<PlatformRole>()

    suspend fun assert(
      mfaTimeLimit: Duration?,
      permissionPermission: Permission,
      permissionBusinessGuid: UUID,
      platformRolePlatformRole: PlatformRole? = null,
    ) {
      if (mfaTimeLimit == null) assertThat(this.mfaTimeLimit.isCaptured).isFalse()
      else assertThat(this.mfaTimeLimit.captured).isEqualTo(mfaTimeLimit)
      assertThat(this.permissionPermission.captured).isEqualTo(permissionPermission)
      assertThat(this.permissionBusinessGuid.captured()).isEqualTo(permissionBusinessGuid)

      if (platformRolePlatformRole == null) {
        assertThat(this.platformRolePlatformRole.isCaptured).isFalse()
      } else {
        assertThat(this.platformRolePlatformRole.captured).isEqualTo(platformRolePlatformRole)
      }
    }
  }

  private val authMfa: AuthMfa.Provider = mockk()
  private val authPermission: AuthPermission.Provider = mockk()
  private val authPlatformRole: AuthPlatformRole.Provider = mockk()

  private val featureFlagService = FakeFeatureFlagService()
  private val paymentService: PaymentService = mockk()

  private val authPayment: AuthPayment.Provider =
    AuthPayment.Provider(
      authMfa = authMfa,
      authPermission = authPermission,
      authPlatformRole = authPlatformRole,
      featureFlagService = featureFlagService,
      paymentService = paymentService,
    )

  @Test
  fun `happy path (mfa=false)`() =
    runBlocking {
      val result = test(permission = Permission.Payment_Read, mfa = false)

      result.assert(
        mfaTimeLimit = null,
        permissionPermission = Permission.Payment_Read,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `happy path (mfa=true)`() =
    runBlocking {
      val result = test(permission = Permission.Payment_Read, mfa = true)

      result.assert(
        mfaTimeLimit = Duration.ofHours(24),
        permissionPermission = Permission.Payment_Read,
        permissionBusinessGuid = Constants.businessGuid,
      )
    }

  @Test
  fun `happy path (internalForPaymentEvaluation=true) with payment policies enabled`() =
    runBlocking {
      val result = test(
        permission = Permission.Payment_Read,
        mfa = false,
        isPaymentPoliciesEnabled = true,
        internalForPaymentEvaluation = true,
      )

      result.assert(
        mfaTimeLimit = null,
        permissionPermission = Permission.Payment_Read,
        permissionBusinessGuid = Constants.businessGuid,
        platformRolePlatformRole = PlatformRole.HIGHBEAM_SERVER,
      )
    }

  @Test
  fun `happy path (internalForPaymentEvaluation=true) with payment policies disabled`() =
    runBlocking {
      val result = test(
        permission = Permission.Payment_Read,
        mfa = false,
        isPaymentPoliciesEnabled = false,
        internalForPaymentEvaluation = true,
      )

      result.assert(
        mfaTimeLimit = null,
        permissionPermission = Permission.Payment_Read,
        permissionBusinessGuid = Constants.businessGuid,
        platformRolePlatformRole = null,
      )
    }

  private suspend fun test(
    permission: Permission,
    mfa: Boolean,
    isPaymentPoliciesEnabled: Boolean = false,
    internalForPaymentEvaluation: Boolean = false,
  ): TestResult {
    val result = TestResult()

    every {
      authMfa(
        timeLimit = capture(result.mfaTimeLimit),
      )
    } returns mockk {
      coEvery { authorize(any(), any()) } returns true
    }

    every {
      authPermission(
        permission = capture(result.permissionPermission),
        businessGuid = capture(result.permissionBusinessGuid),
      )
    } returns mockk {
      coEvery { authorize(any(), any()) } returns true
    }

    every {
      authPlatformRole(capture(result.platformRolePlatformRole))
    } returns mockk {
      coEvery { authorize(any(), any()) } returns true
    }

    every { paymentService.get(Constants.guid) } returns mockk {
      every { businessGuid } returns Constants.businessGuid
    }

    if (isPaymentPoliciesEnabled) {
      featureFlagService[BusinessFlag.PaymentPolicies] = true
    }

    authPayment(
      paymentGuid = Constants.guid,
      mfa = mfa,
      internalForPaymentEvaluation = internalForPaymentEvaluation,
    ) { permission }

    featureFlagService.reset()

    return result
  }
}
