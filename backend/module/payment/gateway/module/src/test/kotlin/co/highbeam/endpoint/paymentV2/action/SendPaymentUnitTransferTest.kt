package co.highbeam.endpoint.paymentV2.action

import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.payment.PaymentClient
import co.highbeam.exception.paymentV2.PaymentCannotBeSent
import co.highbeam.rep.paymentV2.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.PaymentFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.utils.createUnitAchTransfer
import co.highbeam.testing.utils.createUnitBookTransfer
import co.highbeam.testing.utils.mockBankAccount
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamConnectTimeout
import co.highbeam.testing.utils.mockCreateUnitPaymentHighbeamTimeout
import co.highbeam.testing.utils.mockLegacyPaymentEndpoints
import co.highbeam.testing.utils.sendUnitTransfer
import co.highbeam.testing.utils.unitAchTransfer
import co.highbeam.testing.utils.unitBookTransfer
import com.fasterxml.jackson.databind.node.NullNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class SendPaymentUnitTransferTest(
  server: Server<*>,
) : PaymentFeatureIntegrationTest(server) {
  @BeforeEach
  fun beforeEach() {
    mockBankAccount()
  }

  @Test
  fun `happy path - immediate payment - book transfer`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/successful-book-transfer.json"
    )
    val payment = createUnitBookTransfer(send = true)
    assertThat(payment)
      .isEqualTo(
        unitBookTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }

  @Test
  fun `payment approval exists - external ach`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/successful-external-ach-transfer.json"
    )
    val payment = createUnitAchTransfer(send = true)
    assertThat(payment)
      .isEqualTo(
        unitAchTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse =
          objectMapper.convertValue(
            paymentResponse.unitPaymentResponse,
          )
        )
      )
  }


  @Test
  fun `payment was already sent`() = integrationTest {
    mockLegacyPaymentEndpoints()
    val (paymentGuid) = createUnitBookTransfer(send = true)
    assertHighbeamException {
      sendUnitTransfer(paymentGuid)
    }.isHighbeamException(PaymentCannotBeSent())
  }

  @Test
  fun `immediate payment failed by Unit`() = integrationTest {
    val paymentResponse = mockLegacyPaymentEndpoints(
      unitJsonResponseFixturePath = "sampleUnitResponses/failed-ach.json"
    )
    val payment = createUnitBookTransfer(send = true)
    assertThat(payment)
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Failed,
        reason = "The account does not have sufficient balance.",
        paymentSenderResponse = objectMapper.convertValue(paymentResponse.unitPaymentResponse),
      ))
  }


  @Test
  fun `immediate payment times out by HB infra - mark as sent and investigate`() = integrationTest {
    mockCreateUnitPaymentHighbeamTimeout()

    val payment = createUnitBookTransfer(send = true)
    assertThat(payment)
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Sent,
        paymentSenderResponse = NullNode.instance,
      ))
  }

  @Test
  fun `immediate payment connect times out by HB infra - mark as sent and investigate`() =
    integrationTest {
      mockCreateUnitPaymentHighbeamConnectTimeout()

      val payment = createUnitBookTransfer(send = true)
      assertThat(payment)
        .isEqualTo(unitBookTransfer(
          guid = uuidGenerator[0],
          status = PaymentRep.Status.Sent,
          paymentSenderResponse = NullNode.instance,
        ))
    }

  @Test
  fun `immediate payment fails due to an exception - mark as failed`() = integrationTest {
    coEvery {
      get<PaymentClient>().request(any<PaymentApi.CreatePayment>())
    } throws Exception("Something went wrong")

    val payment = createUnitBookTransfer(send = true)
    assertThat(payment)
      .isEqualTo(unitBookTransfer(
        guid = uuidGenerator[0],
        status = PaymentRep.Status.Failed,
        reason = "Payment failed. Please contact support for more information.",
        paymentSenderResponse = NullNode.instance,
      ))
  }
}
