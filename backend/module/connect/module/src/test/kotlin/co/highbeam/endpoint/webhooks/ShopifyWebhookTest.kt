package co.highbeam.endpoint.webhooks

import co.highbeam.api.shopify.ShopifyWebhookApi
import co.highbeam.endpoint.event.connect.ShopifyWebhookTopic
import co.highbeam.endpoint.event.connect.ShopifyWebhookTopic.CUSTOMERS_DATA_REQUEST
import co.highbeam.endpoint.event.connect.ShopifyWebhookTopic.CUSTOMERS_REDACT
import co.highbeam.endpoint.event.connect.ShopifyWebhookTopic.SHOP_REDACT
import co.highbeam.model.shopify.ShopifyWebhookModel
import co.highbeam.server.Server
import co.highbeam.store.shopify.ShopifyWebhookStore
import co.highbeam.testing.ConnectFeatureIntegrationTest
import co.highbeam.testing.createConnection
import co.highbeam.testing.createRedirectUrl
import co.highbeam.testing.mockShopifyCreateAccessToken
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class ShopifyWebhookTest(
  server: Server<*>,
) : ConnectFeatureIntegrationTest(server) {
  private val shopSubdomain = "magical-store-of-justins-musings"

  @BeforeEach
  fun beforeEach() {
    // Create a shopify store
    val businessGuid = UUID.randomUUID()
    runBlocking {
      createRedirectUrl(businessGuid)
    }
    mockShopifyCreateAccessToken()
    runBlocking {
      createConnection(
        businessGuid = businessGuid,
        state = uuidGenerator[0].toString(),
        shopHost = "$shopSubdomain.myshopify.com"
      )
    }
  }

  @Test
  fun `customers data request`() = integrationTest {
    // As defined in:
    // https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#customers-data_request
    val dataRequestPayload: JsonNode = objectMapper.convertValue(
      mapOf(
        "shop_id" to 954889,
        "shop_domain" to "$shopSubdomain.myshopify.com",
        "orders_requested" to listOf(299938, 280263, 220458),
        "customer" to mapOf(
          "id" to 191167,
          "email" to "<EMAIL>",
          "phone" to "************",
        ),
        "data_request" to mapOf(
          "id" to 9999,
        ),
      )
    )

    // Hit the /shopify-webhooks endpoint with CUSTOMERS_REDACT
    makeShopifyWebhookRequest(payload = dataRequestPayload, topic = CUSTOMERS_DATA_REQUEST)

    assertShopifyWebhookEvent(expectedShopifyWebhookTopic = CUSTOMERS_DATA_REQUEST)
  }

  @Test
  fun `customers redact request`() = integrationTest {
    // As defined in:
    // https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#customers-redact-payload
    val dataRequestPayload: JsonNode = objectMapper.convertValue(
      mapOf(
        "shop_id" to 954889,
        "shop_domain" to "$shopSubdomain.myshopify.com",
        "customer" to mapOf(
          "id" to 191167,
          "email" to "<EMAIL>",
          "phone" to "************",
        ),
        "orders_to_redact" to listOf(299938, 280263, 220458)
      )
    )

    // Hit the /shopify-webhooks endpoint with CUSTOMERS_REDACT
    makeShopifyWebhookRequest(payload = dataRequestPayload, topic = CUSTOMERS_REDACT)

    // Assert recorded event in DB
    assertShopifyWebhookEvent(expectedShopifyWebhookTopic = CUSTOMERS_REDACT)
  }

  @Test
  fun `shop redact request`() = integrationTest {
    // As defined in:
    // https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks#shop-redact-payload
    val dataRequestPayload: JsonNode = objectMapper.convertValue(
      mapOf(
        "shop_id" to 954889,
        "shop_domain" to "$shopSubdomain.myshopify.com",
      )
    )

    // Hit the /shopify-webhooks endpoint with SHOP_REDACT
    makeShopifyWebhookRequest(payload = dataRequestPayload, topic = SHOP_REDACT)

    // Assert recorded event in DB
    assertShopifyWebhookEvent(expectedShopifyWebhookTopic = SHOP_REDACT)
  }

  @Test
  fun `multiple connections for shop domain doesn't break webhook storage`() = integrationTest {
    // setup method first creates a connection on [businessGuid]; now we create another:
    val otherBusinessGuid = UUID.randomUUID()
    createRedirectUrl(otherBusinessGuid)
    mockShopifyCreateAccessToken()
    createConnection(
      businessGuid = otherBusinessGuid,
      state = uuidGenerator[2].toString(),
      shopHost = "$shopSubdomain.myshopify.com"
    )
    // Now check that some arbitrary webhook is successfully stored
    val dataRequestPayload: JsonNode = objectMapper.convertValue(
      mapOf(
        "shop_id" to 954889,
        "shop_domain" to "$shopSubdomain.myshopify.com",
      )
    )
    makeShopifyWebhookRequest(payload = dataRequestPayload, topic = SHOP_REDACT)
    assertShopifyWebhookEvent(
      expectedShopifyWebhookTopic = SHOP_REDACT,
      webhookGuid = uuidGenerator[4],
      connectionGuid = uuidGenerator[3],
    )
  }

  private suspend fun makeShopifyWebhookRequest(payload: JsonNode, topic: ShopifyWebhookTopic) {
    shopifyWebhookClient.request(ShopifyWebhookApi.Create(payload),
      builder = {
        setShopifyWebhookHeaders(topic, shopSubdomain = shopSubdomain)
      }
    )
  }

  private fun assertShopifyWebhookEvent(
    expectedShopifyWebhookTopic: ShopifyWebhookTopic,
    webhookGuid: UUID = uuidGenerator[2],
    connectionGuid: UUID = uuidGenerator[1]
  ) {
    assertThat(get<ShopifyWebhookStore>().get(webhookGuid)).isEqualTo(
      ShopifyWebhookModel(
        guid = webhookGuid,
      type = expectedShopifyWebhookTopic.topic,
        connectionGuid = connectionGuid,
      shopSubdomain = shopSubdomain,
      status = ShopifyWebhookModel.Status.ACTION_REQUIRED,
    ))
  }
}
