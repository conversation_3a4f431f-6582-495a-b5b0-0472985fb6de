package co.highbeam.endpoint.shopify

import co.highbeam.api.shopify.ShopifyPayoutApi
import co.highbeam.mapper.shopify.ShopifyPayoutMapper
import co.highbeam.model.shopify.ShopifyPayoutModel
import co.highbeam.money.Balance
import co.highbeam.rep.shopify.ShopifyPayoutRep
import co.highbeam.server.Server
import co.highbeam.store.shopify.ShopifyPayoutStore
import co.highbeam.testing.ConnectFeatureIntegrationTest
import co.highbeam.testing.createConnection
import co.highbeam.testing.createRedirectUrl
import co.highbeam.testing.mockShopifyCreateAccessToken
import com.fasterxml.jackson.databind.node.NullNode
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.Currency
import java.util.UUID

internal class GetShopifyPayoutsByBusinessTest(
  server: Server<*>,
) : ConnectFeatureIntegrationTest(server) {
  @Test
  fun `no payouts`() {
    val businessGuid = UUID.randomUUID()

    val clientCall = runBlocking {
      shopifyPayoutClient.request(ShopifyPayoutApi.GetByBusiness(businessGuid, false))
    }
    assertThat(clientCall).isEqualTo(
      emptyList<ShopifyPayoutRep.Complete>()
    )
  }

  @Test
  fun `payouts pending`() {
    val businessGuid = UUID.randomUUID()

    runBlocking {
      createRedirectUrl(businessGuid)
    }

    mockShopifyCreateAccessToken()

    runBlocking {
      createConnection(businessGuid, uuidGenerator[0].toString())
    }

    val payout1 = ShopifyPayoutModel(
      guid = UUID.randomUUID(),
      businessGuid = businessGuid,
      connectionGuid = uuidGenerator[1],
      shopifyId = 1,
      json = NullNode.instance,
      status = ShopifyPayoutRep.Status.InTransit,
      date = LocalDate.of(2020, 1, 1),
      currency = Currency.getInstance("USD"),
      amount = Balance.fromDollarsAndCents(123, 1),
      unitCoTransactionId = null,
    )
    val payout2 = ShopifyPayoutModel(
      guid = UUID.randomUUID(),
      businessGuid = businessGuid,
      connectionGuid = uuidGenerator[1],
      shopifyId = 2,
      json = NullNode.instance,
      status = ShopifyPayoutRep.Status.Paid,
      date = LocalDate.of(2020, 1, 2),
      currency = Currency.getInstance("USD"),
      amount = Balance.fromDollarsAndCents(123, 2),
      unitCoTransactionId = null,
    )

    runBlocking {
      get<ShopifyPayoutStore>().create(payout1)
      get<ShopifyPayoutStore>().create(payout2)
    }

    val clientCall = runBlocking {
      shopifyPayoutClient.request(
        ShopifyPayoutApi.GetByBusiness(
          businessGuid = businessGuid,
          pending = true,
          fromDate = LocalDate.of(2020, 1, 1)
        )
      )
    }

    assertThat(
      clientCall
    ).containsAll(
      listOf(payout1).map { payout ->
        get<ShopifyPayoutMapper>().map(payout)
      }
    )
  }

  @Test
  fun `payouts all`() {
    val businessGuid = UUID.randomUUID()

    runBlocking {
      createRedirectUrl(businessGuid)
    }

    mockShopifyCreateAccessToken()

    runBlocking {
      createConnection(businessGuid, uuidGenerator[0].toString())
    }

    val payout1 = ShopifyPayoutModel(
      guid = UUID.randomUUID(),
      businessGuid = businessGuid,
      connectionGuid = uuidGenerator[1],
      shopifyId = 1,
      json = NullNode.instance,
      status = ShopifyPayoutRep.Status.InTransit,
      date = LocalDate.of(2020, 1, 1),
      currency = Currency.getInstance("USD"),
      amount = Balance.fromDollarsAndCents(123, 1),
      unitCoTransactionId = null,
    )
    val payout2 = ShopifyPayoutModel(
      guid = UUID.randomUUID(),
      businessGuid = businessGuid,
      connectionGuid = uuidGenerator[1],
      shopifyId = 2,
      json = NullNode.instance,
      status = ShopifyPayoutRep.Status.Paid,
      date = LocalDate.of(2020, 1, 2),
      currency = Currency.getInstance("USD"),
      amount = Balance.fromDollarsAndCents(123, 2),
      unitCoTransactionId = null,
    )

    runBlocking {
      get<ShopifyPayoutStore>().create(payout1)
      get<ShopifyPayoutStore>().create(payout2)
    }

    val clientCall = runBlocking {
      shopifyPayoutClient.request(
        ShopifyPayoutApi.GetByBusiness(
          businessGuid = businessGuid,
          pending = false,
          fromDate = LocalDate.of(2020, 1, 1)
        )
      )
    }

    assertThat(clientCall).containsAll(
      listOf(payout1, payout2).map { payout ->
        get<ShopifyPayoutMapper>().map(payout)
      }
    )
  }
}
