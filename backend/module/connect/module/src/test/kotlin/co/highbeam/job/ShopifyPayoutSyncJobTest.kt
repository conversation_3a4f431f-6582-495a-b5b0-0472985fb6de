package co.highbeam.job

import co.highbeam.api.shopify.ShopifyConnectionApi
import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.model.shopify.ShopifyPayoutModel
import co.highbeam.money.Balance
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.UUIDSelection
import co.highbeam.rep.shopify.ShopifyConnectionRep
import co.highbeam.rep.shopify.ShopifyPayoutRep
import co.highbeam.server.Server
import co.highbeam.store.shopify.ShopifyPayoutStore
import co.highbeam.testing.ConnectFeatureIntegrationTest
import co.highbeam.testing.createConnection
import co.highbeam.testing.createRedirectUrl
import co.highbeam.testing.executeJob
import co.highbeam.testing.mockShopifyCreateAccessToken
import co.highbeam.testing.uninstallConnection
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import com.shopify.client.ShopifyPayoutClient
import com.shopify.rep.PayoutRep
import io.mockk.coEvery
import io.mockk.confirmVerified
import kotlinx.coroutines.flow.flowOf
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.util.Currency
import java.util.UUID

internal class ShopifyPayoutSyncJobTest(
  server: Server<*>,
) : ConnectFeatureIntegrationTest(server) {
  @Test
  fun `no businesses (all)`() {
    coEvery {
      get<BusinessGuidClient>().businessGuids(UUIDSelection.All)
    } returns emptyList()
    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.All),
    )
    confirmVerified(get<ShopifyPayoutClient>())
  }

  @Test
  fun `no businesses (explicit)`() {
    coEvery {
      get<BusinessGuidClient>().businessGuids(UUIDSelection.Explicit(emptyList()))
    } returns emptyList()
    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.Explicit(emptyList())),
    )
    confirmVerified(get<ShopifyPayoutClient>())
  }

  @Test
  fun `no shopify shops`() {
    val businessGuid = UUID.randomUUID()

    setup("Create redirect URL") {
      createRedirectUrl(businessGuid)
    }

    mockShopifyCreateAccessToken()

    setup("Create and soft-delete connection") {
      createConnection(businessGuid, uuidGenerator[0].toString())
      uninstallConnection(businessGuid, uuidGenerator[1], softDelete = true)
    }

    coEvery {
      get<BusinessGuidClient>().businessGuids(UUIDSelection.Explicit(listOf(businessGuid)))
    } returns listOf(businessGuid)
    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.Explicit(listOf(businessGuid))),
    )
    confirmVerified(get<ShopifyPayoutClient>())
  }

  @Test
  fun `all new payouts`() = integrationTest {
    val businessGuid = UUID.randomUUID()

    setup("Create redirect URL") {
      createRedirectUrl(businessGuid)
    }

    mockShopifyCreateAccessToken()

    setup("Create connection") {
      createConnection(businessGuid, uuidGenerator[0].toString())
    }

    coEvery {
      get<BusinessGuidClient>().businessGuids(UUIDSelection.Explicit(listOf(businessGuid)))
    } returns listOf(businessGuid)

    syncMetadata(businessGuid)

    val payouts = objectMapper.readValue<List<PayoutRep.Complete>>(
      Resources.getResource("shopify/sample-shopify-payouts-initial.json")
    )
    coEvery {
      get<ShopifyPayoutClient>().getSince(
        shopSubdomain = "somecustomer",
        accessToken = ProtectedString("some-access-token"),
        dateMin = null,
      )
    } returns flowOf(payouts)

    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.Explicit(listOf(businessGuid))),
    )

    assertThat(shopifyConnectionClient.request(ShopifyConnectionApi.GetByBusiness(businessGuid)))
      .containsExactlyInAnyOrder(
        ShopifyConnectionRep.Complete(
          guid = uuidGenerator[1],
          shopName = "Some Customer",
          shopSubdomain = "somecustomer",
          hasSyncedOrders = false,
          hasSyncedTransactions = false,
          hasSyncedPayouts = true,
          hasSyncedBalances = false,
          connectionBannerDisabled = false,
          isActive = false,
          hasAllPermissions = false,
          isShopifyPaymentsEnabled = true,
          missingPermissions = setOf("shopify", "test", "api"),
        ),
      )

    assertThat(get<ShopifyPayoutStore>().getByBusiness(businessGuid))
      .containsExactlyInAnyOrder(
        ShopifyPayoutModel(
          guid = uuidGenerator[2],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 1,
          json = payouts[0].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 1),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 1),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[3],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 2,
          json = payouts[1].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 2),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 2),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[4],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 3,
          json = payouts[2].json,
          status = ShopifyPayoutRep.Status.Scheduled,
          date = LocalDate.of(2020, 1, 3),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 3),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[5],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 4,
          json = payouts[3].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 4),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 4),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[6],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 5,
          json = payouts[4].json,
          status = ShopifyPayoutRep.Status.Scheduled,
          date = LocalDate.of(2020, 1, 5),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 5),
          unitCoTransactionId = null,
        ),
      )
  }

  @Test
  fun `updated prior payouts get upserted`() = integrationTest {
    val businessGuid = UUID.randomUUID()

    setup("Create redirect URL") {
      createRedirectUrl(businessGuid)
    }

    mockShopifyCreateAccessToken()

    setup("Create connection") {
      createConnection(businessGuid, uuidGenerator[0].toString())
    }

    coEvery {
      get<BusinessGuidClient>().businessGuids(UUIDSelection.Explicit(listOf(businessGuid)))
    } returns listOf(businessGuid)

    syncMetadata(businessGuid)

    val initialPayouts = objectMapper.readValue<List<PayoutRep.Complete>>(
      Resources.getResource("shopify/sample-shopify-payouts-initial.json")
    )
    val updatedPayouts = objectMapper.readValue<List<PayoutRep.Complete>>(
      Resources.getResource("shopify/sample-shopify-payouts-updated.json")
    )
    coEvery {
      get<ShopifyPayoutClient>().getSince(
        shopSubdomain = "somecustomer",
        accessToken = ProtectedString("some-access-token"),
        dateMin = any(),
      )
    } returns flowOf(initialPayouts) andThen flowOf(updatedPayouts)

    // Each sync syncs different payouts. See the mocking code.
    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.Explicit(listOf(businessGuid))),
    )
    executeJob<ShopifyPayoutSyncJob, ShopifyPayoutSyncJob.Params>(
      ShopifyPayoutSyncJob.Params(UUIDSelection.Explicit(listOf(businessGuid))),
    )

    assertThat(shopifyConnectionClient.request(ShopifyConnectionApi.GetByBusiness(businessGuid)))
      .containsExactlyInAnyOrder(
        ShopifyConnectionRep.Complete(
          guid = uuidGenerator[1],
          shopName = "Some Customer",
          shopSubdomain = "somecustomer",
          hasSyncedOrders = false,
          hasSyncedTransactions = false,
          hasSyncedPayouts = true,
          hasSyncedBalances = false,
          connectionBannerDisabled = false,
          isActive = false,
          hasAllPermissions = false,
          isShopifyPaymentsEnabled = true,
          missingPermissions = setOf("shopify", "test", "api"),
        ),
      )

    assertThat(get<ShopifyPayoutStore>().getByBusiness(businessGuid))
      .containsExactlyInAnyOrder(
        ShopifyPayoutModel(
          guid = uuidGenerator[2],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 1,
          json = initialPayouts[0].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 1),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 1),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[3],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 2,
          json = initialPayouts[1].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 2),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 2),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[4],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 3,
          json = updatedPayouts[0].json,
          status = ShopifyPayoutRep.Status.Scheduled,
          date = LocalDate.of(2020, 1, 3),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 3),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[5],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 4,
          json = updatedPayouts[1].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 4),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 4),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[6],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 5,
          json = updatedPayouts[2].json,
          status = ShopifyPayoutRep.Status.Paid,
          date = LocalDate.of(2020, 1, 5),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 5),
          unitCoTransactionId = null,
        ),
        ShopifyPayoutModel(
          guid = uuidGenerator[10],
          businessGuid = businessGuid,
          connectionGuid = uuidGenerator[1],
          shopifyId = 6,
          json = updatedPayouts[3].json,
          status = ShopifyPayoutRep.Status.Scheduled,
          date = LocalDate.of(2020, 1, 6),
          currency = Currency.getInstance("USD"),
          amount = Balance.fromDollarsAndCents(123, 6),
          unitCoTransactionId = null,
        ),
      )
  }
}
