package co.highbeam.endpoint.event.connect

import co.highbeam.rep.shopify.ShopifyConnectionRep
import co.highbeam.server.Server
import co.highbeam.service.shopify.ShopifyConnectionService
import co.highbeam.testing.ConnectFeatureIntegrationTest
import co.highbeam.testing.createConnection
import co.highbeam.testing.createRedirectUrl
import co.highbeam.testing.mockShopifyCreateAccessToken
import io.ktor.http.Url
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class ShopifyAppUninstalledEventHandlerTest(
  server: Server<*>,
) : ConnectFeatureIntegrationTest(server) {
  private val handler: ShopifyAppUninstalledEventHandler by lazy {
    injector.getInstance(ShopifyAppUninstalledEventHandler::class.java)
  }

  private val connectionService: ShopifyConnectionService by lazy {
    injector.getInstance(ShopifyConnectionService::class.java)
  }


  @Test
  fun `No connections`() = integrationTest {
    handler.handleEvent("nonexistent domain")
  }

  @Test
  fun `One connection found, gets deleted`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val shopSubdomain = "test"
    createShopifyConnection(businessGuid, shopSubdomain)
    handler.handleEvent(shopSubdomain)
    assertThat(connectionService.getAllByShopdomain(shopSubdomain)).isEmpty()
  }

  @Test
  fun `Multiple connections found, all but newest are deleted`() = integrationTest {
    val businessGuid1 = UUID.randomUUID()
    val businessGuid2 = UUID.randomUUID()
    val businessGuid3 = UUID.randomUUID()
    val shopSubdomain = "test"
    createShopifyConnection(businessGuid1, shopSubdomain)
    createShopifyConnection(businessGuid2, shopSubdomain)
    createShopifyConnection(businessGuid3, shopSubdomain)
    // Check that connections were set up right
    assertThat(connectionService.getAllByShopdomain(shopSubdomain)).hasSize(3)
    // Now run the handler and check the result
    handler.handleEvent(shopSubdomain)
    assertThat(connectionService.getAllByShopdomain(shopSubdomain))
      .hasSize(1).first().matches { it.businessGuid == businessGuid3 }
  }

  private suspend fun createShopifyConnection(
    businessGuid: UUID,
    shopSubdomain: String,
  ): ShopifyConnectionRep.Complete {
    val redirectUrl = createRedirectUrl(businessGuid).url
    val connectionNonce = checkNotNull(Url(redirectUrl).parameters["state"])
    mockShopifyCreateAccessToken()
    return createConnection(
      businessGuid = businessGuid,
      state = connectionNonce,
      shopHost = "$shopSubdomain.myshopify.com"
    )
  }
}
