package co.highbeam.auth.auth

import co.highbeam.protectedString.ProtectedString
import co.highbeam.testing.TEST_RUTTER_API_SECRET
import io.ktor.http.Headers
import io.ktor.http.HeadersSingleImpl
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class AuthRutterWebhookTest {
  private val authRutterWebhook = AuthRutterWebhook.Provider(
    mockk {
      every { apiSecret } returns ProtectedString(TEST_RUTTER_API_SECRET)
    },
  )

  @Test
  fun `Empty headers`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = Headers.Empty
    val result = authRutterWebhook(body).authorize(null, headers)
    assertFalse(result)
  }

  @Test
  fun `Incorrect signature`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = HeadersSingleImpl("X-Rutter-Signature", listOf("asdf"))
    val result = authRutterWebhook(body).authorize(null, headers)
    assertFalse(result)
  }

  @Test
  fun `Correct signature`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = HeadersSingleImpl(
      "X-Rutter-Signature",
      listOf("sha256=7eJR4FehZIXu57/yLwpLGRWBtm6ikGio9kf4P2zjK0M=")
    )
    val result = authRutterWebhook(body).authorize(null, headers)
    assertTrue(result)
  }
}
