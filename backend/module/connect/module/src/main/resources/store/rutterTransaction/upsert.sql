insert into connect.rutter_transaction (guid, business_guid, connection_guid, rutter_guid, status,
                                        type, currency, amount, initiated_at, json)
values (:guid, :businessGuid, :connectionGuid, :rutterGuid, :status,
        :type, :currency, :amount, :initiatedAt, :json::jsonb)
on conflict on constraint uniq__rutter_transaction__rutter_guid
  do update
  set status       = :status,
      type         = :type,
      currency     = :currency,
      amount       = :amount,
      initiated_at = :initiatedAt,
      json         = :json::jsonb
returning *
