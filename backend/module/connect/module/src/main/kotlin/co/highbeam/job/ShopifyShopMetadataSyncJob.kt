package co.highbeam.job

import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.metrics.Metrics
import co.highbeam.model.shopify.ShopifyConnectionModel
import co.highbeam.rep.UUIDSelection
import co.highbeam.service.shopify.ShopifyConnectionService
import com.google.inject.Inject
import com.shopify.client.ShopifyShopClient
import mu.KotlinLogging
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID
import co.highbeam.job.ShopifyShopMetadataSyncJob as Job

// Exemplar: Repeated Quartz Job
internal class ShopifyShopMetadataSyncJob @Inject constructor(
  businessGuidClient: BusinessGuidClient,
  private val clock: Clock,
  private val connectionService: ShopifyConnectionService,
  metrics: Metrics,
  private val shopifyShopClient: ShopifyShopClient,
) : ShopifyJob<Job.Params>(
  businessGuidClient = businessGuidClient,
  connectionService = connectionService,
  jobName = "shop metadata sync",
  metrics = metrics,
) {
  private val logger = KotlinLogging.logger {}

  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  internal data class Params(
    override val businessGuids: UUIDSelection,
  ) : ShopifyJob.Params()

  override suspend fun execute(
    params: Params,
    businessGuid: UUID,
    connection: ShopifyConnectionModel,
  ) {
    val shopSubdomain = connection.shopSubdomain
    logger.info { "Syncing Shopify shop metadata for business $businessGuid, shop $shopSubdomain." }
    val shop = shopifyShopClient.get(shopSubdomain, connection.accessToken) ?: run {
      logger.warn { "Shop metadata not found for business $businessGuid, shop $shopSubdomain." }
      return
    }
    logger.info { "Found shop metadata for business $businessGuid, shop $shopSubdomain: $shop." }
    connectionService.update(
      businessGuid = businessGuid,
      connectionGuid = connection.guid,
      update = ShopifyConnectionModel.Update(
        shopName = shop.name,
        lastMetadataSyncAt = ZonedDateTime.now(clock),
      ),
    )
  }
}
