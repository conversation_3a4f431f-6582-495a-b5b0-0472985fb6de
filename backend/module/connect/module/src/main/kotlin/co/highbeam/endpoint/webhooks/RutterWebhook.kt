package co.highbeam.endpoint.webhooks

import co.highbeam.auth.auth.AuthRutterWebhook
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.RAW_TEXT_ATTRIBUTE_KEY
import co.highbeam.restInterface.template
import co.highbeam.service.rutter.RutterWebhookService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.rutter.RutterWebhookApi as Api

internal class RutterWebhook @Inject constructor(
  private val rutterWebhookService: RutterWebhookService,
  private val authRutterWebhook: AuthRutterWebhook.Provider,
) : EndpointHandler<Api.Post, Unit>(
  template = Api.Post::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Post =
    Api.Post(wrapper = call.body())

  override suspend fun Handler.handle(endpoint: Api.Post) {
    auth(authRutterWebhook(call.attributes[RAW_TEXT_ATTRIBUTE_KEY]))
    rutterWebhookService.processWebhook(endpoint.wrapper)
  }
}
