package co.highbeam.service.rutter

import co.highbeam.model.rutter.RutterPayoutModel
import co.highbeam.rep.rutter.RutterPayoutRep
import com.google.inject.ImplementedBy
import java.time.LocalDate
import java.util.UUID

@ImplementedBy(RutterPayoutServiceImpl::class)
internal interface RutterPayoutService {
  fun upsert(businessGuid: UUID, connectionGuid: UUID, payouts: List<RutterPayoutModel>)

  fun getLatestFinalized(businessGuid: UUID, connectionGuid: UUID): RutterPayoutModel?

  fun getLatestPaid(
    businessGuid: UUID,
    connectionGuid: UUID,
    beforeDate: LocalDate
  ): RutterPayoutModel?

  fun getByBusiness(
    businessGuid: UUID,
    fromDate: LocalDate,
    toDate: LocalDate? = null,
  ): List<RutterPayoutRep>

  fun getAllPayoutsByBusiness(
    businessGuid: UUID,
    fromDate: LocalDate,
    toDate: LocalDate? = null,
  ): List<RutterPayoutRep.Complete>

  suspend fun syncAllPayoutsForBusiness(businessGuid: UUID)
}
