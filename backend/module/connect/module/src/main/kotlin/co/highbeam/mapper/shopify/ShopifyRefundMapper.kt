package co.highbeam.mapper.shopify

import co.highbeam.model.shopify.ShopifyRefundModel
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import com.shopify.rep.RefundRep
import java.util.UUID

internal class ShopifyRefundMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun map(
    businessGuid: UUID,
    connectionGuid: UUID,
    refund: RefundRep.Complete,
  ): ShopifyRefundModel =
    ShopifyRefundModel(
      guid = uuidGenerator.generate(),
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      shopifyId = refund.id,
      json = refund.json,
      date = refund.date.toLocalDate(),
    )
}
