package co.highbeam.service.shopify

import co.highbeam.model.shopify.ShopifyRefundModel
import co.highbeam.store.shopify.ShopifyRefundStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class ShopifyRefundServiceImpl @Inject constructor(
  private val refundStore: ShopifyRefundStore,
) : ShopifyRefundService {
  private val logger = KotlinLogging.logger {}

  override fun upsert(businessGuid: UUID, connectionGuid: UUID, refunds: List<ShopifyRefundModel>) {
    logger.info { "Upserting refunds." }
    refundStore.upsert(businessGuid, connectionGuid, refunds)
  }
}
