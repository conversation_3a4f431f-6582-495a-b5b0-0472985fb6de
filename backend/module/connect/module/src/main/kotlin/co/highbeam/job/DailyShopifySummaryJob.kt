package co.highbeam.job

import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.metrics.Metrics
import co.highbeam.model.shopify.DailyShopifySummaryModel
import co.highbeam.model.shopify.ShopifyConnectionModel
import co.highbeam.model.shopify.ShopifyTransactionModel
import co.highbeam.money.Balance
import co.highbeam.rep.UUIDSelection
import co.highbeam.rep.shopify.ShopifyPayoutRep
import co.highbeam.service.shopify.DailyShopifySummaryService
import co.highbeam.service.shopify.ShopifyConnectionService
import co.highbeam.service.shopify.ShopifyTransactionService
import com.google.inject.Inject
import mu.KotlinLogging
import java.time.Clock
import java.time.LocalDate
import java.util.Currency
import java.util.UUID
import co.highbeam.job.DailyShopifySummaryJob as Job

private const val DAYS_TO_CALCULATE: Int = 70

internal class DailyShopifySummaryJob @Inject constructor(
  businessGuidClient: BusinessGuidClient,
  private val clock: Clock,
  connectionService: ShopifyConnectionService,
  private val dailyShopifySummaryService: DailyShopifySummaryService,
  metrics: Metrics,
  private val transactionService: ShopifyTransactionService,
) : ShopifyJob<Job.Params>(
  businessGuidClient = businessGuidClient,
  connectionService = connectionService,
  jobName = "daily sales summary",
  metrics = metrics,
) {
  private val logger = KotlinLogging.logger {}

  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  internal data class Params(
    override val businessGuids: UUIDSelection,
    val firstDay: LocalDate? = null,
    val lastDay: LocalDate? = null,
  ) : ShopifyJob.Params()

  private data class ShopifySummary(
    val amount: Balance,
    val fee: Balance,
    val net: Balance,
  ) {
    companion object {
      val ZERO = ShopifySummary(Balance.ZERO, Balance.ZERO, Balance.ZERO)
    }

    operator fun plus(other: ShopifyTransactionModel): ShopifySummary =
      ShopifySummary(
        amount = amount + other.amount,
        fee = fee + other.fee,
        net = net + other.net,
      )
  }

  override suspend fun execute(
    params: Params,
    businessGuid: UUID,
    connection: ShopifyConnectionModel,
  ) {
    val now = LocalDate.now(clock)
    val firstDay = params.firstDay ?: now.minusDays(DAYS_TO_CALCULATE.toLong() - 1)
    val lastDay = params.lastDay ?: now
    firstDay.datesUntil(lastDay.plusDays(1)).forEach { date ->
      execute(businessGuid, connection, date)
    }
  }

  private fun execute(businessGuid: UUID, connection: ShopifyConnectionModel, date: LocalDate) {
    val shopSubdomain = connection.shopSubdomain
    logger.info {
      "Re-calculating recent daily sales summaries for business $businessGuid," +
        " shop $shopSubdomain, date $date."
    }

    val transactions = transactionService.getByDate(businessGuid, connection.guid, date)
    val totals = calculateTotalsByCurrency(transactions)

    dailyShopifySummaryService.setForDate(
      businessGuid = businessGuid,
      connectionGuid = connection.guid,
      date = date,
      summaries = totals.map { total ->
        DailyShopifySummaryModel.Creation(
          payoutStatus = total.key.second,
          currency = total.key.first,
          amount = total.value.amount,
          fee = total.value.fee,
          net = total.value.net,
        )
      }
    )
  }

  private fun calculateTotalsByCurrency(
    orders: List<ShopifyTransactionModel>,
  ): Map<Pair<Currency, ShopifyPayoutRep.Status?>, ShopifySummary> =
    orders
      .filter {
        // Since the transaction sum is zero, we sum everything except payouts to get summaries that
        // reflect what will be paid out.
        it.type != "payout"
      }
      .groupBy { Pair(it.currency, it.payoutStatus) }
      .mapValues { it.value.fold(ShopifySummary.ZERO) { acc, order -> acc + order } }
}
