package co.highbeam.endpoint.shopify

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.shopify.ShopifyConnectionMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.shopify.ShopifyConnectionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.shopify.ShopifyConnectionApi as Api
import co.highbeam.rep.shopify.ShopifyConnectionRep as Rep

internal class DisableShopifyConnectionBannersByBusiness @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val connectionService: ShopifyConnectionService,
  private val connectionMapper: ShopifyConnectionMapper,
) : EndpointHandler<Api.DisableBannersByBusiness, List<Rep.Complete>>(
  template = Api.DisableBannersByBusiness::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.DisableBannersByBusiness =
    Api.DisableBannersByBusiness(businessGuid = call.getParam("businessGuid"))

  override suspend fun Handler.handle(
    endpoint: Api.DisableBannersByBusiness,
  ): List<Rep.Complete> {
    auth(authPermission(Permission.StoreConnectionBanner_Delete) { endpoint.businessGuid })
    val connections = connectionService.disableConnectionBannersByBusiness(endpoint.businessGuid)
    return connections.map { connectionMapper.map(it) }
  }
}
