package co.highbeam.store.shopify

import co.highbeam.exception.shopify.ShopifyWebhookEventNotFound
import co.highbeam.model.shopify.ShopifyWebhookModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.util.UUID

internal class ShopifyWebhookStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(model: ShopifyWebhookModel): ShopifyWebhookModel =
    transaction { handle ->
      handle.createQuery(sqlResource("store/shopifyWebhook/create.sql"))
        .bindKotlin(model)
        .mapTo(ShopifyWebhookModel::class.java)
        .single()
    }

  fun get(webhookEventGuid: UUID): ShopifyWebhookModel? =
    handle { handle ->
      handle.createQuery(sqlResource("store/shopifyWebhook/get.sql"))
        .bind("webhookEventGuid", webhookEventGuid)
        .mapTo(ShopifyWebhookModel::class.java)
        .singleNullOrThrow()
    }

  fun update(webhookEventGuid: UUID, update: ShopifyWebhookModel.Update): ShopifyWebhookModel =
    transaction { handle ->
      handle.createQuery(sqlResource("store/shopifyWebhook/update.sql"))
        .bind("webhookEventGuid", webhookEventGuid)
        .bindKotlin(update)
        .mapTo(ShopifyWebhookModel::class.java)
        .singleNullOrThrow() ?: throw ShopifyWebhookEventNotFound()
    }
}
