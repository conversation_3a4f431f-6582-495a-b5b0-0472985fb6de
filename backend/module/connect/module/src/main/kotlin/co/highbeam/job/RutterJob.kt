package co.highbeam.job

import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.metrics.Metrics
import co.highbeam.model.rutter.RutterConnectionModel
import co.highbeam.rep.UUIDSelection
import mu.KotlinLogging
import java.io.IOException
import java.util.UUID

internal abstract class RutterJob<P : RutterJob.Params>(
  private val businessGuidClient: BusinessGuidClient,
  private val jobName: String,
  metrics: Metrics,
) : HighbeamJob<P>() {
  private val logger = KotlinLogging.logger {}

  private val jobExecutedCounter = metrics.counter(
    "rutter_job_execution_count",
    "jobName", jobName,
  ) {}
  private val connectionsSyncCounter = metrics.counter(
    "rutter_connections_sync_count",
    "jobName", jobName,
  ) {}
  private val serverErrorCounter = metrics.counter(
    "server_error_count",
    "service_name", "Rutter",
    "jobName", jobName,
  ) {}

  internal abstract class Params : HighbeamJob.Params() {
    abstract val businessGuids: UUIDSelection
  }

  final override suspend fun execute(params: P) {
    logger.info { "Starting Rutter $jobName job: $params." }
    val businessGuids = businessGuidClient.businessGuids(params.businessGuids)
    businessGuids.forEach {
      @Suppress("TooGenericExceptionCaught")
      try {
        execute(params, it)
      } catch (e: Exception) {
        logger.error(e) { "Rutter $jobName job failed for $params." }
      }
    }
  }

  abstract suspend fun getConnections(businessGuid: UUID): List<RutterConnectionModel>

  suspend fun execute(params: P, businessGuid: UUID) {
    logger.info { "Syncing Rutter $jobName for business $businessGuid." }
    jobExecutedCounter.increment()
    val connections = getConnections(businessGuid)
    if (connections.isEmpty()) {
      logger.info { "No Rutter connections for business $businessGuid. Skipping." }
      return
    }
    logger.info {
      "${connections.size} Rutter connections found for business $businessGuid: $connections."
    }
    connections.forEach {
      try {
        ignoreTimeouts {
          execute(params, businessGuid, it)
        }
        connectionsSyncCounter.increment()
      } catch (e: HighbeamHttpClientException) {
        when {
          e.isServerError -> handleServerError(e, businessGuid)
          else -> throw e
        }
      } catch (e: IOException) {
        handleServerError(e, businessGuid)
      }
    }
  }

  protected abstract suspend fun execute(
    params: P,
    businessGuid: UUID,
    connection: RutterConnectionModel,
  )

  private fun handleServerError(e: Exception, businessGuid: UUID) {
    logger.warn(e) {
      "Rutter server error for business $businessGuid." +
        " Suppressing the error because this job is idempotent."
    }
    serverErrorCounter.increment()
  }
}
