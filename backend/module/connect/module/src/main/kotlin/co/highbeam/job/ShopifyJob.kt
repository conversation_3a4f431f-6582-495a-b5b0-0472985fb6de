package co.highbeam.job

import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.metrics.Metrics
import co.highbeam.model.shopify.ShopifyConnectionModel
import co.highbeam.rep.UUIDSelection
import co.highbeam.rep.shopify.ShopifyConnectionStatus
import co.highbeam.service.shopify.ShopifyConnectionService
import io.ktor.http.HttpStatusCode
import mu.KotlinLogging
import java.io.IOException
import java.util.UUID

internal abstract class ShopifyJob<P : ShopifyJob.Params>(
  private val businessGuidClient: BusinessGuidClient,
  private val connectionService: ShopifyConnectionService,
  private val jobName: String,
  metrics: Metrics,
) : HighbeamJob<P>() {
  private val logger = KotlinLogging.logger {}

  private val jobExecutedCounter = metrics.counter(
    "shopify_job_execution_count",
    "jobName", jobName,
  ) {}
  private val connectionsSyncCounter = metrics.counter(
    "shopify_connections_sync_count",
    "jobName", jobName,
  ) {}
  private val paymentRequiredCounter = metrics.counter(
    "payment_required_counter",
    "jobName", jobName,
  ) {}
  private val serverErrorCounter = metrics.counter(
    "server_error_count",
    "service_name", "Shopify",
    "jobName", jobName,
  ) {}
  private val connectionUnauthorizedErrorCounter = metrics.counter(
    "connection_unauthorized_error_count",
    "service_name", "Shopify",
    "jobName", jobName,
  ) {}
  private val connectionTimeoutErrorCounter = metrics.counter(
    "connection_timeout_error_count",
    "service_name", "Shopify",
    "jobName", jobName,
  ) {}
  private val connectionGeneralErrorCounter = metrics.counter(
    "connection_general_error_count",
    "service_name", "Shopify",
    "jobName", jobName,
  ) {}

  internal abstract class Params : HighbeamJob.Params() {
    abstract val businessGuids: UUIDSelection
  }

  final override suspend fun execute(params: P) {
    logger.info { "Starting Shopify $jobName job: $params." }
    val businessGuids = businessGuidClient.businessGuids(params.businessGuids)
    businessGuids.forEach { businessGuid ->
      @Suppress("TooGenericExceptionCaught")
      try {
        execute(params, businessGuid)
      } catch (e: HighbeamHttpClientException) {
        when (e.statusCode) {
          HttpStatusCode.Unauthorized -> {
            connectionUnauthorizedErrorCounter.increment()
            handleServerError(e, businessGuid)
          }
          HttpStatusCode.RequestTimeout -> {
            connectionTimeoutErrorCounter.increment()
            handleServerError(e, businessGuid)
          }
          else -> {
            connectionGeneralErrorCounter.increment()
            handleServerError(e, businessGuid)
          }
        }
        connectionUnauthorizedErrorCounter.increment()
      } catch (e: Exception) {
        logger.error(e) { "Shopify $jobName job failed for $params." }
      }
    }
  }

  private suspend fun execute(params: P, businessGuid: UUID) {
    logger.info { "Syncing Shopify $jobName for business $businessGuid." }
    jobExecutedCounter.increment()
    val connections = connectionService.getByBusiness(
      businessGuid = businessGuid,
      status = ShopifyConnectionStatus.Active,
    )
    if (connections.isEmpty()) {
      logger.info { "No Shopify connections for business $businessGuid. Skipping." }
      return
    }
    logger.info {
      "${connections.size} Shopify connections found for business $businessGuid: $connections."
    }
    connections.forEach {
      connectionsSyncCounter.increment()
      try {
        ignoreTimeouts {
          execute(params, businessGuid, it)
        }
      } catch (e: HighbeamHttpClientException) {
        when {
          e.statusCode == HttpStatusCode.PaymentRequired -> handlePaymentRequired(e, businessGuid)
          e.isServerError -> handleServerError(e, businessGuid)
          else -> throw e
        }
      } catch (e: IOException) {
        handleServerError(e, businessGuid)
      }
    }
  }

  protected abstract suspend fun execute(
    params: P,
    businessGuid: UUID,
    connection: ShopifyConnectionModel,
  )

  private fun handlePaymentRequired(e: Exception, businessGuid: UUID) {
    logger.warn(e) {
      "Payment is required for business $businessGuid on Shopify." +
        "Suppressing the error because this job is idempotent."
    }
    paymentRequiredCounter.increment()
  }

  private fun handleServerError(e: Exception, businessGuid: UUID) {
    logger.warn(e) {
      "Shopify server error for business $businessGuid." +
        " Suppressing the error because this job is idempotent."
    }
    serverErrorCounter.increment()
  }
}
