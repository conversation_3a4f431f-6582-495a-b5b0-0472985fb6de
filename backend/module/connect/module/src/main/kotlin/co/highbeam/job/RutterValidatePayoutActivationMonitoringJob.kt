package co.highbeam.job

import co.highbeam.client.business.BusinessGuidClient
import co.highbeam.listener.ValidateRutterPayoutActivationListener
import co.highbeam.metrics.Metrics
import co.highbeam.model.rutter.RutterConnectionModel
import co.highbeam.rep.UUIDSelection
import co.highbeam.service.rutter.RutterConnectionService
import com.google.inject.Inject
import com.rutter.rep.RutterPlatformType
import java.util.UUID
import co.highbeam.job.RutterValidatePayoutActivationMonitoringJob as Job

internal class RutterValidatePayoutActivationMonitoringJob @Inject constructor(
  private val connectionService: RutterConnectionService,
  private val validatePayoutActivationListener: ValidateRutterPayoutActivationListener,
  businessGuidClient: BusinessGuidClient,
  metrics: Metrics,
) : RutterJob<Job.Params>(
  businessGuidClient = businessGuidClient,
  jobName = "monitor rutter payout activation",
  metrics = metrics,
) {
  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  internal data class Params(
    override val businessGuids: UUIDSelection,
  ) : RutterJob.Params()

  override suspend fun getConnections(businessGuid: UUID): List<RutterConnectionModel> =
    connectionService.getValidConnectionsByBusiness(businessGuid).filter {
      it.platformName.platformType == RutterPlatformType.COMMERCE
    }

  override suspend fun execute(
    params: Params,
    businessGuid: UUID,
    connection: RutterConnectionModel,
  ) {
    validatePayoutActivationListener.publish(
      connection.guid
    )
  }
}
