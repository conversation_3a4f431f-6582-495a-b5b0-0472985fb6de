package co.highbeam.service.rutter

import co.highbeam.event.publisher.EventPublisher
import co.highbeam.publisher.RUTTER_WEBHOOK_TOPIC_NAME
import co.highbeam.rep.UUIDSelection
import com.fasterxml.jackson.databind.JsonNode
import com.google.inject.Inject
import com.google.inject.name.Named
import mu.KotlinLogging
import java.util.UUID

internal class RutterWebhookServiceImpl @Inject constructor(
  @Named(RUTTER_WEBHOOK_TOPIC_NAME) private val publisher: EventPublisher<JsonNode>,
  private val rutterConnectionService: RutterConnectionService,
  private val rutterPayoutSyncTaskService: RutterPayoutSyncTaskService,
) : RutterWebhookService {
  private val logger = KotlinLogging.logger {}

  /**
   * Handle an incoming Rutter webhook.
   *
   * We currently only handle webhooks with type "CONNECTION" and code "INITIAL_UPDATE",
   * which indicates that <PERSON><PERSON> has finished their initial sync of data for a new
   * connection and that we can now perform our own syncs from their data.
   *
   * This currently does just one thing - submits a payout sync task. This is async
   * work, so it doesn't warrant going through pubsub like most of our webhook handlers do.
   * If/when more types of webhooks are handled, we should probably modify this to use a pubsub
   * intermediary so that we don't rely on Rutter's automatic webhook retries.
   */
  override suspend fun processWebhook(wrapper: JsonNode) {
    logger.info { "[Rutter Webhook] Emitting Rutter webhook. Event=$wrapper" }

    publishWebhook(wrapper)

    val webhookType = checkNotNull(wrapper.get("type")).asText()
    val webhookCode = checkNotNull(wrapper.get("code")).asText()
    if (webhookType != "CONNECTION" || webhookCode != "INITIAL_UPDATE") {
      logger.info { "Ignoring unhandled webhook with type $webhookType and code $webhookCode" }
      return
    }
    val rutterConnectionGuid = UUID.fromString(wrapper.get("connection_id").asText())
    val connection = rutterConnectionService.getByRutterConnectionGuid(rutterConnectionGuid)
    if (connection == null) {
      logger.warn { "Connection not found. Ignoring webhook." }
      return
    }
    logger.info {
      "Webhook notifies us that initial update for Rutter connection guid " +
        "$rutterConnectionGuid has finished; triggering payout sync task."
    }
    rutterPayoutSyncTaskService.createForBusinessGuidSelection(
      uuidSelection = UUIDSelection.Explicit(
        guids = listOf(connection.businessGuid),
      ),
    )
  }

  private fun publishWebhook(wrapper: JsonNode) {
    val webhookType = checkNotNull(wrapper.get("type")).textValue()
    publisher.publishEvent(wrapper, mapOf("type" to webhookType))
  }
}
