package co.highbeam.service.shopify

import co.highbeam.model.shopify.ShopifyBalanceModel
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(ShopifyBalanceServiceImpl::class)
internal interface ShopifyBalanceService {
  fun upsert(
    businessGuid: UUID, connectionGuid: UUID, balance: ShopifyBalanceModel
  ): ShopifyBalanceModel

  fun getByBusiness(businessGuid: UUID): List<ShopifyBalanceModel>
}
