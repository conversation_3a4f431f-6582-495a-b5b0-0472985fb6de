package co.highbeam.store.shopify

import co.highbeam.model.shopify.DailyShopifySummaryModel
import co.highbeam.rep.shopify.DailyShopifySummaryRep
import co.highbeam.sql.store.SqlStore
import co.highbeam.util.testing.TestingOnly
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging
import org.jdbi.v3.core.Handle
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.time.LocalDate
import java.util.UUID

@Singleton
internal class DailyShopifySummaryStore @Inject constructor(
  jdbi: Jdbi,
  private val uuidGenerator: UuidGenerator,
) : SqlStore(jdbi) {
  private val logger = KotlinLogging.logger {}

  fun setForDate(
    businessGuid: UUID,
    connectionGuid: UUID,
    date: LocalDate,
    summaries: List<DailyShopifySummaryModel.Creation>,
  ): List<DailyShopifySummaryRep.Complete> =
    transaction { handle ->
      handle.deleteByDate(businessGuid, connectionGuid, date)
      handle.create(businessGuid, connectionGuid, date, summaries)
      return@transaction handle.getByDate(businessGuid, connectionGuid, date)
    }

  private fun Handle.create(
    businessGuid: UUID,
    connectionGuid: UUID,
    date: LocalDate,
    summaries: List<DailyShopifySummaryModel.Creation>,
  ) {
    val batch = prepareBatch(sqlResource("store/dailyShopifySummary/create.sql"))
    summaries.forEach { summary ->
      batch.bind("guid", uuidGenerator.generate())
      batch.bind("businessGuid", businessGuid)
      batch.bind("connectionGuid", connectionGuid)
      batch.bind("date", date)
      batch.bindKotlin(summary)
      batch.add()
    }
    batch.execute().also {
      logger.info { "Created $it summaries." }
    }
  }

  private fun Handle.getByDate(
    businessGuid: UUID,
    connectionGuid: UUID,
    date: LocalDate,
  ): List<DailyShopifySummaryRep.Complete> {
    val query = createQuery(sqlResource("store/dailyShopifySummary/getByDate.sql"))
    query.bind("businessGuid", businessGuid)
    query.bind("connectionGuid", connectionGuid)
    query.bind("date", date)
    return query.mapTo(DailyShopifySummaryRep.Complete::class.java).toList()
  }

  @TestingOnly
  fun getByBusiness(businessGuid: UUID): List<DailyShopifySummaryRep.Complete> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/dailyShopifySummary/getByBusiness.sql"))
      query.bind("businessGuid", businessGuid)
      return@handle query.mapTo(DailyShopifySummaryRep.Complete::class.java).toList()
    }

  fun getByBusinessInDateRange(
    businessGuid: UUID,
    fromDate: LocalDate,
    toDate: LocalDate,
  ): List<DailyShopifySummaryRep.Complete> =
    handle { handle ->
      val query = handle.createQuery(sqlResource(
        "store/dailyShopifySummary/getByBusinessInDateRange.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("fromDate", fromDate)
      query.bind("toDate", toDate)
      return@handle query.mapTo(DailyShopifySummaryRep.Complete::class.java).toList()
    }

  private fun Handle.deleteByDate(businessGuid: UUID, connectionGuid: UUID, date: LocalDate) {
    val update = createUpdate(sqlResource("store/dailyShopifySummary/deleteByDate.sql"))
    update.bind("businessGuid", businessGuid)
    update.bind("connectionGuid", connectionGuid)
    update.bind("date", date)
    update.execute().also {
      logger.info { "Deleted $it summaries." }
    }
  }
}
