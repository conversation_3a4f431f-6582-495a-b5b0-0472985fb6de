package co.highbeam.service.shopify

import co.highbeam.model.shopify.ShopifyTransactionModel
import com.google.inject.ImplementedBy
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID

@ImplementedBy(ShopifyTransactionServiceImpl::class)
internal interface ShopifyTransactionService {
  fun upsert(businessGuid: UUID, connectionGuid: UUID, transactions: List<ShopifyTransactionModel>)

  fun getLatestFinalized(businessGuid: UUID, connectionGuid: UUID): ShopifyTransactionModel?

  fun getLatestOlderThanDateOrOldest(
    businessGuid: UUID,
    connectionGuid: UUID,
    date: ZonedDateTime
  ): ShopifyTransactionModel?

  fun getByDate(
    businessGuid: UUID,
    connectionGuid: UUID,
    date: LocalDate,
  ): List<ShopifyTransactionModel>
}
