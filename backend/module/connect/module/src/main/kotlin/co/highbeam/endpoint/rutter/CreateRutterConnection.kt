package co.highbeam.endpoint.rutter

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.rutter.RutterConnectionMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.rutter.RutterConnectionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.rutter.RutterConnectionApi as Api
import co.highbeam.rep.rutter.RutterConnectionRep as Rep

internal class CreateRutterConnection @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val connectionMapper: RutterConnectionMapper,
  private val connectionService: RutterConnectionService,
) : EndpointHandler<Api.Create, Rep.Complete>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall,
  ): Api.Create = Api.Create(
    businessGuid = call.getParam("businessGuid"),
    creator = call.body()
  )

  override fun loggingContext(endpoint: Api.Create) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Create): Rep.Complete {
    auth(authPermission(Permission.StoreConnection_Create) { endpoint.businessGuid })
    return connectionMapper.map(
      connectionService.createAccessToken(
        businessGuid = endpoint.businessGuid,
        creator = endpoint.creator,
      )
    )
  }
}
