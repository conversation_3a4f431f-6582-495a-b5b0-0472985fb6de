package co.highbeam.rep.event.connect

import co.highbeam.event.HighbeamEvent
import co.highbeam.money.Balance
import java.time.ZonedDateTime
import java.util.UUID

data class TransactionSettledEvent(
  val createdAt: ZonedDateTime,
  val businessGuid: UUID,
  val bankAccountGuid: UUID,
  val unitCoTransactionId: String,
  val transactionType: String,
  val amount: Balance,
  val summary: String,
) : HighbeamEvent()
