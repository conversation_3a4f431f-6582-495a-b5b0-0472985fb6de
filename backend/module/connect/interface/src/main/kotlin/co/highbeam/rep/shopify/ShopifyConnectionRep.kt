package co.highbeam.rep.shopify

import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.util.shopify.getShopSubdomainFromDomain
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import java.time.ZonedDateTime
import java.util.UUID

enum class ShopifyConnectionStatus {
  Active,
  Deleted,
  Uninstalled,
}

object ShopifyConnectionRep {
  /**
   * See https://shopify.dev/apps/auth/oauth#step-4-confirm-installation.
   *
   * [accessCode] The authorization_code.
   * [hmac] The literal hmac parameter from Shopify.
   * [hmacMessage] The query string from Shopify without the hmac entry, as per the documentation at
   *  https://shopify.dev/apps/auth/oauth/making-requests#verification.
   * [state] The nonce we generated earlier.
   */
  data class Creator(
    val accessCode: ProtectedString,
    val businessGuid: UUID?,
    val hmac: String,
    val hmacMessage: String,
    val shopHost: String,
    val state: String,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()

    val shopSubdomain: String = getShopSubdomainFromDomain(shopHost)
  }

  data class Complete(
    val guid: UUID,
    val shopName: String,
    val shopSubdomain: String,
    val hasSyncedOrders: Boolean,
    val hasSyncedTransactions: Boolean,
    val hasSyncedPayouts: Boolean,
    val hasSyncedBalances: Boolean,
    val connectionBannerDisabled: Boolean,
    val isActive: Boolean,
    val lastPayoutAt: ZonedDateTime? = null,
    val hasAllPermissions: Boolean,
    val isShopifyPaymentsEnabled: Boolean,
    val missingPermissions: Set<String>,
  ) : CompleteRep

  data class Updater(
    val connectionBannerDisabled: Boolean? = null,
    val payoutAlias: String? = null,
    val isShopifyPaymentsEnabled: Boolean? = null,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::connectionBannerDisabled) {
        ifPresent { this }
      }
      validate(Updater::payoutAlias) {
        ifPresent { Validator.UserInput.short(this, allowEmpty = false) }
      }
      validate(Updater::isShopifyPaymentsEnabled) {
        ifPresent { this }
      }
    }
  }
}
