package co.highbeam.client.shopify

import co.highbeam.api.shopify.ShopifyPayoutApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.connect.CONNECT_FEATURE
import co.highbeam.rep.shopify.ShopifyPayoutRep
import com.google.inject.Inject
import com.google.inject.name.Named

class ShopifyPayoutClient @Inject constructor(
  @Named(CONNECT_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(
    endpoint: ShopifyPayoutApi.GetByBusiness,
  ): List<ShopifyPayoutRep.Complete> =
    httpClient.request(endpoint).readValue()
}
