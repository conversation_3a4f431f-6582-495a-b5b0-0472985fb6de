package co.highbeam.client.rutter

import co.highbeam.api.rutter.RutterBalanceApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.connect.CONNECT_FEATURE
import co.highbeam.rep.rutter.RutterBalanceRep
import com.google.inject.Inject
import com.google.inject.name.Named

class InternalRutterBalanceClient @Inject constructor(
  @Named(CONNECT_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(
    endpoint: RutterBalanceApi.GetCurrentBalances,
  ): List<RutterBalanceRep.Complete> =
    httpClient.request(endpoint).readValue()
}
