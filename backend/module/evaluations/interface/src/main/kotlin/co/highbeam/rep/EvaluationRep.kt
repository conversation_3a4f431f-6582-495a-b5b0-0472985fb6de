package co.highbeam.rep

import co.highbeam.validation.RepValidation
import java.time.ZonedDateTime
import java.util.UUID

data class EvaluationRep(
  val guid: UUID,
  val businessGuid: UUID,
  val status: Status,
  val lastUpdatedAt: ZonedDateTime,
  val metadata: EvaluationMetadata?,
) {
  enum class Status {
    InReview,
    Approved,
    Rejected,
  }

  data class Creator(
    val businessGuid: UUID,
    val status: Status,
    val metadata: EvaluationMetadata?,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Updater(
    val status: Status,
    val metadata: EvaluationMetadata?,
    val sendNotification: Boolean = true,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
