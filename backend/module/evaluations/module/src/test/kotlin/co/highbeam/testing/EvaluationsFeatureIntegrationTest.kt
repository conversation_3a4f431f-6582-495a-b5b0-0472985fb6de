package co.highbeam.testing

import co.highbeam.client.evaluation.EvaluationClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.EvaluationsFeatureTestConfig
import co.highbeam.event.FakeEventFeature
import co.highbeam.feature.evaluations.EvaluationsFeature
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext

@ExtendWith(EvaluationsFeatureIntegrationTest.Extension::class)
internal abstract class EvaluationsFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private class MockFeature(
    val fakeFeatureFlagService: FakeFeatureFlagService) : AbstractMockFeature() {
    override fun bind() {
      mock(FeatureFlagService::class, fakeFeatureFlagService)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: EvaluationsFeatureTestConfig = ConfigLoader.load("test")
      val eventFeature = FakeEventFeature
      val fakeFeatureFlagService = FakeFeatureFlagService()
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "evaluations",
      )
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<EvaluationsFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            eventFeature,
            sqlFeature,
            MockFeature(fakeFeatureFlagService),
            EvaluationsFeature(),
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
      fakeFeatureFlagService.reset()
      eventFeature.reset()
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val evaluationClient: EvaluationClient by lazy {
    EvaluationClient(httpClient)
  }
}
