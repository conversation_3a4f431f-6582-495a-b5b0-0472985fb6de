package co.highbeam.mapper

import co.highbeam.model.EvaluationModel
import co.highbeam.rep.EvaluationRep
import com.google.inject.Inject
import java.time.Clock
import java.time.ZonedDateTime

internal class EvaluationMapper @Inject constructor(
  private val clock: Clock,
) {
  fun toCreatorModel(rep: EvaluationRep.Creator): EvaluationModel.Creator =
    EvaluationModel.Creator(
      businessGuid = rep.businessGuid,
      status = rep.status,
      metadata = rep.metadata,
      lastUpdatedAt = ZonedDateTime.now(clock),
    )

  fun toRep(model: EvaluationModel): EvaluationRep =
    EvaluationRep(
      guid = model.guid,
      businessGuid = model.businessGuid,
      status = model.status,
      metadata = model.metadata,
      lastUpdatedAt = model.lastUpdatedAt,
    )

  fun toUpdate(rep: EvaluationRep.Updater): EvaluationModel.Updater =
    EvaluationModel.Updater(
      status = rep.status,
      metadata = rep.metadata,
      lastUpdatedAt = ZonedDateTime.now(clock),
    )
}
