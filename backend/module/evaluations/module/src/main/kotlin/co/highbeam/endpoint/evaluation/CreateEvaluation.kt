package co.highbeam.endpoint.evaluation

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.mapper.EvaluationMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.EvaluationRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.EvaluationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.evaluation.EvaluationApi as Api

internal class CreateEvaluation @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val evaluationMapper: EvaluationMapper,
  private val evaluationService: EvaluationService,
) : EndpointHandler<Api.Create, EvaluationRep>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create =
    Api.Create(creator = call.body())

  override fun loggingContext(endpoint: Api.Create) = super.loggingContext(endpoint) +
    mapOf(
      "businessGuid" to endpoint.creator.businessGuid.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.Create): EvaluationRep {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val evaluation = evaluationService.create(endpoint.creator)

    return evaluationMapper.toRep(evaluation)
  }
}
