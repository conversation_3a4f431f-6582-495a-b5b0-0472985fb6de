package co.highbeam.api.payment

import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.rep.payment.InternationalPaymentRep
import co.highbeam.rep.payment.InternationalPaymentReturnRep
import co.highbeam.rep.payment.InternationalQuoteRep
import co.highbeam.rep.payment.PaymentRep
import co.highbeam.rep.payment.WirePaymentRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod

object PaymentApi {
  data class CreateAch(val creator: AchPaymentRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/ach",
    body = creator,
  )

  data class CreateScheduledAch(val creator: AchPaymentRep.ScheduledCreator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/ach/schedule",
    body = creator,
  )

  data class CreateWire(val creator: WirePaymentRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/wire",
    body = creator,
  )

  data class CreateInternationalWire(
    val creator: InternationalPaymentRep.Creator
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/international-wire",
    body = creator,
  )

  data class CreateInternationalWireReturn(
    val creatorReturn: InternationalPaymentReturnRep.Creator
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/international-wire/return",
    body = creatorReturn,
  )

  data class CreatePayment(
    val creator: PaymentRep.Creator
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment",
    body = creator,
  ) {
    val unitCoAccountId: String?
      get() =
        creator.unitPayload
          .get("relationships")
          ?.get("account")
          ?.get("data")
          ?.get("id")
          ?.textValue()
  }

  data class GetInternationalWireQuote(
    val creator: InternationalQuoteRep.Creator
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment/international-wire/quote",
    body = creator,
  )
}
