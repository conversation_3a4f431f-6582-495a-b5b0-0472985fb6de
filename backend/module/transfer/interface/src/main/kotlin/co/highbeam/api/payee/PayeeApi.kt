package co.highbeam.api.payee

import co.highbeam.rep.payee.PayeeRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object PayeeApi {
  data class Create(val businessGuid: UUID, val creator: PayeeRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/businesses/$businessGuid/payees",
    body = creator,
  )

  data class Get(val businessGuid: UUID, val payeeGuid: UUID) : Endpoint(
    path = "/businesses/$businessGuid/payees/$payeeGuid",
  )

  data class GetByBusiness(
    val businessGuid: UUID,
    val status: PayeeRep.Status? = null,
  ) : Endpoint(
    path = "/businesses/$businessGuid/payees",
    qp = buildMap {
      status?.let { put("status", listOf(status.toString())) }
    },
  )

  data class Update(
    val businessGuid: UUID,
    val payeeGuid: UUID,
    val update: PayeeRep.Updater,
  ) : Endpoint(
    httpMethod = HttpMethod.Patch,
    path = "/businesses/$businessGuid/payees/$payeeGuid",
    body = update,
  )

  data class Delete(
    val businessGuid: UUID,
    val payeeGuid: UUID,
  ) : Endpoint(
    httpMethod = HttpMethod.Delete,
    path = "/businesses/$businessGuid/payees/$payeeGuid",
  )
}
