package co.highbeam.api.paymentDetails

import co.highbeam.rep.paymentDetails.PaymentDetailsRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object PaymentDetailsApi {
  data class BatchGet(val rep: PaymentDetailsRep.BatchGet) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/payment-details/batch-get",
    body = rep,
  )

  data class Get(val businessGuid: UUID, val paymentMetadataGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/payment-details/$businessGuid/$paymentMetadataGuid",
  )

  data class GetPaymentReceipt(
    val businessGuid: UUID,
    val paymentMetadataGuid: UUID
  ) : Endpoint(
    httpMethod = HttpMethod.Get,
    path = "/payment-details/$paymentMetadataGuid/receipt",
    qp = mapOf("businessGuid" to listOf(businessGuid.toString())),
  )
}

