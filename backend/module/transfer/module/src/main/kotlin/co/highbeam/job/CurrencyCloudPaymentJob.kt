package co.highbeam.job

import co.highbeam.featureFlags.AnonymousFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.metrics.Metrics
import co.highbeam.model.paymentMetadata.PaymentMetadataModel
import co.highbeam.service.payment.CURRENCY_CLOUD_SLACK_WEBHOOK_PATH
import co.highbeam.service.payment.PaymentMetadataService
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.exception.CurrencyCloudException
import com.google.inject.Inject
import com.google.inject.name.Named
import com.slack.client.SlackMessageClient
import mu.KotlinLogging
import java.time.Clock
import java.time.ZoneId
import java.time.ZonedDateTime
import co.highbeam.job.CurrencyCloudPaymentJob as Job

internal class CurrencyCloudPaymentJob @Inject constructor(
  private val currencyCloudClient: CurrencyCloudClient,
  private val featureFlagService: FeatureFlagService,
  private val paymentMetadataService: PaymentMetadataService,
  @Named(CURRENCY_CLOUD_SLACK_WEBHOOK_PATH) private val currencyCloudWebhookPath: String,
  private val slackMessageClient: SlackMessageClient,
  private val clock: Clock,
  metrics: Metrics,
) : HighbeamJob<Job.Params>() {
  private val logger = KotlinLogging.logger {}

  private val serviceUnavailableCounter = metrics.counter(
    "service_unavailable_count",
    "service_name", "CurrencyCloud",
    "jobName", "CurrencyCloudPaymentJob",
  ) {}
  private val jobExecutedCounter = metrics.counter(
    "currency_cloud_job_execution_count",
    "jobName", "CurrencyCloudPaymentJob",
  ) {}
  private val paymentTrackedCounter = metrics.counter(
    "currency_cloud_payment_tracked",
    "jobName", "CurrencyCloudPaymentJob",
  ) {}

  object Params : HighbeamJob.Params()
  internal class Creator : HighbeamJob.Creator<Job, Params>() {
    override val job = Job::class
    override val params = Params::class
  }

  override suspend fun execute(params: Params) {
    logger.info { "Starting CurrencyCloudPaymentJob" }
    val payments = paymentMetadataService.getPendingCurrencyCloudPayments()
    payments.forEach {
      @Suppress("TooGenericExceptionCaught")
      try {
        val paymentStatus = currencyCloudClient.getPaymentTrackingInfo(
          it.currencyCloudPaymentId.toString()
        )
        if (paymentStatus.transactionStatus.status == "rejected") {
          slackMessageClient.sendMessage(
            key = null,
            webhookPath = currencyCloudWebhookPath,
            body = mapOf(
              "message" to "Payment has been rejected, ${paymentStatus.transactionStatus.reason}",
              "business_guid" to it.businessGuid.toString(),
              "amount" to it.currencyCloudPaymentAmount.toString(),
              "payment_metadata_guid" to it.guid.toString(),
              "beneficiary_guid" to it.currencyCloudBeneficiaryId.toString(),
              "description" to "paymentId - ${it.currencyCloudPaymentId}",
              "currency_cloud_account_id" to it.currencyCloudAccountId.toString(),
              "internal_description" to "international_wire_rejected",
            )
          )
        }

        val zonedNow = ZonedDateTime.now(clock)
        val paymentHasNotBeenUpdatedInFiveDays =
          paymentStatus.lastUpdateTime
            .toInstant()
            .atZone(ZoneId.of("UTC"))
            .isBefore(zonedNow.minusDays(5))
        val shouldSendOutSlackDelayedMessage =
          featureFlagService.isEnabledAnonymously(AnonymousFlag.CurrencyCloudDelayedMessage) &&
            paymentStatus.transactionStatus.status != "completed"
            &&
            paymentHasNotBeenUpdatedInFiveDays &&
            it.slackDelayedMessageSentAt == null

        if (shouldSendOutSlackDelayedMessage) {
          slackMessageClient.sendMessage(
            key = null,
            webhookPath = currencyCloudWebhookPath,
            body = mapOf(
              "message" to "Payment has not been updated in 5 days, please send a trace request" +
                " ${paymentStatus.transactionStatus.reason}",
              "business_guid" to it.businessGuid.toString(),
              "amount" to it.currencyCloudPaymentAmount.toString(),
              "payment_metadata_guid" to it.guid.toString(),
              "beneficiary_guid" to it.currencyCloudBeneficiaryId.toString(),
              "description" to "paymentId - ${it.currencyCloudPaymentId}",
              "currency_cloud_account_id" to it.currencyCloudAccountId.toString(),
              "internal_description" to "international_wire_delayed",
            )
          )
        }

        val update = PaymentMetadataModel.Update(
          currencyCloudPaymentTrackingStatus = paymentStatus.transactionStatus.status,
          currencyCloudPaymentTrackingReason = paymentStatus.transactionStatus.reason,
          slackDelayedMessageSentAt = if (shouldSendOutSlackDelayedMessage) {
            zonedNow
          } else {
            it.slackDelayedMessageSentAt
          },
        )
        paymentMetadataService.updatePaymentMetadata(it.guid, update)
      } catch (e: CurrencyCloudException) {
        handleCurrencyCloudError(e = e, payment = it)
      }
      paymentTrackedCounter.increment()
    }
    jobExecutedCounter.increment()
  }

  private fun handleCurrencyCloudError(e: CurrencyCloudException, payment: PaymentMetadataModel) {
    logger.warn(e) {
      "CurrencyCloud request failed for payment  ${payment.currencyCloudPaymentId}."
    }
    serviceUnavailableCounter.increment()
  }
}
