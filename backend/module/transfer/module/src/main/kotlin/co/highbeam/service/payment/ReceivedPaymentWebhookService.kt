package co.highbeam.service.payment

import co.highbeam.model.unitCoEvent.UnitCoReceivedPaymentCreatedEventModel
import co.highbeam.model.unitCoEvent.UnitCoReceivedPaymentMarkedForReturnEventModel
import com.google.inject.ImplementedBy

@ImplementedBy(ReceivedPaymentWebhookServiceImpl::class)
internal interface ReceivedPaymentWebhookService {
  suspend fun handleReceivedPaymentCreated(
    event: UnitCoReceivedPaymentCreatedEventModel
  )

  suspend fun handleReceivedPaymentMarkedForReturn(
    event: UnitCoReceivedPaymentMarkedForReturnEventModel
  )
}
