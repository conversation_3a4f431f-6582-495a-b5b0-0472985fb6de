package co.highbeam.endpoint.paymentDetails

import co.highbeam.api.paymentDetails.PaymentDetailsApi
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.paymentMetadata.PaymentMetadataMapper
import co.highbeam.rep.payment.PaymentMetadataRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.payment.PaymentMetadataService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentDetails.PaymentDetailsApi as Api

internal class BatchGetPaymentDetails @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val paymentMetadataMapper: PaymentMetadataMapper,
  private val paymentMetadataService: PaymentMetadataService,
) : EndpointHandler<Api.BatchGet, List<PaymentMetadataRep.Complete>>(
  template = Api.BatchGet::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.BatchGet = Api.BatchGet(
    rep = call.body()
  )

  override fun loggingContext(endpoint: Api.BatchGet) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.rep.businessGuid.toString(),
    "paymentMetadataGuids" to endpoint.rep.paymentMetadataGuids.toString(),
  )

  override suspend fun Handler.handle(
    endpoint: PaymentDetailsApi.BatchGet
  ): List<PaymentMetadataRep.Complete> {
    auth(authPermission(Permission.Transaction_Read) { endpoint.rep.businessGuid })
    return paymentMetadataService.batchGet(
      paymentMetadataGuids = endpoint.rep.paymentMetadataGuids,
      businessGuid = endpoint.rep.businessGuid
    ).map { paymentMetadataMapper.completeRep(it) }
  }
}
