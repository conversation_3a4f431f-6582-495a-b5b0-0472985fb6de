package co.highbeam.endpoint.payee

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.payee.PayeeMapper
import co.highbeam.mapper.payeePaymentMethod.PayeeCreatePaymentMethodMapper
import co.highbeam.rep.TransferRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.externalPayee.ExternalPayeeService
import co.highbeam.service.payee.PayeeService
import co.highbeam.service.payeePaymentMethod.PayeePaymentMethodService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.payee.PayeeApi as Api
import co.highbeam.rep.payee.PayeeRep as Rep

internal class CreatePayee @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val externalPayeeService: ExternalPayeeService,
  private val payeeMapper: Payee<PERSON>apper,
  private val payeePaymentMethodMapper: PayeeCreatePaymentMethodMapper,
  private val payeeService: PayeeService,
  private val payeePaymentMethodService: PayeePaymentMethodService,
) : EndpointHandler<Api.Create, Rep.Complete>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Create = Api.Create(
    businessGuid = call.getParam("businessGuid"),
    creator = call.body(),
  )

  override fun loggingContext(endpoint: Api.Create) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Create): Rep.Complete {
    auth(authPermission(Permission.Payee_Create) { endpoint.businessGuid })

    val payee = payeeService.upsert(
      payeeMapper.model(
        businessGuid = endpoint.businessGuid,
        rep = endpoint.creator,
      )
    )

    val payeePaymentMethods = payeePaymentMethodMapper.models(
      payeeGuid = payee.guid,
      businessGuid = endpoint.businessGuid,
      rep = endpoint.creator,
    ).map { payeePaymentMethodService.upsert(it) }

    val internationalWireTransferMethod = payeePaymentMethods.firstOrNull {
      it.paymentMethod == TransferRep.PaymentMethod.International
    }
    if (internationalWireTransferMethod != null) {
      externalPayeeService.upsertInternational(
        businessGuid = payee.businessGuid,
        internationalWireTransferMethod
      )
    }

    return payeeMapper.newCompleteRep(payee, payeePaymentMethods)
  }
}
