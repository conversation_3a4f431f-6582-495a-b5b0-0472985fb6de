package co.highbeam.model.currencyCloudEvent

import co.highbeam.serialization.readValueNotNull
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.ZonedDateTime

@JsonDeserialize(using = ConversionEventModel.Deserializer::class)
internal data class ConversionEventModel(
  override val eventId: String,
  override val type: String,
  override val createdAt: ZonedDateTime,
  val accountId: String,
  val coreRate: String,
  val clientRate: String,
  val creatorContactId: String,
  val currencyPair: String,
  val notificationType: String,
  val paymentIds: List<String>,
  val status: String,
) : CurrencyCloudEventModel() {

  internal class Deserializer : StdDeserializer<ConversionEventModel>(
    ConversionEventModel::class.java,
  ) {
    override fun deserialize(
      p: JsonParser,
      ctxt: DeserializationContext,
    ): ConversionEventModel {
      val tree = p.readValueAsTree<JsonNode>()
      val headers = tree.get("header")
      val body = tree.get("body")

      return ConversionEventModel(
        eventId = body.readValueNotNull(ctxt, "id"),
        type = headers.readValueNotNull(ctxt, "message_type"),
        createdAt = body.readValueNotNull(ctxt, "created_at"),
        accountId = body.readValueNotNull(ctxt, "account_id"),
        coreRate = body.readValueNotNull(ctxt, "core_rate"),
        creatorContactId = body.readValueNotNull(ctxt, "creator_contact_id"),
        clientRate = body.readValueNotNull(ctxt, "client_rate"),
        currencyPair = body.readValueNotNull(ctxt, "currency_pair"),
        notificationType = headers.readValueNotNull(ctxt, "notification_type"),
        paymentIds = body.readValueNotNull(ctxt, "payment_ids"),
        status = body.readValueNotNull(ctxt, "status"),
      )
    }
  }
}
