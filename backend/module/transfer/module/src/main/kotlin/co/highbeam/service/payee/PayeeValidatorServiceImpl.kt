package co.highbeam.service.payee

import co.highbeam.exception.payee.PayeeValidationIncorrectCurrency
import co.highbeam.mapper.payeePaymentMethod.PayeePaymentMethodValidatorMapper
import co.highbeam.rep.payee.PayeeRep
import co.highbeam.rep.payee.PayeeValidatorRep
import com.currencycloud.client.CurrencyCloudClient
import com.google.inject.Inject
import mu.KotlinLogging

internal class PayeeValidatorServiceImpl @Inject constructor(
  private val currencyCloudClient: CurrencyCloudClient,
  private val payeeValidatorMethodMapper: PayeePaymentMethodValidatorMapper
) : PayeeValidatorService {
  private val logger = KotlinLogging.logger {}

  override fun validate(payeeInfo: PayeeValidatorRep.Validate): PayeeValidatorRep.Complete {
    logger.info { "Validating payee info" }
    val internationalWireTransferMethod = payeeInfo.internationalWireTransferMethod
    if (internationalWireTransferMethod != null) {
      if (internationalWireTransferMethod.currency == "GBP") {
        return validateGbpBeneficiary(internationalWireTransferMethod)
      }
    }
    return PayeeValidatorRep.Complete(
      message = "No validation implemented for account details provided",
      type = PayeeValidatorRep.MessageType.Success
    )
  }

  private fun validateGbpBeneficiary(
    internationalWireTransferMethod: PayeeRep.InternationalWireTransferMethod
  ): PayeeValidatorRep.Complete {
    if (internationalWireTransferMethod.currency != "GBP") {
      logger.error { "Invalid currency for validation" }
      throw PayeeValidationIncorrectCurrency()
    }
    val verifiedAccount = currencyCloudClient.verifyAccount(
      payeeValidatorMethodMapper.accountVerification(internationalWireTransferMethod)
    )
    val (message, messageType) = when (verifiedAccount.answer) {
      "no_match" -> when (verifiedAccount.reasonCode) {
        "AV201" ->
          "Bank account holder name does not match account details." to
            PayeeValidatorRep.MessageType.Error
        else -> "No match found for account details" to PayeeValidatorRep.MessageType.Error
      }
      "close_match" -> when (verifiedAccount.reasonCode) {
        "AV300" ->
          "Close match found for account details. Suggested name: ${verifiedAccount.actualName}" to
            PayeeValidatorRep.MessageType.Warning
        "AV301", "AV303" ->
          "Close match found for account details. Suggested type: Company" to
            PayeeValidatorRep.MessageType.Warning
        "AV302", "AV304" ->
          "Close match found for account details. Suggested type: Individual" to
            PayeeValidatorRep.MessageType.Warning
        "AV305" ->
          "Close match found for account details. Account has moved organisations." to
            PayeeValidatorRep.MessageType.Warning
        else ->
          "Close match found for account details. Please verify bank details." to
            PayeeValidatorRep.MessageType.Warning
      }
      "full_match" ->
        "Full match found for account details" to PayeeValidatorRep.MessageType.Success
      else -> "Unknown account verification result" to PayeeValidatorRep.MessageType.Success
    }
    return PayeeValidatorRep.Complete(message = message, type = messageType)
  }
}
