package co.highbeam.service.externalPayee

import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.exception.payee.ExternalPayeeBadRequest
import co.highbeam.exception.unprocessable
import co.highbeam.mapper.payeePaymentMethod.PayeeUpdatePaymentMethodMapper
import co.highbeam.model.payeePaymentMethod.PayeePaymentMethodModel
import co.highbeam.rep.TransferRep
import co.highbeam.service.payeePaymentMethod.PayeePaymentMethodService
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.exception.CurrencyCloudException
import com.currencycloud.client.model.Beneficiary
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class ExternalPayeeServiceImpl @Inject constructor(
  private val currencyCloudClient: CurrencyCloudClient,
  private val internationalBankAccountClient: InternationalBankAccountClient,
  private val payeeUpdatePaymentMethodMapper: PayeeUpdatePaymentMethodMapper,
  private val payeePaymentMethodService: PayeePaymentMethodService,
) : ExternalPayeeService {
  private val logger = KotlinLogging.logger {}

  // The client surfaces the error message so we rethrow it as a ExternalPayeeBadRequest
  // and give a more generic error message
  @SuppressWarnings("SwallowedException")
  override suspend fun upsertInternational(
    businessGuid: UUID,
    paymentMethod: PayeePaymentMethodModel
  ) {
    if (paymentMethod.paymentMethod != TransferRep.PaymentMethod.International) return
    logger.info {
      "Updating payment method: $paymentMethod" +
        " for payee: ${paymentMethod.payeeGuid}," +
        " for business: $businessGuid"
    }
    val internationalBankAccount = internationalBankAccountClient.request(
      endpoint = InternationalBankAccountApi.GetByBusinessGuid(businessGuid)
    ) ?: throw unprocessable(InternationalBankAccountNotFound())
    val beneficiary = payeeUpdatePaymentMethodMapper.currencyCloudBeneficiary(paymentMethod)
    currencyCloudClient.onBehalfOfDo(
      internationalBankAccount.currencyCloudContactGuid.toString()
    ) {
      try {
        if (beneficiary?.id == null) {
          createCurrencyCloudBeneficiary(beneficiary, paymentMethod)
        } else {
          updateCurrencyCloudBeneficiary(beneficiary, paymentMethod)
        }
      } catch (e: CurrencyCloudException) {
        logger.error(e) { "Could not create or update beneficiary" }
        throw ExternalPayeeBadRequest(e.message ?: "Invalid account details", e)
      }
    }
  }

  private fun createCurrencyCloudBeneficiary(
    beneficiary: Beneficiary?,
    paymentMethod: PayeePaymentMethodModel
  ) {
    val createdBeneficiary = currencyCloudClient.createBeneficiary(beneficiary)

    val updatedPaymentMethod = paymentMethod.copy(
      currencyCloudBeneficiaryId = UUID.fromString(
        createdBeneficiary.id
      )
    )
    payeePaymentMethodService.upsert(updatedPaymentMethod)
  }

  @SuppressWarnings("SwallowedException")
  private fun updateCurrencyCloudBeneficiary(
    beneficiary: Beneficiary?,
    paymentMethod: PayeePaymentMethodModel
  ) {
    try {
      currencyCloudClient.updateBeneficiary(beneficiary)
    } catch (e: CurrencyCloudException) {
      // Currencycloud does not allow updating a beneficiary type (individual vs company)
      // In the event that this does happen, we create a new beneficiary
      createCurrencyCloudBeneficiary(beneficiary, paymentMethod)
    }
  }
}
