package co.highbeam.endpoint.payment

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.payment.InternationalPaymentService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.payment.PaymentApi as Api
import co.highbeam.rep.payment.InternationalQuoteRep as Rep

internal class GetInternationalWireQuote @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val internationalPaymentService: InternationalPaymentService,
) : EndpointHandler<Api.GetInternationalWireQuote, Rep.Complete>(
  template = Api.GetInternationalWireQuote::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall) = Api.GetInternationalWireQuote(
    creator = call.body(),
  )

  override fun loggingContext(endpoint: Api.GetInternationalWireQuote) =
    super.loggingContext(endpoint) +
      mapOf(
        "businessGuid" to endpoint.creator.businessGuid.toString(),
        "currency" to endpoint.creator.currency
      )

  override suspend fun Handler.handle(endpoint: Api.GetInternationalWireQuote): Rep.Complete {
    auth(authPermission(Permission.InternationalWireQuote_Create) { endpoint.creator.businessGuid })
    return internationalPaymentService.getInternationalWireQuote(endpoint.creator.currency)
  }
}
