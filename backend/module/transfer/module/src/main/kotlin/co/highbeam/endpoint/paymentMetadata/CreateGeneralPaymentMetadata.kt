package co.highbeam.endpoint.paymentMetadata

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.paymentMetadata.GeneralPaymentMetadataMapper
import co.highbeam.rep.paymentMetadata.GeneralPaymentMetadataRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentMetadata.GeneralPaymentMetadataService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentMetadata.GeneralPaymentMetadataApi as Api

internal class CreateGeneralPaymentMetadata @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val paymentMetadataMapper: GeneralPaymentMetadataMapper,
  private val paymentMetadataService: GeneralPaymentMetadataService,
) : EndpointHandler<Api.Create, GeneralPaymentMetadataRep.Complete>(
  template = Api.Create::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall
  ): Api.Create = Api.Create(
    businessGuid = call.getParam("businessGuid"),
    unitCoDepositAccountId = call.getParam("unitCoDepositAccountId", optional = true),
    transactionId = call.getParam("transactionId", optional = true),
    rep = call.body()
  )

  override suspend fun Handler.handle(
    endpoint: Api.Create
  ): GeneralPaymentMetadataRep.Complete {
    authSome(
      authPermission(Permission.TransactionMetadata_Write) { endpoint.businessGuid },
      authPermission(Permission.TransactionMetadata_WriteOwn) { endpoint.businessGuid },
      authPermission(Permission.TransactionMetadata_WriteAny) { endpoint.businessGuid },
    )

    val paymentMetadata = paymentMetadataService.create(
      unitCoDepositAccountId = endpoint.unitCoDepositAccountId,
      transactionId = endpoint.transactionId,
      model = paymentMetadataMapper.creatorModel(endpoint.businessGuid, endpoint.rep))
    return paymentMetadataMapper.completeRep(paymentMetadata)
  }
}
