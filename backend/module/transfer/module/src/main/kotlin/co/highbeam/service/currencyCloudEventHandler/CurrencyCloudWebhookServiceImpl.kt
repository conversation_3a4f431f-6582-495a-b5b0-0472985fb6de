package co.highbeam.service.currencyCloudEventHandler

import co.highbeam.metrics.Metrics
import co.highbeam.model.currencyCloudEvent.ConversionEventModel
import co.highbeam.model.currencyCloudEvent.CurrencyCloudEventModel
import co.highbeam.model.currencyCloudEvent.CurrencyCloudIgnoredEventModel
import co.highbeam.model.currencyCloudEvent.PaymentEventModel
import co.highbeam.model.currencyCloudEvent.TransactionEventModel
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import mu.KotlinLogging

internal class CurrencyCloudWebhookServiceImpl @Inject constructor(
  private val objectMapper: ObjectMapper,
  private val currencyCloudEventHandler: CurrencyCloudEventHandler,
  private val metrics: Metrics,
) : CurrencyCloudWebhookService {
  private val logger = KotlinLogging.logger {}

  override suspend fun handleEvent(eventJson: JsonNode) {
    val event = readEvent(eventJson)
    handleEvent(event)
  }

  private fun readEvent(eventJson: JsonNode): CurrencyCloudEventModel {
    val headers = eventJson.get("header")
    val eventType = headers.get("message_type").asText()

    metrics.counter(
      "currency_cloud_webhook_count",
      "event_type", eventType,
    ) {}.increment()

    return when (eventType) {
      "cash_manager_transaction" ->
        objectMapper.convertValue<TransactionEventModel>(eventJson)
      "conversion" -> objectMapper.convertValue<ConversionEventModel>(eventJson)
      "payment" -> objectMapper.convertValue<PaymentEventModel>(eventJson)
      else -> objectMapper.convertValue<CurrencyCloudIgnoredEventModel>(eventJson)
    }
  }

  private suspend fun handleEvent(event: CurrencyCloudEventModel) {
    // We generally don't want to catch the base Exception class, but here we're doing it for
    // resiliency against webhook processing errors.
    @Suppress("TooGenericExceptionCaught")
    try {
      currencyCloudEventHandler.handle(event)
    } catch (e: UnrecognizedCurrencyCloudEventType) {
      logger.info(e) { "Ignoring webhook event $event" }
      return
    } catch (e: Exception) {
      logger.error(e) { "Failed to handle webhook event $event." }
      throw e
    }
  }
}
