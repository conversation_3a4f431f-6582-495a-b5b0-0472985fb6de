package co.highbeam.service.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.business.BusinessIsNotOnboardedWithHighbeamYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.exception.payment.InternationalQuoteNotFound
import co.highbeam.exception.payment.InternationalWireBeneficiaryNotFound
import co.highbeam.exception.payment.InternationalWireConversionFailed
import co.highbeam.exception.payment.InternationalWireFundingCancelFailed
import co.highbeam.exception.payment.InternationalWireFundingFailed
import co.highbeam.exception.payment.InternationalWirePaymentCreationFailed
import co.highbeam.exception.payment.InternationalWirePaymentMethodCurrencyMismatch
import co.highbeam.exception.payment.InternationalWirePaymentMethodNotFound
import co.highbeam.exception.payment.InternationalWireQuoteMismatch
import co.highbeam.exception.payment.InternationalWireQuoteNotFound
import co.highbeam.exception.paymentMetadata.InternationalPaymentMetadataNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.amountWithFees
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.toUnitCoAchCreation
import co.highbeam.mapper.payment.InternationalPaymentUnitCoMapper.toUnitCoWireCreation
import co.highbeam.metrics.Metrics
import co.highbeam.model.payeePaymentMethod.PayeePaymentMethodModel
import co.highbeam.model.paymentMetadata.PaymentMetadataModel
import co.highbeam.money.Money
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.TransferRep
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.internationalBankAccount.InternationalBankAccountRep
import co.highbeam.rep.payee.PayeeRep
import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.rep.payment.CURRENCY_RATE
import co.highbeam.rep.payment.CURRENCY_RATE_EPSILON
import co.highbeam.rep.payment.ENABLED_BUY_CURRENCIES
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_FUNDING_WIRE_ADDRESS
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_FUNDING_WIRE_REQUIREMENT
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_PRIORITY_FEE
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_REGULAR_FEE
import co.highbeam.rep.payment.InternationalPaymentRep
import co.highbeam.rep.payment.InternationalQuoteRep
import co.highbeam.rep.payment.PaymentMetadataRep
import co.highbeam.rep.payment.WirePaymentRep
import co.highbeam.service.payeePaymentMethod.PayeePaymentMethodService
import co.highbeam.util.string.fullName
import co.highbeam.util.uuid.UuidGenerator
import co.highbeam.util.uuid.asByteArray
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoAchPaymentRep
import co.unit.rep.UnitCoWirePaymentRep
import co.unit.rep.UnitPaymentRep
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.exception.CurrencyCloudException
import com.currencycloud.client.model.Conversion
import com.currencycloud.client.model.ConversionCancellation
import com.currencycloud.client.model.Payment
import com.google.inject.Inject
import com.google.inject.name.Named
import com.slack.client.SlackMessageClient
import mu.KotlinLogging
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Clock
import java.time.ZonedDateTime
import java.util.Currency
import java.util.Date
import java.util.UUID
import kotlin.math.abs

@Suppress("LargeClass", "TooManyFunctions")
internal class InternationalPaymentServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val clock: Clock,
  private val currencyCloudClient: CurrencyCloudClient,
  @Named(CURRENCY_CLOUD_SLACK_WEBHOOK_PATH) private val currencyCloudWebhookPath: String,
  private val internationalBankAccountClient: InternationalBankAccountClient,
  private val metrics: Metrics,
  private val paymentService: PaymentService,
  private val paymentMetadataService: PaymentMetadataService,
  private val payeePaymentMethodService: PayeePaymentMethodService,
  private val slackMessageClient: SlackMessageClient,
  private val unitCoClient: UnitCoClient,
  private val uuidGenerator: UuidGenerator,
) : InternationalPaymentService {
  private val logger = KotlinLogging.logger {}

  @Suppress("LongMethod")
  override suspend fun createInternationalWire(
    creator: InternationalPaymentRep.Creator
  ): InternationalPaymentRep.Complete {
    val business = businessClient.request(BusinessApi.Get(creator.businessGuid))
      ?: throw unprocessable(BusinessNotFound())
    val businessName = business.name ?: throw BusinessIsNotOnboardedWithHighbeamYet()
    val bankAccount = bankAccountClient.request(BankAccountApi.Get(creator.bankAccountGuid))
      ?: throw unprocessable(BankAccountNotFound())

    val internationalWireTransferMethod =
      payeePaymentMethodService
        .getByPayeeGuid(creator.payeeGuid).singleNullOrThrow {
          it.paymentMethod == TransferRep.PaymentMethod.International
        } ?: throw unprocessable(InternationalWirePaymentMethodNotFound())

    if (internationalWireTransferMethod.currency != creator.receivedCurrency) {
      throw InternationalWirePaymentMethodCurrencyMismatch()
    }

    // TODO: Sign the quote clients so can't pass in any quote
    if (creator.receivedCurrency != "USD") {
      val buyRate = creator.buyRate ?: throw InternationalWireQuoteNotFound()
      val passedReceivedAmount = creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      )
      val expectedReceivedAmount =
        Money.fromDollarsAndCents(creator.amount.toDollarsAndCents() * buyRate)
          .toDollarsAndCents(
            Currency.getInstance(creator.receivedCurrency)
          )
      if (abs(expectedReceivedAmount - passedReceivedAmount) > CURRENCY_RATE_EPSILON) {
        throw InternationalWireQuoteMismatch()
      }
    }
    val beneficiaryId = internationalWireTransferMethod.currencyCloudBeneficiaryId
      ?: throw unprocessable(InternationalWireBeneficiaryNotFound())
    val paymentMetadataGuid = uuidGenerator.generate()

    paymentService.checkMinimumBalanceRequirement(
      bankAccount = bankAccount,
      paymentAmount = creator.amountWithFees(),
      unitPaymentType = UnitPaymentRep.UnitPaymentType.Wire,
      direction = UnitPaymentRep.Direction.Credit,
    )

    val internationalBankAccount = internationalBankAccountClient.request(
      endpoint = InternationalBankAccountApi.GetByBusinessGuid(creator.businessGuid)
    ) ?: throw InternationalBankAccountNotFound()
    val (unitPaymentId, paymentType) = fundInternational(
      creator = creator,
      bankAccount = bankAccount,
      internationalBankAccount = internationalBankAccount,
      businessName = businessName,
      paymentGatewayPaymentGuid = creator.paymentGuid,
      paymentMetadataGuid = paymentMetadataGuid,
    )

    var conversion: Conversion? = null
    var externalPayment: Payment? = null
    var paymentMetadata: PaymentMetadataModel? = null

    @Suppress("TooGenericExceptionCaught")
    try {
      conversion = if (creator.receivedCurrency != "USD")
        createCurrencyCloudConversion(
          contactId = internationalBankAccount.currencyCloudContactGuid.toString(),
          creator = creator,
          transactionId = paymentMetadataGuid,
        )
      else null

      externalPayment = createCurrencyCloudPayment(
        beneficiaryId = beneficiaryId,
        creator = creator,
        contactId = internationalBankAccount.currencyCloudContactGuid.toString(),
        conversion = conversion
      )

      paymentMetadata = createPaymentMetadata(
        guid = paymentMetadataGuid,
        beneficiaryId = beneficiaryId,
        businessGuid = creator.businessGuid,
        internationalBankAccount = internationalBankAccount,
        internationalWireTransferMethod = internationalWireTransferMethod,
        creator = creator,
        currencyCloudPaymentId = externalPayment.id,
        paymentGatewayPaymentGuid = creator.paymentGuid,
        unitPaymentId = unitPaymentId
      )

      return InternationalPaymentRep.Complete(
        amount = creator.receivedAmount,
        createdAt = ZonedDateTime.now(clock),
        externalPaymentId = UUID.fromString(externalPayment.id),
        currency = creator.receivedCurrency,
        paymentFee = paymentMetadata.currencyCloudPaymentFee,
        paymentFeeCurrency = "USD",
        paymentMetadataGuid = paymentMetadata.guid,
        paymentType = creator.paymentType,
        reason = creator.reason,
        status = PaymentMetadataRep.CurrencyCloudPaymentStatus.ReadyToSend.value,
      )
    } catch (e: Exception) {
      logger.error("Failed to create international wire payment: $e")
      cancelInProgressInternationalWire(
        unitPaymentId = unitPaymentId,
        paymentType = paymentType,
        conversion = conversion,
        externalPayment = externalPayment,
        paymentMetadata = paymentMetadata,
      )
      throw e
    }
  }

  override suspend fun cancelInternationalWire(businessGuid: UUID, paymentMetadataGuid: UUID) {
    val paymentMetadata = paymentMetadataService.get(
      businessGuid = businessGuid,
      guid = paymentMetadataGuid,
    ) ?: throw InternationalPaymentMetadataNotFound()
    logger.info("Cancelling currency cloud payment: ${paymentMetadata.currencyCloudPaymentId}")

    if (paymentMetadata.currencyCloudPaymentCurrency == "USD") {
      currencyCloudClient.deletePayment(paymentMetadata.currencyCloudPaymentId.toString())
      metrics.counter(
        "currency_cloud_payment_cancellation_success",
      ) {}.increment()
    } else {
      // If it is a non USD payment, we do not want to cancel conversions automatically
      // since there could be a non-significant cost to cancelling it

      logger.info(
        "Could not cancel currency cloud payment since it has an attached conversion"
          + "${paymentMetadata.currencyCloudPaymentId}"
      )
      metrics.counter(
        "currency_cloud_payment_cancellation_failed",
      ) {}.increment()
    }
  }

  override suspend fun getInternationalWireQuote(currency: String): InternationalQuoteRep.Complete {
    if (!ENABLED_BUY_CURRENCIES.contains(currency))
      throw unprocessable(InternationalQuoteNotFound())

    // TODO: We will eventually have to cache this as there is a limit on the number of requests
    // Limit is 200 requests per minute

    val rates = currencyCloudClient.detailedRates(
      currency,
      "USD",
      // This doesn't change the rate
      "sell",
      // We need an amount larger than 1 GBP to get a rate
      // We do not have volume discounts, so we use 100 USD as a base
      100.toBigDecimal(),
      null,
      null
    )
    val isUsdRate = rates.currencyPair.first().startsWith("USD")

    // The frontend doesn't make a distinction between USDXXX and XXXUSD
    // and always expects the rates to be USDXXX
    val usdRate = if (isUsdRate)
      rates.clientRate
    else BigDecimal.ONE.divide(rates.clientRate, 16, RoundingMode.HALF_EVEN)

    val currencyRateWithFee = (usdRate * (CURRENCY_RATE.toBigDecimal()))
      // We round after taking the fee to allow for half even round
      .setScale(5, RoundingMode.HALF_EVEN)
    val inverse = BigDecimal.ONE.divide(currencyRateWithFee, 16, RoundingMode.HALF_EVEN)

    return InternationalQuoteRep.Complete(
      rate = currencyRateWithFee.toDouble(),
      inverse = inverse.toDouble(),
    )
  }

  private suspend fun fundInternational(
    creator: InternationalPaymentRep.Creator,
    bankAccount: BankAccountRep.Complete,
    internationalBankAccount: InternationalBankAccountRep.Complete,
    businessName: String,
    paymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): Pair<String, UnitPaymentRep.UnitPaymentType> {
    val currentLimitAllowsForAchPayment =
      creator.amount <= INTERNATIONAL_WIRE_FUNDING_WIRE_REQUIREMENT

    if (currentLimitAllowsForAchPayment) {
      val unitACHPayment = fundInternationalAccountWithACH(
        bankAccount = bankAccount,
        creator = creator,
        internationalBankAccount = internationalBankAccount,
        businessName = businessName,
        paymentMetadataGuid = paymentMetadataGuid,
        paymentGatewayPaymentGuid = paymentGatewayPaymentGuid
      )
      if (unitACHPayment.status in UnitCoAchPaymentRep.successfulStatuses()) {
        return Pair(unitACHPayment.id, UnitPaymentRep.UnitPaymentType.Ach)
      } else {
        logger.warn {
          "International ACH funding failed for : $businessName for ${creator.amount}. " +
            "Falling back to Wire"
        }
      }
    }

    // We need to change the idempotency key. If we don't, Unit will retry the ACH payment.
    // We use a UUID deterministically-derived from the existing idempotency key.
    val newIdempotencyKey =
      UUID.nameUUIDFromBytes(creator.idempotencyKey.asByteArray() + "wire".toByteArray())
    val retryCreation = creator.copy(idempotencyKey = newIdempotencyKey)
    val unitWirePayment = fundInternationalAccountWithWire(
      bankAccount = bankAccount,
      creator = retryCreation,
      internationalBankAccount = internationalBankAccount,
      businessName = businessName,
      paymentMetadataGuid = paymentMetadataGuid,
      paymentGatewayPaymentGuid = paymentGatewayPaymentGuid
    )
    if (!UnitCoWirePaymentRep.successfulStatuses().contains(unitWirePayment.status)) {
      throw InternationalWireFundingFailed()
    }
    return Pair(unitWirePayment.id, UnitPaymentRep.UnitPaymentType.Wire)
  }

  private suspend fun fundInternationalAccountWithACH(
    bankAccount: BankAccountRep.Complete,
    creator: InternationalPaymentRep.Creator,
    internationalBankAccount: InternationalBankAccountRep.Complete,
    businessName: String,
    paymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): UnitCoAchPaymentRep.Complete {
    val counterparty = AchPaymentRep.InlineCounterparty(
      routingNumber = ProtectedString(internationalBankAccount.achRoutingNumber),
      accountNumber = ProtectedString(internationalBankAccount.achAccountNumber),
      accountType = AchPaymentRep.AccountType.Checking,
      name = businessName
    )
    val unitCoAchCreation = creator.toUnitCoAchCreation(
      bankAccount = bankAccount,
      inlineCounterparty = counterparty,
      payeeGuid = creator.payeeGuid,
      paymentGuid = paymentGatewayPaymentGuid,
      paymentMetadataGuid = paymentMetadataGuid,
      // We only want to add the payeeEmail if the user has toggled it for this payment
      payeeEmail = if (creator.payeeEmailToggle) creator.payeeEmail else null,
      generalPaymentMetadataGuid = creator.generalPaymentMetadataGuid,
      sameDay = true
    )

    logger.info { "Funding via ACH payment in unitCo for international wire : $unitCoAchCreation." }
    return unitCoClient.payment.createAch(unitCoAchCreation)
  }

  private suspend fun fundInternationalAccountWithWire(
    bankAccount: BankAccountRep.Complete,
    creator: InternationalPaymentRep.Creator,
    internationalBankAccount: InternationalBankAccountRep.Complete,
    businessName: String,
    paymentMetadataGuid: UUID,
    paymentGatewayPaymentGuid: UUID?
  ): UnitCoWirePaymentRep.Complete {
    val inlineCounterparty = WirePaymentRep.InlineCounterparty(
      routingNumber = ProtectedString(internationalBankAccount.wireRoutingNumber),
      accountNumber = ProtectedString(internationalBankAccount.wireAccountNumber),
      name = businessName,
      address = INTERNATIONAL_WIRE_FUNDING_WIRE_ADDRESS
    )

    val unitCoWireCreation = creator.toUnitCoWireCreation(
      bankAccount = bankAccount,
      inlineCounterparty = inlineCounterparty,
      payeeGuid = creator.payeeGuid,
      paymentGuid = paymentGatewayPaymentGuid,
      paymentMetadataGuid = paymentMetadataGuid,
      payeeEmail = creator.payeeEmail,
      generalPaymentMetadataGuid = creator.generalPaymentMetadataGuid
    )

    logger.info {
      "Funding via wire payment in unitCo for international wire : $unitCoWireCreation."
    }
    return unitCoClient.payment.createWire(unitCoWireCreation)
  }

  private suspend fun cancelInProgressInternationalWire(
    unitPaymentId: String,
    paymentType: UnitPaymentRep.UnitPaymentType,
    conversion: Conversion?,
    externalPayment: Payment?,
    paymentMetadata: PaymentMetadataModel? = null,
  ) {
    @Suppress("TooGenericExceptionCaught")
    try {
      if (paymentType == UnitPaymentRep.UnitPaymentType.Ach) {
        logger.info("Cancelling ACH payment: $unitPaymentId")
        unitCoClient.payment.cancelAch(unitPaymentId)
      } else {
        logger.info("Cannot cancel wire payment: $unitPaymentId")
        throw InternationalWireFundingCancelFailed()
      }
      if (conversion != null) {
        logger.info("Cancelling currency cloud conversion: ${conversion.id}")
        val cancelConversion = ConversionCancellation.create()
        cancelConversion.id = conversion.id
        currencyCloudClient.cancelConversion(cancelConversion)
      }
      if (externalPayment != null) {
        logger.info("Cancelling currency cloud payment: ${externalPayment.id}")
        currencyCloudClient.deletePayment(externalPayment.id)
      }
    } catch (e: Exception) {
      logger.error("Failed to cancel international payment for unit payment $unitPaymentId")
      sendSlackNotification(
        paymentMetadata = paymentMetadata,
        currencyCloudPaymentId = externalPayment?.id
      )
      throw e
    }
  }

  private suspend fun sendSlackNotification(
    paymentMetadata: PaymentMetadataModel?,
    currencyCloudPaymentId: String?,
  ) {
    slackMessageClient.sendMessage(
      key = null,
      webhookPath = currencyCloudWebhookPath,
      body = mapOf(
        "message" to ":alert: Unable to cancel payment, please cancel manually :alert:",
        "business_guid" to paymentMetadata?.businessGuid.toString(),
        "amount" to paymentMetadata?.currencyCloudPaymentAmount.toString(),
        "payment_metadata_guid" to paymentMetadata?.guid.toString(),
        "beneficiary_guid" to paymentMetadata?.currencyCloudBeneficiaryId.toString(),
        "description" to "paymentId - $currencyCloudPaymentId",
        "currency_cloud_account_id" to paymentMetadata?.currencyCloudAccountId.toString(),
        "internal_description" to "international_wire_funding_cancellation_failed",
      )
    )
  }

  private fun createCurrencyCloudConversion(
    contactId: String,
    creator: InternationalPaymentRep.Creator,
    transactionId: UUID
  ): Conversion {
    val conversion = Conversion.create()
    // We always sell USD and fix the buy currency
    conversion.sellCurrency = "USD"
    conversion.fixedSide = "buy"
    // As long as they accept our terms, this remains true
    conversion.termAgreement = true
    // - 'default' for conversion - T+1 for APAC, T+2 for everywhere else.
    // TODO: If we ever roll out support for INR, PHP, MYR, IDR, we need to calculate the date
    // T + 1  does not guarantee that the funding will land in time
    conversion.conversionDatePreference = "default"
    conversion.uniqueRequestId = transactionId.toString()

    // TODO: Sign and verify the rate
    // Specifying the sell amount allows CurrencyCloud to calculate client rate
    // and move the profit to our house account
    conversion.clientSellAmount = creator.amount.toDollarsAndCents().toBigDecimal()
    conversion.amount =
      creator.receivedAmount.toDollarsAndCents(
        Currency.getInstance(creator.receivedCurrency)
      ).toBigDecimal()
    conversion.buyCurrency = creator.receivedCurrency
    conversion.reason = creator.reason
    conversion.uniqueRequestId = creator.idempotencyKey.toString()

    @Suppress("LateinitUsage")
    lateinit var currencyCloudCompletedConversion: Conversion
    try {
      logger.info { "Creating international conversion: $creator." }
      currencyCloudClient.onBehalfOfDo(contactId) {
        currencyCloudCompletedConversion = currencyCloudClient.createConversion(
          conversion
        )
      }
    } catch (e: CurrencyCloudException) {
      logger.error("Conversion failed to create: $e")
      throw InternationalWireConversionFailed(e)
    }
    return currencyCloudCompletedConversion
  }

  private fun createCurrencyCloudPayment(
    beneficiaryId: UUID,
    creator: InternationalPaymentRep.Creator,
    contactId: String,
    conversion: Conversion?
  ): Payment {
    val payment = Payment.create()
    payment.currency = creator.receivedCurrency
    payment.beneficiaryId = beneficiaryId.toString()
    payment.amount = if (conversion != null)
      conversion.clientBuyAmount
    else creator.amount.toDollarsAndCents(
      Currency.getInstance(creator.receivedCurrency)
    ).toBigDecimal()
    payment.reason = creator.reason
    // This is provided to the beneficiary bankxq
    // Currency cloud only sends 134 characters in the MT103 message even though the API accepts a
    // longer string. Anything past 134 will be truncated.
    // The FE limits the input to 134 characters
    payment.reference = creator.reason
    payment.paymentType = creator.paymentType.toString().lowercase()
    if (creator.paymentType == InternationalPaymentRep.PaymentType.Priority) {
      // This ensures that our SWIFT payment lands in full
      // The charge is $20 per payment
      payment.chargeType = "ours"
    }
    // Do not charge a fee for NON USD payments  since it's baked into the rate.
    if (creator.receivedCurrency == "USD") {
      payment.feeCurrency = "USD"
      payment.feeAmount = INTERNATIONAL_WIRE_PRIORITY_FEE.toDollarsAndCents().toBigDecimal()
    } else {
      payment.feeCurrency = "USD"
      payment.feeAmount = 0.toBigDecimal()
    }
    payment.uniqueRequestId = creator.idempotencyKey.toString()

    if (conversion != null) {
      payment.conversionId = conversion.id
    }

    if (creator.purposeCode != null) {
      payment.purposeCode = creator.purposeCode
    }

    if (creator.invoiceNumber != null) {
      payment.invoiceNumber = creator.invoiceNumber
    }

    if (creator.invoiceDate != null) {
      payment.invoiceDate = Date.from(creator.invoiceDate!!.toInstant())
    }

    @Suppress("LateinitUsage")
    lateinit var currencyCloudCompletedPayment: Payment
    try {
      logger.info { "Creating international payment: $creator." }
      currencyCloudClient.onBehalfOfDo(contactId) {
        currencyCloudCompletedPayment = currencyCloudClient.createPayment(
          payment,
          null
        )
      }
    } catch (e: CurrencyCloudException) {
      logger.error("Payment failed to create: $e")
      throw InternationalWirePaymentCreationFailed(e)
    }

    metrics.counter(
      "payment_volume",
      "event_type", "internationalWire_${creator.receivedCurrency}}",
    ) {}.increment(creator.amount.toDollarsAndCents())

    return currencyCloudCompletedPayment
  }

  private fun createPaymentMetadata(
    guid: UUID,
    beneficiaryId: UUID,
    businessGuid: UUID,
    internationalBankAccount: InternationalBankAccountRep.Complete,
    internationalWireTransferMethod: PayeePaymentMethodModel,
    creator: InternationalPaymentRep.Creator,
    currencyCloudPaymentId: String,
    unitPaymentId: String,
    paymentGatewayPaymentGuid: UUID?,
  ): PaymentMetadataModel {
    val payeeName = if (
      internationalWireTransferMethod.entityType == PayeeRep.EntityType.Company
    ) {
      internationalWireTransferMethod.companyName
    } else {
      fullName(
        internationalWireTransferMethod.firstName,
        internationalWireTransferMethod.lastName
      )
    }
    // Only charge for USD -> USD payment regardless of the method of payment
    // We are charged $20 per payment for Priority and a variable amount for Regular
    val paymentFee = if (creator.receivedCurrency == "USD") {
      INTERNATIONAL_WIRE_PRIORITY_FEE
    } else {
      INTERNATIONAL_WIRE_REGULAR_FEE
    }

    val paymentMetadata = PaymentMetadataModel(
      guid = guid,
      bankAccountGuid = creator.bankAccountGuid,
      businessGuid = businessGuid,
      fundingAmount = creator.amount,
      unitCoPaymentId = unitPaymentId,
      currencyCloudAccountId = internationalBankAccount.currencyCloudBankAccountGuid,
      currencyCloudBeneficiaryId = beneficiaryId,
      currencyCloudContactId = internationalBankAccount.currencyCloudContactGuid,
      currencyCloudPaymentAmount = creator.receivedAmount,
      currencyCloudPaymentCurrency = creator.receivedCurrency,
      currencyCloudPaymentFee = paymentFee,
      currencyCloudPaymentId = UUID.fromString(currencyCloudPaymentId),
      currencyCloudPaymentFeeCurrency = "USD",
      currencyCloudPaymentReason = creator.reason,
      currencyCloudPaymentStatus = PaymentMetadataRep.CurrencyCloudPaymentStatus.ReadyToSend.value,
      currencyCloudPaymentType = creator.paymentType,
      currencyCloudPaymentClientBuyRate = creator.buyRate,
      payeeGuid = creator.payeeGuid,
      payeePaymentMethodGuid = internationalWireTransferMethod.guid,
      payeeName = payeeName,
      paymentGuid = paymentGatewayPaymentGuid,
    )
    return paymentMetadataService.createPaymentMetadata(paymentMetadata)
  }
}
