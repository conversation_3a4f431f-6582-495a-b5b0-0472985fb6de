package co.highbeam.endpoint.paymentMetadata

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.paymentMetadata.GeneralPaymentMetadataMapper
import co.highbeam.rep.paymentMetadata.GeneralPaymentMetadataRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.paymentMetadata.GeneralPaymentMetadataService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentMetadata.GeneralPaymentMetadataApi as Api

internal class BatchGetGeneralPaymentMetadata @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val paymentMetadataMapper: GeneralPaymentMetadataMapper,
  private val paymentMetadataService: GeneralPaymentMetadataService,
) : EndpointHandler<Api.BatchGet, List<GeneralPaymentMetadataRep.Complete>>(
  template = Api.BatchGet::class.template(),
) {
  override suspend fun endpoint(
    call: ApplicationCall
  ): Api.BatchGet = Api.BatchGet(rep = call.body())

  override fun loggingContext(endpoint: Api.BatchGet) =
    super.loggingContext(endpoint) + mapOf(
      "businessGuid" to endpoint.rep.businessGuid.toString(),
      "paymentMetadataGuids" to endpoint.rep.guids.toString(),
    )

  override suspend fun Handler.handle(
    endpoint: Api.BatchGet
  ): List<GeneralPaymentMetadataRep.Complete> {
    auth(authPermission(Permission.TransactionMetadata_Read) { endpoint.rep.businessGuid })
    val paymentMetadata = paymentMetadataService.getAll(
      businessGuid = endpoint.rep.businessGuid,
      guids = endpoint.rep.guids,
    )
    return paymentMetadata.map { paymentMetadataMapper.completeRep(it) }
  }
}
