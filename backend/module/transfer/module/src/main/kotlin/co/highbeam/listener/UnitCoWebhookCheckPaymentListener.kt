package co.highbeam.listener

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.exception.business.UnrecognizedUnitCoEventType
import co.highbeam.model.unitCoEvent.UnitCoCheckPaymentCreatedEventModel
import co.highbeam.model.unitCoEvent.UnitCoCheckPaymentReturnedEventModel
import co.highbeam.model.unitCoEvent.UnitCoEventModel
import co.highbeam.service.payment.CheckPaymentReturnedWebhookService
import co.highbeam.service.payment.CheckPaymentWebhookService
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.common.annotations.VisibleForTesting
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging

@Singleton
internal class UnitCoWebhookCheckPaymentListener @Inject constructor(
  factory: EventListenerFactory,
  private val objectMapper: ObjectMapper,
  private val checkPaymentWebhookService: CheckPaymentWebhookService,
  private val checkPaymentReturnedWebhookService: CheckPaymentReturnedWebhookService,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig("unit-co-webhook"),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "transfer-check-payments",
        filter = "attributes.type = \"checkPayment.created\" OR " +
          "attributes.type = \"checkPayment.returned\""
      ),
      clazz = JsonNode::class.java,
      listener = ::onReceive
    )
  }

  @VisibleForTesting
  suspend fun onReceive(json: JsonNode) {
    logger.info { "Received event: $json." }
    when (val event = readEvent(json)) {
      is UnitCoCheckPaymentCreatedEventModel ->
        checkPaymentWebhookService.handleCheckPaymentCreated(event)
      is UnitCoCheckPaymentReturnedEventModel ->
        checkPaymentReturnedWebhookService.handleCheckPaymentReturned(event)
      else -> throw UnrecognizedUnitCoEventType(event.type)
    }
  }

  private fun readEvent(eventJson: JsonNode): UnitCoEventModel =
    objectMapper.convertValue(eventJson)
}
