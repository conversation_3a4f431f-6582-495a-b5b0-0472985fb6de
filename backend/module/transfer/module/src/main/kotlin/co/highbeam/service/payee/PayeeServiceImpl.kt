package co.highbeam.service.payee

import co.highbeam.model.payee.PayeeModel
import co.highbeam.rep.payee.PayeeRep
import co.highbeam.store.payee.PayeeStore
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class PayeeServiceImpl @Inject constructor(
  private val payeeStore: PayeeStore,
) : PayeeService {
  private val logger = KotlinLogging.logger {}

  override fun upsert(payee: PayeeModel): PayeeModel {
    logger.info { "Creating transfer payee: $payee." }
    return payeeStore.upsert(payee)
  }

  override fun get(businessGuid: UUID, payeeGuid: UUID): PayeeModel? =
    payeeStore.get(businessGuid, payeeGuid)

  override fun getByBusinessGuid(
    businessGuid: UUID,
    status: PayeeRep.Status,
  ): List<PayeeModel> {
    return when (status) {
      PayeeRep.Status.Active -> payeeStore.getActiveByBusinessGuid(businessGuid)
      PayeeRep.Status.All -> payeeStore.getAllByBusiness(businessGuid)
    }
  }

  override fun update(
    businessGuid: UUID,
    payeeGuid: UUID,
    update: PayeeModel.Update
  ): PayeeModel {
    logger.info { "Updating transfer payee with $update." }
    return payeeStore.update(businessGuid, payeeGuid, update)
  }

  override fun delete(businessGuid: UUID, payeeGuid: UUID): PayeeModel {
    logger.info { "Deleting transfer payee." }
    return payeeStore.delete(businessGuid, payeeGuid)
  }
}
