package co.highbeam.service.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.config.AppConfig
import co.highbeam.email.EmailService
import co.highbeam.email.template.CheckPaymentReturnedEmailTemplate
import co.highbeam.email.template.EmailTemplate
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.unitCoWebhook.payment.UnitPaymentNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.metrics.Metrics
import co.highbeam.model.unitCoEvent.UnitCoCheckPaymentReturnedEventModel
import co.unit.client.UnitCoClient
import co.unit.rep.UnitCoCheckPaymentRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.time.ZoneId
import java.util.UUID

internal class CheckPaymentReturnedWebhookServiceImpl @Inject constructor(
  private val appConfig: AppConfig,
  private val bankAccountClient: BankAccountClient,
  private val businessMemberClient: BusinessMemberClient,
  private val emailService: EmailService,
  private val featureFlagService: FeatureFlagService,
  private val metrics: Metrics,
  private val unitCoClient: UnitCoClient,
) : CheckPaymentReturnedWebhookService {
  private val logger = KotlinLogging.logger {}

  override suspend fun handleCheckPaymentReturned(event: UnitCoCheckPaymentReturnedEventModel) {
    logger.info { "Handling check payment returned event: $event." }

    val unitCheckPayment = getUnitCheckPayment(event.checkPaymentId)
    val bankAccount = getBankAccount(unitCheckPayment.accountId)
    val businessGuid = bankAccount.businessGuid
    val recipients = businessAdminRecipients(businessGuid)

    val sendEmailFeatureFlag = featureFlagService.isEnabled(
      flag = BusinessFlag.SendCheckPaymentReturnedEmail,
      businessGuid = businessGuid,
    )

    val emailKey = "CheckPaymentReturned|${businessGuid}|${unitCheckPayment.id}"

    if(!sendEmailFeatureFlag) {
      logger.info { "Skipping sending check payment returned email for business $businessGuid" }
      return
    }

    emailService.sync(key = emailKey) { sendEmail ->
      sendEmail(
        CheckPaymentReturnedEmailTemplate(
          recipients = recipients,
          accountWithMask = bankAccount.nameWithMask,
          amount = unitCheckPayment.amount,
          description = unitCheckPayment.description,
          counterparty = unitCheckPayment.counterparty,
          failureDate = unitCheckPayment
            .updatedAt.withZoneSameInstant(ZoneId.of("America/New_York")).toLocalDate(),
          failureReason = unitCheckPayment.returnReason,
          transactionsUrl = appConfig.appBaseUrl + "/transactions",
        )
      )
    }

    trackMetric()
  }

  private suspend fun getUnitCheckPayment(id: String): UnitCoCheckPaymentRep.Complete {
    return unitCoClient.checkPayment.get(id) ?: throw UnitPaymentNotFound()
  }

  private suspend fun getBankAccount(unitCoDepositAccountId: String) =
    bankAccountClient.request(BankAccountApi.GetByUnitCoDepositAccountId(unitCoDepositAccountId))
      ?: throw unprocessable(BankAccountNotFound())

  private suspend fun businessAdminRecipients(businessGuid: UUID): List<EmailTemplate.Recipient> {
    val businessAdminMembers = businessMemberClient.request(
      BusinessMemberApi.GetAdminsByBusiness(businessGuid)
    )
    return businessAdminMembers.mapNotNull {
      it.emailAddress?.let { it1 ->
        EmailTemplate.Recipient(it1, it.fullName)
      }
    }
  }

  private fun trackMetric() {
    metrics.counter(
      "unit_check_payment_returned",
    ) {}.increment()
  }
}
