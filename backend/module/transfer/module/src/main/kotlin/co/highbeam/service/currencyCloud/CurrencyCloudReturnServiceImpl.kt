package co.highbeam.service.currencyCloud

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.money.Money
import co.highbeam.rep.payment.InternationalPaymentReturnRep
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.model.Beneficiary
import com.currencycloud.client.model.Payment
import com.google.inject.Inject
import mu.KotlinLogging
import java.time.Clock
import java.time.ZonedDateTime
import java.util.UUID

internal class CurrencyCloudReturnServiceImpl @Inject constructor(
  private val bankAccountClient: BankAccountClient,
  private val businessClient: BusinessClient,
  private val clock: Clock,
  private val currencyCloudBeneficiaryService: CurrencyCloudBeneficiaryService,
  private val currencyCloudClient: CurrencyCloudClient,
  private val internationalBankAccountClient: InternationalBankAccountClient,
) : CurrencyCloudReturnService {
  private val logger = KotlinLogging.logger {}

  override suspend fun returnFundsToPrimary(
    amount: Money,
    businessGuid: UUID,
    reference: String,
  ): InternationalPaymentReturnRep.Complete {
    val internationalBankAccount = internationalBankAccountClient.request(
      InternationalBankAccountApi.GetByBusinessGuid(businessGuid)
    ) ?: throw InternationalBankAccountNotFound()
    val highbeamBankAccount = bankAccountClient.request(
      BankAccountApi.GetPrimaryBankAccountByBusinessGuid(internationalBankAccount.businessGuid)
    ) ?: throw BankAccountNotFound()
    val business = getBusiness(businessGuid) ?: throw BusinessNotFound()
    val beneficiary = currencyCloudBeneficiaryService.getOrCreateBeneficiary(
      bankAccount = highbeamBankAccount,
      business = business,
      internationalBankAccount = internationalBankAccount,
    )

    val payment = createPayment(
      amount = amount,
      beneficiary = beneficiary,
      reference = reference
    )

    logger.info { "Returning funds to primary account: $payment" }
    currencyCloudClient.onBehalfOfDo(internationalBankAccount.currencyCloudContactGuid.toString()) {
      currencyCloudClient.createPayment(payment, null)
    }

    return InternationalPaymentReturnRep.Complete(
      amount = amount,
      reference = reference,
      createdAt = ZonedDateTime.now(clock),
    )
  }

  private fun createPayment(
    amount: Money,
    beneficiary: Beneficiary,
    reference: String,
  ): Payment {

    val payment = Payment.create()
    payment.currency = "USD"
    payment.beneficiaryId = beneficiary.id
    payment.amount = amount.toDollarsAndCents().toBigDecimal()
    payment.reason = "Returning funds"
    payment.reference = reference
    payment.paymentType = "regular"
    payment.feeCurrency = "USD"
    payment.feeAmount = 0.toBigDecimal()
    payment.uniqueRequestId = "return-$reference-$amount"

    return payment
  }

  private suspend fun getBusiness(businessGuid: UUID) =
    businessClient.request(
      BusinessApi.Get(businessGuid)
    )
}
