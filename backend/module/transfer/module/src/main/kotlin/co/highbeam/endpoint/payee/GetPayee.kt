package co.highbeam.endpoint.payee

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.payee.PayeeNotFound
import co.highbeam.mapper.payee.PayeeMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.payee.PayeeService
import co.highbeam.service.payeePaymentMethod.PayeePaymentMethodService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.payee.PayeeApi as Api
import co.highbeam.rep.payee.PayeeRep as Rep

internal class GetPayee @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val payeeMapper: PayeeMapper,
  private val payeeService: PayeeService,
  private val payeePaymentMethodService: PayeePaymentMethodService,
) : EndpointHandler<Api.Get, Rep.Complete>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get(
    businessGuid = call.getParam("businessGuid"),
    payeeGuid = call.getParam("payeeGuid"),
  )

  override fun loggingContext(endpoint: Api.Get) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.businessGuid.toString(),
    "payeeGuid" to endpoint.payeeGuid.toString(),
  )

  override suspend fun Handler.handle(endpoint: Api.Get): Rep.Complete {
    auth(authPermission(Permission.Payee_Read) { endpoint.businessGuid })
    val payee = payeeService.get(endpoint.businessGuid, endpoint.payeeGuid)
      ?: throw PayeeNotFound()
    val payeePaymentMethods =
      payeePaymentMethodService.getByPayeeGuid(payee.guid)
    return payeeMapper.newCompleteRep(
      payee = payee,
      payeePaymentMethods = payeePaymentMethods
    )
  }
}
