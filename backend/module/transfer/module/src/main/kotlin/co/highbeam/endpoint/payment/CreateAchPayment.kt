package co.highbeam.endpoint.payment

import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.transfer.AuthBankAccount
import co.highbeam.auth.permissions.Permission
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.payment.PaymentService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.payment.PaymentApi as Api
import co.highbeam.rep.payment.AchPaymentRep as Rep

internal class CreateAchPayment @Inject constructor(
  private val authBankAccount: AuthBankAccount.Provider,
  private val authMfa: AuthMfa.Provider,
  private val paymentService: PaymentService,
) : EndpointHandler<Api.CreateAch, Rep.Complete>(
  template = Api.CreateAch::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.CreateAch = Api.CreateAch(
    creator = call.body(),
  )

  override fun loggingContext(endpoint: Api.CreateAch) = super.loggingContext(endpoint) +
    mapOf("bankAccountGuid" to endpoint.creator.bankAccountGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.CreateAch): Rep.Complete {
    authAll(authBankAccount(Permission.Payment_Create, endpoint.creator.bankAccountGuid), authMfa())

    return paymentService.createAch(endpoint.creator)
  }
}
