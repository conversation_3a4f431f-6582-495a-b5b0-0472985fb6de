package co.highbeam.endpoint.paymentDetails

import co.highbeam.api.paymentDetails.PaymentDetailsApi
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.paymentMetadata.PaymentReceiptNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.payment.PaymentMetadataService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.paymentDetails.PaymentDetailsApi as Api

internal class GetPaymentReceipt @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val paymentMetadataService: PaymentMetadataService,
) : EndpointHandler<Api.GetPaymentReceipt, String>(
  template = Api.GetPaymentReceipt::class.template(),
) {

  override suspend fun endpoint(call: ApplicationCall): Api.GetPaymentReceipt =
    Api.GetPaymentReceipt(
      businessGuid = call.getParam("businessGuid"),
      paymentMetadataGuid = call.getParam("paymentMetadataGuid")
    )

  override suspend fun Handler.handle(
    endpoint: PaymentDetailsApi.GetPaymentReceipt
  ): String {
    auth(authPermission(Permission.Transaction_Read) { endpoint.businessGuid })

    return paymentMetadataService.getPaymentReceipt(
      businessGuid = endpoint.businessGuid,
      paymentGuid = endpoint.paymentMetadataGuid,
    ) ?: throw PaymentReceiptNotFound()
  }
}
