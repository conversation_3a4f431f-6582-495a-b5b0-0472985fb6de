package co.highbeam.service.payment

import co.highbeam.exception.paymentMetadata.PaymentMetadataNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.model.paymentMetadata.PaymentMetadataModel
import co.highbeam.rep.payment.PaymentMetadataRep
import co.highbeam.store.payment.PaymentMetadataStore
import com.currencycloud.client.CurrencyCloudClient
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class PaymentMetadataServiceImpl @Inject constructor(
  private val currencyCloudClient: CurrencyCloudClient,
  private val paymentMetadataStore: PaymentMetadataStore,
) : PaymentMetadataService {
  private val logger = KotlinLogging.logger {}

  override fun batchGet(
    paymentMetadataGuids: List<UUID>,
    businessGuid: UUID
  ): List<PaymentMetadataModel> {
    logger.info { "Batch fetching paymentMetadata with ids: $paymentMetadataGuids" }
    return paymentMetadataStore.batchGet(paymentMetadataGuids, businessGuid)
  }

  override fun createPaymentMetadata(
    model: PaymentMetadataModel
  ): PaymentMetadataModel {
    logger.info { "Creating paymentMetadata for Unit.co payment: ${model.unitCoPaymentId}" }
    return paymentMetadataStore.create(model)
  }

  override fun get(businessGuid: UUID, guid: UUID): PaymentMetadataModel? =
    paymentMetadataStore.get(businessGuid, guid)

  override fun getByBusinessGuid(businessGuid: UUID): List<PaymentMetadataRep.Complete> =
    paymentMetadataStore.getByBusinessGuid(businessGuid)

  override fun getByInternationalPaymentId(paymentId: UUID): PaymentMetadataModel? =
    paymentMetadataStore.getByInternationalPaymentId(paymentId)

  override fun getPaymentReceipt(businessGuid: UUID, paymentGuid: UUID): String? {
    val paymentMetadata = paymentMetadataStore.get(
      guid = paymentGuid,
      businessGuid = businessGuid
    )
    val currencyCloudPaymentId = paymentMetadata?.currencyCloudPaymentId ?: throw unprocessable(
      PaymentMetadataNotFound()
    )

    return currencyCloudClient.retrievePaymentSubmission(currencyCloudPaymentId.toString()).mt103
  }

  override fun getPendingPaymentsByPayee(
    businessGuid: UUID, payeeGuid: UUID
  ): List<PaymentMetadataModel> =
    paymentMetadataStore.getPendingPaymentsByPayee(businessGuid, payeeGuid)

  override fun getPendingCurrencyCloudPayments(): List<PaymentMetadataModel> =
    paymentMetadataStore.getPendingSwiftCurrencyCloudPayments()

  override fun updatePaymentMetadata(
    guid: UUID, update: PaymentMetadataModel.Update
  ): PaymentMetadataModel {
    logger.info {
      "Updating paymentMetadata: $guid with status ${update.currencyCloudPaymentStatus}"
    }
    return paymentMetadataStore.update(guid, update)
  }
}
