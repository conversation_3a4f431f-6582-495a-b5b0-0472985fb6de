package co.highbeam.service.currencyCloudEventHandler

import co.highbeam.model.currencyCloudEvent.ConversionEventModel
import co.highbeam.model.currencyCloudEvent.CurrencyCloudEventModel
import co.highbeam.model.currencyCloudEvent.CurrencyCloudIgnoredEventModel
import co.highbeam.model.currencyCloudEvent.PaymentEventModel
import co.highbeam.model.currencyCloudEvent.TransactionEventModel
import com.google.inject.Inject

internal class CurrencyCloudEventHandler @Inject constructor(
  private val conversionEventHandlerProvider: ConversionEventHandlerProvider,
  private val paymentEventHandlerProvider: PaymentEventHandlerProvider,
  private val transactionEventHandlerProvider: TransactionEventHandlerProvider,
) {
  suspend fun handle(event: CurrencyCloudEventModel) {
    when (event) {
      is ConversionEventModel -> conversionEventHandlerProvider.handleEvent(event)
      is PaymentEventModel -> paymentEventHandlerProvider.handleEvent(event)
      is TransactionEventModel -> transactionEventHandlerProvider.handleEvent(event)
      is CurrencyCloudIgnoredEventModel -> throw UnrecognizedCurrencyCloudEventType(event.type)
    }
  }
}
