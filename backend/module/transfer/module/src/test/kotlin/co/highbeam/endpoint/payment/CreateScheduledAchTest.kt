package co.highbeam.endpoint.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.payment.AchPaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.AchCounterpartyRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoScheduledAchPaymentRep
import io.mockk.coEvery
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.assertTrue

internal class CreateScheduledAchTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  @Test
  fun `happy path Highbeam`() = integrationTest {
    mockBusinessClient()
    mockBankAccountClient()
    mockUnitCoCounterpartyCreation()
    mockUnitCoPaymentCreation()

    val payeeGuid = uuidGenerator[0]

    val scheduledCreation = AchPaymentRep.ScheduledCreator(
      payment = AchPaymentRep.Creator(
        bankAccountGuid = uuidGenerator[1],
        amount = Money(100),
        counterparty = AchPaymentRep.InlineCounterparty(
          routingNumber = ProtectedString("*********"),
          accountNumber = ProtectedString("**********"),
          name = "Highbeam User 1",
          accountType = AchPaymentRep.AccountType.Checking,
        ),
        description = "Small Description but big enough",
        addenda = "",
        idempotencyKey = UUID.randomUUID(),
        payeeGuid = payeeGuid,
        payeeEmail = "<EMAIL>",
      ),
      scheduleDate = ZonedDateTime.of(
        2022, 12, 3, 5, 15, 30, 789_000_000,
        ZoneOffset.UTC,
      ),
      businessGuid = uuidGenerator[2],
    )

    val expected = AchPaymentRep.Complete(
      unitCoId = uuidGenerator[3].toString(),
      type = "recurringCreditAchPayment",
      createdAt = ZonedDateTime.of(
        2007, 12, 3, 5, 15, 30, 789_000_000,
        ZoneOffset.UTC,
      ),
      amount = Money(100),
      status = "Sent",
      payeeGuid = payeeGuid,
    )

    assertTrue { scheduledCreation.validate().isValid }

    assertThat(paymentClient.request(PaymentApi.CreateScheduledAch(scheduledCreation)))
      .isEqualTo(expected)

    val unitCoPayments = get<UnitCoClient>().payment

    coVerify(exactly = 1) {
      unitCoPayments.createScheduledAch(
        UnitCoScheduledAchPaymentRep.Creator(
          amount = Money(100),
          description = "Small Desc",
          addenda = "ription but big enough",
          scheduledDate = ZonedDateTime.of(
            2022, 12, 3, 5, 15, 30, 789_000_000,
            ZoneOffset.UTC,
          ),
          idempotencyKey = scheduledCreation.payment.idempotencyKey,
          payeeGuid = payeeGuid,
          fromAccountType = "depositAccount",
          fromAccountId = "697843",
          counterpartyId = "879643",
        )
      )
    }
  }

  private fun mockBusinessClient() {
    coEvery { get<BusinessClient>().request(BusinessApi.Get(uuidGenerator[2])) } returns
      BusinessRep.Complete(
        guid = uuidGenerator[2],
        dba = null,
        name = "Highbeam Bank",
        referralLinkGuid = null,
        ownerUserGuid = UUID.randomUUID(),
        unitCoCustomerId = "12673",
        status = BusinessRep.Complete.Status.Active,
        stateOfIncorporation = null,
        naics = null,
      )
  }

  private fun mockBankAccountClient() {
    coEvery { get<BankAccountClient>().request(BankAccountApi.Get(uuidGenerator[1])) } returns
      BankAccountRep.Complete(
        guid = uuidGenerator[1],
        unitCoDepositAccountId = "697843",
        businessGuid = UUID.randomUUID(),
        name = "Highbeam Bank",
        status = BankAccountRep.Status.OPEN,
        isPrimary = false,
        availableBalance = Balance(600),
        routingNumber = "*********",
        accountNumber = "142683",
        type = "depositAccount",
        highbeamType = BankAccountRep.Type.DepositAccount,
        depositProduct = DepositAccountRep.DepositProduct.Checking,
        minimumRequiredBalance = Balance.ZERO,
      )
  }

  private fun mockUnitCoCounterpartyCreation() {
    coEvery {
      get<UnitCoClient>().payment.createAchCounterparty(
        AchCounterpartyRep.Creator(
          routingNumber = "*********",
          accountNumber = "**********",
          name = "Highbeam User 1",
          accountType = AchCounterpartyRep.AccountType.Checking,
          payeeGuid = uuidGenerator[0],
          customerId = "12673",
        )
      )
    } returns AchCounterpartyRep.Complete(
      id = "879643",
      createdAt = ZonedDateTime.of(
        2007, 12, 3, 5, 15, 30, 789_000_000,
        ZoneOffset.UTC,
      ),
      routingNumber = "*********",
      accountNumber = "**********",
      name = "Highbeam User 1",
      accountType = AchCounterpartyRep.AccountType.Checking,
      payeeGuid = uuidGenerator[0],
    )
  }

  private fun mockUnitCoPaymentCreation() {
    coEvery { get<UnitCoClient>().payment.createScheduledAch(any()) } answers {
      val creator = firstArg<UnitCoScheduledAchPaymentRep.Creator>()
      return@answers UnitCoScheduledAchPaymentRep.Complete(
        id = uuidGenerator[3].toString(),
        createdAt = ZonedDateTime.of(
          2007, 12, 3, 5, 15, 30, 789_000_000,
          ZoneOffset.UTC,
        ),
        updatedAt = ZonedDateTime.of(
          2007, 12, 3, 5, 15, 30, 789_000_000,
          ZoneOffset.UTC,
        ),
        amount = creator.amount,
        description = creator.description,
        addenda = creator.addenda,
        status = "Sent",
        numberOfPayments = 1,
        schedule = UnitCoScheduledAchPaymentRep.Schedule(
          startTime = creator.scheduledDate.toLocalDate(),
          endTime = creator.scheduledDate.plusDays(1).toLocalDate(),
          interval = UnitCoScheduledAchPaymentRep.Interval.Monthly,
          dayOfMonth = creator.scheduledDate.let { scheduledDate ->
            if (scheduledDate.dayOfMonth <= 28) scheduledDate.dayOfMonth
            else scheduledDate.dayOfMonth - YearMonth.from(scheduledDate).lengthOfMonth() - 1
          },
          totalNumberOfPayments = null,
          nextScheduledAction = null,
        ),
        payeeGuid = creator.payeeGuid,
      )
    }
  }
}
