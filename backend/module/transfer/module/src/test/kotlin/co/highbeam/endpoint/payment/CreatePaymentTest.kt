package co.highbeam.endpoint.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.exception.ForbiddenException
import co.highbeam.exception.business.BankAccountNotFound
import co.highbeam.exception.payment.InsufficientFundsException
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.payment.PaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitPaymentRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertFailsWith

internal class CreatePaymentTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private val paymentGuid = UUID.randomUUID()
  private val unitCoAccountId: String = "555"
  private val bankAccount = BankAccountRep.Complete(
    guid = UUID.randomUUID(),
    unitCoDepositAccountId = "92834",
    businessGuid = businessGuid,
    name = "the one and only",
    status = BankAccountRep.Status.OPEN,
    isPrimary = true,
    availableBalance = Balance.fromCents(********),
    routingNumber = "123",
    accountNumber = "456",
    type = "depositAccount",
    highbeamType = BankAccountRep.Type.HighYield,
    depositProduct = DepositAccountRep.DepositProduct.HighYield,
    minimumRequiredBalance = Balance.ZERO,
  )

  private val fakeFeatureFlagService = get<FeatureFlagService>() as FakeFeatureFlagService

  @BeforeEach
  fun beforeEach() {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount
    fakeFeatureFlagService[BusinessFlag.PaymentsThroughBackend] = true
  }

  private val achJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "direction" to "Credit",
        "amount" to 200,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val sameDayAchJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "amount" to 200,
        "direction" to "Credit",
        "sameDay" to true,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val successResponseJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "amount" to 200,
        "status" to "Sent",
        "tags" to mapOf(
          "paymentGuid" to paymentGuid,
        )
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val largeAchJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "direction" to "Credit",
        "amount" to 7_500_000_00,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val largeIncomingAchJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "direction" to "Debit",
        "amount" to 7_500_000_00,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val successResponseLargeJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "amount" to *********,
        "status" to "Sent",
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val successSameDayAchResponseLargeJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "achPayment",
      "attributes" to mapOf(
        "amount" to *********,
        "sameDay" to true,
        "status" to "Sent",
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val largeWireJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "wirePayment",
      "attributes" to mapOf(
        "amount" to 7_500_000_00,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  private val bookTransferJson = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to "123",
      "type" to "bookPayment",
      "attributes" to mapOf(
        "amount" to 7_500_000_00,
      ),
      "relationships" to mapOf(
        "account" to mapOf(
          "data" to mapOf(
            "type" to "account",
            "id" to unitCoAccountId,
          ),
        ),
      ),
    ),
  )

  @Test
  fun `happy path`() = integrationTest {
    mockUnitCoPaymentCreation()
    val payment = PaymentRep.Creator(
      unitPayload = achJson,
    )
    assertThat(paymentClient.request(PaymentApi.CreatePayment(payment)))
      .isEqualTo(PaymentRep(successResponseJson))
  }

  @Test
  fun `no bank account`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns null
    val payment = PaymentRep.Creator(
      unitPayload = achJson,
    )
    assertHighbeamException {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.isHighbeamException(unprocessable(BankAccountNotFound()))
  }

  @Test
  fun `feature flag off`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.PaymentsThroughBackend] = false

    val payment = PaymentRep.Creator(
      unitPayload = achJson,
    )
    assertHighbeamException {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.isHighbeamException(ForbiddenException())
  }

  @Test
  fun `large outgoing ach transfer`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      availableBalance = Balance.fromCents(8_000_000_00),
      minimumRequiredBalance = Balance.fromCents(500_000_00),
    )
    mockUnitCoPaymentCreation(successResponseLargeJson)

    val payment = PaymentRep.Creator(
      unitPayload = largeAchJson,
    )
    assertThat(paymentClient.request(PaymentApi.CreatePayment(payment)))
      .isEqualTo(PaymentRep(successResponseLargeJson))
  }

  @Test
  fun `large outgoing ach transfer - with minimum balance set`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      availableBalance = Balance.fromCents(8_000_000_00),
      minimumRequiredBalance = Balance.fromCents(500_000_01),
    )

    val payment = PaymentRep.Creator(
      unitPayload = largeAchJson,
    )
    assertHighbeamException {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.isHighbeamException(InsufficientFundsException(
      bankAccountGuid = bankAccount.guid,
      paymentAmount = Money.fromCents(7_500_000_00),
      minimumBalance = Balance.fromCents(500_000_01),
      availableBalance = Balance.fromCents(8_000_000_00),
    ))
  }

  @Test
  fun `large wire transfer - with minimum balance set`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      availableBalance = Balance.fromCents(8_000_000_00),
      minimumRequiredBalance = Balance.fromCents(500_000_01),
    )

    val payment = PaymentRep.Creator(
      unitPayload = largeWireJson,
    )
    assertHighbeamException {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.isHighbeamException(InsufficientFundsException(
      bankAccountGuid = bankAccount.guid,
      paymentAmount = Money.fromCents(7_500_000_00),
      minimumBalance = Balance.fromCents(500_000_01),
      availableBalance = Balance.fromCents(8_000_000_00),
    ))
  }

  @Test
  fun `large incoming ACH - does not perform min balance check`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      availableBalance = Balance.fromCents(8_000_000_00),
      minimumRequiredBalance = Balance.fromCents(500_000_01),
    )
    mockUnitCoPaymentCreation(successResponseLargeJson)

    val payment = PaymentRep.Creator(
      unitPayload = largeIncomingAchJson,
    )

    paymentClient.request(PaymentApi.CreatePayment(payment))

    assertThat(paymentClient.request(PaymentApi.CreatePayment(payment)))
      .isEqualTo(PaymentRep(successResponseLargeJson))
  }

  @Test
  fun `large outgoing book transfer - with min balance check`() = integrationTest {
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      availableBalance = Balance.fromCents(8_000_000_00),
      minimumRequiredBalance = Balance.fromCents(500_000_01),
    )

    val payment = PaymentRep.Creator(
      unitPayload = bookTransferJson)
    assertHighbeamException {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.isHighbeamException(InsufficientFundsException(
      bankAccountGuid = bankAccount.guid,
      paymentAmount = Money.fromCents(7_500_000_00),
      minimumBalance = Balance.fromCents(500_000_01),
      availableBalance = Balance.fromCents(8_000_000_00),
    ))
  }

  @Test
  fun `sets same day ach for outgoing ACHs from Thread`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.SameDayAchDomesticAch] = true

    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetByUnitCoDepositAccountId(unitCoAccountId))
    } returns bankAccount.copy(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVipThread,
    )
    mockUnitCoPaymentCreation(successSameDayAchResponseLargeJson)

    val payment = PaymentRep.Creator(
      unitPayload = achJson,
    )

    assertThat(
      paymentClient.request(PaymentApi.CreatePayment(payment))
    ).isEqualTo(PaymentRep(successSameDayAchResponseLargeJson))

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createPayment(
        creator = sameDayAchJson,
        type = UnitPaymentRep.UnitPaymentType.Ach,
      )
    }
  }

  // TODO(shubham): Testing the current behaviour as is but this is not ideal. Fix.
  @Test
  fun `unit throws error`() = integrationTest {
    coEvery { get<UnitCoClient>().payment.createPayment(any(), any()) } throws (RuntimeException())
    val payment = PaymentRep.Creator(achJson)
    assertFailsWith<HighbeamHttpClientException> {
      paymentClient.request(PaymentApi.CreatePayment(payment))
    }.let {
      assertThat(it.isServerError)
      assertThat(it.statusCode).isEqualTo(HttpStatusCode.InternalServerError)
    }
  }

  private fun mockUnitCoPaymentCreation(unitJson: JsonNode = successResponseJson) {
    coEvery { get<UnitCoClient>().payment.createPayment(any(), any()) } answers {
      return@answers unitJson
    }
  }
}
