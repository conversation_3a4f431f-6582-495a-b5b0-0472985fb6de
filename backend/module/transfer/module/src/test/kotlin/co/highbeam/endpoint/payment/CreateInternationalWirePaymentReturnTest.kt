package co.highbeam.endpoint.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.businessAddress.BusinessAddressApi
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi
import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.client.businessAddress.BusinessAddressClient
import co.highbeam.client.internationalBankAccount.InternationalBankAccountClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.businessAddress.BusinessAddressRep
import co.highbeam.rep.internationalBankAccount.InternationalBankAccountRep
import co.highbeam.rep.payment.INTERNATIONAL_WIRE_PRIORITY_FEE
import co.highbeam.rep.payment.InternationalPaymentReturnRep
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.highbeam.testing.mockCurrencyCloudClientOnBehalfOf
import co.highbeam.util.time.inUTC
import co.unit.rep.DepositAccountRep
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.model.Beneficiary
import com.currencycloud.client.model.Contact
import com.currencycloud.client.model.Payment
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class CreateInternationalWirePaymentReturnTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {

  @Test
  fun `Successfully returns amount by creating a payment to primary account`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val contact = setUpCurrencyCloudContact()

    mockBankAccountClient(businessGuid)
    mockEmptyFindBeneficiaries()
    mockBusinessAddress(businessGuid)
    mockCreateBeneficiary()

    val paymentRep = createReturnPaymentRep(
      amount = Money(100),
      businessGuid = businessGuid,
      reference = "Returning failed funds"
    )
    val externalBankAccountId = UUID.randomUUID()
    val externalPaymentId = UUID.randomUUID()

    mockInternationalBankAccount(
      businessGuid = businessGuid,
      currencyCloudContactGuid = UUID.fromString(contact.id),
      currencyCloudBankAccountGuid = externalBankAccountId
    )
    mockBusiness(businessGuid = businessGuid, name = contact.accountName)
    mockCurrencyCloudClientOnBehalfOf(UUID.fromString(contact.id))
    mockCurrencyCloudPayment(externalPaymentId)

    val expectedResult = InternationalPaymentReturnRep.Complete(
      amount = paymentRep.amount,
      reference = paymentRep.reference,
      createdAt = ZonedDateTime.now(clock).inUTC(),
    )
    assertThat(
      paymentClient.request(PaymentApi.CreateInternationalWireReturn(paymentRep))
    ).isEqualTo(expectedResult)
  }

  private fun mockCurrencyCloudPayment(
    paymentId: UUID = UUID.randomUUID(),
    receivedCurrency: String = "USD"
  ) {
    val payment = Payment.create()
    payment.id = paymentId.toString()
    payment.feeAmount =
      if (receivedCurrency == "USD")
        INTERNATIONAL_WIRE_PRIORITY_FEE.toDollarsAndCents().toBigDecimal()
      else 0.toBigDecimal()

    every {
      get<CurrencyCloudClient>()
        // TODO - Mock the right parameters
        .createPayment(any(), null)
    } returns payment
  }

  private fun mockCreateBeneficiary() {
    coEvery { get<CurrencyCloudClient>().createBeneficiary(any()) } answers {
      val beneficiary = firstArg<Beneficiary>()
      beneficiary.id = "123"
      return@answers beneficiary
    }
  }

  private fun mockBusinessAddress(businessGuid: UUID) {
    coEvery {
      get<BusinessAddressClient>().request(
        BusinessAddressApi.Get(businessGuid)
      )
    } returns BusinessAddressRep(
      line1 = "123 Main St",
      line2 = "Apt 1",
      city = "New York",
      state = "NY",
      postalCode = "94105",
      country = "US",
    )
  }

  private fun mockEmptyFindBeneficiaries() {
    coEvery { get<CurrencyCloudClient>().findBeneficiaries(any(), any()) } returns
      mockk {
        every { <EMAIL> } returns emptyList()
      }
  }


  private fun createReturnPaymentRep(
    amount: Money,
    businessGuid: UUID,
    reference: String,
  ) = InternationalPaymentReturnRep.Creator(
    amount = amount,
    businessGuid = businessGuid,
    reference = reference,
  )

  private suspend fun mockInternationalBankAccount(
    businessGuid: UUID,
    currencyCloudContactGuid: UUID = UUID.randomUUID(),
    currencyCloudBankAccountGuid: UUID = UUID.randomUUID(),
  ) {
    coEvery {
      get<InternationalBankAccountClient>().request(
        InternationalBankAccountApi.GetByBusinessGuid(businessGuid)
      )
    } returns InternationalBankAccountRep.Complete(
      guid = UUID.randomUUID(),
      businessGuid = businessGuid,
      currencyCloudBankAccountGuid = currencyCloudBankAccountGuid,
      currencyCloudContactGuid = currencyCloudContactGuid,
      achRoutingNumber = "this should be the ach routing number",
      achAccountNumber = "this should be the ach account number",
      wireRoutingNumber = "this should be the local wire routing number",
      wireAccountNumber = "this is local wire account number",
      iban = "this is the iban",
      swiftCode = "this is the bic swift",
      enabled = true,
      termsAcceptedAt = null,
    )
  }

  private fun mockGetCurrencyCloudContact(currencyCloudContactId: UUID): Contact {
    val contact = Contact.create()
    contact.id = currencyCloudContactId.toString()
    contact.accountName = "Stark Industries"
    every {
      get<CurrencyCloudClient>().retrieveContact(currencyCloudContactId.toString())
    } returns contact

    return contact
  }

  private suspend fun mockBankAccountClient(
    businessGuid: UUID,
    depositProduct: DepositAccountRep.DepositProduct = DepositAccountRep.DepositProduct.Checking,
    minimumRequiredBalance: Balance = Balance.ZERO
  ): BankAccountRep.Complete {
    val bankAccountRep = BankAccountRep.Complete(
      guid = UUID.randomUUID(),
      unitCoDepositAccountId = "some unit account id",
      businessGuid = businessGuid,
      name = "Tony Stark",
      status = BankAccountRep.Status.OPEN,
      isPrimary = true,
      availableBalance = Balance.fromDollarsAndCents(100_020, 0),
      routingNumber = "routing number",
      accountNumber = "account number",
      type = "unit type",
      highbeamType = BankAccountRep.Type.DepositAccount,
      depositProduct = depositProduct,
      minimumRequiredBalance = minimumRequiredBalance,
    )
    coEvery {
      get<BankAccountClient>().request(
        BankAccountApi.GetPrimaryBankAccountByBusinessGuid(businessGuid)
      )
    } returns bankAccountRep

    return bankAccountRep
  }

  private fun setUpCurrencyCloudContact(
    contactId: UUID = UUID.randomUUID(),
  ): Contact = mockGetCurrencyCloudContact(contactId)
}
