package co.highbeam.endpoint.paymentMetadata

import co.highbeam.api.paymentMetadata.GeneralPaymentMetadataApi
import co.highbeam.rep.paymentMetadata.GeneralPaymentMetadataRep
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class DeleteInvoiceGeneralPaymentMetadataTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  @Test
  fun `payment metadata does not exist`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val paymentMetadataGuid = UUID.randomUUID()

    assertThat(generalPaymentMetadataClient.request(GeneralPaymentMetadataApi.DeleteInvoice(
      businessGuid = businessGuid,
      paymentMetadataGuid = paymentMetadataGuid,
    ))).isNull()
  }

  @Test
  fun `payment metadata exists`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val invoiceGuid = UUID.randomUUID()
    val invoiceName = "testFile.jpg"
    val notes = "test"

    val expected = GeneralPaymentMetadataRep.Complete(
      guid = uuidGenerator[0],
      businessGuid = businessGuid,
      invoiceGuid = null,
      invoiceName = null,
      notes = notes,
    )

    generalPaymentMetadataClient.request(GeneralPaymentMetadataApi.Create(
      businessGuid = businessGuid,
      rep = GeneralPaymentMetadataRep.Creator(
        invoiceGuid = invoiceGuid,
        invoiceName = invoiceName,
        notes = notes,
      )
    ))

    val paymentMetadata = generalPaymentMetadataClient.request(
      GeneralPaymentMetadataApi.DeleteInvoice(
        businessGuid = businessGuid,
        paymentMetadataGuid = uuidGenerator[0],
      ))

    assertThat(paymentMetadata).isEqualTo(expected)

    val paymentMetadataRetrieved = generalPaymentMetadataClient.request(
      GeneralPaymentMetadataApi.Get(
        businessGuid = businessGuid,
        paymentMetadataGuid = uuidGenerator[0],
      ))
    assertThat(paymentMetadataRetrieved).isEqualTo(expected)
  }
}
