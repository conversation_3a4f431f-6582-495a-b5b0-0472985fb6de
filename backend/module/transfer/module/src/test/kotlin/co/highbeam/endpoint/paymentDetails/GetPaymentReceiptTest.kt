package co.highbeam.endpoint.paymentDetails

import co.highbeam.api.paymentDetails.PaymentDetailsApi
import co.highbeam.exception.paymentMetadata.PaymentMetadataNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.highbeam.testing.setUpPaymentMetadata
import com.currencycloud.client.CurrencyCloudClient
import com.currencycloud.client.model.PaymentSubmission
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetPaymentReceiptTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  @Test
  fun `returns mt103 if one is available`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val paymentId = UUID.randomUUID()

    val paymentMetadata = setUpPaymentMetadata(paymentId = paymentId, businessGuid = businessGuid)

    coEvery {
      get<CurrencyCloudClient>().retrievePaymentSubmission(paymentId.toString())
    } returns PaymentSubmission.create(
      "status",
      "submissionRef",
      "mt103"
    )

    assertThat(paymentDetailsClient.request(
      endpoint = PaymentDetailsApi.GetPaymentReceipt(
        businessGuid = businessGuid,
        paymentMetadataGuid = paymentMetadata.guid
      )
    )).isEqualTo("mt103")
  }

  @Test
  fun `errors if no metadata is available`() = integrationTest {
    val businessGuid = UUID.randomUUID()

    assertHighbeamException {
      paymentDetailsClient.request(
        endpoint = PaymentDetailsApi.GetPaymentReceipt(
          businessGuid = businessGuid,
          paymentMetadataGuid = UUID.randomUUID()
        ))
    }.isHighbeamException(unprocessable(PaymentMetadataNotFound()))
  }
}
