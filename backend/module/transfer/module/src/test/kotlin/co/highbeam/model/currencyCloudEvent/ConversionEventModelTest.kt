package co.highbeam.model.currencyCloudEvent

import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime

internal class ConversionEventModelTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {

  @Test
  fun `deserializes conversion event`() = test {
    val json = objectMapper.readValue<JsonNode>(
      Resources.getResource("currencyCloudWebhook/conversion.json").readText()
    )

    assertThat(objectMapper.convertValue<ConversionEventModel>(json)).isEqualTo(
      ConversionEventModel(
        eventId = "a0d9034e-bc9f-45e7-a1e4-6485735794c0",
        type = "conversion",
        createdAt = ZonedDateTime.parse("2018-04-30T12:49:06Z"),
        accountId = "d2b04c30-9585-4ba6-acea-bf9add10444d",
        coreRate = "0.7104",
        clientRate = "0.7249",
        creatorContactId = "669b4860-4bb3-4636-8ee4-9e672810d350",
        currencyPair = "GBPUSD",
        notificationType = "trade_closed_notification",
        paymentIds = emptyList(),
        status = "closed",
      )
    )
  }
}
