package co.highbeam.endpoint.paymentDetails

import co.highbeam.api.paymentDetails.PaymentDetailsApi
import co.highbeam.mapper.paymentMetadata.PaymentMetadataMapper
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.highbeam.testing.setUpPaymentMetadata
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetPaymentDetailsTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  @Test
  fun `happy path get`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val paymentId = UUID.randomUUID()

    val paymentMetadata = setUpPaymentMetadata(paymentId = paymentId, businessGuid = businessGuid)

    val paymentDetails = paymentDetailsClient.request(
      endpoint = PaymentDetailsApi.Get(
        businessGuid = businessGuid,
        paymentMetadataGuid = paymentMetadata.guid
      )
    )

    val mapper = get<PaymentMetadataMapper>()

    assertThat(paymentDetails).isEqualTo(mapper.completeRep(paymentMetadata))
  }

  @Test
  fun `happy path no metadata`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val paymentId = UUID.randomUUID()

    setUpPaymentMetadata(paymentId = paymentId, businessGuid = businessGuid)

    val paymentDetails = paymentDetailsClient.request(
      endpoint = PaymentDetailsApi.Get(
        businessGuid = UUID.randomUUID(),
        paymentMetadataGuid = UUID.randomUUID()
      )
    )

    assertThat(paymentDetails).isEqualTo(null)
  }

  @Test
  fun `does not return any values if the business id is invalid`() = integrationTest {
    val paymentId = UUID.randomUUID()

    val paymentMetadata = setUpPaymentMetadata(paymentId = paymentId)

    val paymentDetails = paymentDetailsClient.request(
      endpoint = PaymentDetailsApi.Get(
        businessGuid = UUID.randomUUID(),
        paymentMetadataGuid = paymentMetadata.guid
      )
    )

    assertThat(paymentDetails).isEqualTo(null)
  }

}
