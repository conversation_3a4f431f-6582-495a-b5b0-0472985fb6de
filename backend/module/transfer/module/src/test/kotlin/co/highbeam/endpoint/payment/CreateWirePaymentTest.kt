package co.highbeam.endpoint.payment

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.payment.PaymentApi
import co.highbeam.client.bankAccount.BankAccountClient
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.TransferRep
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.payment.WirePaymentRep
import co.highbeam.server.Server
import co.highbeam.testing.TransferFeatureIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.AddressRep
import co.unit.rep.DepositAccountRep
import co.unit.rep.UnitCoWirePaymentRep
import io.mockk.coEvery
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.assertTrue

internal class CreateWirePaymentTest(
  server: Server<*>,
) : TransferFeatureIntegrationTest(server) {
  @Test
  fun `happy path Highbeam`() = integrationTest {
    mockUnitCoPaymentCreation()
    mockBankAccountClient()

    val payeeGuid = UUID.randomUUID()

    val payment = WirePaymentRep.Creator(
      bankAccountGuid = uuidGenerator[1],
      amount = Money(100),
      inlineCounterparty = WirePaymentRep.InlineCounterparty(
        routingNumber = ProtectedString("*********"),
        accountNumber = ProtectedString("**********"),
        name = "Highbeam User 1",
        address = TransferRep.Address(
          addressLine1 = "123 Main St",
          addressLine2 = "Apt A",
          city = "New York",
          state = "NY",
          zipCode = "10004",
          country = "US",
        )
      ),
      description = "Why would you need a description " +
        "this long to remember the transaction? Let's trim it.",
      idempotencyKey = UUID.randomUUID(),
      payeeGuid = payeeGuid,
    )

    val expected = WirePaymentRep.Complete(
      unitCoId = uuidGenerator[0].toString(),
      type = "wirePayment",
      createdAt = ZonedDateTime.of(
        2007, 12, 3, 5, 15, 30, 789_000_000,
        ZoneOffset.UTC,
      ),
      amount = Money(100),
      status = "Sent",
      payeeGuid = payeeGuid,
    )

    assertTrue { payment.validate().isValid }

    assertThat(paymentClient.request(PaymentApi.CreateWire(payment)))
      .isEqualTo(expected)

    coVerify(exactly = 1) {
      get<UnitCoClient>().payment.createWire(
        UnitCoWirePaymentRep.Creator(
          amount = Money(100),
          inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
            routingNumber = "*********",
            accountNumber = "**********",
            name = "Highbeam User 1",
            address = AddressRep(
              street = "123 Main St",
              street2 = "Apt A",
              city = "New York",
              state = "NY",
              postalCode = "10004",
              country = "US",
            ),
          ),
          description = "Why would you need a description this " +
            "long to remember the transaction? Let's tr",
          idempotencyKey = payment.idempotencyKey,
          payeeGuid = payeeGuid,
          fromAccountType = "depositAccount",
          fromAccountId = "697843",
        )
      )
    }
  }

  private fun mockUnitCoPaymentCreation() {
    coEvery { get<UnitCoClient>().payment.createWire(any()) } answers {
      val creator = firstArg<UnitCoWirePaymentRep.Creator>()
      val id = uuidGenerator.generate().toString()
      return@answers UnitCoWirePaymentRep.Complete(
        id = id,
        createdAt = ZonedDateTime.of(
          2007, 12, 3, 5, 15, 30, 789_000_000,
          ZoneOffset.UTC,
        ),
        amount = creator.amount,
        description = creator.description,
        inlineCounterparty = creator.inlineCounterparty,
        status = UnitCoWirePaymentRep.Status.Sent,
        reason = null,
        payeeGuid = creator.payeeGuid,
      )
    }
  }

  private fun mockBankAccountClient() {
    coEvery { get<BankAccountClient>().request(BankAccountApi.Get(uuidGenerator[1])) } returns
      BankAccountRep.Complete(
        guid = uuidGenerator[1],
        unitCoDepositAccountId = "697843",
        businessGuid = UUID.randomUUID(),
        name = "Highbeam Bank",
        status = BankAccountRep.Status.OPEN,
        isPrimary = false,
        availableBalance = Balance(600),
        routingNumber = "*********",
        accountNumber = "142683",
        type = "depositAccount",
        highbeamType = BankAccountRep.Type.DepositAccount,
        depositProduct = DepositAccountRep.DepositProduct.Checking,
        minimumRequiredBalance = Balance.ZERO,
      )
  }
}
