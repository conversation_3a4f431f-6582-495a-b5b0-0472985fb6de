package co.highbeam.rep.businessMember

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.slug.Slug
import co.highbeam.util.string.fullName
import co.highbeam.util.string.initials
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import java.time.LocalDate
import java.util.UUID

object BusinessMemberRep {
  data class Creator(
    val userInvitationSlug: Slug,
    val userGuid: UUID,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Complete(
    val guid: UUID,
    val businessGuid: UUID,
    val addedByUserGuid: UUID?,
    val userGuid: UUID,
    val emailAddress: String?,
    val firstName: String?,
    val lastName: String?,
    val phoneNumber: String?,
    val dateOfBirth: LocalDate?,
    val isOnboarded: Boolean?,
  ) : CompleteRep {
    val fullName: String? = fullName(firstName, lastName)
    val displayName: String = fullName ?: emailAddress ?: "Deleted user"
    val initials: String = initials(firstName, lastName)
      ?: emailAddress?.let { it.first().uppercase() }
      ?: "-"
  }

  data class Updater(
    val firstName: String? = null,
    val lastName: String? = null,
    val emailAddress: String? = null,
    val phoneNumber: String? = null,
    val dateOfBirth: LocalDate? = null,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::firstName) { ifPresent { Validator.humanName(this) } }
      validate(Updater::lastName) { ifPresent { Validator.humanName(this) } }
      validate(Updater::emailAddress) { ifPresent { Validator.emailAddress(this) } }
      validate(Updater::phoneNumber) { ifPresent { Validator.phoneNumber(this) } }
    }
  }
}
