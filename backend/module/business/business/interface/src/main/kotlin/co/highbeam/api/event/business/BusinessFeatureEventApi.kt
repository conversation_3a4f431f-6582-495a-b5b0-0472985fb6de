package co.highbeam.api.event.business

import co.highbeam.event.business.UnitCoCustomerCreatedEvent
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod

/**
 * This event API (and all Highbeam "Event" APIs, which are in the same package) is TEMPORARY until
 * Highbeam gets a messaging platform in place. Usages of this API would be better served by
 * asynchronous messaging than by synchronous REST.
 */
object BusinessFeatureEventApi {
  data class UnitCoCustomerCreated(val event: UnitCoCustomerCreatedEvent) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/events/business-feature/unit-co-customer-created",
    body = event,
  )
}
