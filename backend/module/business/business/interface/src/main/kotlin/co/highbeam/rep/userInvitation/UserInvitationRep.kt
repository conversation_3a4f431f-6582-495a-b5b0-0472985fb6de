package co.highbeam.rep.userInvitation

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.slug.Slug
import co.highbeam.util.string.fullName
import co.highbeam.util.string.initials
import co.highbeam.util.testing.TestingOnly
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import java.util.UUID

object UserInvitationRep {
  data class Creator(
    val firstName: String,
    val lastName: String,
    val emailAddress: String,
    val phoneNumber: String?,
    val senderUserGuid: UUID,
    val userRoleGuids: List<UUID>,
  ) : CreatorRep {
    @TestingOnly
    constructor(invitation: Complete) : this(
      firstName = invitation.firstName,
      lastName = invitation.lastName,
      emailAddress = invitation.emailAddress,
      phoneNumber = invitation.phoneNumber,
      senderUserGuid = invitation.senderUserGuid,
      userRoleGuids = invitation.userRoleGuids,
    )

    override fun validate(): RepValidation = RepValidation {
      validate(Creator::firstName) { Validator.humanName(this) }
      validate(Creator::lastName) { Validator.humanName(this) }
      validate(Creator::emailAddress) { Validator.emailAddress(this) }
      validate(Creator::phoneNumber) { ifPresent { Validator.phoneNumber(this) } }
      validate(Creator::userRoleGuids) { isNotEmpty() }
    }
  }

  data class Complete(
    val guid: UUID,
    val businessGuid: UUID,
    val slug: Slug,
    val businessDisplayName: String?,
    val firstName: String,
    val lastName: String,
    val emailAddress: String,
    val phoneNumber: String?,
    val senderUserGuid: UUID,
    val senderName: String,
    val userRoleGuids: List<UUID>,
    val accepted: Boolean,
  ) : CompleteRep {
    val fullName: String = checkNotNull(fullName(firstName, lastName))
    val displayName: String = fullName
    val initials: String = initials(firstName, lastName) ?: emailAddress.first().uppercase()
  }
}
