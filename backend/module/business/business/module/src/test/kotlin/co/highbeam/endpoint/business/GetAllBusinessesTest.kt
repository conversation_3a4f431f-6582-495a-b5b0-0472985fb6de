package co.highbeam.endpoint.business

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.user.UserClient
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.user.UserRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class GetAllBusinessesTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `no businesses`() = integrationTest {
    assertThat(businessClient.request(BusinessApi.GetAll))
      .isEmpty()
  }

  @Test
  fun `business exists`() = integrationTest {
    val businesses = List(2) { i ->
      BusinessRep.Complete(
        guid = uuidGenerator[i * 2],
        dba = null,
        name = null,
        referralLinkGuid = null,
        ownerUserGuid = uuidGenerator[i * 2],
        unitCoCustomerId = null,
        status = BusinessRep.Complete.Status.PendingReview,
        stateOfIncorporation = null,
        naics = null,
      )
    }

    coEvery {
      get<UserClient>().request(UserApi.Get(uuidGenerator[0]))
    } returns UserRep.Complete(
      guid = uuidGenerator[2],
      firstName = null,
      lastName = null,
      emailAddress = "<EMAIL>",
      profilePhotoUrl = null,
    )

    coEvery {
      get<UserClient>().request(UserApi.Get(uuidGenerator[2]))
    } returns UserRep.Complete(
      guid = uuidGenerator[2],
      firstName = null,
      lastName = null,
      emailAddress = "<EMAIL>",
      profilePhotoUrl = null,
    )

    businesses.forEach { business ->
      businessClient.request(BusinessApi.Create(BusinessRep.Creator(business)))
    }
    assertThat(businessClient.request(BusinessApi.GetAll))
      .containsExactlyInAnyOrderElementsOf(businesses)
  }
}
