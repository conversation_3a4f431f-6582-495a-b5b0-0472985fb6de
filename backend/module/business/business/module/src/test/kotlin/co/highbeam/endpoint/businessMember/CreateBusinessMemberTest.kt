package co.highbeam.endpoint.businessMember

import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.user.UserApi
import co.highbeam.api.userInvitation.UserInvitationApi
import co.highbeam.client.user.UserClient
import co.highbeam.exception.unprocessable
import co.highbeam.exception.userInvitation.UserInvitationNotFound
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.rep.userInvitation.UserInvitationRep
import co.highbeam.server.Server
import co.highbeam.slug.Slug
import co.highbeam.testing.BusinessFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class CreateBusinessMemberTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `invitation does not exist`() = integrationTest {
    assertHighbeamException {
      businessMemberClient.request(
        BusinessMemberApi.Create(
          member = BusinessMemberRep.Creator(Slug(UUID.randomUUID()), UUID.randomUUID()),
        ),
      )
    }.isHighbeamException(unprocessable(UserInvitationNotFound()))
  }

  @Test
  fun `happy path`() = integrationTest {
    val (ownerUser, memberUser) = createUsers()
    val business = createBusiness(ownerUserGuid = ownerUser.guid)
    val invitation = createInvitation(
      2,
      business = business,
      sender = ownerUser,
      firstName = checkNotNull(memberUser.firstName),
      lastName = checkNotNull(memberUser.lastName),
      emailAddress = memberUser.emailAddress,
    )

    val member = BusinessMemberRep.Complete(
      guid = uuidGenerator[4],
      businessGuid = business.guid,
      addedByUserGuid = ownerUser.guid,
      userGuid = memberUser.guid,
      emailAddress = memberUser.emailAddress,
      firstName = memberUser.firstName,
      lastName = memberUser.lastName,
      phoneNumber = "*************",
      dateOfBirth = null,
      isOnboarded = false,
    )
    assertThat(
      businessMemberClient.request(
        BusinessMemberApi.Create(BusinessMemberRep.Creator(invitation.slug, memberUser.guid)),
      ),
    ).isEqualTo(member)

    assertThat(
      userInvitationClient.request(
        UserInvitationApi.GetByBusiness(business.guid, accepted = false),
      )
    ).isEmpty()
  }

  private fun createUsers(): Pair<UserRep.Complete, UserRep.Complete> {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser
    val memberUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Gautam",
      lastName = "Gupta",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(memberUser.guid)) } returns memberUser
    return Pair(ownerUser, memberUser)
  }

  private suspend fun createInvitation(
    i: Int,
    business: BusinessRep.Complete,
    sender: UserRep.Complete,
    firstName: String,
    lastName: String,
    emailAddress: String,
  ): UserInvitationRep.Complete {
    val invitation = UserInvitationRep.Complete(
      guid = uuidGenerator[i],
      businessGuid = business.guid,
      slug = Slug(uuidGenerator[i + 1]),
      businessDisplayName = business.displayName,
      firstName = firstName,
      lastName = lastName,
      emailAddress = emailAddress,
      phoneNumber = "*************",
      senderUserGuid = sender.guid,
      senderName = checkNotNull(sender.firstName),
      userRoleGuids = listOf(UUID.randomUUID()),
      accepted = false,
    )
    coEvery {
      get<UserClient>().request(UserApi.GetByEmailAddress(invitation.emailAddress))
    } returns null
    userInvitationClient.request(
      UserInvitationApi.Create(
        businessGuid = business.guid,
        invitation = UserInvitationRep.Creator(invitation),
      ),
    )
    return invitation
  }
}
