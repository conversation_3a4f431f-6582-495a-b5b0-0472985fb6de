package co.highbeam.endpoint.businessMember

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.user.UserClient
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.LocalDate
import java.time.Month
import java.util.UUID

internal class UpdateBusinessMemberTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `business member does not exist`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val memberGuid = UUID.randomUUID()

    assertThat(businessMemberClient.request(BusinessMemberApi.Update(
      businessGuid = businessGuid,
      memberGuid = memberGuid,
      rep = BusinessMemberRep.Updater(),
    ))).isNull()
  }

  @Test
  fun `business member exists`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser

    val businessGuid = uuidGenerator[0]

    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = ownerUser.guid,
    )))

    val updatedOwnerMember = BusinessMemberRep.Complete(
      guid = uuidGenerator[1],
      businessGuid = businessGuid,
      addedByUserGuid = null,
      userGuid = ownerUser.guid,
      emailAddress = "<EMAIL>",
      firstName = "Max",
      lastName = "Wu",
      phoneNumber = "**************",
      dateOfBirth = LocalDate.of(2000, Month.JANUARY, 1),
      isOnboarded = false,
    )

    assertThat(businessMemberClient.request(
      BusinessMemberApi.Update(businessGuid, updatedOwnerMember.guid,
        BusinessMemberRep.Updater(
          dateOfBirth = LocalDate.of(2000, Month.JANUARY, 1),
          phoneNumber = "**************",
          firstName = "Max",
          lastName = "Wu",
          emailAddress = "<EMAIL>"
        ))
    ))
      .isEqualTo(updatedOwnerMember)
  }
}
