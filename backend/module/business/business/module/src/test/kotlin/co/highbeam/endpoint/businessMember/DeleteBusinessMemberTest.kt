package co.highbeam.endpoint.businessMember

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.user.UserApi
import co.highbeam.api.userInvitation.UserInvitationApi
import co.highbeam.client.user.UserClient
import co.highbeam.event.businessMember.BusinessMemberChangeEvent
import co.highbeam.event.publisher.getPublisher
import co.highbeam.exception.businessMember.BusinessMemberNotFound
import co.highbeam.exception.businessMember.CannotDeleteBusinessOwner
import co.highbeam.exception.unprocessable
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.rep.userInvitation.UserInvitationRep
import co.highbeam.server.Server
import co.highbeam.slug.Slug
import co.highbeam.testing.BusinessFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class DeleteBusinessMemberTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  private val topicName = "business-member-change"
  private val businessMemberPublisher = getPublisher<BusinessMemberChangeEvent>(topicName)

  @Test
  fun `member does not exist`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val memberGuid = UUID.randomUUID()

    assertHighbeamException {
      assertThat(businessMemberClient.request(
        BusinessMemberApi.Delete(
          businessGuid = businessGuid,
          memberGuid = memberGuid,
          shouldArchiveResources = true,
        ),
      ))
    }.isHighbeamException(unprocessable(BusinessMemberNotFound()))
  }

  @Test
  fun `cannot delete owner`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser

    val businessGuid = uuidGenerator[0]
    val ownerMember = BusinessMemberRep.Complete(
      guid = uuidGenerator[1],
      businessGuid = businessGuid,
      addedByUserGuid = null,
      userGuid = ownerUser.guid,
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      phoneNumber = null,
      dateOfBirth = null,
      isOnboarded = true,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = ownerUser.guid,
    )))

    assertHighbeamException {
      businessMemberClient.request(
        BusinessMemberApi.Delete(
          businessGuid = businessGuid,
          memberGuid = ownerMember.guid,
          shouldArchiveResources = true,
        ),
      )
    }.isHighbeamException(CannotDeleteBusinessOwner())

    assertThat(businessMemberClient.request(
      BusinessMemberApi.Get(businessGuid, ownerMember.guid),
    ))
      .isNotNull()
  }

  @Test
  fun `member exists`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser

    val nonOwnerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Alex",
      lastName = "Akagi",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(nonOwnerUser.guid)) } returns nonOwnerUser

    val businessGuid = uuidGenerator[0]
    val business = businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = ownerUser.guid,
    )))

    val invitation = UserInvitationRep.Complete(
      guid = uuidGenerator[2],
      businessGuid = business.guid,
      slug = Slug(uuidGenerator[3]),
      businessDisplayName = "Foo",
      firstName = "Alex",
      lastName = "Akagi",
      emailAddress = "<EMAIL>",
      phoneNumber = null,
      senderUserGuid = ownerUser.guid,
      senderName = "Jeff",
      userRoleGuids = listOf(UUID.randomUUID()),
      accepted = false,
    )
    coEvery {
      get<UserClient>().request(UserApi.GetByEmailAddress(invitation.emailAddress))
    } returns null
    val nonOwnerMember = BusinessMemberRep.Complete(
      guid = uuidGenerator[4],
      businessGuid = business.guid,
      addedByUserGuid = ownerUser.guid,
      userGuid = nonOwnerUser.guid,
      emailAddress = "<EMAIL>",
      firstName = "Alex",
      lastName = "Akagi",
      phoneNumber = null,
      dateOfBirth = null,
      isOnboarded = false,
    )
    userInvitationClient.request(UserInvitationApi.Create(
      businessGuid = business.guid,
      invitation = UserInvitationRep.Creator(
        firstName = "Alex",
        lastName = "Akagi",
        emailAddress = "<EMAIL>",
        phoneNumber = null,
        senderUserGuid = ownerUser.guid,
        userRoleGuids = listOf(UUID.randomUUID()),
      ),
    ))

    businessMemberClient.request(
      BusinessMemberApi.Create(
        BusinessMemberRep.Creator(
          userInvitationSlug = invitation.slug,
          userGuid = nonOwnerUser.guid,
        ),
      ))

    assertThat(
      businessMemberClient.request(
        BusinessMemberApi.Get(businessGuid, nonOwnerMember.guid),
      ))
      .isEqualTo(nonOwnerMember)

    assertThat(businessMemberClient.request(
      BusinessMemberApi.Delete(
        businessGuid = businessGuid,
        memberGuid = nonOwnerMember.guid,
        shouldArchiveResources = true,
      )))
      .isEqualTo(nonOwnerMember)

    assertThat(businessMemberClient.request(
      BusinessMemberApi.Get(businessGuid, nonOwnerMember.guid),
    ))
      .isNull()

    assertThat(businessMemberPublisher.events).hasSize(1)
    assertThat(businessMemberPublisher.events.first()).isEqualTo(
      Pair(
        BusinessMemberChangeEvent(
          businessGuid = businessGuid,
          businessUnitCoCustomerId = null,
          businessMemberGuid = nonOwnerMember.guid,
          userGuid = nonOwnerMember.userGuid,
          userEmailAddress = "<EMAIL>",
          type = BusinessMemberChangeEvent.Type.Deleted,
          actions = listOf(BusinessMemberChangeEvent.Action.ArchiveCards),
        ),
        emptyMap<String, String>()
      )
    )
  }
}
