package co.highbeam.endpoint.businessMember

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.user.UserClient
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetBusinessMemberTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `member does not exist`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val invitationGuid = UUID.randomUUID()

    assertThat(businessMemberClient.request(BusinessMemberApi.Get(businessGuid, invitationGuid)))
      .isNull()
  }

  @Test
  fun `member exists`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser

    val businessGuid = uuidGenerator[0]
    val ownerMember = BusinessMemberRep.Complete(
      guid = uuidGenerator[1],
      businessGuid = businessGuid,
      addedByUserGuid = null,
      userGuid = ownerUser.guid,
      emailAddress = ownerUser.emailAddress,
      firstName = ownerUser.firstName,
      lastName = ownerUser.lastName,
      phoneNumber = null,
      dateOfBirth = null,
      isOnboarded = false,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = ownerUser.guid,
    )))

    assertThat(businessMemberClient.request(BusinessMemberApi.Get(businessGuid, ownerMember.guid)))
      .isEqualTo(ownerMember)
  }
}
