package co.highbeam.endpoint.bankApplication

import co.highbeam.api.bankApplication.BankApplicationApi
import co.highbeam.api.business.BusinessApi
import co.highbeam.api.lead.LeadApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.lead.LeadClient
import co.highbeam.client.user.UserClient
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.bankApplication.BankApplicationRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.ApplicationFormRep
import co.unit.rep.BusinessContactRep
import co.unit.rep.FullNameRep
import co.unit.rep.PhoneRep
import co.unit.rep.UnitCompleteRep
import com.auth0.client.Auth0ManagementClient
import com.auth0.rep.Auth0EnrollmentRep
import com.auth0.rep.Auth0User
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class CreateBankApplicationTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `business does not exist`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    assertHighbeamException {
      bankApplicationClient.request(BankApplicationApi.Create(businessGuid))
    }.isHighbeamException(unprocessable(BusinessNotFound()))
  }

  @Test
  fun `happy path`() = integrationTest {
    val auth0User = Auth0User.Complete(
      userId = UUID.randomUUID().toString(),
    )
    val businessName = "Acme Corporation"
    val emailAddress = "<EMAIL>"
    val firstName = "Max"
    val lastName = "Wu"
    mockAllUsers(emailAddress)
    mockAllLeads(businessName, emailAddress, firstName, lastName)
    val business = createBusiness()
    mockUnitCoApplicationFormCreation()
    mockAuth0User(auth0User)

    val application = BankApplicationRep.Complete(
      businessGuid = business.guid,
      unitCoApplicationId = null,
      status = BankApplicationRep.Status.FormCreated,
      url = "url ${uuidGenerator[2]}"
    )

    assertThat(bankApplicationClient.request(BankApplicationApi.Create(business.guid)))
      .isEqualTo(application)

    coVerify(exactly = 1) {
      get<UnitCoClient>().applicationForm.create(
        creator = ApplicationFormRep.Creation(
          applicantDetails = ApplicationFormRep.Creation.ApplicantDetails(
            name = businessName,
            phone = PhoneRep("1", "**********"),
            contact = BusinessContactRep(
              fullName = FullNameRep("Max", "Wu"),
              email = emailAddress,
              phone = PhoneRep("1", "**********"),
            )
          ),
          tags = ApplicationFormRep.Creation.Tags(businessGuid = business.guid),
          allowedApplicationTypes = listOf("Business")
        ),
      )
    }
  }

  @Test
  fun `no lead`() = integrationTest {
    val auth0User = Auth0User.Complete(
      userId = UUID.randomUUID().toString(),
    )
    val emailAddress = "<EMAIL>"
    mockAllUsers(emailAddress)
    val business = createBusiness()
    mockUnitCoApplicationFormCreation()
    mockAuth0User(auth0User)
    coEvery { get<LeadClient>().request(any<LeadApi.GetByEmailAddress>()) } returns null

    val application = BankApplicationRep.Complete(
      businessGuid = business.guid,
      unitCoApplicationId = null,
      status = BankApplicationRep.Status.FormCreated,
      url = "url ${uuidGenerator[2]}"
    )

    assertThat(bankApplicationClient.request(BankApplicationApi.Create(business.guid)))
      .isEqualTo(application)

    coVerify(exactly = 1) {
      get<UnitCoClient>().applicationForm.create(
        creator = ApplicationFormRep.Creation(
          applicantDetails = ApplicationFormRep.Creation.ApplicantDetails(
            name = null,
            phone = PhoneRep("1", "**********"),
            contact = BusinessContactRep(
              fullName = null,
              email = emailAddress,
              phone = PhoneRep("1", "**********"),
            )
          ),
          tags = ApplicationFormRep.Creation.Tags(businessGuid = business.guid),
          allowedApplicationTypes = listOf("Business")
        ),
      )
    }
  }

  @Test
  fun `lead with blank names`() = integrationTest {
    val auth0User = Auth0User.Complete(
      userId = UUID.randomUUID().toString(),
    )
    val businessName = "Acme Corporation"
    val emailAddress = "<EMAIL>"
    val firstName = ""
    val lastName = ""
    mockAllUsers(emailAddress)
    mockAllLeads(businessName, emailAddress, firstName, lastName)
    val business = createBusiness()
    mockUnitCoApplicationFormCreation()
    mockAuth0User(auth0User)

    val application = BankApplicationRep.Complete(
      businessGuid = business.guid,
      unitCoApplicationId = null,
      status = BankApplicationRep.Status.FormCreated,
      url = "url ${uuidGenerator[2]}"
    )

    assertThat(bankApplicationClient.request(BankApplicationApi.Create(business.guid)))
      .isEqualTo(application)

    coVerify(exactly = 1) {
      get<UnitCoClient>().applicationForm.create(
        creator = ApplicationFormRep.Creation(
          applicantDetails = ApplicationFormRep.Creation.ApplicantDetails(
            name = businessName,
            phone = PhoneRep("1", "**********"),
            contact = BusinessContactRep(
              fullName = null,
              email = emailAddress,
              phone = PhoneRep("1", "**********"),
            )
          ),
          tags = ApplicationFormRep.Creation.Tags(businessGuid = business.guid),
          allowedApplicationTypes = listOf("Business")
        ),
      )
    }
  }

  private fun mockAllUsers(
    emailAddress: String
  ) {
    coEvery { get<UserClient>().request(any<UserApi.Get>()) } answers {
      mockk {
        every { <EMAIL> } returns firstArg<UserApi.Get>().userGuid
        every { <EMAIL> } returns emailAddress
        every { <EMAIL> } returns null
        every { <EMAIL> } returns null
      }
    }
  }

  private fun mockAllLeads(
    businessName: String,
    emailAddress: String,
    firstName: String,
    lastName: String,
  ) {
    val objectMapper = ObjectMapper()
    val other: ObjectNode = objectMapper.createObjectNode()
    other.put("business_name", businessName)

    coEvery { get<LeadClient>().request(any<LeadApi.GetByEmailAddress>()) } answers {
      mockk {
        every { <EMAIL> } returns emailAddress
        every { <EMAIL> } returns other
        every { <EMAIL> } returns firstName
        every { <EMAIL> } returns lastName

      }
    }
  }

  private suspend fun createBusiness(): BusinessRep.Complete {
    val business = BusinessRep.Complete(
      guid = uuidGenerator[0],
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = UUID.randomUUID(),
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.Active,
      stateOfIncorporation = null,
      naics = null,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = business.ownerUserGuid,
    )))
    return business
  }

  private fun mockUnitCoApplicationFormCreation() {
    coEvery { get<UnitCoClient>().applicationForm.create(any()) } answers {
      val creator = firstArg<ApplicationFormRep.Creation>()
      val id = uuidGenerator.generate().toString()
      return@answers UnitCompleteRep(
        type = "applicationForm",
        id = id,
        attributes = ApplicationFormRep.Complete(
          stage = "ChooseBusinessOrIndividual",
          url = "url $id",
          tags = ApplicationFormRep.Complete.Tags(businessGuid = creator.tags.businessGuid),
        ),
        relationships = emptyMap(),
        json = objectMapper.createObjectNode())
    }
  }

  private fun mockAuth0User(auth0User: Auth0User.Complete) {
    coEvery {
      get<Auth0ManagementClient>().getUsersByEmailAddress(any())
    } returns listOf(auth0User)
    coEvery {
      get<Auth0ManagementClient>().getUserEnrollments(any())
    } returns listOf(Auth0EnrollmentRep.Complete(
      id = auth0User.userId,
      phoneNumber = "**********",
      authMethod = "sms",
    ))
  }
}
