package co.highbeam.endpoint.business

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.user.UserApi
import co.highbeam.api.userInvitation.UserInvitationApi
import co.highbeam.client.user.UserClient
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessMember.BusinessMemberRep
import co.highbeam.rep.user.UserRep
import co.highbeam.rep.userInvitation.UserInvitationRep
import co.highbeam.server.Server
import co.highbeam.slug.Slug
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetBusinessByMemberUserTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `business does not exist`() = integrationTest {
    assertThat(businessClient.request(BusinessApi.GetByMemberUser(UUID.randomUUID())))
      .isEmpty()
  }

  @Test
  fun `business exists, owner member`() = integrationTest {
    val ownerUserGuid = UUID.randomUUID()
    val business = BusinessRep.Complete(
      guid = uuidGenerator[0],
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUserGuid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    coEvery {
      get<UserClient>().request(UserApi.Get(ownerUserGuid))
    } returns UserRep.Complete(
      guid = UUID.randomUUID(),
      firstName = null,
      lastName = null,
      emailAddress = "<EMAIL>",
      profilePhotoUrl = null,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(business)))
    assertThat(businessClient.request(BusinessApi.GetByMemberUser(ownerUserGuid)))
      .containsExactlyInAnyOrder(business)
  }

  @Test
  fun `business exists, non-owner member`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser
    val memberUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Gautam",
      lastName = "Gupta",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(memberUser.guid)) } returns memberUser

    val business = BusinessRep.Complete(
      guid = uuidGenerator[0],
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUser.guid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(business)))

    val invitation = UserInvitationRep.Complete(
      guid = uuidGenerator[2],
      slug = Slug(uuidGenerator[3]),
      businessGuid = business.guid,
      businessDisplayName = business.displayName,
      firstName = checkNotNull(memberUser.firstName),
      lastName = checkNotNull(memberUser.lastName),
      emailAddress = memberUser.emailAddress,
      phoneNumber = "*************",
      senderUserGuid = ownerUser.guid,
      senderName = checkNotNull(ownerUser.firstName),
      userRoleGuids = listOf(UUID.randomUUID()),
      accepted = false,
    )
    coEvery {
      get<UserClient>().request(UserApi.GetByEmailAddress(invitation.emailAddress))
    } returns null
    userInvitationClient.request(UserInvitationApi.Create(
      businessGuid = business.guid,
      invitation = UserInvitationRep.Creator(invitation),
    ))

    businessMemberClient.request(BusinessMemberApi.Create(
      member = BusinessMemberRep.Creator(invitation.slug, memberUser.guid),
    ))

    assertThat(businessClient.request(BusinessApi.GetByMemberUser(memberUser.guid)))
      .containsExactlyInAnyOrder(business)
  }
}
