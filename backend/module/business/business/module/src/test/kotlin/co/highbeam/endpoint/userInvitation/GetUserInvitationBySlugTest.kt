package co.highbeam.endpoint.userInvitation

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.user.UserApi
import co.highbeam.api.userInvitation.UserInvitationApi
import co.highbeam.client.user.UserClient
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.user.UserRep
import co.highbeam.rep.userInvitation.UserInvitationRep
import co.highbeam.server.Server
import co.highbeam.slug.Slug
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class GetUserInvitationBySlugTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `invitation does not exist`() = integrationTest {
    val invitationSlug = Slug(UUID.randomUUID())
    assertThat(userInvitationClient.request(UserInvitationApi.GetBySlug(invitationSlug)))
      .isNull()
  }

  @Test
  fun `invitation exists`() = integrationTest {
    val ownerUser = UserRep.Complete(
      guid = UUID.randomUUID(),
      emailAddress = "<EMAIL>",
      firstName = "Jeff",
      lastName = "Hudson",
      profilePhotoUrl = null,
    )
    coEvery { get<UserClient>().request(UserApi.Get(ownerUser.guid)) } returns ownerUser

    val business = BusinessRep.Complete(
      guid = uuidGenerator[0],
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUser.guid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.Active,
      stateOfIncorporation = null,
      naics = null,
    )
    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = ownerUser.guid,
    )))

    val invitation = UserInvitationRep.Complete(
      guid = uuidGenerator[2],
      businessGuid = business.guid,
      slug = Slug(uuidGenerator[3]),
      businessDisplayName = business.displayName,
      firstName = "Gautam",
      lastName = "Gupta",
      emailAddress = "<EMAIL>",
      phoneNumber = "*************",
      senderUserGuid = ownerUser.guid,
      senderName = checkNotNull(ownerUser.firstName),
      userRoleGuids = listOf(UUID.randomUUID()),
      accepted = false,
    )
    coEvery {
      get<UserClient>().request(UserApi.GetByEmailAddress(invitation.emailAddress))
    } returns null
    userInvitationClient.request(UserInvitationApi.Create(
      businessGuid = business.guid,
      invitation = UserInvitationRep.Creator(invitation),
    ))

    assertThat(userInvitationClient.request(UserInvitationApi.GetBySlug(invitation.slug)))
      .isEqualTo(invitation)
  }
}
