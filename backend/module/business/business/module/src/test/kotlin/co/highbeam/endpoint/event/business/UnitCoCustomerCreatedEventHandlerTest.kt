package co.highbeam.endpoint.event.business

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.event.business.BusinessFeatureEventApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.user.UserClient
import co.highbeam.event.business.UnitCoCustomerCreatedEvent
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.business.BusinessRep.StateOfIncorporation
import co.highbeam.rep.user.UserRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessFeatureIntegrationTest
import io.mockk.coEvery
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class UnitCoCustomerCreatedEventHandlerTest(
  server: Server<*>,
) : BusinessFeatureIntegrationTest(server) {
  @Test
  fun `happy path`() = integrationTest {
    val ownerUserGuid = UUID.randomUUID()
    var business = BusinessRep.Complete(
      guid = uuidGenerator[0],
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUserGuid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    coEvery {
      get<UserClient>().request(UserApi.Get(ownerUserGuid))
    } returns UserRep.Complete(
      guid = UUID.randomUUID(),
      firstName = null,
      lastName = null,
      emailAddress = "<EMAIL>",
      profilePhotoUrl = null,
    )

    businessClient.request(BusinessApi.Create(BusinessRep.Creator(
      referralLinkSlug = null,
      ownerUserGuid = business.ownerUserGuid,
    )))

    business = business.copy(
      dba = "Some DBA",
      name = "Some customer name",
      unitCoCustomerId = "customer-0",
      stateOfIncorporation = StateOfIncorporation.NY,
    )
    businessFeatureEventClient.request(BusinessFeatureEventApi.UnitCoCustomerCreated(
      event = UnitCoCustomerCreatedEvent(
        businessGuid = business.guid,
        dba = "Some DBA",
        name = "Some customer name",
        unitCoCustomerId = "customer-0",
        stateOfIncorporation = "NY",
      ),
    ))

    assertThat(businessClient.request(BusinessApi.Get(business.guid)))
      .isEqualTo(business)
  }
}
