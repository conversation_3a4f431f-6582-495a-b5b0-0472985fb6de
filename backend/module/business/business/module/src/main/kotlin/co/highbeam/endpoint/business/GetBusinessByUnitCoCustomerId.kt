package co.highbeam.endpoint.business

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.business.BusinessService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.business.BusinessApi as Api
import co.highbeam.rep.business.BusinessRep as Rep

internal class GetBusinessByUnitCoCustomerId @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val businessService: BusinessService,
) : EndpointHandler<Api.GetByUnitCoCustomerId, Rep.Complete>(
  template = Api.GetByUnitCoCustomerId::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByUnitCoCustomerId =
    Api.GetByUnitCoCustomerId(unitCoCustomerId = call.getParam("unitCoCustomerId"))

  override fun loggingContext(endpoint: Api.GetByUnitCoCustomerId) =
    super.loggingContext(endpoint) +
      mapOf("unitCoCustomerId" to endpoint.unitCoCustomerId)

  override suspend fun Handler.handle(endpoint: Api.GetByUnitCoCustomerId): Rep.Complete {
    val business = businessService.getByUnitCoCustomerId(endpoint.unitCoCustomerId)
      ?: throw BusinessNotFound()
    auth(authPermission(Permission.Business_Read) { business.guid })
    return business
  }
}
