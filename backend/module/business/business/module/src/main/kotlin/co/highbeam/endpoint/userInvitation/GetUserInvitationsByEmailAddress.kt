package co.highbeam.endpoint.userInvitation

import co.highbeam.api.user.UserApi
import co.highbeam.auth.auth.AuthUser
import co.highbeam.client.user.UserClient
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userInvitation.UserInvitationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userInvitation.UserInvitationApi as Api
import co.highbeam.rep.userInvitation.UserInvitationRep as Rep

internal class GetUserInvitationsByEmailAddress @Inject constructor(
  private val authUser: AuthUser.Provider,
  private val invitationService: UserInvitationService,
  private val userClient: UserClient,
) : EndpointHandler<Api.GetByEmailAddress, List<Rep.Complete>>(
  template = Api.GetByEmailAddress::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByEmailAddress =
    Api.GetByEmailAddress(
      emailAddress = call.getParam("emailAddress"),
      accepted = call.getParam("accepted", optional = true),
    )

  override fun loggingContext(endpoint: Api.GetByEmailAddress): Map<String, String?> =
    super.loggingContext(endpoint) + mapOf(
      "emailAddress" to endpoint.emailAddress,
      "accepted" to endpoint.accepted.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.GetByEmailAddress): List<Rep.Complete> {
    auth(authUser { userClient.request(UserApi.GetByEmailAddress(endpoint.emailAddress))?.guid })
    return invitationService.getByEmailAddress(
      emailAddress = endpoint.emailAddress,
      accepted = endpoint.accepted,
    )
  }
}
