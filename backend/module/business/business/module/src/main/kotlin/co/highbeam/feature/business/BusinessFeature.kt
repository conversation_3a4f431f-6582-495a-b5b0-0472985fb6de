package co.highbeam.feature.business

import co.highbeam.endpoint.authorizedUser.CreateAuthorizedUser
import co.highbeam.endpoint.bankApplication.CreateBankApplication
import co.highbeam.endpoint.bankApplication.GetBankApplicationsByBusiness
import co.highbeam.endpoint.business.CreateBusiness
import co.highbeam.endpoint.business.GetAllBusinesses
import co.highbeam.endpoint.business.GetBusiness
import co.highbeam.endpoint.business.GetBusinessByMemberUser
import co.highbeam.endpoint.business.GetBusinessByUnitCoCustomerId
import co.highbeam.endpoint.business.MigrateBusinessInformation
import co.highbeam.endpoint.business.SearchBusinessGuids
import co.highbeam.endpoint.business.SetBusinessOwner
import co.highbeam.endpoint.business.UpdateBusiness
import co.highbeam.endpoint.businessMember.CreateBusinessMember
import co.highbeam.endpoint.businessMember.DeleteBusinessMember
import co.highbeam.endpoint.businessMember.GetBusinessMember
import co.highbeam.endpoint.businessMember.GetBusinessMemberAdminsByBusiness
import co.highbeam.endpoint.businessMember.GetBusinessMembersOfUserRoleByBusiness
import co.highbeam.endpoint.businessMember.GetBusinessMemberByUser
import co.highbeam.endpoint.businessMember.GetBusinessMemberSummariesByUser
import co.highbeam.endpoint.businessMember.GetBusinessMembersByBusiness
import co.highbeam.endpoint.businessMember.UpdateBusinessMember
import co.highbeam.endpoint.businessMember.UpdateBusinessMemberByUserGuid
import co.highbeam.endpoint.event.business.UnitCoCustomerCreatedEventHandler
import co.highbeam.endpoint.referralLink.CreateReferralLink
import co.highbeam.endpoint.referralLink.DeleteReferralLink
import co.highbeam.endpoint.referralLink.GetReferralLink
import co.highbeam.endpoint.referralLink.GetReferralLinkByBusiness
import co.highbeam.endpoint.userInvitation.CreateUserInvitation
import co.highbeam.endpoint.userInvitation.DeleteUserInvitation
import co.highbeam.endpoint.userInvitation.GetUserInvitation
import co.highbeam.endpoint.userInvitation.GetUserInvitationBySlug
import co.highbeam.endpoint.userInvitation.GetUserInvitationsByBusiness
import co.highbeam.endpoint.userInvitation.GetUserInvitationsByEmailAddress
import co.highbeam.endpoint.userInvitation.ReSendUserInvitationEmail
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.feature.Feature
import co.highbeam.listener.business.BusinessApprovedListener
import co.highbeam.listener.business.BusinessCreationEmailListener
import co.highbeam.listener.business.BusinessRejectedListener
import co.highbeam.listener.business.EvaluationChangeListener
import co.highbeam.listener.business.UnitCoCustomerCreatedWebhookListener
import co.highbeam.listener.business.UnitCoWebhookListener
import co.highbeam.publisher.businessCreation.BusinessApprovedPublisherFactory
import co.highbeam.publisher.businessCreation.BusinessCreationPublisherFactory
import co.highbeam.publisher.businessCreation.BusinessRejectedPublisherFactory
import co.highbeam.publisher.businessMember.BusinessMemberChangePublisherFactory
import com.google.inject.TypeLiteral

// Exemplar: Feature
class BusinessFeature : Feature() {
  private val publishers: MutableSet<TypeLiteral<out EventPublisher<*>>> = mutableSetOf()

  override fun bind() {
    bindApiEndpoints()
    bindEventHandlers()
    bindPublishers()
    bindListeners()
  }

  private fun bindApiEndpoints() {
    bind(CreateAuthorizedUser::class.java).asEagerSingleton()

    bind(CreateBankApplication::class.java).asEagerSingleton()
    bind(GetBankApplicationsByBusiness::class.java).asEagerSingleton()

    bind(CreateBusiness::class.java).asEagerSingleton()
    bind(GetAllBusinesses::class.java).asEagerSingleton()
    bind(SearchBusinessGuids::class.java).asEagerSingleton()
    bind(GetBusiness::class.java).asEagerSingleton()
    bind(GetBusinessByMemberUser::class.java).asEagerSingleton()
    bind(GetBusinessByUnitCoCustomerId::class.java).asEagerSingleton()
    bind(MigrateBusinessInformation::class.java).asEagerSingleton()
    bind(UpdateBusiness::class.java).asEagerSingleton()

    bind(SetBusinessOwner::class.java).asEagerSingleton()

    bind(CreateReferralLink::class.java).asEagerSingleton()
    bind(GetReferralLink::class.java).asEagerSingleton()
    bind(GetReferralLinkByBusiness::class.java).asEagerSingleton()
    bind(DeleteReferralLink::class.java).asEagerSingleton()

    bind(CreateUserInvitation::class.java).asEagerSingleton()
    bind(GetUserInvitation::class.java).asEagerSingleton()
    bind(GetUserInvitationBySlug::class.java).asEagerSingleton()
    bind(GetUserInvitationsByBusiness::class.java).asEagerSingleton()
    bind(GetUserInvitationsByEmailAddress::class.java).asEagerSingleton()
    bind(DeleteUserInvitation::class.java).asEagerSingleton()

    bind(ReSendUserInvitationEmail::class.java).asEagerSingleton()

    bind(CreateBusinessMember::class.java).asEagerSingleton()
    bind(GetBusinessMember::class.java).asEagerSingleton()
    bind(GetBusinessMembersByBusiness::class.java).asEagerSingleton()
    bind(GetBusinessMemberByUser::class.java).asEagerSingleton()
    bind(GetBusinessMemberAdminsByBusiness::class.java).asEagerSingleton()
    bind(GetBusinessMembersOfUserRoleByBusiness::class.java).asEagerSingleton()
    bind(UpdateBusinessMember::class.java).asEagerSingleton()
    bind(UpdateBusinessMemberByUserGuid::class.java).asEagerSingleton()
    bind(DeleteBusinessMember::class.java).asEagerSingleton()

    bind(GetBusinessMemberSummariesByUser::class.java).asEagerSingleton()
  }

  private fun bindEventHandlers() {
    bind(UnitCoCustomerCreatedEventHandler::class.java).asEagerSingleton()
  }

  private fun bindPublishers() {
    BusinessCreationPublisherFactory.bind(binder(), publishers)
    BusinessApprovedPublisherFactory.bind(binder(), publishers)
    BusinessRejectedPublisherFactory.bind(binder(), publishers)

    BusinessMemberChangePublisherFactory.bind(binder(), publishers)
  }

  private fun bindListeners() {
    bind(BusinessApprovedListener::class.java)
    bind(BusinessRejectedListener::class.java)
    bind(BusinessCreationEmailListener::class.java)
    bind(UnitCoWebhookListener::class.java)
    bind(EvaluationChangeListener::class.java)
    bind(UnitCoCustomerCreatedWebhookListener::class.java)
  }
}
