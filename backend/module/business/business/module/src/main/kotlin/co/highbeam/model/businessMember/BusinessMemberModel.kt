package co.highbeam.model.businessMember

import java.time.LocalDate
import java.util.UUID

internal object BusinessMemberModel {
  data class Complete(
    val guid: UUID,
    val businessGuid: UUID,
    val addedByUserGuid: UUID?,
    val dateOfBirth: LocalDate?,
    val emailAddress: String?,
    val isOnboarded: Boolean?,
    val firstName: String?,
    val lastName: String?,
    val phoneNumber: String?,
    val userGuid: UUID,
  )

  data class Update(
    val dateOfBirth: LocalDate? = null,
    val emailAddress: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val isOnboarded: Boolean? = null,
    val phoneNumber: String? = null,
  )
}
