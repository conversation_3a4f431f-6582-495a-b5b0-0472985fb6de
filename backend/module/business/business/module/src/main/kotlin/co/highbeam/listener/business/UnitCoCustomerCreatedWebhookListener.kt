package co.highbeam.listener.business

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.model.unitCoEvent.UnitCoCustomerCreatedEventModel
import co.highbeam.service.business.UnitCoWebhookService
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.common.annotations.VisibleForTesting
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging

@Singleton
internal class UnitCoCustomerCreatedWebhookListener @Inject constructor(
  factory: EventListenerFactory,
  private val objectMapper: ObjectMapper,
  private val unitCoWebhookService: UnitCoWebhookService
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig("unit-co-webhook"),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "business-v2",
        filter = "attributes.type = \"customer.created\""
      ),
      clazz = JsonNode::class.java,
      listener = ::onReceive
    )
  }

  @VisibleForTesting
  suspend fun onReceive(json: JsonNode) {
    logger.info { "[Business feature] Received customer.created event: $json." }
    val event = readEvent(json)

    unitCoWebhookService.handleApplicationApproved(event)
  }

  private fun readEvent(eventJson: JsonNode): UnitCoCustomerCreatedEventModel =
    objectMapper.convertValue(eventJson)
}
