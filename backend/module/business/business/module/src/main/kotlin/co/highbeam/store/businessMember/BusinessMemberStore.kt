package co.highbeam.store.businessMember

import co.highbeam.exception.businessMember.BusinessMemberNotFound
import co.highbeam.exception.businessMember.UserAlreadyMemberOfBusiness
import co.highbeam.exception.unprocessable
import co.highbeam.model.businessMember.BusinessMemberModel
import co.highbeam.sql.store.SqlStore
import co.highbeam.sql.store.isUniqueConstraintViolation
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Handle
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import org.jdbi.v3.core.statement.UnableToExecuteStatementException
import org.postgresql.util.ServerErrorMessage
import java.util.UUID

@Singleton
internal class BusinessMemberStore @Inject constructor(jdbi: Jdbi) : SqlStore(jdbi) {
  fun create(member: BusinessMemberModel.Complete): BusinessMemberModel.Complete =
    transaction { handle -> handle.create(member) }

  fun Handle.create(member: BusinessMemberModel.Complete): BusinessMemberModel.Complete {
    val query = createQuery(sqlResource("store/businessMember/create.sql"))
    query.bindKotlin(member)
    return query.mapTo(BusinessMemberModel.Complete::class.java).single()
  }

  fun get(businessGuid: UUID, memberGuid: UUID): BusinessMemberModel.Complete? =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/businessMember/get.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("memberGuid", memberGuid)
      return@handle query.mapTo(BusinessMemberModel.Complete::class.java).singleNullOrThrow()
    }

  fun getByBusiness(businessGuid: UUID): List<BusinessMemberModel.Complete> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/businessMember/getByBusiness.sql"))
      query.bind("businessGuid", businessGuid)
      return@handle query.mapTo(BusinessMemberModel.Complete::class.java).toList()
    }

  fun getByBusinessAndUser(
    businessGuid: UUID,
    userGuid: UUID,
  ): BusinessMemberModel.Complete? =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/businessMember/getByBusinessAndUser.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("userGuid", userGuid)
      return@handle query.mapTo(BusinessMemberModel.Complete::class.java).singleNullOrThrow()
    }

  fun getByUser(userGuid: UUID): List<BusinessMemberModel.Complete> =
    handle { handle ->
      val query = handle.createQuery(sqlResource("store/businessMember/getByUser.sql"))
      query.bind("userGuid", userGuid)
      return@handle query.mapTo(BusinessMemberModel.Complete::class.java).toList()
    }

  fun update(
    businessGuid: UUID,
    memberGuid: UUID,
    update: BusinessMemberModel.Update
  ): BusinessMemberModel.Complete =
    transaction { handle ->
      val query = handle.createQuery(
        sqlResource("store/businessMember/update.sql")
      )
      query.bind("businessGuid", businessGuid)
      query.bind("memberGuid", memberGuid)
      query.bindKotlin(update)
      return@transaction query.mapTo(BusinessMemberModel.Complete::class.java).singleNullOrThrow()
        ?: throw BusinessMemberNotFound()
    }

  fun updateByUserGuid(
    businessGuid: UUID,
    userGuid: UUID,
    update: BusinessMemberModel.Update
  ): BusinessMemberModel.Complete =
    transaction { handle ->
      val query = handle.createQuery(
        sqlResource("store/businessMember/updateByUserGuid.sql")
      )
      query.bind("businessGuid", businessGuid)
      query.bind("userGuid", userGuid)
      query.bindKotlin(update)
      return@transaction query.mapTo(BusinessMemberModel.Complete::class.java).singleNullOrThrow()
        ?: throw BusinessMemberNotFound()
    }

  fun delete(businessGuid: UUID, memberGuid: UUID): BusinessMemberModel.Complete =
    transaction { handle ->
      val query = handle.createQuery(sqlResource("store/businessMember/delete.sql"))
      query.bind("businessGuid", businessGuid)
      query.bind("memberGuid", memberGuid)
      return@transaction query.mapTo(BusinessMemberModel.Complete::class.java).singleNullOrThrow()
        ?: throw unprocessable(BusinessMemberNotFound())
    }

  override fun ServerErrorMessage.onError(e: UnableToExecuteStatementException): Nothing {
    when {
      isUniqueConstraintViolation("uniq__business_member__user_guid") ->
        throw UserAlreadyMemberOfBusiness()

      else -> throw e
    }
  }
}
