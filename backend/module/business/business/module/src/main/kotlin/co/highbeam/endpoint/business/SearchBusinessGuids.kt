package co.highbeam.endpoint.business

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.business.BusinessService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import java.util.UUID
import co.highbeam.api.business.BusinessGuidApi as Api

internal class SearchBusinessGuids @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val businessService: BusinessService,
) : EndpointHandler<Api.Search, List<UUID>>(
  template = Api.Search::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Search =
    Api.Search(filter = call.getParam("filter"))

  override suspend fun Handler.handle(endpoint: Api.Search): List<UUID> {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    return businessService.searchGuids(endpoint.filter)
  }
}
