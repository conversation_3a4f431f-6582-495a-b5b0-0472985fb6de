package co.highbeam.service.business

import co.highbeam.api.user.UserApi
import co.highbeam.api.userRole.UserRoleApi
import co.highbeam.api.userRole.UserRoleMembershipApi
import co.highbeam.auth.permissions.PermissionsJson
import co.highbeam.client.user.UserClient
import co.highbeam.client.userRole.UserRoleClient
import co.highbeam.client.userRole.UserRoleMembershipClient
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.model.business.BusinessModel
import co.highbeam.model.businessMember.BusinessMemberModel
import co.highbeam.rep.business.BusinessFilter
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.userRole.UserRoleMembershipRep
import co.highbeam.rep.userRole.UserRoleRep
import co.highbeam.service.authorizedUser.AuthorizedUserService
import co.highbeam.service.businessMember.BusinessMemberService
import co.highbeam.service.referralLink.ReferralLinkService
import co.highbeam.service.userManagementEmail.UserManagementEmailService
import co.highbeam.sql.store.coTransaction
import co.highbeam.store.business.BusinessStore
import co.highbeam.util.uuid.UuidGenerator
import co.unit.client.UnitCoClient
import co.unit.rep.CustomerRep
import co.unit.rep.FullNameRep
import co.unit.rep.PhoneRep
import com.auth0.rep.Auth0User
import com.google.inject.Inject
import com.google.inject.name.Named
import mu.KotlinLogging
import org.jdbi.v3.core.Jdbi
import java.time.Clock
import java.util.UUID

internal class BusinessServiceImpl @Inject constructor(
  private val auth0ManagementClient: com.auth0.client.Auth0ManagementClient,
  private val authorizedUserService: AuthorizedUserService,
  @Named("business")
  private val businessPublisher: EventPublisher<UUID>,
  private val businessStore: BusinessStore,
  private val clock: Clock,
  private val jdbi: Jdbi,
  private val memberService: BusinessMemberService,
  private val referralLinkService: ReferralLinkService,
  private val unitCoClient: UnitCoClient,
  private val userClient: UserClient,
  private val userManagementEmailService: UserManagementEmailService,
  private val userRoleClient: UserRoleClient,
  private val userRoleMembershipClient: UserRoleMembershipClient,
  private val uuidGenerator: UuidGenerator,
) : BusinessService {
  private val logger = KotlinLogging.logger {}

  override suspend fun create(creator: BusinessRep.Creator): BusinessRep.Complete {
    val referralLink = creator.referralLinkSlug?.let { referralLinkSlug ->
      val referralLink = referralLinkService.getBySlug(referralLinkSlug) ?: return@let null
      if (!referralLink.isActive(clock)) return@let null // Omit the referral link if it's inactive.
      return@let referralLink
    }
    val business = BusinessRep.Complete(
      guid = uuidGenerator.generate(),
      dba = null,
      name = null,
      referralLinkGuid = referralLink?.guid,
      ownerUserGuid = creator.ownerUserGuid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    if (referralLink != null) {
      referralLinkService.setManuallyDisabled(referralLink.slug, manuallyDisabled = true)
    }

    val ownerInfo = checkNotNull(userClient.request(UserApi.Get(creator.ownerUserGuid)))
    val adminUserRole = getAdminUserRole(business.guid)
    logger.info { "Creating business: $business." }
    return jdbi.coTransaction {
      return@coTransaction businessStore.create(business).also {
        val ownerUserGuid = business.ownerUserGuid
        memberService.create(
          member = BusinessMemberModel.Complete(
            guid = uuidGenerator.generate(),
            businessGuid = business.guid,
            addedByUserGuid = null,
            userGuid = ownerUserGuid,
            firstName = ownerInfo.firstName,
            lastName = ownerInfo.lastName,
            emailAddress = ownerInfo.emailAddress,
            phoneNumber = null,
            dateOfBirth = null,
            isOnboarded = false,
          ),
          userRoleGuids = listOf(adminUserRole.guid),
        )
        businessPublisher.publishEvent(business.guid)
      }
    }
  }

  override fun get(businessGuid: UUID): BusinessRep.Complete? =
    businessStore.get(businessGuid)

  override fun getByUnitCoCustomerId(unitCoCustomerId: String): BusinessRep.Complete? =
    businessStore.getByUnitCoCustomerId(unitCoCustomerId)

  override fun getByMemberUser(userGuid: UUID): List<BusinessRep.Complete> =
    businessStore.getByMemberUser(userGuid)

  override suspend fun searchGuids(filter: BusinessFilter): List<UUID> =
    when (filter) {
      BusinessFilter.All -> businessStore.getAllGuids()
      BusinessFilter.AllNonArchived -> businessStore.getAllNonArchivedGuids()
    }

  override fun getAll(): List<BusinessRep.Complete> =
    businessStore.getAll()

  override suspend fun update(
    businessGuid: UUID,
    update: BusinessModel.Updater,
  ): BusinessRep.Complete {
    logger.info { "Updating business with $update." }
    val before = get(businessGuid) ?: throw unprocessable(BusinessNotFound())
    return jdbi.coTransaction {
      val after = businessStore.update(businessGuid, update)
      if (after.ownerUserGuid != before.ownerUserGuid) {
        userRoleMembershipClient.request(
          UserRoleMembershipApi.SetByUser(
            businessGuid = businessGuid,
            userGuid = after.ownerUserGuid,
            creator = listOf(UserRoleMembershipRep.Creator(getAdminUserRole(businessGuid).guid))
          ),
        )
        updateUnitCoCustomerContact(
          business = after,
          oldOwnerUserGuid = before.ownerUserGuid,
          newOwnerUserGuid = after.ownerUserGuid,
        )
      }
      return@coTransaction after
    }.also { after ->
      if (after.ownerUserGuid != before.ownerUserGuid) {
        // Business ownership has been transferred.
        userManagementEmailService.sendBusinessOwnershipTransferredEmail(
          businessGuid = after.guid,
          previousOwnerGuid = before.ownerUserGuid,
          newOwnerGuid = after.ownerUserGuid,
        )
      }
    }
  }

  private suspend fun updateUnitCoCustomerContact(
    business: BusinessRep.Complete,
    oldOwnerUserGuid: UUID,
    newOwnerUserGuid: UUID,
  ) {
    if (business.unitCoCustomerId == null) return
    val user = checkNotNull(userClient.request(UserApi.Get(newOwnerUserGuid)))
    val fullName = if (!(user.firstName.isNullOrEmpty() || user.lastName.isNullOrEmpty())) {
      FullNameRep(
        first = checkNotNull(user.firstName),
        last = checkNotNull(user.lastName),
      )
    } else {
      null
    }
    val auth0User = getAuth0User(user.emailAddress)
    val phoneNumber = getUserPhoneNumberFromAuth0(auth0User)
    val update = CustomerRep.Updater(
      type = "businessCustomer",
      jwtSubject = auth0User.userId,
      businessContactPhone = phoneNumber,
      businessContactFullName = fullName,
      businessContactEmail = user.emailAddress,
    )
    unitCoClient.customer.update(
      customerId = checkNotNull(business.unitCoCustomerId),
      update = update,
    )
    // Old owner may not yet be registered as an authorized user, so add them
    authorizedUserService.create(business.guid, oldOwnerUserGuid, notify = false)
  }

  private suspend fun getAuth0User(emailAddress: String): Auth0User.Complete {
    return auth0ManagementClient.getUsersByEmailAddress(emailAddress)
      .singleNullOrThrow()
      ?: error("Unexpected number of Auth0 users for email address $emailAddress.")
  }

  private suspend fun getUserPhoneNumberFromAuth0(auth0User: Auth0User.Complete): PhoneRep? {
    val auth0UserEnrollments = auth0User.userId.let {
      auth0ManagementClient.getUserEnrollments(userId = it)
    }
    val phoneNumber = auth0UserEnrollments.singleNullOrThrow { enrollment ->
      enrollment.authMethod == "sms" && !enrollment.phoneNumber.isNullOrBlank()
    }?.phoneNumber
    return phoneNumber?.let { PhoneRep.fromString(it) }
  }

  private suspend fun getAdminUserRole(businessGuid: UUID): UserRoleRep =
    userRoleClient.request(UserRoleApi.GetByBusiness(businessGuid))
      .single { userRole -> userRole.permissions is PermissionsJson.Admin }
}
