package co.highbeam.endpoint.business

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.rep.business.BusinessRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.business.MigrationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.business.BusinessApi as Api

internal class MigrateBusinessInformation @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val migrationService: MigrationService,
) : EndpointHandler<Api.MigrateBusinessInformation, List<BusinessRep.Complete>>(
  template = Api.MigrateBusinessInformation::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.MigrateBusinessInformation =
    Api.MigrateBusinessInformation

  override suspend fun Handler.handle(
    endpoint: Api.MigrateBusinessInformation,
  ): List<BusinessRep.Complete> {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))
    return migrationService.migrate()
  }
}
