package co.highbeam.endpoint.userInvitation

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userInvitation.UserInvitationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userInvitation.UserInvitationApi as Api
import co.highbeam.rep.userInvitation.UserInvitationRep as Rep

internal class GetUserInvitationsByBusiness @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val invitationService: UserInvitationService,
) : EndpointHandler<Api.GetByBusiness, List<Rep.Complete>>(
  template = Api.GetByBusiness::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByBusiness =
    Api.GetByBusiness(
      businessGuid = call.getParam("businessGuid"),
      accepted = call.getParam("accepted", optional = true),
    )

  override fun loggingContext(endpoint: Api.GetByBusiness): Map<String, String?> =
    super.loggingContext(endpoint) + mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "accepted" to endpoint.accepted.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.GetByBusiness): List<Rep.Complete> {
    auth(authPermission(Permission.UserInvitation_Read) { endpoint.businessGuid })
    return invitationService.getByBusiness(
      businessGuid = endpoint.businessGuid,
      accepted = endpoint.accepted,
    )
  }
}
