package co.highbeam.endpoint.businessMember

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.BusinessMemberMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.businessMember.BusinessMemberService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.businessMember.BusinessMemberApi as Api
import co.highbeam.rep.businessMember.BusinessMemberRep as Rep

internal class GetBusinessMembersByBusiness @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val memberService: BusinessMemberService,
  private val memberMapper: BusinessMemberMapper,
) : EndpointHandler<Api.GetByBusiness, List<Rep.Complete>>(
  template = Api.GetByBusiness::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByBusiness =
    Api.GetByBusiness(businessGuid = call.getParam("businessGuid"))

  override fun loggingContext(endpoint: Api.GetByBusiness) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.businessGuid.toString(),
  )

  override suspend fun Handler.handle(endpoint: Api.GetByBusiness): List<Rep.Complete> {
    auth(authPermission(Permission.BusinessMember_Read) { endpoint.businessGuid })
    val members = memberService.getByBusiness(endpoint.businessGuid)
    return memberMapper.map(members)
  }
}
