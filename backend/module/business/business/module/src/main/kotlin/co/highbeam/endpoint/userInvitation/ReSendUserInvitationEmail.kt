package co.highbeam.endpoint.userInvitation

import co.highbeam.auth.Auth
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userInvitation.UserInvitationService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userInvitation.UserInvitationActionApi as ActionApi
import co.highbeam.rep.userInvitation.UserInvitationRep as Rep

internal class ReSendUserInvitationEmail @Inject constructor(
  private val invitationService: UserInvitationService,
) : EndpointHandler<ActionApi.ReSendEmail, Rep.Complete>(
  template = ActionApi.ReSendEmail::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): ActionApi.ReSendEmail =
    ActionApi.ReSendEmail(
      userInvitationGuid = call.getParam("userInvitationGuid"),
    )

  override fun loggingContext(endpoint: ActionApi.ReSendEmail): Map<String, String?> =
    super.loggingContext(endpoint) +
      mapOf("userInvitationGuid" to endpoint.userInvitationGuid.toString())

  override suspend fun Handler.handle(endpoint: ActionApi.ReSendEmail): Rep.Complete {
    /**
     * This endpoint is intentionally public (to anyone who is signed in).
     * You need to have the user invitation GUID in order to execute it, so it's reasonably safe.
     */
    auth(Auth.AnyJwt)
    return invitationService.reSendEmail(endpoint.userInvitationGuid)
  }
}
