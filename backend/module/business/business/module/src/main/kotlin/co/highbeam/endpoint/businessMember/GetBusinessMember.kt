package co.highbeam.endpoint.businessMember

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthUser
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.businessMember.BusinessMemberNotFound
import co.highbeam.mapper.BusinessMemberMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.businessMember.BusinessMemberService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.businessMember.BusinessMemberApi as Api
import co.highbeam.rep.businessMember.BusinessMemberRep as Rep

internal class GetBusinessMember @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authUser: AuthUser.Provider,
  private val memberService: BusinessMemberService,
  private val memberMapper: BusinessMemberMapper,
) : EndpointHandler<Api.Get, Rep.Complete>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get(
    businessGuid = call.getParam("businessGuid"),
    memberGuid = call.getParam("memberGuid"),
  )

  override fun loggingContext(endpoint: Api.Get) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.businessGuid.toString(),
    "businessMemberGuid" to endpoint.memberGuid.toString(),
  )

  override suspend fun Handler.handle(endpoint: Api.Get): Rep.Complete {
    auth(authPermission(Permission.BusinessMember_Read) { endpoint.businessGuid })
    val member = memberService.get(endpoint.businessGuid, endpoint.memberGuid)
      ?: throw BusinessMemberNotFound()
    auth(authUser(member.userGuid))
    return memberMapper.map(member)
  }
}
