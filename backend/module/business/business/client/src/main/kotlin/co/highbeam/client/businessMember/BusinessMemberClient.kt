package co.highbeam.client.businessMember

import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.business.BUSINESS_FEATURE
import co.highbeam.rep.businessMember.BusinessMemberRep
import com.google.inject.Inject
import com.google.inject.name.Named

class BusinessMemberClient @Inject constructor(
  @Named(BUSINESS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: BusinessMemberApi.Create): BusinessMemberRep.Complete =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.Get): BusinessMemberRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.GetByBusiness): List<BusinessMemberRep.Complete> =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.GetByUser): BusinessMemberRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: BusinessMemberApi.GetAdminsByBusiness
  ): List<BusinessMemberRep.Complete> =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: BusinessMemberApi.GetOfUserRoleByBusiness
  ): List<BusinessMemberRep.Complete> =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.Update): BusinessMemberRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.UpdateByUserGuid): BusinessMemberRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(endpoint: BusinessMemberApi.Delete): BusinessMemberRep.Complete? =
    httpClient.request(endpoint).readValue()
}
