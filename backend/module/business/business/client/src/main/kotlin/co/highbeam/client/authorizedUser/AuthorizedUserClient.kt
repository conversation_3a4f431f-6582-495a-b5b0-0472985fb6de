package co.highbeam.client.authorizedUser

import co.highbeam.api.authorizedUser.AuthorizedUserApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.business.BUSINESS_FEATURE
import com.google.inject.Inject
import com.google.inject.name.Named

class AuthorizedUserClient @Inject constructor(
  @Named(BUSINESS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: AuthorizedUserApi.Create): Unit =
    httpClient.request(endpoint).readValue()
}
