package co.highbeam.client.userInvitation

import co.highbeam.api.userInvitation.UserInvitationActionApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.business.BUSINESS_FEATURE
import co.highbeam.rep.userInvitation.UserInvitationRep
import com.google.inject.Inject
import com.google.inject.name.Named

class UserInvitationActionClient @Inject constructor(
  @Named(BUSINESS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: UserInvitationActionApi.ReSendEmail): UserInvitationRep.Complete =
    httpClient.request(endpoint).readValue()
}
