package co.highbeam.endpoint.businessDetails

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.businessDetails.BusinessDetailsNotFound
import co.highbeam.mapper.businessDetails.BusinessDetailsMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.businessDetails.BusinessDetailsService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.businessDetails.BusinessDetailsApi as Api
import co.highbeam.rep.businessDetails.BusinessDetailsRep as Rep

internal class GetBusinessDetails @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val detailsMapper: BusinessDetailsMapper,
  private val detailsService: BusinessDetailsService,
) : EndpointHandler<Api.Get, Rep>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get =
    Api.Get(businessGuid = call.getParam("businessGuid"))

  override fun loggingContext(endpoint: Api.Get) =
    super.loggingContext(endpoint) +
      mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.Get): Rep {
    auth(authPermission(Permission.Business_Read) { endpoint.businessGuid })
    val details = detailsService.get(endpoint.businessGuid)
      ?: throw BusinessDetailsNotFound()
    return detailsMapper.map(details)
  }
}
