package co.highbeam.service.businessAddress

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.exception.business.BusinessIsNotOnboardedWithUnitYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.model.businessAddress.BusinessAddressDataModel
import co.highbeam.model.businessAddress.BusinessAddressModel
import co.highbeam.store.BusinessAddressStore
import co.unit.client.UnitCoClient
import co.unit.rep.AddressRep
import co.unit.rep.CustomerRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import kotlinx.coroutines.flow.toList
import java.util.UUID

internal class BusinessAddressServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val objectMapper: ObjectMapper,
  private val unitCoClient: UnitCoClient,
  private val businessAddressStore: BusinessAddressStore,
) : BusinessAddressService {
  override suspend fun getCurrentUnitBusinessAddress(
    businessGuid: UUID,
  ): BusinessAddressDataModel? {
    val customer = getCustomer(businessGuid) ?: return null
    return map(customer.address)
  }

  override suspend fun setCurrentUnitBusinessAddress(
    businessGuid: UUID,
    updater: BusinessAddressModel.Updater,
  ): BusinessAddressDataModel {
    val customer = setCustomerAddress(businessGuid, updater)
    return map(customer.address)
  }

  override suspend fun appendBusinessAddress(
    creator: BusinessAddressModel.Creator,
  ): BusinessAddressModel {
    return businessAddressStore.create(
      creator = creator,
    ) ?: error("Unable to create business address history: [creator=$creator]")
  }

  override suspend fun getBusinessAddresses(
    businessGuid: UUID,
  ): List<BusinessAddressModel> {
    return businessAddressStore.getByBusiness(
      businessGuid = businessGuid,
    )
  }

  private suspend fun getCustomer(businessGuid: UUID): CustomerRep.Complete.Business? {
    val customerId = getCustomerId(businessGuid)
      ?: return null
    val customer = unitCoClient.customer.get(customerId)
    if (customer !is CustomerRep.Complete.Business) {
      error("Only business customers are supported.")
    }
    return customer
  }

  private suspend fun getCustomerId(businessGuid: UUID): String? {
    val business = businessClient.request(BusinessApi.Get(businessGuid))
      ?: throw unprocessable(BusinessNotFound())
    return business.unitCoCustomerId
  }

  private suspend fun setCustomerAddress(
    businessGuid: UUID,
    updater: BusinessAddressModel.Updater,
  ): CustomerRep.Complete.Business {
    val customer = getCustomer(businessGuid)
      ?: throw BusinessIsNotOnboardedWithUnitYet()

    val address = AddressRep(
      street = updater.line1,
      street2 = updater.line2,
      city = updater.city,
      state = updater.state,
      postalCode = updater.postalCode,
      country = updater.country,
    )

    val cards = unitCoClient.card.list(customerId = customer.id, tags = null)
      .toList().flatten()

    cards.forEach { card ->
      val cardAddress = objectMapper.convertValue<AddressRep>(card.get("attributes").get("address"))
      if (cardAddress != customer.address) return@forEach
      unitCoClient.card.update(
        cardId = card.get("id").textValue(),
        update = objectMapper.convertValue<JsonNode>(
          mapOf(
            "type" to card.get("type").textValue(),
            "attributes" to mapOf(
              "address" to address,
            ),
          ),
        ),
      )
    }

    return unitCoClient.customer.update(
      customerId = customer.id,
      update = CustomerRep.Updater(
        type = "businessCustomer",
        address = address,
      ),
    ) as CustomerRep.Complete.Business
  }

  private fun map(address: AddressRep): BusinessAddressDataModel {
    return BusinessAddressDataModel(
      line1 = address.street,
      line2 = address.street2,
      city = address.city,
      state = address.state,
      postalCode = address.postalCode,
      country = address.country,
    )
  }
}
