package co.highbeam.model.businessAddress

import co.highbeam.rep.businessAddress.BusinessAddressRep
import org.jdbi.v3.json.Json
import java.time.ZonedDateTime
import java.util.UUID

data class BusinessAddressModel(
  val guid: UUID,
  val businessGuid: UUID,
  val effectiveAt: ZonedDateTime,
  val type: BusinessAddressRep.Type,
  @Json val address: BusinessAddressDataModel,
) {
  data class Creator(
    val guid: UUID,
    val businessGuid: UUID,
    val effectiveAt: ZonedDateTime,
    val type: BusinessAddressRep.Type,
    @Json val address: BusinessAddressDataModel,
  )

  data class Updater(
    val line1: String,
    val line2: String?,
    val city: String,
    val state: String,
    val postalCode: String,
    val country: String,
  )
}
