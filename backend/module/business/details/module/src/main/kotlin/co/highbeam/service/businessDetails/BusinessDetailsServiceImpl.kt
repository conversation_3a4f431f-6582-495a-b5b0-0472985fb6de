package co.highbeam.service.businessDetails

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.exception.business.BusinessIsNotOnboardedWithUnitYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.model.businessDetails.BusinessDetailsModel
import co.highbeam.model.businessDetails.InternalBusinessDetailsModel
import co.highbeam.rep.business.BusinessRep
import co.unit.client.UnitCoClient
import co.unit.rep.ApplicationRep
import co.unit.rep.CustomerRep
import co.unit.rep.PhoneRep
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class BusinessDetailsServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val unitCoClient: UnitCoClient,
) : BusinessDetailsService {
  private val logger = KotlinLogging.logger {}

  override suspend fun get(businessGuid: UUID): BusinessDetailsModel? {
    val business = getBusiness(businessGuid)
    val customerId = business.unitCoCustomerId ?: return null
    val customer = getCustomer(customerId)
    val application = getApplication(businessGuid)
    return map(business, customer, application)
  }

  override suspend fun getInternal(businessGuid: UUID): InternalBusinessDetailsModel? {
    val business = getBusiness(businessGuid)
    val customerId = business.unitCoCustomerId ?: return null
    val customer = getCustomer(customerId)
    val application = getApplication(businessGuid)
    return mapInternal(business, customer, application)
  }

  override suspend fun update(
    businessGuid: UUID,
    updater: BusinessDetailsModel.Updater,
  ): BusinessDetailsModel {
    val business = getBusiness(businessGuid)
    val customerId = business.unitCoCustomerId
      ?: throw BusinessIsNotOnboardedWithUnitYet()
    val customer = setCustomerDetails(customerId, updater)
    val application = getApplication(businessGuid)
    return map(business, customer, application)
  }

  private suspend fun getBusiness(businessGuid: UUID): BusinessRep.Complete =
    businessClient.request(BusinessApi.Get(businessGuid))
      ?: throw unprocessable(BusinessNotFound())

  private suspend fun getCustomer(customerId: String): CustomerRep.Complete.Business {
    val customer = unitCoClient.customer.get(customerId)
      ?: error("Unit customer with ID $customerId not found.")
    if (customer !is CustomerRep.Complete.Business) {
      error("Only business customers are supported.")
    }
    return customer
  }

  private suspend fun getApplication(businessGuid: UUID): ApplicationRep.Complete.Business {
    val applications = unitCoClient.application.getByBusiness(businessGuid)
    val application = applications.singleNullOrThrow {
      it.status == ApplicationRep.Status.Approved
    }
    if (application !is ApplicationRep.Complete.Business) {
      error("Only business customers are supported.")
    }
    return application
  }

  private suspend fun setCustomerDetails(
    customerId: String,
    updater: BusinessDetailsModel.Updater,
  ): CustomerRep.Complete.Business =
    unitCoClient.customer.update(
      customerId = customerId,
      update = CustomerRep.Updater(
        type = "businessCustomer",
        dba = updater.dba,
        phone = updater.phoneNumber?.let { PhoneRep.fromString(it) },
      ),
    ) as CustomerRep.Complete.Business

  private fun map(
    business: BusinessRep.Complete,
    customer: CustomerRep.Complete.Business,
    application: ApplicationRep.Complete.Business,
  ): BusinessDetailsModel {
    val name = checkNotNull(business.name) {
      "Once the Unit customer ID is set, the name should exist."
    }
    if (customer.name != name) {
      logger.warn { "Business $business has different name locally and in Unit." }
    }
    if (customer.dba != business.dba) {
      logger.warn { "Business $business has different dba locally and in Unit." }
    }
    return BusinessDetailsModel(
      name = customer.name,
      dba = customer.dba,
      website = application.website,
      phoneNumber = customer.phone.toE164(),
    )
  }

  private fun mapInternal(
    business: BusinessRep.Complete,
    customer: CustomerRep.Complete.Business,
    application: ApplicationRep.Complete.Business,
  ): InternalBusinessDetailsModel {
    val name = checkNotNull(business.name) {
      "Once the Unit customer ID is set, the name should exist."
    }
    if (customer.name != name) {
      logger.warn { "Business $business has different name locally and in Unit." }
    }
    if (customer.dba != business.dba) {
      logger.warn { "Business $business has different dba locally and in Unit." }
    }
    val fullName = application.contact.fullName
    return InternalBusinessDetailsModel(
      name = customer.name,
      dba = customer.dba,
      website = application.website,
      phoneNumber = customer.phone.toE164(),
      ein = application.ein,
      incorporationState = application.stateOfIncorporation,
      associatedPerson = fullName?.run { "$first $last" }.orEmpty(),
    )
  }
}
