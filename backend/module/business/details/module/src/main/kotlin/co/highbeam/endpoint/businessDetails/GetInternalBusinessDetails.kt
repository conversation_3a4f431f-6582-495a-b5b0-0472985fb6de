package co.highbeam.endpoint.businessDetails

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.businessDetails.BusinessDetailsNotFound
import co.highbeam.mapper.businessDetails.BusinessDetailsMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.businessDetails.BusinessDetailsService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.businessDetails.BusinessDetailsApi as Api
import co.highbeam.rep.businessDetails.InternalBusinessDetailsRep as Rep

internal class GetInternalBusinessDetails @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val detailsMapper: BusinessDetailsMapper,
  private val detailsService: BusinessDetailsService,
) : EndpointHandler<Api.GetInternal, Rep>(
  template = Api.GetInternal::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetInternal =
    Api.GetInternal(businessGuid = call.getParam("businessGuid"))

  override fun loggingContext(endpoint: Api.GetInternal) =
    super.loggingContext(endpoint) +
      mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.GetInternal): Rep {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    val details = detailsService.getInternal(endpoint.businessGuid)
      ?: throw BusinessDetailsNotFound()
    return detailsMapper.mapInternal(details)
  }
}
