package co.highbeam.listener.businessDetails

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.model.businessAddress.BusinessAddressDataModel
import co.highbeam.model.businessAddress.BusinessAddressModel
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessAddress.BusinessAddressRep
import co.highbeam.server.Server
import co.highbeam.service.businessAddress.BusinessAddressService
import co.highbeam.testing.BusinessDetailsFeatureIntegrationTest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID

internal class UnitCoWebhookEventListenerTest(
  server: Server<*>,
) : BusinessDetailsFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()
  private val customerId = "10000"

  @BeforeEach
  fun beforeEach() {
    coEvery {
      get<BusinessClient>().request(BusinessApi.GetByUnitCoCustomerId(customerId))
    } returns mockk {
      every { <EMAIL> } returns businessGuid
    }

    coEvery {
      get<BusinessClient>().request(any<BusinessApi.Update>())
    } returns mockk()
  }

  @Test
  fun `dba updated`() = integrationTest {
    val json = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitCoWebhook/customer-dba-updated.json"),
    )

    get<UnitCoWebhookEventListener>().onReceive(json)

    coVerify {
      get<BusinessClient>().request(
        BusinessApi.Update(
          businessGuid = businessGuid,
          update = BusinessRep.Updater(dba = "Jeff's Pizza"),
        ),
      )
    }
  }

  @Test
  fun `address updated`() = integrationTest {
    val json = objectMapper.readValue<JsonNode>(
      Resources.getResource("unitCoWebhook/customer-address-updated.json"),
    )

    get<UnitCoWebhookEventListener>().onReceive(json)
    val expectedBusinessAddress = BusinessAddressDataModel(
      line1 = "20 Ingram St",
      line2 = null,
      city = "Forest Hills",
      state = "NY",
      postalCode = "11375",
      country = "US",
    )
    assertThat(get<BusinessAddressService>().getBusinessAddresses(businessGuid))
      .isEqualTo(
        listOf(
          BusinessAddressModel(
            guid = uuidGenerator[1],
            businessGuid = businessGuid,
            effectiveAt = ZonedDateTime.parse(
              json.get("attributes").get("createdAt").textValue(),
              DateTimeFormatter.ISO_ZONED_DATE_TIME),
            type = BusinessAddressRep.Type.CreditAddress,
            address = expectedBusinessAddress,
          ),
          BusinessAddressModel(
            guid = uuidGenerator[0],
            businessGuid = businessGuid,
            effectiveAt = ZonedDateTime.parse(
              json.get("attributes").get("createdAt").textValue(),
              DateTimeFormatter.ISO_ZONED_DATE_TIME),
            type = BusinessAddressRep.Type.UnitCustomerAddress,
            address = expectedBusinessAddress,
          )
        )
      )
  }
}
