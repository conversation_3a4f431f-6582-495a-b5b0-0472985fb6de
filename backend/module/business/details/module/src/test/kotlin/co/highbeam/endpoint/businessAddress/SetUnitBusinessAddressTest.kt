package co.highbeam.endpoint.businessAddress

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.businessAddress.BusinessAddressApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.businessAddress.BusinessAddressRep
import co.highbeam.server.Server
import co.highbeam.testing.BusinessDetailsFeatureIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.AddressRep
import co.unit.rep.CardRep
import co.unit.rep.CustomerRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.ktor.http.HttpStatusCode
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.flow.asFlow
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID
import kotlin.test.assertFailsWith

internal class SetUnitBusinessAddressTest(
  server: Server<*>,
) : BusinessDetailsFeatureIntegrationTest(server) {
  private val businessGuid: UUID = UUID.randomUUID()

  @Test
  fun `business does not exist`() = integrationTest {
    mockBusiness(null)
    val updater = BusinessAddressRep.Updater(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    assertHighbeamException {
      addressClient.request(BusinessAddressApi.Set(businessGuid, updater))
    }.isHighbeamException(unprocessable(BusinessNotFound()))
  }

  @Test
  fun `unit customer does not exist`() = integrationTest {
    mockBusiness(business(customerId = "unit-customer-123"))
    mockUnitCustomer(id = "unit-customer-123", null)
    val updater = BusinessAddressRep.Updater(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    assertFailsWith<HighbeamHttpClientException> {
      addressClient.request(BusinessAddressApi.Set(businessGuid, updater))
    }.let {
      assertThat(it.statusCode).isEqualTo(HttpStatusCode.InternalServerError)
    }
  }

  @Test
  fun `happy path, no cards`() = integrationTest {
    mockBusiness(business(customerId = "unit-customer-123"))
    val address = AddressRep(
      street = "401 Park Dr",
      street2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    mockUnitCustomer(id = "unit-customer-123", unitCustomer("unit-customer-123", address))
    mockUnitCards(customerId = "unit-customer-123", emptyList())
    val updater = BusinessAddressRep.Updater(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    val expected = BusinessAddressRep(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    assertThat(addressClient.request(BusinessAddressApi.Set(businessGuid, updater)))
      .isEqualTo(expected)
  }

  @Test
  fun `happy path, with cards`() = integrationTest {
    mockBusiness(business(customerId = "unit-customer-123"))
    val address = AddressRep(
      street = "401 Park Dr",
      street2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    mockUnitCustomer(id = "unit-customer-123", unitCustomer("unit-customer-123", address))
    mockUnitCards(
      customerId = "unit-customer-123",
      cards = listOf(
        "unit-card-a" to address,
        "unit-card-b" to address.copy(street = "281 River St"),
      ),
    )
    val updater = BusinessAddressRep.Updater(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    val expected = BusinessAddressRep(
      line1 = "401 Park Dr",
      line2 = "Suite 801",
      city = "Boston",
      state = "MA",
      postalCode = "02215",
      country = "US",
    )
    assertThat(addressClient.request(BusinessAddressApi.Set(businessGuid, updater)))
      .isEqualTo(expected)
  }

  private fun mockBusiness(business: BusinessRep.Complete?) {
    coEvery {
      get<BusinessClient>().request(BusinessApi.Get(businessGuid))
    } returns business
  }

  private fun business(customerId: String): BusinessRep.Complete =
    mockk {
      every { <EMAIL> } returns customerId
    }

  private fun mockUnitCustomer(id: String, customer: CustomerRep.Complete?) {
    coEvery {
      get<UnitCoClient>().customer.get(id)
    } returns customer
    coEvery {
      get<UnitCoClient>().customer.update(id, any())
    } returns customer
  }

  private fun unitCustomer(id: String, address: AddressRep): CustomerRep.Complete.Business =
    mockk {
      every { <EMAIL> } returns id
      every { <EMAIL> } returns address
    }

  private fun mockUnitCards(customerId: String, cards: List<Pair<String, AddressRep>>) {
    coEvery {
      get<UnitCoClient>().card.list(customerId = customerId, tags = null)
    } returns cards.map { card ->
      objectMapper.convertValue<JsonNode>(
        listOf(
          mapOf(
            "id" to card.first,
            "type" to "businessDebit",
            "attributes" to mapOf("address" to card.second),
          ),
        ),
      )
    }.asFlow()
    cards.forEach { card ->
      coEvery {
        get<UnitCoClient>().card.update(card.first, any<JsonNode>())
      } returns mockk<CardRep.Complete.BusinessDebit> {
        every { <EMAIL> } returns card.first
        every { <EMAIL> } returns card.second
      }
    }
  }
}
