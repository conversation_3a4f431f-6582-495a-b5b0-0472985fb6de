package co.highbeam.rep.actionItem

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.util.testing.TestingOnly
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import java.time.ZonedDateTime
import java.util.UUID

data class ActionItemRep(
  val guid: UUID,
  val name: String,
  val ownerGuid: UUID,
  val milestoneName: String,
  val state: State,
  val stateLastSetAt: ZonedDateTime,
) : CompleteRep {
  enum class State {
    Complete,
    Dismissed;
  }

  data class Creator(
    val name: String,
    val ownerGuid: UUID,
    val milestoneName: String,
    val state: State,
  ) : CreatorRep {
    @TestingOnly
    constructor(actionItem: ActionItemRep) : this(
      name = actionItem.name,
      ownerGuid = actionItem.ownerGuid,
      milestoneName = actionItem.milestoneName,
      state = actionItem.state
    )

    override fun validate(): RepValidation = RepValidation {
      validate(Creator::name) { Validator.UserInput.short(this, allowEmpty = false) }
      validate(Creator::milestoneName) { Validator.UserInput.short(this, allowEmpty = false) }
    }
  }

  data class Updater(
    val name: String? = null,
    val state: State? = null,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::name) { ifPresent { Validator.UserInput.short(this, allowEmpty = false) } }
    }
  }
}
