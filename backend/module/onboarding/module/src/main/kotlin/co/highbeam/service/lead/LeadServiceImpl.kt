package co.highbeam.service.lead

import co.highbeam.config.NotionConfig
import co.highbeam.notion.NotionClient
import co.highbeam.rep.lead.LeadRep
import co.highbeam.store.lead.LeadStore
import co.highbeam.util.uuid.UuidGenerator
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

internal class LeadServiceImpl @Inject constructor(
  private val leadStore: LeadStore,
  private val notionConfig: NotionConfig,
  private val notionClient: NotionClient,
  private val uuidGenerator: UuidGenerator,
) : LeadService {
  private val logger = KotlinLogging.logger {}

  override suspend fun set(lead: LeadRep.Creator): LeadRep {
    logger.info { "Setting lead: $lead" }

    return leadStore.upsert(
      LeadRep(
        guid = uuidGenerator.generate(),
        emailAddress = lead.emailAddress,
        firstName = lead.firstName,
        lastName = lead.lastName,
        source = lead.source,
        other = lead.other,
        userGuid = lead.userGuid,
      )
    ).also {
      writeToNotion(it)
    }
  }

  override fun getByUser(userGuid: UUID) =
    leadStore.getByUser(userGuid)

  override fun getByEmailAddress(emailAddress: String) =
    leadStore.getByEmailAddress(emailAddress)

  private suspend fun writeToNotion(lead: LeadRep) =
    // The shape of this is dependent on the database shape in Notion
    notionClient.addPageToDatabase(
      databaseId = notionConfig.databaseId,
      properties = buildMap {
        put("Email", mapOf(
          "type" to "title",
          "title" to listOf(
            mapOf(
              "type" to "text",
              "text" to mapOf(
                "content" to lead.emailAddress,
              )
            )
          )
        ))
        lead.source?.let {
          put(
            "Intent",
            mapOf(
              "type" to "multi_select",
              "multi_select" to listOf(
                mapOf("name" to lead.source)
              )
            )
          )
        }
      }
    )
}
