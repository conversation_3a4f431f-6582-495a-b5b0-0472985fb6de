package co.highbeam.endpoint.lead

import co.highbeam.auth.Auth
import co.highbeam.exception.lead.LeadNotFound
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.lead.LeadService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.lead.LeadApi as Api
import co.highbeam.rep.lead.LeadRep as Rep

internal class GetLeadByUser @Inject constructor(
  private val leadService: LeadService,
) : EndpointHandler<Api.GetByUser, Rep>(
  template = Api.GetByUser::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByUser =
    Api.GetByUser(userGuid = call.getParam("userGuid"))

  override suspend fun Handler.handle(endpoint: Api.GetByUser): Rep {
    auth(Auth.Allow)
    return leadService.getByUser(endpoint.userGuid) ?: throw LeadNotFound()
  }
}
