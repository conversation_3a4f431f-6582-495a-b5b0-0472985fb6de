package co.highbeam.endpoint.lead

import co.highbeam.api.lead.LeadApi
import co.highbeam.rep.lead.LeadRep
import co.highbeam.server.Server
import co.highbeam.testing.OnboardingFeatureIntegrationTest
import com.fasterxml.jackson.databind.node.NullNode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class SetLeadTest(
  server: Server<*>,
) : OnboardingFeatureIntegrationTest(server) {
  @Test
  fun `creating the same lead upserts`() = integrationTest {
    val emailAddress = "<EMAIL>"
    val firstName = "Terence"
    val lastName = "Li"
    mockAddPageNotion(emailAddress, "insights")
    mockAddPageNotion(emailAddress, "credit")

    assertThat(leadClient.request(LeadApi.Set(LeadRep.Creator(
      emailAddress = emailAddress,
      firstName = firstName,
      lastName = lastName,
      source = "insights",
      userGuid = null,
    ))))
      .isEqualTo(LeadRep(
        guid = uuidGenerator[0],
        emailAddress = emailAddress,
        firstName = firstName,
        lastName = lastName,
        source = "insights",
        other = NullNode.instance,
        userGuid = null,
      ))

    assertThat(leadClient.request(LeadApi.GetByEmailAddress(emailAddress)))
      .isEqualTo(LeadRep(
        guid = uuidGenerator[0],
        firstName = firstName,
        lastName = lastName,
        emailAddress = emailAddress,
        source = "insights",
        other = NullNode.instance,
        userGuid = null,
      ))

    val userGuid = UUID.randomUUID()

    assertThat(leadClient.request(LeadApi.Set(LeadRep.Creator(
      emailAddress = emailAddress,
      firstName = firstName,
      lastName = lastName,
      source = "credit",
      userGuid = userGuid,
    ))))
      .isEqualTo(LeadRep(
        guid = uuidGenerator[0],
        emailAddress = emailAddress,
        firstName = firstName,
        lastName = lastName,
        source = "credit",
        other = NullNode.instance,
        userGuid = userGuid,
      ))

    assertThat(leadClient.request(LeadApi.GetByEmailAddress(emailAddress)))
      .isEqualTo(LeadRep(
        guid = uuidGenerator[0],
        emailAddress = emailAddress,
        firstName = firstName,
        lastName = lastName,
        source = "credit",
        other = NullNode.instance,
        userGuid = userGuid,
      ))
  }
}
