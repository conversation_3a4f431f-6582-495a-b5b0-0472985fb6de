package co.highbeam.endpoint.actionItem

import co.highbeam.api.actionItem.ActionItemApi
import co.highbeam.rep.actionItem.ActionItemRep
import co.highbeam.server.Server
import co.highbeam.store.actionItem.ActionItemStore
import co.highbeam.testing.OnboardingFeatureIntegrationTest
import co.highbeam.util.time.inUTC
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.time.ZonedDateTime
import java.util.UUID

internal class GetByMilestoneTest(
  server: Server<*>,
) : OnboardingFeatureIntegrationTest(server) {
  @Test
  fun `no action items`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val fetchApi = ActionItemApi.GetByMilestoneName(
      ownerGuid = businessGuid,
      milestoneName = "ImAMilestone",
    )

    assertThat(actionItemClient.request(fetchApi)).isEmpty()
  }

  @Test
  fun `fetches all the action items with the given milestoneName`() = integrationTest {
    val businessGuid = UUID.randomUUID()
    val milestoneName = "GetStarted"
    val now = ZonedDateTime.now(clock).inUTC()
    val actionItem1 = ActionItemRep(
      guid = uuidGenerator[0],
      name = "action1",
      ownerGuid = businessGuid,
      milestoneName = milestoneName,
      state = ActionItemRep.State.Dismissed,
      stateLastSetAt = now,
    )
    val actionItem2 = ActionItemRep(
      guid = uuidGenerator[1],
      name = "action2",
      ownerGuid = businessGuid,
      milestoneName = milestoneName,
      state = ActionItemRep.State.Dismissed,
      stateLastSetAt = now,
    )
    val actionItem3 = ActionItemRep(
      guid = uuidGenerator[2],
      name = "action2",
      ownerGuid = businessGuid,
      milestoneName = "SeparateMilestone",
      state = ActionItemRep.State.Dismissed,
      stateLastSetAt = now,
    )

    get<ActionItemStore>().create(actionItem1)
    get<ActionItemStore>().create(actionItem2)
    get<ActionItemStore>().create(actionItem3)

    val fetchApi = ActionItemApi.GetByMilestoneName(
      ownerGuid = businessGuid,
      milestoneName = milestoneName,
    )

    assertThat(actionItemClient.request(fetchApi))
      .containsExactlyInAnyOrder(actionItem1, actionItem2)
  }
}
