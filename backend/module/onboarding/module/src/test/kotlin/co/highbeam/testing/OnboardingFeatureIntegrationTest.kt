package co.highbeam.testing

import co.highbeam.client.actionItem.ActionItemClient
import co.highbeam.client.lead.LeadClient
import co.highbeam.config.ConfigLoader
import co.highbeam.config.OnboardingFeatureTestConfig
import co.highbeam.feature.notion.NotionFeature
import co.highbeam.feature.onboarding.OnboardingFeature
import co.highbeam.feature.rest.TestRestFeature
import co.highbeam.feature.sql.TestSqlFeature
import co.highbeam.notion.NotionClient
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import co.highbeam.testing.integration.AbstractMockFeature
import co.highbeam.testing.integration.get
import io.mockk.coEvery
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext

@ExtendWith(OnboardingFeatureIntegrationTest.Extension::class)
internal abstract class OnboardingFeatureIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  private object MockFeature : AbstractMockFeature() {
    override fun bind() {
      mock(ActionItemClient::class)
      mock(NotionClient::class)
    }
  }

  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: OnboardingFeatureTestConfig = ConfigLoader.load("test")
      val sqlFeature: TestSqlFeature = TestSqlFeature(
        config = config.highbeamDatabase,
        schemaName = "onboarding",
      )
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) {
        object : Server<OnboardingFeatureTestConfig>(config) {
          override val features = setOf(
            TestRestFeature(),
            sqlFeature,
            MockFeature,
            NotionFeature(config.notion),
            OnboardingFeature(),
          )
        }
      }
    }

    override fun beforeEach(context: ExtensionContext) {
      super.beforeEach(context)
      sqlFeature.truncateSchema(context[Server::class.java].injector)
    }

    override fun stop() {
      sharedState.stop()
    }
  }

  val actionItemClient: ActionItemClient by lazy {
    ActionItemClient(httpClient)
  }

  val leadClient: LeadClient by lazy {
    LeadClient(httpClient)
  }

  internal fun mockAddPageNotion(emailAddress: String, source: String) {
    coEvery {
      get<NotionClient>().addPageToDatabase(
        "test-notion-database-id",
        mapOf(
          "Email" to mapOf(
            "type" to "title",
            "title" to listOf(
              mapOf(
                "type" to "text",
                "text" to mapOf(
                  "content" to emailAddress,
                )
              )
            )
          ),
          "Intent" to mapOf(
            "type" to "multi_select",
            "multi_select" to listOf(
              mapOf(
                "name" to source,
              )
            )
          )
        )
      )
    } returns Unit
  }
}
