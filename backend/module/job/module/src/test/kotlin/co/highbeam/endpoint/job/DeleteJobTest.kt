package co.highbeam.endpoint.job

import co.highbeam.api.job.JobApi
import co.highbeam.rep.job.EVERY_15_MIN_SCHEDULE
import co.highbeam.rep.job.EVERY_HOUR_SCHEDULE
import co.highbeam.rep.job.JobRep
import co.highbeam.rep.job.creator
import co.highbeam.server.Server
import co.highbeam.testing.JobFeatureIntegrationTest
import com.fasterxml.jackson.module.kotlin.convertValue
import kotlin.test.Test

internal class DeleteJobTest(
  server: Server<*>,
) : JobFeatureIntegrationTest(server) {
  @Test
  fun `job does not exist`() {
    test(expectResult = null) {
      jobClient.request(JobApi.Delete("Test Job"))
    }
  }

  @Test
  fun `happy path`() {
    val job0 = JobRep.Complete(
      name = "Test Job 0",
      service = "server",
      className = "co.highbeam.job.TestJob",
      params = objectMapper.convertValue(mapOf("param" to "something")),
      schedule = EVERY_15_MIN_SCHEDULE,
    )
    val job1 = JobRep.Complete(
      name = "Test Job 1",
      service = "server",
      className = "co.highbeam.job.TestJob",
      params = objectMapper.convertValue(mapOf("param" to "something")),
      schedule = EVERY_HOUR_SCHEDULE,
    )

    setup("Create jobs") {
      jobClient.request(JobApi.Create(job0.creator()))
      jobClient.request(JobApi.Create(job1.creator()))
    }

    test(expectResult = job0) {
      jobClient.request(JobApi.Delete(job0.name))
    }

    test(expectResult = listOf(job1)) {
      jobClient.request(JobApi.GetAll)
    }
  }
}
