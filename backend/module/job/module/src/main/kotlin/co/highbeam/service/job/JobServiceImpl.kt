package co.highbeam.service.job

import co.highbeam.exception.InvalidJobParams
import co.highbeam.exception.JobNameAlreadyExists
import co.highbeam.exception.JobNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.job.HighbeamJob
import co.highbeam.mapper.job.JOB_CREATOR_SUFFIX
import co.highbeam.mapper.job.JOB_PARAMS_SUFFIX
import co.highbeam.mapper.job.JobMapper
import co.highbeam.rep.job.JobRep
import co.highbeam.rep.job.JobScheduleRep
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.google.inject.Inject
import mu.KotlinLogging
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobKey
import org.quartz.ObjectAlreadyExistsException
import org.quartz.ScheduleBuilder
import org.quartz.Scheduler
import org.quartz.SimpleScheduleBuilder
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.quartz.TriggerKey
import org.quartz.impl.matchers.GroupMatcher

private const val DEFAULT_JOB_GROUP = "server-job"
private const val DEFAULT_TRIGGER_GROUP = "server-trigger"

internal class JobServiceImpl @Inject constructor(
  private val jobMapper: JobMapper,
  private val objectMapper: ObjectMapper,
  private val scheduler: Scheduler,
) : JobService {
  private val logger = KotlinLogging.logger {}

  override fun create(creator: JobRep.Creator): JobRep.Complete {
    logger.info { "Creating job: $creator." }

    val jobCreatorClass = getJobCreatorClass(creator.className)
    val jobParamsClass = getJobParamsClass(creator.className)

    val jobKey = jobKey(creator.name)
    val job = JobBuilder.newJob(jobCreatorClass).apply {
      withIdentity(jobKey)
      val params = try {
        objectMapper.convertValue(creator.params, jobParamsClass)
      } catch (e: IllegalArgumentException) {
        logger.error(e) { "Invalid job params." }
        throw InvalidJobParams()
      }
      if (params == null) {
        logger.error { "Missing job params." }
        throw InvalidJobParams()
      }
      setJobData(objectMapper.convertValue(params))
      storeDurably(false) // Non-durable is the default, but just making it explicit.
    }.build()

    val triggerKey = triggerKey(creator.name)
    val trigger = TriggerBuilder.newTrigger().apply {
      withIdentity(triggerKey)
      withSchedule(buildSchedule(creator.schedule))
    }.build()

    try {
      scheduler.scheduleJob(job, trigger)
    } catch (e: ObjectAlreadyExistsException) {
      logger.warn(e) { "Job scheduling failed." }
      throw JobNameAlreadyExists()
    }

    return jobMapper.completeRep(job, trigger)
  }

  private fun getJobCreatorClass(className: String): Class<out HighbeamJob.Creator<*, *>> {
    val creatorClass = try {
      Class.forName(className + JOB_CREATOR_SUFFIX)
    } catch (e: ClassNotFoundException) {
      logger.warn(e) { "Tried to create a job for a missing class: $className." }
      throw unprocessable(JobNotFound())
    }
    if (!HighbeamJob.Creator::class.java.isAssignableFrom(creatorClass)) {
      logger.warn { "Tried to create a job for a non-job class: $className." }
      throw unprocessable(JobNotFound())
    }
    @Suppress("UNCHECKED_CAST")
    return creatorClass as Class<out HighbeamJob.Creator<*, *>>
  }

  private fun getJobParamsClass(className: String): Class<out HighbeamJob.Params> {
    val paramsClass = try {
      Class.forName(className + JOB_PARAMS_SUFFIX)
    } catch (e: ClassNotFoundException) {
      logger.warn(e) { "Tried to create job params for a missing class: $className." }
      throw e
    }
    @Suppress("UNCHECKED_CAST")
    return paramsClass as Class<out HighbeamJob.Params>
  }

  private fun buildSchedule(schedule: JobScheduleRep): ScheduleBuilder<out Trigger> =
    when (schedule) {
      is JobScheduleRep.Cron -> CronScheduleBuilder.cronSchedule(schedule.value)
      is JobScheduleRep.Interval -> SimpleScheduleBuilder.simpleSchedule()
        .withIntervalInMilliseconds(schedule.value)
        .repeatForever()
      is JobScheduleRep.Now -> SimpleScheduleBuilder.simpleSchedule()
    }

  override fun get(jobName: String): JobRep.Complete? {
    val jobKey = jobKey(jobName)
    val job = scheduler.getJobDetail(jobKey) ?: return null

    val triggerKey = triggerKey(job.key.name)
    val trigger = checkNotNull(scheduler.getTrigger(triggerKey))

    return jobMapper.completeRep(job, trigger)
  }

  override fun getAll(): List<JobRep.Complete> =
    scheduler.getJobKeys(GroupMatcher.jobGroupEquals(DEFAULT_JOB_GROUP)).map { jobKey ->
      val job = checkNotNull(scheduler.getJobDetail(jobKey))

      val triggerKey = triggerKey(jobKey.name)
      val trigger = checkNotNull(scheduler.getTrigger(triggerKey))

      return@map jobMapper.completeRep(job, trigger)
    }

  override fun delete(jobName: String): JobRep.Complete {
    logger.info { "Deleting job." }

    val job = get(jobName) ?: throw JobNotFound()

    val jobKey = jobKey(jobName)
    scheduler.deleteJob(jobKey)

    return job
  }

  private fun jobKey(jobName: String) = JobKey(jobName, DEFAULT_JOB_GROUP)

  private fun triggerKey(jobName: String) = TriggerKey(jobName, DEFAULT_TRIGGER_GROUP)
}
