package co.highbeam.api.job

import co.highbeam.rep.job.JobRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod

object JobApi {
  data class Create(val job: JobRep.Creator) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/jobs",
    body = job,
  )

  data class Get(val jobName: String) : Endpoint("/jobs/$jobName")

  object GetAll : Endpoint("/jobs")

  data class Delete(val jobName: String) : Endpoint(HttpMethod.Delete, "/jobs/$jobName")
}
