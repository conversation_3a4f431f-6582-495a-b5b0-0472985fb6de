package co.highbeam.endpoint.healthCheck

import co.highbeam.auth.Auth
import co.highbeam.exception.healthCheck.HealthCheckFailed
import co.highbeam.mapper.healthCheck.HealthCheckMapper
import co.highbeam.model.healthCheck.HealthCheckModel
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.healthCheck.HealthCheckService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.healthCheck.HealthCheckApi as Api
import co.highbeam.rep.healthCheck.HealthCheckRep as Rep

internal class HealthCheck @Inject constructor(
  private val healthCheckMapper: HealthCheckMapper,
  private val healthCheckService: HealthCheckService,
) : EndpointHandler<Api.Get, Rep.Complete>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get

  override suspend fun Handler.handle(endpoint: Api.Get): Rep.Complete {
    auth(Auth.Allow)
    when (val healthCheck = healthCheckService.healthCheck()) {
      is HealthCheckModel.Unhealthy -> throw HealthCheckFailed(healthCheck.reason, healthCheck.e)
      is HealthCheckModel.Healthy -> return healthCheckMapper.completeRep(healthCheck)
    }
  }
}
