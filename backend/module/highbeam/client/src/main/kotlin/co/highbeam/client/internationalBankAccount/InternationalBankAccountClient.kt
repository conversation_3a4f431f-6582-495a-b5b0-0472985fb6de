package co.highbeam.client.internationalBankAccount

import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.bankAccounts.BANK_ACCOUNTS_FEATURE
import co.highbeam.rep.internationalBankAccount.InternationalBankAccountRep
import com.google.inject.Inject
import com.google.inject.name.Named

class InternationalBankAccountClient @Inject constructor(
  @Named(BANK_ACCOUNTS_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(
    endpoint: InternationalBankAccountApi.Create,
  ): InternationalBankAccountRep.Complete =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.Patch,
  ): InternationalBankAccountRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.PatchSyncFromUnit,
  ): Unit = httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.PatchTermsAcceptedAt,
  ): Unit = httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.GetByBusinessGuid,
  ): InternationalBankAccountRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.GetByCurrencyCloudBankAccountGuid,
  ): InternationalBankAccountRep.Complete? =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.Enable,
  ): InternationalBankAccountRep.Complete =
    httpClient.request(endpoint).readValue()

  suspend fun request(
    endpoint: InternationalBankAccountApi.Disable,
  ): InternationalBankAccountRep.Complete =
    httpClient.request(endpoint).readValue()
}
