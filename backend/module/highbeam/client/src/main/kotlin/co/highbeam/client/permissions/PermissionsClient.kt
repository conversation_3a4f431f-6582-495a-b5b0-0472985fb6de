package co.highbeam.client.permissions

import co.highbeam.api.permissions.PermissionsApi
import co.highbeam.client.HttpClient
import co.highbeam.feature.auth.AUTH_FEATURE
import co.highbeam.rep.permissions.PermissionsRep
import com.google.inject.Inject
import com.google.inject.name.Named

class PermissionsClient @Inject constructor(
  @Named(AUTH_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: PermissionsApi.GetByUser): PermissionsRep =
    httpClient.request(endpoint).readValue()
}
