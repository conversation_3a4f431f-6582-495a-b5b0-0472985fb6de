package co.highbeam.client.jwtClaimsRequest

import co.highbeam.api.jwtClaimsRequest.JwtClaimsRequestApi
import co.highbeam.auth.jwt.Jwt
import co.highbeam.client.HttpClient
import co.highbeam.feature.auth.AUTH_FEATURE
import com.google.inject.Inject
import com.google.inject.name.Named

class JwtClaimsRequestClient @Inject constructor(
  @Named(AUTH_FEATURE) private val httpClient: HttpClient,
) {
  suspend fun request(endpoint: JwtClaimsRequestApi.Post): Jwt =
    httpClient.request(endpoint).readValue()
}
