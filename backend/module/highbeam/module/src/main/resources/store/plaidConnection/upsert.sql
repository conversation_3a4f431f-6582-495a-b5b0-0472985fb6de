insert into bank_accounts.plaid_connection (guid, business_guid, plaid_item_id, access_token,
                                            is_active, institution_id, institution_name,
                                            plaid_error_code, inactive_since)
values (:guid, :businessGuid, :plaidItemId, :accessToken,
        :isActive, :institutionId, :institutionName, :plaidErrorCode, :inactiveSince)
on conflict
  on constraint uniq__plaid_connection__plaid_item_id
  do update
  set access_token     = :accessToken,
      is_active        = :isActive,
      plaid_error_code = :plaidErrorCode,
      inactive_since   = :inactiveSince
returning *
