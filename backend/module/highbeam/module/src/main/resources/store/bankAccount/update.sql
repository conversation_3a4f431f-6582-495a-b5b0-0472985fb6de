update bank_accounts.bank_account
set name                    = coalesce(:name, name),
    status                  = coalesce(:status, status),
    is_primary              = coalesce(:isPrimary, is_primary),
    unit_co_deposit_product = coalesce(:unitCoDepositProduct, unit_co_deposit_product),
    type                    = coalesce(:type, type),
    data                    = coalesce(:data::jsonb, data),
    unit_co_counterparty_id = coalesce(:unitCoCounterpartyId, unit_co_counterparty_id),
    account_mask            = coalesce(:accountMask, account_mask)
where guid = :accountGuid
returning *
