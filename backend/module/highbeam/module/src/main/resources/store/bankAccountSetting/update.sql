update bank_accounts.bank_account_setting
set banking_support_tier    = coalesce(:bankingSupportTier, banking_support_tier),
    banking_limit_tier       = coalesce(:bankingLimitTier, banking_limit_tier),
    banking_partner          = coalesce(:banking<PERSON><PERSON>ner, banking_partner),
    checking_deposit_product = coalesce(:checkingDepositProduct,
                                        checking_deposit_product),
    yield_deposit_product    = coalesce(:yieldDepositProduct,
                                        yield_deposit_product),
    promotion_expires_at     = coalesce(:promotionExpiresAt, promotion_expires_at)
where business_guid = :businessGuid
returning *
