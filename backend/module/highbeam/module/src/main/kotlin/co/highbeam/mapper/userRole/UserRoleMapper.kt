package co.highbeam.mapper.userRole

import co.highbeam.model.userRole.UserRoleModel
import co.highbeam.rep.userRole.UserRoleRep
import com.google.inject.Inject

internal class UserRoleMapper @Inject constructor() {
  fun rep(model: UserRoleModel): UserRoleRep {
    return UserRoleRep(
      guid = model.guid,
      businessGuid = model.businessGuid,
      type = model.type,
      name = model.name,
      permissions = model.permissions,
    )
  }
}
