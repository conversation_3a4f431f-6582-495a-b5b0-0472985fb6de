package co.highbeam.endpoint.userRole

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.userRole.UserRoleMembershipMapper
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.userRole.UserRoleMembershipService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.userRole.UserRoleMembershipApi as Api
import co.highbeam.rep.userRole.UserRoleMembershipRep as Rep

internal class GetUserRoleMembershipsByBusinessAndUser @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val userRoleMembershipMapper: UserRoleMembershipMapper,
  private val userRoleMembershipService: UserRoleMembershipService,
) : EndpointHandler<Api.GetByBusinessAndUser, List<Rep>>(
  template = Api.GetByBusinessAndUser::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByBusinessAndUser =
    Api.GetByBusinessAndUser(
      businessGuid = call.getParam("businessGuid"),
      userGuid = call.getParam("userGuid"),
    )

  override fun loggingContext(endpoint: Api.GetByBusinessAndUser): Map<String, String?> =
    super.loggingContext(endpoint) + mapOf(
      "businessGuid" to endpoint.businessGuid.toString(),
      "userGuid" to endpoint.userGuid.toString(),
    )

  override suspend fun Handler.handle(endpoint: Api.GetByBusinessAndUser): List<Rep> {
    auth(authPermission(Permission.UserRoleMembership_Read) { endpoint.businessGuid })
    val userRoles = userRoleMembershipService.getByBusinessAndUser(
      businessGuid = endpoint.businessGuid,
      userGuid = endpoint.userGuid,
    )
    return userRoles.map { userRoleMembershipMapper.rep(it) }
  }
}
