package co.highbeam.service.userRole

import co.highbeam.model.userRole.UserRoleMembershipModel
import co.highbeam.rep.userRole.UserRoleMembershipRep
import co.highbeam.sql.store.transaction
import co.highbeam.store.userRole.UserRoleMembershipStore
import com.google.inject.Inject
import mu.KotlinLogging
import org.jdbi.v3.core.Jdbi
import java.util.UUID

internal class UserRoleMembershipServiceImpl @Inject constructor(
  private val jdbi: Jdbi,
  private val userRoleMembershipStore: UserRoleMembershipStore,
) : UserRoleMembershipService {
  private val logger = KotlinLogging.logger {}

  override fun setByUser(
    businessGuid: UUID,
    userGuid: UUID,
    creators: List<UserRoleMembershipRep.Creator>,
  ): List<UserRoleMembershipModel> {
    logger.info { "Setting memberships: $creators." }
    return jdbi.transaction {
      userRoleMembershipStore.deleteByUser(businessGuid, userGuid)
      return@transaction userRoleMembershipStore.create(creators.map { membership ->
        UserRoleMembershipModel(
          businessGuid = businessGuid,
          userGuid = userGuid,
          userRoleGuid = membership.userRoleGuid,
        )
      })
    }
  }

  override fun getByBusiness(businessGuid: UUID): List<UserRoleMembershipModel> {
    return userRoleMembershipStore.getByBusiness(businessGuid)
  }

  override fun getByBusinessAndUser(
    businessGuid: UUID,
    userGuid: UUID,
  ): List<UserRoleMembershipModel> =
    userRoleMembershipStore.getByBusinessAndUser(businessGuid, userGuid)

  override fun getByUser(userGuid: UUID): List<UserRoleMembershipModel> =
    userRoleMembershipStore.getByUser(userGuid)
}
