package co.highbeam.endpoint.unitCoCustomerToken

import co.highbeam.api.unitCo.UnitCoCustomerTokenApi
import co.highbeam.auth.Auth
import co.highbeam.auth.auth.AuthBusiness
import co.highbeam.auth.auth.AuthMfa
import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.getRestContext
import co.highbeam.auth.permissions.Permission
import co.highbeam.auth.principal.JwtAccess
import co.highbeam.mapper.unitCo.UnitCoCustomerTokenMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.card.CardService
import co.highbeam.service.unitCoCustomerToken.UnitCoCustomerTokenService
import co.unit.rep.CustomerTokenRep
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import java.time.Clock
import java.time.Duration
import java.time.ZonedDateTime
import co.highbeam.api.unitCo.UnitCoCustomerTokenApi as Api
import co.highbeam.rep.unitCo.UnitCoCustomerTokenRep as Rep

private val DEFAULT_SCOPES: Set<CustomerTokenRep.Scope> = setOf(
  CustomerTokenRep.Scope.ACCOUNTS_READ,
  CustomerTokenRep.Scope.ACCOUNT_HOLDS_READ,
  CustomerTokenRep.Scope.AUTHORIZATIONS_READ,
  CustomerTokenRep.Scope.CARDS_READ,
  CustomerTokenRep.Scope.CHECK_DEPOSITS_READ,
  CustomerTokenRep.Scope.CHECK_PAYMENTS_READ,
  CustomerTokenRep.Scope.COUNTERPARTIES_READ,
  CustomerTokenRep.Scope.CUSTOMERS_READ,
  CustomerTokenRep.Scope.PAYMENTS_READ,
  CustomerTokenRep.Scope.RECEIVED_PAYMENTS_READ,
  CustomerTokenRep.Scope.STATEMENTS_READ,
  CustomerTokenRep.Scope.TRANSACTIONS_READ,
).also { scopes ->
  // IMPORTANT: All default scopes must have DEFAULT type, since non-DEFAULT scopes require 2FA.
  check(scopes.all { it.type == CustomerTokenRep.Scope.Type.Default })
}

private const val EXPIRES_IN = 86400 // 24 hours.

/**
 * IMPORTANT NOTE: This endpoint (unlike most Highbeam API endpoints) doesn't function independently
 * of the auth principal. The JWT string used to call the endpoint is passed directly to Unit from
 * the Authorization header. This can result in unexpected behavior (a 500) if the endpoint is
 * called for a user other than the authenticated user, but authorization still succeeds (such as
 * due to [PlatformRole.SUPERUSER]).
 */
internal class PostUnitCoCustomerToken @Inject constructor(
  private val authBusiness: AuthBusiness.Provider,
  private val authMfa: AuthMfa.Provider,
  private val authPermission: AuthPermission.Provider,
  private val cardService: CardService,
  private val clock: Clock,
  private val customerTokenMapper: UnitCoCustomerTokenMapper,
  private val customerTokenService: UnitCoCustomerTokenService,
) : EndpointHandler<Api.Post, Rep.Complete>(
  template = Api.Post::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Post = Api.Post(rep = call.body())

  /**
   * [JwtAccess] is required to read [lastMfa] from the jwt object to properly configure the
   * expiration timestamp of a Unit.co customer token.
   */
  @OptIn(JwtAccess::class)
  override suspend fun Handler.handle(endpoint: Api.Post): Rep.Complete {
    val scopes = deriveScopes(endpoint)
    val resources = deriveResources(
      endpoint = endpoint,
      scopes = scopes,
    )

    auth(authBusiness(endpoint.rep.businessGuid))
    auth(Auth.All(
      scopes
        .map { scope -> permissionsForScope(scope) }
        .distinct()
        .map { permissions ->
          Auth.Some(permissions.map { authPermission(it) { endpoint.rep.businessGuid } })
        }
    ))

    var expiresInSeconds = EXPIRES_IN
    if (requiresMfa(endpoint.rep.scopes)) {
      auth(authMfa())
      val lastMfa = getRestContext().highbeamPrincipal?.jwt?.mfa?.lastMfa
      checkNotNull(lastMfa) { "Last MFA should never be null, or auth would have already failed." }
      val tokenExpiresAt = lastMfa.plusSeconds(EXPIRES_IN.toLong())
      expiresInSeconds = Duration.between(ZonedDateTime.now(clock), tokenExpiresAt)
        .toSeconds().toInt()
    }

    val customerToken = customerTokenService.create(
      businessGuid = endpoint.rep.businessGuid,
      scopes = scopes,
      expiresInSeconds = expiresInSeconds,
      strategy = endpoint.rep.strategy,
      resources = resources,
    )

    return customerTokenMapper.completeRep(customerToken, ZonedDateTime.now(clock))
  }

  /**
   * If the user requests scopes they do not have access to,
   * we do not respond with a 403.
   * Rather, we filter them out and respond with a token containing
   * only the scopes they have access to.
   */
  @OptIn(JwtAccess::class)
  private suspend fun deriveScopes(
    endpoint: UnitCoCustomerTokenApi.Post,
  ): Set<CustomerTokenRep.Scope> {
    val scopes = endpoint.rep.scopes ?: DEFAULT_SCOPES
    val aclValue = getRestContext().highbeamPrincipal?.jwt?.acl?.get(endpoint.rep.businessGuid)
      ?: return scopes
    return scopes.filter { scope ->
      permissionsForScope(scope).any { it in aclValue.permissions }
    }.toSet()
  }

  @OptIn(JwtAccess::class)
  // TODO(shubham): This is a temporary solution to restrict card access until we store cards in
  // our database. If we have another use case, we should consider storing cards first.
  private suspend fun deriveResources(
    endpoint: UnitCoCustomerTokenApi.Post,
    scopes: Set<CustomerTokenRep.Scope>,
  ): CustomerTokenRep.RestrictedResources? {
    val aclValue = getRestContext().highbeamPrincipal?.jwt?.acl?.get(endpoint.rep.businessGuid)
      ?: return null

    val needsResourceRestriction =
      (
        CustomerTokenRep.Scope.CARDS_READ in scopes
          && Permission.Card_ReadAny !in aclValue.permissions
          && Permission.Card_ReadOwn in aclValue.permissions)
        || (
        CustomerTokenRep.Scope.CARDS_SENSITIVE in scopes
          && Permission.Card_ReadNumberAny !in aclValue.permissions
          && Permission.Card_ReadNumberOwn in aclValue.permissions)
        || (
        CustomerTokenRep.Scope.CARDS_WRITE in scopes
          && Permission.Card_ActivateAny !in aclValue.permissions
          && Permission.Card_ActivateOwn in aclValue.permissions)

    if (!needsResourceRestriction) return null

    val userGuid = getRestContext().highbeamPrincipal?.jwt?.user?.guid ?: return null

    val ownCards = cardService.getCardUnitCoIdsByUser(
      businessGuid = endpoint.rep.businessGuid,
      userGuid = userGuid,
    ).toSet()

    return CustomerTokenRep.RestrictedResources(ownCards)
  }

  private fun permissionsForScope(scope: CustomerTokenRep.Scope): List<Permission> =
    when (scope) {
      CustomerTokenRep.Scope.ACCOUNTS_READ ->
        listOf(Permission.BankAccount_Read)
      CustomerTokenRep.Scope.ACCOUNT_HOLDS_READ ->
        listOf(Permission.BankAccount_Read)
      CustomerTokenRep.Scope.AUTHORIZATIONS_READ ->
        listOf(Permission.Transaction_Read)
      CustomerTokenRep.Scope.CARDS_READ ->
        listOf(Permission.Card_Read, Permission.Card_ReadOwn, Permission.Card_ReadAny)
      CustomerTokenRep.Scope.CARDS_SENSITIVE ->
        listOf(Permission.Card_ReadNumber, Permission.Card_ReadNumberOwn,
          Permission.Card_ReadNumberAny)
      CustomerTokenRep.Scope.CARDS_SENSITIVE_WRITE ->
        listOf(Permission.Card_Activate, Permission.Card_ActivateOwn, Permission.Card_ActivateAny)
      CustomerTokenRep.Scope.CARDS_WRITE ->
        listOf(Permission.Card_Create, Permission.Card_Update)
      CustomerTokenRep.Scope.CHECK_DEPOSITS_READ ->
        listOf(Permission.Transaction_Read)
      CustomerTokenRep.Scope.CHECK_DEPOSITS_WRITE ->
        listOf(Permission.CheckDeposit_Create)
      CustomerTokenRep.Scope.CHECK_PAYMENTS_READ ->
        listOf(Permission.Transaction_Read)
      CustomerTokenRep.Scope.CHECK_PAYMENTS_WRITE ->
        listOf(Permission.Payment_CreateAny)
      CustomerTokenRep.Scope.COUNTERPARTIES_READ ->
        listOf(Permission.Payee_Read)
      CustomerTokenRep.Scope.COUNTERPARTIES_WRITE ->
        listOf(Permission.Payee_Create, Permission.Payee_Update, Permission.Payee_Delete)
      CustomerTokenRep.Scope.CUSTOMERS_READ ->
        listOf(Permission.Business_Read)
      CustomerTokenRep.Scope.PAYMENTS_READ ->
        listOf(Permission.Transaction_Read)
      CustomerTokenRep.Scope.PAYMENTS_WRITE ->
        listOf(Permission.Payment_Create, Permission.Payment_CreateTransfer)
      CustomerTokenRep.Scope.PAYMENTS_WRITE_ACH_DEBIT ->
        listOf(Permission.Payment_Create)
      CustomerTokenRep.Scope.RECEIVED_PAYMENTS_READ ->
        listOf(Permission.Payment_Read)
      CustomerTokenRep.Scope.RECEIVED_PAYMENTS_WRITE ->
        listOf(Permission.Payment_CreateAny)
      CustomerTokenRep.Scope.STATEMENTS_READ ->
        listOf(Permission.Transaction_Read)
      CustomerTokenRep.Scope.TRANSACTIONS_READ ->
        listOf(Permission.Transaction_Read)
    }

  private fun requiresMfa(scopes: Set<CustomerTokenRep.Scope>?): Boolean =
    scopes?.any { it.type.requires2FactorAuthentication } == true
}
