package co.highbeam.endpoint.plaid

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.plaid.PlaidConnectionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.plaid.PlaidApi as Api
import co.highbeam.rep.plaid.PlaidConnectionRep as Rep

internal class GetPlaidConnectionsByBusiness @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val plaidConnectionService: PlaidConnectionService,
) : EndpointHandler<Api.GetConnectionsByBusiness, List<Rep.Complete>>(
  template = Api.GetConnectionsByBusiness::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetConnectionsByBusiness =
    Api.GetConnectionsByBusiness(
      businessGuid = call.getParam("businessGuid"),
    )

  override fun loggingContext(endpoint: Api.GetConnectionsByBusiness) =
    super.loggingContext(endpoint) + mapOf("businessGuid" to endpoint.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.GetConnectionsByBusiness): List<Rep.Complete> {
    auth(authPermission(Permission.BankConnection_Read) { endpoint.businessGuid })

    return plaidConnectionService.getConnectionsByAccountType(
      businessGuid = endpoint.businessGuid,
    )
  }
}
