package co.highbeam.endpoint.unitCoWebhook

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.unitCoEventHandler.UnitCoWebhookService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.unitCo.UnitCoWebhookApi as Api

internal class ReplayUnitCoWebhook @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val unitCoWebhookService: UnitCoWebhookService,
) : EndpointHandler<Api.Replay, Unit>(
  template = Api.Replay::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Replay =
    Api.Replay(webhookGuid = call.getParam("webhookGuid"))

  override suspend fun Handler.handle(endpoint: Api.Replay) {
    auth(authPlatformRole(PlatformRole.SUPERUSER))
    unitCoWebhookService.replay(endpoint.webhookGuid)
  }
}
