package co.highbeam.endpoint.plaid

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.plaid.PlaidService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.plaid.PlaidApi as Api
import co.highbeam.rep.plaid.PlaidAccessTokenRep as Rep

internal class CreatePlaidAccessToken @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val plaidService: PlaidService,
) : EndpointHandler<Api.CreateAccessToken, Rep.Complete>(
  template = Api.CreateAccessToken::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.CreateAccessToken =
    Api.CreateAccessToken(rep = call.body())

  override fun loggingContext(endpoint: Api.CreateAccessToken) = super.loggingContext(endpoint) +
    mapOf("businessGuid" to endpoint.rep.businessGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.CreateAccessToken): Rep.Complete {
    auth(authPermission(Permission.BankConnection_Create) { endpoint.rep.businessGuid })
    return plaidService.createAccessToken(endpoint.rep)
  }
}
