package co.highbeam.mapper.bankAccount

import co.highbeam.model.bankAccount.BankAccountDataModel
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.money.Balance
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccount.BankAccountRep.toBankAccountRepStatus
import co.highbeam.rep.bankAccount.BankAccountRep.toBankAccountRepType
import co.highbeam.util.uuid.UuidGenerator
import co.unit.rep.DepositAccountRep
import com.google.inject.Inject

internal class BankAccountMapper @Inject constructor(
  private val uuidGenerator: UuidGenerator,
) {
  fun creatorModel(
    rep: BankAccountRep.Creator,
    unitCoDepositProduct: DepositAccountRep.DepositProduct,
  ): BankAccountModel.Creator {
    return BankAccountModel.Creator(
      guid = uuidGenerator.generate(),
      businessGuid = rep.businessGuid,
      name = rep.name,
      isPrimary = false,
      type = BankAccountRep.Type.DepositAccount,
      unitCoDepositProduct = unitCoDepositProduct,
      data = BankAccountDataModel(minimumRequiredBalance = rep.minimumRequiredBalance)
    )
  }

  fun creatorModel(
    rep: BankAccountRep.CreatorInternal,
  ): BankAccountModel.Creator {
    return BankAccountModel.Creator(
      guid = uuidGenerator.generate(),
      businessGuid = rep.businessGuid,
      name = rep.name,
      isPrimary = rep.isPrimary,
      type = rep.highbeamType,
      unitCoDepositProduct = rep.unitCoDepositProduct,
      data = BankAccountDataModel(minimumRequiredBalance = rep.minimumRequiredBalance)
    )
  }

  fun update(rep: BankAccountRep.Updater): BankAccountModel.Updater =
    BankAccountModel.Updater(
      name = rep.name,
      isPrimary = rep.isPrimary,
      unitCoCounterpartyId = rep.unitCoCounterpartyId,
      data = rep.minimumRequiredBalance?.let {
        BankAccountDataModel(
          minimumRequiredBalance = it,
        )
      },
    )

  fun update(
    bankAccount: BankAccountModel,
    unitCoBankAccount: DepositAccountRep.Complete
  ): BankAccountModel.Updater {
    // We do not have a representative type for line of credit on Unit
    // so we don't want to update line of credit types
    val type = when (bankAccount.type) {
      BankAccountRep.Type.LineOfCredit, BankAccountRep.Type.LockedDepositAccount -> bankAccount.type
      else -> unitCoBankAccount.depositProduct.toBankAccountRepType()
    }
    return BankAccountModel.Updater(
      status = unitCoBankAccount.status.toBankAccountRepStatus(),
      unitCoDepositProduct = unitCoBankAccount.depositProduct,
      type = type
    )
  }

  fun completeRep(
    model: BankAccountModel,
    unitAccountRep: DepositAccountRep.Complete
  ): BankAccountRep.Complete =
    BankAccountRep.Complete(
      guid = model.guid,
      unitCoDepositAccountId = model.unitCoDepositAccountId,
      businessGuid = model.businessGuid,
      unitCoCounterpartyId = model.unitCoCounterpartyId,
      name = model.name,
      status = model.status,
      isPrimary = model.isPrimary,
      availableBalance = unitAccountRep.available,
      routingNumber = unitAccountRep.routingNumber,
      accountNumber = unitAccountRep.accountNumber,
      type = unitAccountRep.type,
      highbeamType = model.type,
      depositProduct = unitAccountRep.depositProduct,
      minimumRequiredBalance = model.data?.minimumRequiredBalance ?: Balance.ZERO,
    )

  fun metadataRep(model: BankAccountModel) =
    BankAccountRep.Summary(
      guid = model.guid,
      unitCoDepositAccountId = model.unitCoDepositAccountId,
      unitCoDepositProduct = model.unitCoDepositProduct,
      businessGuid = model.businessGuid,
      name = model.name,
      status = model.status,
      isPrimary = model.isPrimary,
      highbeamType = model.type,
    )
}
