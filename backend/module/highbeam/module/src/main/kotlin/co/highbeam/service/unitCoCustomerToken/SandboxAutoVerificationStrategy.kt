package co.highbeam.service.unitCoCustomerToken

import co.highbeam.protectedString.ProtectedString
import co.unit.client.UnitCoClient
import co.unit.rep.CustomerTokenRep
import co.unit.rep.CustomerTokenVerificationRep

internal class SandboxAutoVerificationStrategy(
  private val unitCoClient: UnitCoClient,
) : UnitCoTokenStrategy {
  override suspend fun create(context: UnitCoTokenStrategy.Context): CustomerTokenRep.Complete {
    val verification = unitCoClient.customerToken.createVerification(
      customerId = context.unitCoCustomerId,
      creator = CustomerTokenVerificationRep.Creator,
    )

    return unitCoClient.customerToken.create(
      customerId = context.unitCoCustomerId,
      creator = CustomerTokenRep.Creator(
        expiresInSeconds = context.expiresInSeconds,
        scopes = context.scopes,
        verificationCode = ProtectedString("000001"),
        verificationToken = verification.verificationToken,
        resources = context.resources,
      ),
    )
  }
}
