package co.highbeam.endpoint.internationalBankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.currencyCloud.CurrencyCloudService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import mu.KotlinLogging
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi as Api

internal class SyncInternationalBankAccountFromUnit @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val currencyCloudService: CurrencyCloudService,
) : EndpointHandler<Api.PatchSyncFromUnit, Unit>(
  template = Api.PatchSyncFromUnit::class.template(),
) {
  private val logger = KotlinLogging.logger {}

  override suspend fun endpoint(call: ApplicationCall): Api.PatchSyncFromUnit =
    Api.PatchSyncFromUnit(
      internationalBankAccountGuid = call.getParam("internationalBankAccountGuid"),
      rep = call.body()
    )

  override suspend fun Handler.handle(endpoint: Api.PatchSyncFromUnit) {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    val internationalBankAccount = currencyCloudService.get(endpoint.internationalBankAccountGuid)

    if (internationalBankAccount == null) {
      logger.error {
        "CurrencyCloud account not found for guid=" +
          "${endpoint.internationalBankAccountGuid}"
      }
      throw unprocessable(InternationalBankAccountNotFound())
    }

    currencyCloudService.updateCurrencyCloudAccount(
      businessGuid = internationalBankAccount.businessGuid,
      unitCoAddress = endpoint.rep.address,
      companyName = endpoint.rep.companyName
    )
  }
}
