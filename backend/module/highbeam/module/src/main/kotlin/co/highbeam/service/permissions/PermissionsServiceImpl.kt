package co.highbeam.service.permissions

import co.highbeam.api.business.BusinessApi
import co.highbeam.auth.permissions.Permission
import co.highbeam.auth.permissions.PermissionsJson
import co.highbeam.client.business.BusinessClient
import co.highbeam.model.permissions.PermissionsModel
import co.highbeam.model.userRole.UserRoleModel
import co.highbeam.service.userRole.UserRoleService
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import com.google.inject.Inject
import java.util.UUID

internal class PermissionsServiceImpl @Inject constructor(
  private val businessClient: BusinessClient,
  private val objectMapper: ObjectMapper,
  private val userRoleService: UserRoleService,
) : PermissionsService {
  override suspend fun getByUser(userGuid: UUID): PermissionsModel {
    val roles = userRoleService.getByUser(userGuid)
    val ownedBusinessGuids = getOwnedBusinessGuids(userGuid)
    val permissions = getPermissions(roles, ownedBusinessGuids)
    return PermissionsModel(
      userGuid = userGuid,
      permissions = permissions,
    )
  }

  private suspend fun getOwnedBusinessGuids(userGuid: UUID): List<UUID> {
    val businesses = businessClient.request(BusinessApi.GetByMemberUser(userGuid))
    return businesses.filter { it.ownerUserGuid == userGuid }.map { it.guid }
  }

  private fun getPermissions(
    roles: List<UserRoleModel>,
    ownedBusinessGuids: List<UUID>,
  ): Map<Permission, Set<UUID>> {
    return buildMap {
      roles.map { it.businessGuid }.distinct().forEach { businessGuid ->
        fetchDefaults().forEach { permission ->
          addPermission(permission, businessGuid)
        }
      }
      roles.forEach { role ->
        when (role.permissions) {
          is PermissionsJson.Admin ->
            Permission.values().filter { it.admin }.forEach { permission ->
              addPermission(permission, role.businessGuid)
            }
          is PermissionsJson.Permissions ->
            role.permissions.permissions.forEach { permission ->
              addPermission(permission, role.businessGuid)
            }
        }
      }
      ownedBusinessGuids.forEach { businessGuid ->
        Permission.values().forEach { permission ->
          addPermission(permission, businessGuid)
        }
      }
    }
  }

  private fun fetchDefaults(): List<Permission> {
    return Resources.getResource("permissions/default-permissions.json")
      .let { objectMapper.readValue(it) }
  }
}

fun MutableMap<Permission, Set<UUID>>.addPermission(
  permission: Permission,
  businessGuid: UUID,
) {
  compute(permission) { _, value ->
    value.orEmpty() + businessGuid
  }
}
