package co.highbeam.endpoint.transaction

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.transaction.InvoiceService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.transaction.InvoiceApi as Api
import co.highbeam.rep.transaction.InvoiceRep as Rep

internal class GetInvoice @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val invoiceService: InvoiceService,
) : EndpointHandler<Api.Get, Rep.Complete>(
  template = Api.Get::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Get = Api.Get(
    businessGuid = call.getParam("businessGuid"),
    invoiceGuid = call.getParam("invoiceGuid"),
  )

  override fun loggingContext(endpoint: Api.Get) = super.loggingContext(endpoint) + mapOf(
    "businessGuid" to endpoint.businessGuid.toString(),
    "invoiceGuid" to endpoint.invoiceGuid.toString(),
  )

  override suspend fun Handler.handle(endpoint: Api.Get): Rep.Complete {
    authSome(
      authPermission(Permission.TransactionMetadata_Read) { endpoint.businessGuid },
      authPlatformRole(PlatformRole.SUPERBLOCKS),
    )
    return invoiceService.get(endpoint.invoiceGuid)
  }
}
