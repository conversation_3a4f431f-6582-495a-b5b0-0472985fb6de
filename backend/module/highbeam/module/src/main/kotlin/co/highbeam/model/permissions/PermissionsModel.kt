package co.highbeam.model.permissions

import co.highbeam.auth.permissions.Acl
import co.highbeam.auth.permissions.AclValue
import co.highbeam.auth.permissions.Permissions
import java.util.UUID

internal data class PermissionsModel(
  val userGuid: UUID,
  val permissions: Permissions,
) {
  val acl: Acl
    get() = permissions
      .flatMap { (permission, guids) -> guids.map { Pair(permission, it) } }
      .groupBy { it.second }
      .mapValues { (_, value) -> AclValue(value.map { it.first }.toSet()) }
}
