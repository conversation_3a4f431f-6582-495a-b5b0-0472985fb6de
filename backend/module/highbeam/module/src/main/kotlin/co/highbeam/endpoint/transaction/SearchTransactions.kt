package co.highbeam.endpoint.transaction

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.permissions.Permission
import co.highbeam.mapper.transaction.SearchTransactionResponseMapper
import co.highbeam.rep.unitCo.UnitCoTransactionRep
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.transaction.TransactionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.transaction.UnitCoTransactionApi as Api

internal class SearchTransactions @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val transactionService: TransactionService,
  private val responseMapper: SearchTransactionResponseMapper,
) : EndpointHandler<Api.Search, List<UnitCoTransactionRep>>(
  template = Api.Search::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Search =
    Api.Search(
      businessGuid = call.getParam("businessGuid"),
      unitCoDepositAccountId = call.getParam("unitCoDepositAccountId", optional = true),
      query = call.getParam("query", optional = true),
      from = call.getParam("from", optional = true),
      to = call.getParam("to", optional = true),
      fromStatementDate = call.getParam("fromStatementDate", optional = true),
      toStatementDateInclusive = call.getParam("toStatementDateInclusive", optional = true),
      accountType = call.getParam("accountType", optional = true),
    )

  override suspend fun Handler.handle(endpoint: Api.Search): List<UnitCoTransactionRep> {
    // TODO: Authenticate based on the individual bank accounts too.
    //  This was deferred because we rely on Unit.co IDs (the GUIDs are not readily available).
    authAll(authPermission(Permission.Transaction_Read) { endpoint.businessGuid })
    val transactions = transactionService.search(
      businessGuid = endpoint.businessGuid,
      unitCoDepositAccountId = endpoint.unitCoDepositAccountId,
      query = endpoint.query,
      from = endpoint.from,
      to = endpoint.to,
      fromStatementDate = endpoint.fromStatementDate,
      toStatementDateInclusive = endpoint.toStatementDateInclusive,
      accountType = endpoint.accountType,
    )

    return responseMapper.toJson(endpoint.businessGuid, transactions)
  }
}
