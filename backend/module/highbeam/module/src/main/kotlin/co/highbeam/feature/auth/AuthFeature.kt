package co.highbeam.feature.auth

import co.highbeam.config.AuthVerifierConfig
import co.highbeam.config.JwtMechanismConfig
import co.highbeam.endpoint.jwtCliamsRequest.PostJwtClaimsRequest
import co.highbeam.endpoint.permissions.GetPermissionsByUser
import co.highbeam.endpoint.userRole.GetUserRoleMembershipsByBusiness
import co.highbeam.endpoint.userRole.GetUserRoleMembershipsByBusinessAndUser
import co.highbeam.endpoint.userRole.GetUserRoleMembershipsByUser
import co.highbeam.endpoint.userRole.GetUserRolesByBusiness
import co.highbeam.endpoint.userRole.GetUserRolesByBusinessAndUser
import co.highbeam.endpoint.userRole.GetUserRolesByUser
import co.highbeam.endpoint.userRole.SetUserRoleMembershipsByUser
import co.highbeam.feature.Feature
import co.highbeam.listener.auth.BusinessMemberChangeListener
import com.google.api.client.googleapis.auth.oauth2.GooglePublicKeysManager
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.gson.GsonFactory
import kotlin.jvm.java

class AuthFeature(
  private val jwtVerifiers: List<AuthVerifierConfig>,
) : Feature() {
  override fun bind() {
    bindApiEndpoints()
    bindListeners()
    bindHighbeamJwtSigningMechanism()
    bindGooglePublicKeysManager()
  }

  private fun bindApiEndpoints() {
    bind(GetPermissionsByUser::class.java).asEagerSingleton()

    bind(GetUserRolesByBusiness::class.java).asEagerSingleton()
    bind(GetUserRolesByBusinessAndUser::class.java).asEagerSingleton()
    bind(GetUserRolesByUser::class.java).asEagerSingleton()

    bind(SetUserRoleMembershipsByUser::class.java).asEagerSingleton()
    bind(GetUserRoleMembershipsByBusiness::class.java).asEagerSingleton()
    bind(GetUserRoleMembershipsByBusinessAndUser::class.java).asEagerSingleton()
    bind(GetUserRoleMembershipsByUser::class.java).asEagerSingleton()

    bind(PostJwtClaimsRequest::class.java).asEagerSingleton()
  }

  private fun bindListeners() {
    bind(BusinessMemberChangeListener::class.java)
  }

  private fun bindHighbeamJwtSigningMechanism() =
    jwtVerifiers.filterIsInstance<AuthVerifierConfig.Jwt>().singleNullOrThrow()
      ?.let { jwtAuthVerifierConfig ->
        jwtAuthVerifierConfig
          .mechanisms
          .filterIsInstance<JwtMechanismConfig.Static>()
          .singleNullOrThrow()?.let { highbeamJwtMechanism ->
            bind(JwtMechanismConfig.Static::class.java).toInstance(highbeamJwtMechanism)
          }
      }

  private fun bindGooglePublicKeysManager() {
    bind(GooglePublicKeysManager::class.java).toInstance(
      GooglePublicKeysManager(NetHttpTransport(), GsonFactory())
    )
  }
}
