package co.highbeam.publisher.bankAccounts

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import com.google.inject.Inject
import java.util.UUID

internal class PlaidAccountCreationPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<UUID>>() {
  override fun get(): EventPublisher<UUID> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<UUID>(
    topic = TopicConfig("plaid-account-creation"),
    typeLiteral = typeLiteral(),
    provider = PlaidAccountCreationPublisherFactory::class,
  )
}
