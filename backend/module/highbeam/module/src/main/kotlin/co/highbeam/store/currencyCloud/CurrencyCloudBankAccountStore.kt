package co.highbeam.store.currencyCloud

import co.highbeam.model.currencyCloud.CurrencyCloudBankAccountModel
import co.highbeam.sql.store.SqlStore
import com.google.inject.Inject
import com.google.inject.Singleton
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.bindKotlin
import java.time.LocalDate
import java.util.UUID

@Singleton
internal class CurrencyCloudBankAccountStore @Inject constructor(
  jdbi: Jdbi,
) : SqlStore(jdbi) {
  fun upsert(
    currencyCloudBankAccount: CurrencyCloudBankAccountModel
  ): CurrencyCloudBankAccountModel =
    transaction { handle ->
      handle.createQuery(sqlResource("store/currencyCloudBankAccount/upsert.sql"))
        .bindKotlin(currencyCloudBankAccount)
        .mapTo(CurrencyCloudBankAccountModel::class.java)
        .single()
    }

  fun get(guid: UUID): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/get.sql")
      )
      query.bind("guid", guid)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }

  fun enable(guid: UUID): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/enable.sql")
      )
      query.bind("guid", guid)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }

  fun disable(guid: UUID): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/disable.sql")
      )
      query.bind("guid", guid)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }

  fun getByBusinessGuid(businessGuid: UUID): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/getByBusinessGuid.sql")
      )
      query.bind("businessGuid", businessGuid)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }

  fun getByCurrencyCloudBankAccountGuid(
    currencyCloudBankAccountGuid: UUID
  ): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/getByCurrencyCloudBankAccountGuid.sql")
      )
      query.bind("currencyCloudBankAccountGuid", currencyCloudBankAccountGuid)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }

  fun updateTermsAcceptedAt(
    businessGuid: UUID,
    termsAcceptedAt: LocalDate
  ): CurrencyCloudBankAccountModel? =
    handle { handle ->
      val query = handle.createQuery(
        sqlResource("store/currencyCloudBankAccount/updateTermsAcceptedAt.sql")
      )
      query.bind("businessGuid", businessGuid)
      query.bind("termsAcceptedAt", termsAcceptedAt)
      return@handle query.mapTo(CurrencyCloudBankAccountModel::class.java).singleOrNull()
    }
}
