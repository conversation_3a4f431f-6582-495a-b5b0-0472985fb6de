package co.highbeam.service.bankAccount

import co.highbeam.exception.bankAccount.UnitCoDepositAccountNotFound
import co.highbeam.exception.business.InvalidBankAccountType
import co.highbeam.mapper.bankAccount.BankAccountMapper
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.store.bankAccount.BankAccountStore
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import com.google.inject.Inject
import io.ktor.util.logging.error
import mu.KotlinLogging

internal class BankAccountUnitCoConnectorServiceImpl @Inject constructor(
  private val bankAccountStore: BankAccountStore,
  private val bankAccountMapper: BankAccountMapper,
  private val unitCoClient: UnitCoClient,
) : BankAccountUnitCoConnectorService {
  private val logger = KotlinLogging.logger {}

  override suspend fun getUnitCoBankAccount(
    unitCoDepositAccountId: String,
  ): DepositAccountRep.Complete? {
    val unitCoBankAccount = unitCoClient.depositAccount.get(unitCoDepositAccountId)
    if (unitCoBankAccount == null) {
      logger.error(UnitCoDepositAccountNotFound())
    }
    return unitCoBankAccount
  }

  override fun updateFromUnit(
    bankAccount: BankAccountModel,
    unitCoBankAccount: DepositAccountRep.Complete
  ): BankAccountModel? {

    if (bankAccount.unitCoDepositAccountId != unitCoBankAccount.id) {
      return null
    }

    // We only update if the status or deposit product has changed
    // If we are looking to maintain the balance of the account
    // please refactor this code to handle the ever-changing nature of balances
    if (
      bankAccount.status.equalTo(unitCoBankAccount.status) &&
      bankAccount.unitCoDepositProduct == unitCoBankAccount.depositProduct
    ) {
      return null
    }

    val update = bankAccountMapper.update(
      bankAccount = bankAccount,
      unitCoBankAccount = unitCoBankAccount
    )
    return bankAccountStore.update(bankAccount.guid, update)
  }

  override suspend fun createDepositAccountInUnit(
    model: BankAccountModel.Creator,
    unitCoCustomerId: String,
  ): DepositAccountRep.Complete {
    if (!model.type.supportedUnitCoDepositProducts.contains(model.unitCoDepositProduct))
      throw InvalidBankAccountType()
    val depositAccountCreationRep = DepositAccountRep.Creator(
      businessGuid = model.businessGuid,
      bankAccountGuid = model.guid,
      customerId = unitCoCustomerId,
      name = model.name,
      depositProduct = model.unitCoDepositProduct,
    )

    return unitCoClient.depositAccount.create(depositAccountCreationRep)
  }

  override suspend fun updateDepositProduct(
    unitCoDepositAccountId: String,
    depositProduct: DepositAccountRep.DepositProduct
  ) {
    unitCoClient.depositAccount.update(
      accountId = unitCoDepositAccountId,
      updater = DepositAccountRep.Updater(
        depositProduct = depositProduct
      )
    )
  }

  override suspend fun close(unitCoDepositAccountId: String): DepositAccountRep.Complete {
    return unitCoClient.depositAccount.close(
      accountId = unitCoDepositAccountId,
      closeRep = DepositAccountRep.Close(reason = DepositAccountRep.CloseReason.ByCustomer)
    )
  }
}
