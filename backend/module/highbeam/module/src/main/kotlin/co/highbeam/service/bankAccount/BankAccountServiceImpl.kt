package co.highbeam.service.bankAccount

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.exception.ForbiddenException
import co.highbeam.exception.bankAccount.BankAccountNotFound
import co.highbeam.exception.bankAccount.InvalidBankAccount
import co.highbeam.exception.bankAccount.UnitCoDepositAccountNotFound
import co.highbeam.exception.bankAccountSetting.BankAccountSettingNotFound
import co.highbeam.exception.business.BusinessIsNotOnboardedWithUnitYet
import co.highbeam.exception.business.BusinessNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.mapper.bankAccount.BankAccountMapper
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.store.bankAccount.BankAccountStore
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import com.google.inject.Inject
import io.ktor.util.logging.error
import mu.KotlinLogging
import java.util.UUID

@Suppress("TooManyFunctions")
internal class BankAccountServiceImpl @Inject constructor(
  private val bankAccountStore: BankAccountStore,
  private val bankAccountMapper: BankAccountMapper,
  private val bankingSettingService: BankAccountSettingService,
  private val bankAccountUnitCoConnectorService: BankAccountUnitCoConnectorService,
  private val bankingLimitTierService: BankingLimitTierService,
  private val businessClient: BusinessClient,
  private val featureFlagService: FeatureFlagService,
  private val unitCoClient: UnitCoClient,
) : BankAccountService {
  private val logger = KotlinLogging.logger {}

  override suspend fun create(model: BankAccountModel.Creator): BankAccountModel {
    val business = businessClient.request(BusinessApi.Get(model.businessGuid))
      ?: throw unprocessable(BusinessNotFound())

    if (business.status != BusinessRep.Complete.Status.Active) {
      throw ForbiddenException()
    }

    val unitCoCustomerId = business.unitCoCustomerId ?: throw BusinessIsNotOnboardedWithUnitYet()

    if (notAllowedToCreateBankAccount(model.businessGuid)) {
      throw ForbiddenException()
    }

    val depositAccount = bankAccountUnitCoConnectorService.createDepositAccountInUnit(
      model = model,
      unitCoCustomerId = unitCoCustomerId
    )
    return bankAccountStore.create(
      model,
      unitCoDepositAccountId = depositAccount.id,
      accountMask = depositAccount.accountNumber.takeLast(4)
    )
  }

  override suspend fun create(creator: BankAccountRep.Creator): BankAccountModel {
    val bankAccountSetting = bankingSettingService.get(creator.businessGuid)
      ?: throw unprocessable(BankAccountSettingNotFound())
    if(!bankingLimitTierService.canCreateNewAccountForTier(creator.businessGuid)) {
      throw ForbiddenException()
    }

    val depositProduct = bankingLimitTierService.determineDepositProductByBankingTier(
      bankAccountSettingRep = bankAccountSetting,
    )
    val creatorModel = bankAccountMapper.creatorModel(
      rep = creator,
      unitCoDepositProduct = depositProduct,
    )

    return create(creatorModel)
  }

  override fun getSummary(accountGuid: UUID): BankAccountRep.Summary? {
    val bankAccount = bankAccountStore.get(accountGuid) ?: return null
    return bankAccountMapper.metadataRep(bankAccount)
  }

  /**
   * TODO: GETs should not NotFound errors. They should return null instead.
   */
  override suspend fun getAndUpdate(accountGuid: UUID): BankAccountRep.Complete {
    val bankAccount = bankAccountStore.get(accountGuid) ?: throw BankAccountNotFound()
    val unitCoBankAccount = bankAccountUnitCoConnectorService.getUnitCoBankAccount(
      unitCoDepositAccountId = bankAccount.unitCoDepositAccountId
    ) ?: throw BankAccountNotFound()
    return processAndUpdateBankAccount(bankAccount, unitCoBankAccount)
  }

  override suspend fun getByBusinessAndUpdate(
    businessGuid: UUID,
    accountGuid: UUID
  ): BankAccountRep.Complete {
    val bankAccount = bankAccountStore.getByBusiness(
      bankAccountGuid = accountGuid,
      businessGuid = businessGuid
    ) ?: throw BankAccountNotFound()
    val unitCoBankAccount = bankAccountUnitCoConnectorService.getUnitCoBankAccount(
      unitCoDepositAccountId = bankAccount.unitCoDepositAccountId
    ) ?: throw BankAccountNotFound()
    return processAndUpdateBankAccount(bankAccount, unitCoBankAccount)
  }


  override suspend fun getPrimaryBankAccounts(): List<BankAccountRep.Summary> {
    val bankAccountModels = bankAccountStore.getPrimaryBankAccounts()
    return bankAccountModels.map { bankAccountMapper.metadataRep(it) }
  }

  override suspend fun getPrimaryBankAccountByBusinessGuid(businessGuid: UUID):
    BankAccountRep.Complete? {
    val bankAccountModel = bankAccountStore.getOpenPrimaryBankAccountByBusinessGuid(businessGuid)
      ?: return null
    val unitCoBankAccount = bankAccountUnitCoConnectorService.getUnitCoBankAccount(
      unitCoDepositAccountId = bankAccountModel.unitCoDepositAccountId
    ) ?: throw BankAccountNotFound()

    return bankAccountMapper.completeRep(bankAccountModel, unitCoBankAccount)
  }

  override fun getByUnitCoDepositAccountId(unitCoDepositAccountId: String): BankAccountModel? =
    bankAccountStore.getByUnitCoDepositAccountId(unitCoDepositAccountId)

  override suspend fun getAndUpdateByUnitCoDepositAccountId(
    unitCoDepositAccountId: String
  ): BankAccountRep.Complete {
    val bankAccount = bankAccountStore.getByUnitCoDepositAccountId(
      unitCoDepositAccountId = unitCoDepositAccountId
    ) ?: throw BankAccountNotFound()
    val unitCoBankAccount = bankAccountUnitCoConnectorService.getUnitCoBankAccount(
      unitCoDepositAccountId = bankAccount.unitCoDepositAccountId
    ) ?: throw BankAccountNotFound()
    return processAndUpdateBankAccount(bankAccount, unitCoBankAccount)
  }

  override suspend fun getAllAndUpdateByBusinessGuid(
    businessGuid: UUID
  ): List<BankAccountRep.Complete> {
    val bankAccounts = bankAccountStore.getAllByBusinessGuid(businessGuid)
    val unitCoDepositAccounts = unitCoClient.depositAccount.getByBusiness(businessGuid)
      .associateBy { it.bankAccountGuid }

    return bankAccounts.mapNotNull { bankAccount ->
      val unitCoBankAccount = unitCoDepositAccounts[bankAccount.guid]
      if (unitCoBankAccount == null) {
        logger.error(UnitCoDepositAccountNotFound())
        return@mapNotNull null
      }
      processAndUpdateBankAccount(bankAccount, unitCoBankAccount)
    }
  }

  override fun getAllByBusinessGuids(businessGuids: List<UUID>) =
    bankAccountStore.getAllByBusinessGuids(businessGuids)

  override fun update(accountGuid: UUID, updater: BankAccountModel.Updater) =
    bankAccountStore.update(accountGuid, updater)

  override suspend fun update(
    unitCoAccountId: String,
    updater: BankAccountModel.Updater
  ): BankAccountModel {
    updater.unitCoDepositProduct?.let {
      bankAccountUnitCoConnectorService.updateDepositProduct(
        unitCoDepositAccountId = unitCoAccountId,
        depositProduct = it
      )
    }
    return bankAccountStore.update(unitCoAccountId, updater)
  }

  override suspend fun close(accountGuid: UUID): BankAccountRep.Complete {
    val bankAccount = bankAccountStore.get(accountGuid) ?: throw BankAccountNotFound()

    if (!canDeleteBankAccount(bankAccount)) {
      logger.error { "Cannot close bankAccount=$bankAccount" }

      throw InvalidBankAccount()
    }

    val unitCoBankAccount = bankAccountUnitCoConnectorService.close(
      unitCoDepositAccountId = bankAccount.unitCoDepositAccountId
    )

    return processAndUpdateBankAccount(bankAccount, unitCoBankAccount)
  }

  private fun canDeleteBankAccount(bankAccount: BankAccountModel): Boolean {
    return (bankAccount.type == BankAccountRep.Type.DepositAccount ||
      isHighYieldBlueRidgeAccount(bankAccount)) &&
      bankAccount.status == BankAccountRep.Status.OPEN &&
      !bankAccount.isPrimary
  }

  private fun isHighYieldBlueRidgeAccount(bankAccount: BankAccountModel): Boolean =
    (bankAccount.type == BankAccountRep.Type.HighYield) &&
      (bankAccount.unitCoDepositProduct.bank == DepositAccountRep.PartnerBank.BlueRidge)

  private fun processAndUpdateBankAccount(
    bankAccount: BankAccountModel,
    unitCoBankAccount: DepositAccountRep.Complete
  ): BankAccountRep.Complete {
    val updatedBankAccount = bankAccountUnitCoConnectorService.updateFromUnit(
      bankAccount = bankAccount,
      unitCoBankAccount = unitCoBankAccount,
    ) ?: bankAccount
    return bankAccountMapper.completeRep(updatedBankAccount, unitCoBankAccount)
  }

  private fun notAllowedToCreateBankAccount(businessGuid: UUID): Boolean {
    return featureFlagService.isEnabled(BusinessFlag.PreventDepositAccountCreation, businessGuid) ||
      featureFlagService.isEnabled(BusinessFlag.PreventDacaDepositAccountCreation, businessGuid)
  }
}
