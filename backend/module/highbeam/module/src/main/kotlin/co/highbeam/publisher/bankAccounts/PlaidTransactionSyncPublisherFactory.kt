package co.highbeam.publisher.bankAccounts

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import com.google.inject.Inject

internal class PlaidTransactionSyncPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<String>>() {
  override fun get(): EventPublisher<String> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<String>(
    topic = TopicConfig("plaid-transaction-sync"),
    typeLiteral = typeLiteral(),
    provider = PlaidTransactionSyncPublisherFactory::class,
  )
}
