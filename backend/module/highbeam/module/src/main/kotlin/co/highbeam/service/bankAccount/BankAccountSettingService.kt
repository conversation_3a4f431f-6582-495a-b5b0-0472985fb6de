package co.highbeam.service.bankAccount

import co.highbeam.rep.bankAccountSetting.BankAccountSettingRep
import com.google.inject.ImplementedBy
import java.util.UUID

@ImplementedBy(BankAccountSettingServiceImpl::class)
internal interface BankAccountSettingService {
  fun create(rep: BankAccountSettingRep.Create): BankAccountSettingRep
  fun update(rep: BankAccountSettingRep.Update): BankAccountSettingRep?

  fun get(businessGuid: UUID): BankAccountSettingRep?
}
