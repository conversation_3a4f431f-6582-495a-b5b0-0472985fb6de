package co.highbeam.listener.bankAccountSettings

import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.rep.bankAccountSetting.BankingTierChangeEvent
import co.highbeam.service.bankAccount.BANKING_TIER_VIP_UPGRADE_TOPIC_NAME
import co.highbeam.service.bankAccount.BankingLimitTierService
import com.google.inject.Inject
import com.google.inject.Singleton
import mu.KotlinLogging

@Singleton
internal class BankingLimitTierUpgradeListener @Inject constructor(
  factory: EventListenerFactory,
  private val bankingLimitTierService: BankingLimitTierService,
) {
  private val logger = KotlinLogging.logger {}

  init {
    factory.startAsync(
      topicConfig = TopicConfig(BANKING_TIER_VIP_UPGRADE_TOPIC_NAME),
      subscriptionConfig = SubscriptionConfig(
        consumerGroupName = "bank-account-settings",
      ),
      clazz = BankingTierChangeEvent::class.java,
      listener = ::onReceive
    )
  }

  suspend fun onReceive(event: BankingTierChangeEvent) {
    logger.info {
      "Received bankingTier change event for businessGuid=${event.update.businessGuid} and" +
        " bankingTier=${event.update} "
    }

    bankingLimitTierService.changeTier(
      update = event.update,
    )
  }
}
