package co.highbeam.endpoint.internationalBankAccount

import co.highbeam.auth.auth.AuthPermission
import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.auth.permissions.Permission
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.mapper.internationalBankAccount.InternationalBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.currencyCloud.CurrencyCloudService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi as Api
import co.highbeam.rep.internationalBankAccount.InternationalBankAccountRep as Rep

internal class GetInternationalBankAccountsByBusinessGuid @Inject constructor(
  private val authPermission: AuthPermission.Provider,
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val mapper: InternationalBankAccountMapper,
  private val internationalBankAccountService: <PERSON>urrencyCloudService,
) : EndpointHandler<Api.GetByBusinessGuid, Rep.Complete>(
  template = Api.GetByBusinessGuid::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByBusinessGuid =
    Api.GetByBusinessGuid(businessGuid = call.getParam("businessGuid"))

  override suspend fun Handler.handle(endpoint: Api.GetByBusinessGuid): Rep.Complete {
    authSome(
      authPlatformRole(PlatformRole.SUPERBLOCKS),
      authPermission(Permission.BankAccount_ReadNumber) { endpoint.businessGuid }
    )

    val account = internationalBankAccountService.getByBusinessGuid(endpoint.businessGuid)
      ?: throw InternationalBankAccountNotFound()

    return mapper.toCompleteRep(account)
      ?: throw InternationalBankAccountNotFound()
  }
}
