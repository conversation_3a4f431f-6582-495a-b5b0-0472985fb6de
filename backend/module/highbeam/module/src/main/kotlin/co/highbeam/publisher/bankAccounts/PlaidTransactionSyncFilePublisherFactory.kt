package co.highbeam.publisher.bankAccounts

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.rep.plaid.PlaidTransactionSyncFileEvent
import com.google.inject.Inject

internal class PlaidTransactionSyncFilePublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<PlaidTransactionSyncFileEvent>>() {
  override fun get(): EventPublisher<PlaidTransactionSyncFileEvent> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<PlaidTransactionSyncFileEvent>(
    topic = TopicConfig("plaid-transaction-sync-file"),
    typeLiteral = typeLiteral(),
    provider = PlaidTransactionSyncFilePublisherFactory::class,
  )
}
