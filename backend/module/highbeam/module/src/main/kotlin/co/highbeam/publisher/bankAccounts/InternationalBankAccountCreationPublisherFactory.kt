package co.highbeam.publisher.bankAccounts

import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.publisher.EventPublisher
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.PublisherProvider
import co.highbeam.feature.typeLiteral
import co.highbeam.listener.bankAccounts.INTERNATIONAL_BANK_ACCOUNT_CREATION_TOPIC_NAME
import co.highbeam.rep.bankAccount.InternationalBankAccountCreationEvent
import com.google.inject.Inject

internal class InternationalBankAccountCreationPublisherFactory @Inject constructor(
  private val factory: EventPublisherFactory,
) : PublisherProvider<EventPublisher<InternationalBankAccountCreationEvent>>() {
  override fun get(): EventPublisher<InternationalBankAccountCreationEvent> =
    factory.buildPublisher(topic)

  companion object : PublisherProvider.Companion<InternationalBankAccountCreationEvent>(
    topic = TopicConfig(INTERNATIONAL_BANK_ACCOUNT_CREATION_TOPIC_NAME),
    typeLiteral = typeLiteral(),
    provider = InternationalBankAccountCreationPublisherFactory::class,
  )
}
