package co.highbeam.endpoint.permissions

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.mapper.permissions.PermissionsMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.permissions.PermissionsService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.permissions.PermissionsApi as Api
import co.highbeam.rep.permissions.PermissionsRep as Rep

internal class GetPermissionsByUser @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val permissionsMapper: PermissionsMapper,
  private val permissionsService: PermissionsService,
) : EndpointHandler<Api.GetByUser, Rep>(
  template = Api.GetByUser::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetByUser =
    Api.GetByUser(userGuid = call.getParam("userGuid"))

  override suspend fun Handler.handle(endpoint: Api.GetByUser): Rep {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))
    val permissions = permissionsService.getByUser(endpoint.userGuid)
    return permissionsMapper.rep(permissions)
  }
}
