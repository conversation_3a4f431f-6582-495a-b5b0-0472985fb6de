package co.highbeam.endpoint.plaid

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.plaid.PlaidConnectionService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.plaid.PlaidApi as Api
import co.highbeam.rep.plaid.PlaidConnectionRep as Rep

internal class GetPlaidConnection @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val plaidConnectionService: PlaidConnectionService,
) : EndpointHandler<Api.GetConnection, Rep.Complete>(
  template = Api.GetConnection::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.GetConnection =
    Api.GetConnection(
      connectionGuid = call.getParam("connectionGuid"),
    )

  override fun loggingContext(endpoint: Api.GetConnection) =
    super.loggingContext(endpoint) + mapOf("connectionGuid" to endpoint.connectionGuid.toString())

  override suspend fun Handler.handle(endpoint: Api.GetConnection): Rep.Complete {
    auth(authPlatformRole(PlatformRole.HIGHBEAM_SERVER))

    return plaidConnectionService.get(endpoint.connectionGuid)
  }
}
