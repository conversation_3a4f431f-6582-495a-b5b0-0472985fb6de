package co.highbeam.endpoint.internationalBankAccount

import co.highbeam.auth.auth.AuthPlatformRole
import co.highbeam.exception.internationalBankAccount.InternationalBankAccountNotFound
import co.highbeam.exception.unprocessable
import co.highbeam.mapper.internationalBankAccount.InternationalBankAccountMapper
import co.highbeam.permissions.platform.PlatformRole
import co.highbeam.restInterface.EndpointHandler
import co.highbeam.restInterface.template
import co.highbeam.service.currencyCloud.CurrencyCloudService
import com.google.inject.Inject
import io.ktor.server.application.ApplicationCall
import co.highbeam.api.internationalBankAccount.InternationalBankAccountApi as Api
import co.highbeam.rep.internationalBankAccount.InternationalBankAccountRep as Rep

internal class EnableInternationalBankAccount @Inject constructor(
  private val authPlatformRole: AuthPlatformRole.Provider,
  private val mapper: InternationalBankAccountMapper,
  private val internationalBankAccountService: CurrencyCloudService,
) : EndpointHandler<Api.Enable, Rep.Complete>(
  template = Api.Enable::class.template(),
) {
  override suspend fun endpoint(call: ApplicationCall): Api.Enable =
    Api.Enable(
      internationalBankAccountGuid = call.getParam("internationalBankAccountGuid")
    )

  override suspend fun Handler.handle(
    endpoint: Api.Enable
  ): Rep.Complete {
    auth(authPlatformRole(PlatformRole.SUPERBLOCKS))

    return mapper.toCompleteRep(
      internationalBankAccountService.enable(endpoint.internationalBankAccountGuid)
    ) ?: throw unprocessable(InternationalBankAccountNotFound())
  }
}
