package co.highbeam.service.unitCoEventHandler

import co.highbeam.api.businessMember.BusinessMemberApi
import co.highbeam.api.payee.PayeeApi
import co.highbeam.client.businessMember.BusinessMemberClient
import co.highbeam.client.payee.PayeeClient
import co.highbeam.email.EmailService
import co.highbeam.email.template.EmailTemplate
import co.highbeam.email.template.PaymentRejectedEmailTemplate
import co.highbeam.model.unitCoEvent.UnitCoPaymentRejectedEventModel
import co.highbeam.service.bankAccount.BankAccountService
import co.unit.client.UnitCoClient
import com.google.inject.Inject
import mu.KotlinLogging
import java.util.UUID

@Deprecated(
  """DEPRECATION NOTICE:

PLEASE DO NOT ADD ANY MORE CODE TO THIS.
Webhook handling can now be distributed using Pub/Sub.
You can handle webhook events in your module. No need to do it here.
Search for [topic = "unit-co-webhook"] for an example."""
)
internal class UnitCoPaymentRejectedEventHandler @Inject constructor(
  private val bankAccountService: BankAccountService,
  private val businessMemberClient: BusinessMemberClient,
  private val emailService: EmailService,
  private val payeeClient: PayeeClient,
  private val unitCoClient: UnitCoClient,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun handleEvent(event: UnitCoPaymentRejectedEventModel) {
    logger.error("Unit Payment rejected: $event.")
    // Emails only sent for recurring payments, default payments have instant feedback
    val recurringPaymentId = event.recurringPaymentId
    val bankAccount = getBankAccount(event.accountId)

    if (event.paymentType == PaymentRejectedEmailTemplate.PaymentType.Recurring.name
      && recurringPaymentId != null) {
      emailService.sync(
        key = "PaymentRejected|${bankAccount.businessGuid}|${event.paymentId}"
      ) { sendEmail ->
        val bankAccount = getBankAccount(event.accountId)
        val unitCoDepositAccount = getUnitCoDepositAccount(event.accountId)
        val unitCoRecurringPayment = getUnitCoRecurringPayment(recurringPaymentId)
        val dayOfMonth = unitCoRecurringPayment.schedule.dayOfMonth
        val payee = event.payeeGuid?.let { getPayee(bankAccount.businessGuid, it) }
        val businessMembers = getBusinessMembers(bankAccount.businessGuid)
        val isRecurringPaymentComplete = unitCoRecurringPayment.status == "Completed"
        val isScheduledPayment = unitCoRecurringPayment.schedule.totalNumberOfPayments == 1
        val scheduledDate = unitCoRecurringPayment.schedule.startTime

        sendEmail(
          PaymentRejectedEmailTemplate(
            recipients = businessMembers.map { businessMember ->
              EmailTemplate.Recipient(
                emailAddress = businessMember.emailAddress ?: error(
                  "Businesses must have an email address"
                ),
                name = businessMember.fullName,
              )
            },
            recipientName = payee?.let { payee.name },
            amount = event.amount,
            fromAccountName = bankAccount.name,
            fromAccountLast4Digits = unitCoDepositAccount.accountNumber.takeLast(4),
            failureReason = event.reason.value,
            paymentType = PaymentRejectedEmailTemplate.PaymentType.valueOf(event.paymentType),
            recurringDayOfMonth = dayOfMonth,
            isScheduledPayment = isScheduledPayment,
            scheduledDate = scheduledDate,
            isRecurringPaymentComplete = isRecurringPaymentComplete,
          )
        )
      }
    }

  }

  private fun getBankAccount(unitCoDepositAccountId: String) =
    bankAccountService.getByUnitCoDepositAccountId(unitCoDepositAccountId)
      .let(::checkNotNull)

  private suspend fun getUnitCoDepositAccount(unitCoDepositAccountId: String) =
    unitCoClient.depositAccount.get(unitCoDepositAccountId)
      .let(::checkNotNull)

  private suspend fun getUnitCoRecurringPayment(unitCoRecurringPaymentId: String) =
    unitCoClient.payment.getScheduledAch(unitCoRecurringPaymentId)
      .let(::checkNotNull)

  private suspend fun getPayee(businessGuid: UUID, payeeGuid: UUID) =
    payeeClient.request(PayeeApi.Get(businessGuid, payeeGuid))
      .let(::checkNotNull)

  private suspend fun getBusinessMembers(businessGuid: UUID) =
    businessMemberClient.request(BusinessMemberApi.GetByBusiness(businessGuid))
}
