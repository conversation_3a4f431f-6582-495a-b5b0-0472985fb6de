authentication:
  insecure: true
  verifiers:
    - type: Jwt
      mechanisms:
        - source: Static
          issuer: https://highbeam.co/
          leeway: 0
          algorithm: Hmac256
          secret:
            type: Plaintext
            value: highbeam

email:
  enabled: false

highbeamDatabase:
  jdbcUrl:
    type: Plaintext
    value: *****************************************
  username:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_USERNAME
    defaultValue: highbeam
  password:
    type: EnvironmentVariable
    name: HIGHBEAM_TEST_POSTGRES_PASSWORD
  runMigrations: true
  connectionTimeout: 1000
  maximumPoolSize: 2
