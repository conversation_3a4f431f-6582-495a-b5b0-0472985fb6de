package co.highbeam.endpoint.unitCoWebhook

import co.highbeam.api.business.BusinessApi
import co.highbeam.api.event.business.BusinessFeatureEventApi
import co.highbeam.api.unitCo.UnitCoWebhookApi
import co.highbeam.api.user.UserApi
import co.highbeam.client.business.BusinessClient
import co.highbeam.client.event.business.BusinessFeatureEventClient
import co.highbeam.client.user.UserClient
import co.highbeam.event.business.UnitCoCustomerCreatedEvent
import co.highbeam.model.unitCoWebhook.UnitCoWebhookModel
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.business.BusinessRep
import co.highbeam.rep.unitCo.UnitCoEventWrapperRep
import co.highbeam.rep.user.UserRep
import co.highbeam.server.Server
import co.highbeam.service.currencyCloud.CurrencyCloudService
import co.unit.client.UnitCoClient
import co.unit.rep.AddressRep
import co.unit.rep.ApplicationRep
import co.unit.rep.BeneficialOwnerRep
import co.unit.rep.BusinessContactRep
import co.unit.rep.CustomerRep
import co.unit.rep.FullNameRep
import co.unit.rep.OfficerRep
import co.unit.rep.PhoneRep
import com.auth0.client.Auth0ManagementClient
import com.auth0.rep.Auth0User
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

internal class UnitCoCustomerCreatedWebhookTest(
  server: Server<*>,
) : UnitCoWebhookTest(server) {
  private val unitCoWebhookId = UUID.randomUUID().toString()
  private val unitCoApplicationId = UUID.randomUUID().toString()
  private val unitCoCustomerId = UUID.randomUUID().toString()

  private val businessGuid = UUID.randomUUID()
  private val event = objectMapper.convertValue<JsonNode>(
    mapOf(
      "id" to unitCoWebhookId,
      "type" to "customer.created",
      "attributes" to mapOf(
        "createdAt" to "2020-07-29T12:53:05.882Z",
        "tags" to mapOf("businessGuid" to businessGuid),
      ),
      "relationships" to mapOf(
        "customer" to mapOf("data" to mapOf("id" to unitCoCustomerId)),
        "application" to mapOf("data" to mapOf("id" to unitCoApplicationId)),
      ),
    ),
  )

  @Test
  fun `individual customer, business does not exist - webhook failed`() {
    mockIndividualCustomer()

    test(expectResult = Unit) {
      webhookClient.request(UnitCoWebhookApi.Post(UnitCoEventWrapperRep(listOf(event))))
    }
    assertEquals(
      expected = webhookModel(uuidGenerator[0], UnitCoWebhookModel.Status.PROCESSING_FAILED),
      actual = getWebhook(uuidGenerator[0]),
    )
  }

  @Test
  fun `individual customer, business exists - webhook processed`() {
    mockIndividualCustomer()

    val ownerUserGuid = UUID.randomUUID()
    val business = BusinessRep.Complete(
      guid = businessGuid,
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUserGuid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    val user = mockk<UserRep.Complete> { every { emailAddress } returns "test@email" }

    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns business
    coEvery { get<BusinessClient>().request(any<BusinessApi.Update>()) } returns mockk()
    coEvery {
      get<CurrencyCloudService>().setUpCurrencyCloudForUnitCoCustomer(any())
    } returns mockk()
    coEvery { get<UserClient>().request(UserApi.Get(ownerUserGuid)) } returns user

    test(expectResult = Unit) {
      webhookClient.request(UnitCoWebhookApi.Post(UnitCoEventWrapperRep(listOf(event))))
    }
    assertEquals(
      expected = webhookModel(uuidGenerator[0], UnitCoWebhookModel.Status.PROCESSED),
      actual = getWebhook(uuidGenerator[0]),
    )

    coVerify {
      get<BusinessFeatureEventClient>().request(
        BusinessFeatureEventApi.UnitCoCustomerCreated(
          event = UnitCoCustomerCreatedEvent(
            businessGuid = businessGuid,
            dba = "Some DBA",
            name = "Some firstname Some lastname",
            unitCoCustomerId = unitCoCustomerId,
            stateOfIncorporation = "NY",
          )
        )
      )
    }

    coVerify {
      get<UserClient>().request(UserApi.Get(ownerUserGuid))
    }
    confirmVerified(get<UserClient>())
  }

  @Test
  fun `business customer, business does not exist - webhook failed`() {
    mockBusinessCustomer()

    test(expectResult = Unit) {
      webhookClient.request(UnitCoWebhookApi.Post(UnitCoEventWrapperRep(listOf(event))))
    }
    assertEquals(
      expected = webhookModel(uuidGenerator[0], UnitCoWebhookModel.Status.PROCESSING_FAILED),
      actual = getWebhook(uuidGenerator[0]),
    )
  }

  @Test
  fun `business customer, business exists - webhook processed`() {
    mockBusinessCustomer()

    val ownerUserGuid = UUID.randomUUID()
    val business = BusinessRep.Complete(
      guid = businessGuid,
      dba = null,
      name = null,
      referralLinkGuid = null,
      ownerUserGuid = ownerUserGuid,
      unitCoCustomerId = null,
      status = BusinessRep.Complete.Status.PendingReview,
      stateOfIncorporation = null,
      naics = null,
    )
    val user = mockk<UserRep.Complete> { every { emailAddress } returns "test@email" }

    coEvery { get<BusinessClient>().request(BusinessApi.Get(businessGuid)) } returns business
    coEvery { get<BusinessClient>().request(any<BusinessApi.Update>()) } returns mockk()
    coEvery {
      get<CurrencyCloudService>().setUpCurrencyCloudForUnitCoCustomer(any())
    } returns mockk()
    coEvery { get<UserClient>().request(UserApi.Get(ownerUserGuid)) } returns user

    test(expectResult = Unit) {
      webhookClient.request(UnitCoWebhookApi.Post(UnitCoEventWrapperRep(listOf(event))))
    }
    assertEquals(
      expected = webhookModel(uuidGenerator[0], UnitCoWebhookModel.Status.PROCESSED),
      actual = getWebhook(uuidGenerator[0]),
    )

    coVerify {
      get<BusinessFeatureEventClient>().request(
        BusinessFeatureEventApi.UnitCoCustomerCreated(
          event = UnitCoCustomerCreatedEvent(
            businessGuid = businessGuid,
            dba = "Some DBA",
            name = "Some customer name",
            unitCoCustomerId = unitCoCustomerId,
            stateOfIncorporation = "NY",
          )
        )
      )
    }

    coVerify {
      get<UserClient>().request(UserApi.Get(ownerUserGuid))
    }
    confirmVerified(get<UserClient>())
  }

  private fun webhookModel(guid: UUID, status: UnitCoWebhookModel.Status) = UnitCoWebhookModel(
    guid = guid,
    type = "customer.created",
    unitCoId = unitCoWebhookId,
    json = event,
    status = status,
  )

  private fun mockIndividualCustomer(): Pair<ApplicationRep.Complete, CustomerRep.Complete> {
    val auth0User = Auth0User.Complete(UUID.randomUUID().toString())
    mockAuth0User(auth0User)

    val application = ApplicationRep.Complete.Individual(
      id = unitCoApplicationId,
      businessGuid = businessGuid,
      createdAt = ZonedDateTime.now(clock),
      status = ApplicationRep.Status.Approved,
      address = AddressRep(
        street = "123 Main St",
        street2 = null,
        city = "New York",
        state = "NY",
        postalCode = "10004",
        country = "US",
      ),
      applicationFormId = UUID.randomUUID().toString(),
      firstName = "Some firstname",
      lastName = "Some lastname",
      dateOfBirth = LocalDate.of(2001, 8, 10),
      emailAddress = "<EMAIL>",
      phone = PhoneRep(
        countryCode = "1",
        number = "1555555578"
      ),
      ssn = "*********",
      passport = null
    )
    val customer = CustomerRep.Complete.Individual(
      id = unitCoCustomerId,
      businessGuid = businessGuid,
      authorizedUsers = emptyList(),
      dba = "Some DBA",
      firstName = "Some firstname",
      lastName = "Some lastname",
      emailAddress = "<EMAIL>",
      stateOfIncorporation = "NY",
    )
    mockApplication(application)
    mockCustomer(customer)

    return Pair(application, customer)
  }

  private fun mockBusinessCustomer(): Pair<ApplicationRep.Complete, CustomerRep.Complete> {
    val auth0User = Auth0User.Complete(UUID.randomUUID().toString())
    mockAuth0User(auth0User)

    val application = ApplicationRep.Complete.Business(
      id = unitCoApplicationId,
      businessGuid = businessGuid,
      createdAt = ZonedDateTime.now(clock),
      status = ApplicationRep.Status.Approved,
      address = AddressRep(
        street = "123 Main St",
        street2 = null,
        city = "New York",
        state = "NY",
        postalCode = "10004",
        country = "US",
      ),
      name = "Some customer name",
      applicationFormId = UUID.randomUUID().toString(),
      contact = BusinessContactRep(
        fullName = FullNameRep("Contact", "McContact"),
        email = "<EMAIL>",
        phone = PhoneRep(
          countryCode = "1",
          number = "1555555578"
        ),
      ),
      phone = PhoneRep(
        countryCode = "1",
        number = "1555555578"
      ),
      officer = OfficerRep(
        firstName = "Officer",
        lastName = "McOfficer",
        dateOfBirth = LocalDate.of(1974, 7, 4),
        socialSecurityNumber = ProtectedString("officer ssn"),
        passportNumber = ProtectedString("officer pass"),
        emailAddress = "<EMAIL>",
        phoneNumber = PhoneRep("1", "officer phone"),
        address = AddressRep(
          street = "officer street",
          street2 = "officer street2",
          city = "officer city",
          state = "officer state",
          postalCode = "officer postalCode",
          country = "officer country",
        ),
      ),
      beneficialOwners = listOf(
        BeneficialOwnerRep(
          firstName = "Officer",
          lastName = "McOfficer",
          dateOfBirth = LocalDate.of(1974, 7, 4),
          socialSecurityNumber = ProtectedString("officer ssn"),
          passportNumber = ProtectedString("officer pass"),
          emailAddress = "<EMAIL>",
          phoneNumber = PhoneRep("1", "officer phone"),
          address = AddressRep(
            street = "officer street",
            street2 = "officer street2",
            city = "officer city",
            state = "officer state",
            postalCode = "officer postalCode",
            country = "officer country",
          ),
        ),
        BeneficialOwnerRep(
          firstName = "Unrelated",
          lastName = "Beneficial",
          dateOfBirth = LocalDate.of(1956, 5, 6),
          socialSecurityNumber = null,
          passportNumber = null,
          emailAddress = "<EMAIL>",
          phoneNumber = PhoneRep("91", "ben2 phone"),
          address = AddressRep(
            street = "ben1 street",
            street2 = "ben1 street2",
            city = "ben1 city",
            state = "ben1 state",
            postalCode = "ben1 postalCode",
            country = "ben1 country",
          ),
        ),
      ),
      ein = "12345678",
      stateOfIncorporation = "NY",
      website = null,
    )
    val customer = CustomerRep.Complete.Business(
      id = unitCoCustomerId,
      businessGuid = businessGuid,
      name = "Some customer name",
      authorizedUsers = emptyList(),
      dba = "Some DBA",
      address = AddressRep(
        street = "123 Main St",
        street2 = "Apt A",
        city = "New York",
        state = "NY",
        postalCode = "10004",
        country = "US",
      ),
      contact = BusinessContactRep(
        fullName = FullNameRep("Contact", "McContact"),
        email = "<EMAIL>",
        phone = PhoneRep(
          countryCode = "1",
          number = "1555555578",
        ),
      ),
      phone = PhoneRep(
        countryCode = "1",
        number = "1555555579",
      ),
      stateOfIncorporation = "NY",
    )
    mockApplication(application)
    mockCustomer(customer)

    return Pair(application, customer)
  }

  private fun mockAuth0User(auth0User: Auth0User.Complete) {
    coEvery { get<Auth0ManagementClient>().getUsersByEmailAddress(any()) } returns listOf(auth0User)
  }

  private fun mockApplication(application: ApplicationRep.Complete) {
    coEvery { get<UnitCoClient>().application.get(any()) } returns application
  }

  private fun mockCustomer(customer: CustomerRep.Complete) {
    coEvery { get<UnitCoClient>().customer.get(any()) } returns customer
    coEvery { get<UnitCoClient>().customer.update(any(), any()) } returns customer
  }
}
