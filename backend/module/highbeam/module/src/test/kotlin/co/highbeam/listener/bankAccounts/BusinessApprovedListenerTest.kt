package co.highbeam.listener.bankAccounts

import co.highbeam.api.bankAccount.BankAccountSettingApi
import co.highbeam.api.rutter.RutterConnectionApi
import co.highbeam.client.rutter.RutterConnectionClient
import co.highbeam.event.business.HighbeamBusinessApprovedEvent
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.model.bankAccount.BankAccountDataModel
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccountSetting.BankAccountSettingRep
import co.highbeam.server.Server
import co.highbeam.store.bankAccount.BankAccountStore
import co.highbeam.testing.BankAccountIntegrationTest
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class BusinessApprovedListenerTest(
  server: Server<*>,
) : BankAccountIntegrationTest(server) {
  private var fakeFeatureFlagService = get<FeatureFlagService>() as FakeFeatureFlagService

  private val webhookJson = objectMapper.readValue<JsonNode>(
    Resources.getResource("unitCoWebhook/customer-created-28.json"),
  )
  private val fixtureGuid = webhookJson
    .get("attributes")
    .get("tags")
    .get("businessGuid").textValue()
  private val businessGuid = UUID.fromString(fixtureGuid)
  private val unitCoApplicationId = "52"

  @Test
  fun `happy path`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.StopAchDebitsForHighYieldAccounts] = true
    mockBusiness(businessGuid)
    mockUnitCoAccountsGetByBusiness(businessGuid, listOf(UUID.randomUUID()))
    mockUnitCoStopPayment()

    get<BusinessApprovedListener>().onReceive(HighbeamBusinessApprovedEvent(
      businessGuid = businessGuid,
      unitCoApplicationId = unitCoApplicationId,
      sendNotification = true,
    ))

    val bankAccounts = get<BankAccountStore>().getAllByBusinessGuid(businessGuid)
    assertThat(bankAccounts).containsExactlyInAnyOrder(
      BankAccountModel(
        guid = uuidGenerator[1],
        unitCoDepositAccountId = uuidGenerator[1].toString(),
        businessGuid = businessGuid,
        name = "Primary",
        status = BankAccountRep.Status.OPEN,
        isPrimary = true,
        type = BankAccountRep.Type.DepositAccount,
        unitCoDepositProduct = DepositAccountRep.DepositProduct.CheckingRegularThread,
        accountMask = "5678",
        data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
      ),
      BankAccountModel(
        guid = uuidGenerator[2],
        unitCoDepositAccountId = uuidGenerator[2].toString(),
        businessGuid = businessGuid,
        name = "High yield",
        status = BankAccountRep.Status.OPEN,
        isPrimary = false,
        type = BankAccountRep.Type.HighYield,
        unitCoDepositProduct = DepositAccountRep.DepositProduct.HighYieldThread,
        accountMask = "5678",
        data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
      ),
    )
    coVerify(exactly = 1) {
      get<RutterConnectionClient>().request(RutterConnectionApi.ReactivateAll(businessGuid))
      get<UnitCoClient>().stopPayment.createAchStopPayment(
        match { rep ->
          rep.unitAccountId == uuidGenerator[2].toString() &&
            rep.minAmount == Money.fromCents(1) &&
            rep.originatorName == emptyList<String>() &&
            rep.expiration == null &&
            rep.isMultiUse &&
            rep.description == "High yield accounts stop payments"
        }
      )
    }
  }

  @Test
  fun `accounts do not get re created if they exist`() = integrationTest {
    fakeFeatureFlagService[BusinessFlag.StopAchDebitsForHighYieldAccounts] = true
    mockBusiness(businessGuid)
    mockUnitCoAccountsGetByBusiness(businessGuid, listOf(uuidGenerator[1], uuidGenerator[2]))
    mockUnitCoStopPayment()

    get<BusinessApprovedListener>().onReceive(
      HighbeamBusinessApprovedEvent(
        businessGuid = businessGuid,
        unitCoApplicationId = unitCoApplicationId,
        sendNotification = true,
      )
    )

    // Duplicate event
    get<BusinessApprovedListener>().onReceive(
      HighbeamBusinessApprovedEvent(
        businessGuid = businessGuid,
        unitCoApplicationId = unitCoApplicationId,
        sendNotification = true,
      )
    )

    val bankAccounts = get<BankAccountStore>().getAllByBusinessGuid(businessGuid)
    assertThat(bankAccounts).containsExactlyInAnyOrder(
      BankAccountModel(
        guid = uuidGenerator[1],
        unitCoDepositAccountId = uuidGenerator[1].toString(),
        businessGuid = businessGuid,
        name = "Primary",
        status = BankAccountRep.Status.OPEN,
        isPrimary = true,
        type = BankAccountRep.Type.DepositAccount,
        unitCoDepositProduct = DepositAccountRep.DepositProduct.CheckingRegularThread,
        accountMask = "5678",
        data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
      ),
      BankAccountModel(
        guid = uuidGenerator[2],
        unitCoDepositAccountId = uuidGenerator[2].toString(),
        businessGuid = businessGuid,
        name = "High yield",
        status = BankAccountRep.Status.OPEN,
        isPrimary = false,
        type = BankAccountRep.Type.HighYield,
        unitCoDepositProduct = DepositAccountRep.DepositProduct.HighYieldThread,
        accountMask = "5678",
        data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
      ),
    )
  }

  @Test
  fun `continues to create bank accounts if bank account setting already exists`() =
    integrationTest {
      mockBusiness(businessGuid)
      mockUnitCoAccountsGetByBusiness(businessGuid, listOf(UUID.randomUUID()))

      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.Regular
      )

      get<BusinessApprovedListener>().onReceive(HighbeamBusinessApprovedEvent(
        businessGuid = businessGuid,
        unitCoApplicationId = unitCoApplicationId,
        sendNotification = true,
      ))

      val bankAccounts = get<BankAccountStore>().getAllByBusinessGuid(businessGuid)
      assertThat(bankAccounts).containsExactlyInAnyOrder(
        BankAccountModel(
          guid = uuidGenerator[1],
          unitCoDepositAccountId = uuidGenerator[1].toString(),
          businessGuid = businessGuid,
          name = "Primary",
          status = BankAccountRep.Status.OPEN,
          isPrimary = true,
          type = BankAccountRep.Type.DepositAccount,
          unitCoDepositProduct = DepositAccountRep.DepositProduct.CheckingRegularThread,
          accountMask = "5678",
          data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
        ),
        BankAccountModel(
          guid = uuidGenerator[2],
          unitCoDepositAccountId = uuidGenerator[2].toString(),
          businessGuid = businessGuid,
          name = "High yield",
          status = BankAccountRep.Status.OPEN,
          isPrimary = false,
          type = BankAccountRep.Type.HighYield,
          unitCoDepositProduct = DepositAccountRep.DepositProduct.HighYieldThread,
          accountMask = "5678",
          data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
        ),
      )
    }

  @Test
  fun `sets a default bank account setting for the business`() = integrationTest {
    mockBusiness(businessGuid)
    mockUnitCoAccountsGetByBusiness(businessGuid, listOf(UUID.randomUUID()))

    get<BusinessApprovedListener>().onReceive(HighbeamBusinessApprovedEvent(
      businessGuid = businessGuid,
      unitCoApplicationId = unitCoApplicationId,
      sendNotification = true,
    ))

    assertThat(
      bankAccountSettingClient.request(BankAccountSettingApi.Get(
        businessGuid = businessGuid
      ))
    ).isEqualTo(
      BankAccountSettingRep(
        businessGuid = businessGuid,
        bankingPartner = DepositAccountRep.PartnerBank.Thread,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.Regular,
        bankingSupportTier = BankAccountSettingRep.BankingSupportTier.Standard,
      )
    )
  }
}
