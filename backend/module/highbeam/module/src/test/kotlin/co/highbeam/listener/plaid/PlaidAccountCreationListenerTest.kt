package co.highbeam.listener.plaid

import co.highbeam.api.plaid.PlaidApi
import co.highbeam.event.publisher.getPublisher
import co.highbeam.model.plaid.PlaidBankAccountModel
import co.highbeam.model.plaid.PlaidConnectionModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.plaid.PlaidAccount
import co.highbeam.rep.plaid.PlaidBankAccountRep
import co.highbeam.server.Server
import co.highbeam.service.plaid.PLAID_PRODUCTS_NEEDED_FOR_PROCESSOR_TOKEN
import co.highbeam.store.plaid.PlaidBankAccountStore
import co.highbeam.testing.BankAccountsFeatureIntegrationTest
import co.highbeam.testing.createPlaidAccount
import co.highbeam.testing.mockPlaidAccountsGet
import co.highbeam.testing.mockPlaidAuthGet
import co.highbeam.testing.mockPlaidIdentityGet
import co.highbeam.testing.mockPlaidItemGet
import co.highbeam.testing.mockPlaidProcessorTokenCreate
import co.highbeam.testing.plaidAccountsGetResponse
import co.highbeam.testing.plaidAuthGetResponse
import co.highbeam.testing.plaidIdentityGetResponse
import co.highbeam.testing.plaidProcessorTokenCreateResponse
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.plaid.client.model.AccountSubtype
import com.plaid.client.model.AccountType
import com.plaid.client.model.IdentityGetRequest
import com.plaid.client.model.ProcessorTokenCreateRequest
import com.plaid.client.model.Products
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class PlaidAccountCreationListenerTest(
  server: Server<*>,
) : BankAccountsFeatureIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()
  private val connectionGuid = UUID.randomUUID()
  private val institutionId = "sample_institution_id"
  private val institutionName = "sample_institution_name"
  private val eventTopic = "plaid-transaction-sync"
  private val publisher = getPublisher<String>(eventTopic)

  @Test
  fun `enriches depository accounts`() =
    integrationTest {
      // Create initial records (simulating access token creation inserting
      // connection and preliminary account records)
      val connection = createPlaidConnection(
        guid = connectionGuid,
        businessGuid = businessGuid,
        institutionId = institutionId,
        institutionName = institutionName,
      )
      val basicPlaidAccountFromMetadata = createPlaidAccount(
        accountId = "account-id",
        name = "Primary account",
        mask = "3456",
        accountType = AccountType.DEPOSITORY,
        accountSubtype = AccountSubtype.CHECKING
      )
      get<PlaidBankAccountStore>().upsert(
        createBasicPlaidBankAccount(connection, basicPlaidAccountFromMetadata)
      )
      // Mock Plaid API
      mockPlaidItemGet(
        accessToken = connection.accessToken,
        availableProducts = PLAID_PRODUCTS_NEEDED_FOR_PROCESSOR_TOKEN,
      )
      mockPlaidIdentityGet(
        accessToken = connection.accessToken,
        mockPlaidIdentityGetResponse = plaidIdentityGetResponse(
          accountId = basicPlaidAccountFromMetadata.id,
          plaidInstitutionId = institutionId,
          currentBalance = Money.fromString("123.45"),
          availableBalance = null,
        )
      )
      mockPlaidAuthGet(
        accessToken = connection.accessToken,
        mockAuthGetResponse = plaidAuthGetResponse(
          accountId = basicPlaidAccountFromMetadata.id,
          achRoutingNumber = "**********",
          achAccountNumber = "**********",
          achWireRoutingNUmber = "**********",
        )
      )
      val expectedProcessorToken = "I am a processor token"
      mockPlaidProcessorTokenCreate(
        accessToken = connection.accessToken,
        accountId = basicPlaidAccountFromMetadata.id,
        mockProcessorTokenCreateResponse = plaidProcessorTokenCreateResponse(
          mockProcessorToken = expectedProcessorToken
        )
      )
      // Run listener
      get<PlaidAccountCreationListener>().onReceive(connection.guid)
      // Check results
      assertPlaidBankAccount(
        PlaidBankAccountRep.Complete(
          businessGuid = businessGuid,
          connectionGuid = connectionGuid,
          plaidAccountId = "account-id",
          accountOwnerFullName = "Alberta Bobbeth Charleson",
          currentBalance = Balance.fromString("123.45"),
          availableBalance = null,
          creditLimit = null,
          currency = "USD",
          accountName = "Plaid Checking",
          accountOfficialName = "Plaid Gold Checking",
          subtype = AccountSubtype.CHECKING,
          accountMask = "9606",
          accountType = AccountType.DEPOSITORY,
          plaidProcessorToken = expectedProcessorToken,
          institutionId = institutionId,
          institutionName = institutionName,
          isActive = true,
          isConnectionActive = true,
          achRoutingNumber = "**********",
          achAccountNumber = "**********",
          achWireRoutingNumber = "**********",
        )
      )
      verify(exactly = 1) {
        get<com.plaid.client.request.PlaidApi>().identityGet(
          IdentityGetRequest().accessToken(connection.accessToken.value),
        )
      }
      verify(exactly = 1) {
        get<com.plaid.client.request.PlaidApi>().processorTokenCreate(
          ProcessorTokenCreateRequest()
            .accessToken(connection.accessToken.value)
            .accountId(basicPlaidAccountFromMetadata.id)
            .processor(ProcessorTokenCreateRequest.ProcessorEnum.UNIT)
        )
      }
      assertThat(publisher.events).hasSize(1)
      assertThat(publisher.events.first()).isEqualTo(
        Pair(connection.plaidItemId, mapOf("source" to "account-connected"))
      )
    }

  @Test
  fun `enriches credit accounts`() =
    integrationTest {
      // Create initial records (simulating access token creation inserting
      // connection and preliminary account records)
      val connection = createPlaidConnection(
        guid = connectionGuid,
        businessGuid = businessGuid,
        institutionId = institutionId,
        institutionName = institutionName
      )
      val accountMetadata = createPlaidAccount(
        accountId = "account-id",
        name = "Platypus Credit",
        mask = "3456",
        accountType = AccountType.CREDIT,
        accountSubtype = AccountSubtype.CREDIT_CARD
      )
      val plaidBankAccountStore = get<PlaidBankAccountStore>()
      plaidBankAccountStore.upsert(
        createBasicPlaidBankAccount(connection, accountMetadata)
      )
      // Mock Plaid API
      mockPlaidItemGet(
        accessToken = connection.accessToken,
        availableProducts = listOf(Products.LIABILITIES)
      )
      mockPlaidAccountsGet(
        accessToken = connection.accessToken,
        mockPlaidAccountsGet = plaidAccountsGetResponse(
          accountId = accountMetadata.id,
          accountMask = accountMetadata.mask,
          accountName = accountMetadata.name,
          accountOfficialName = "Plaid Platypus Credit Card Official Name",
          accountSubtype = AccountSubtype.CREDIT_CARD,
          accountType = AccountType.CREDIT,
          availableBalance = 123.45,
          currentBalance = 222.34,
          creditLimit = 500.00
        )
      )
      // Run listener
      get<PlaidAccountCreationListener>().onReceive(connection.guid)
      // Check results
      assertPlaidBankAccount(
        PlaidBankAccountRep.Complete(
          businessGuid = businessGuid,
          connectionGuid = connectionGuid,
          plaidAccountId = "account-id",
          accountOwnerFullName = null,
          currentBalance = Balance(222.34),
          availableBalance = Balance(123.45),
          creditLimit = Money.fromDollarsAndCents(500.00),
          currency = "USD",
          accountName = "Platypus Credit",
          accountOfficialName = "Plaid Platypus Credit Card Official Name",
          subtype = AccountSubtype.CREDIT_CARD,
          accountMask = "3456",
          accountType = AccountType.CREDIT,
          plaidProcessorToken = null,
          institutionId = institutionId,
          institutionName = institutionName,
          isActive = true,
          isConnectionActive = true,
          achRoutingNumber = null,
          achAccountNumber = null,
          achWireRoutingNumber = null,
        )
      )
      verify(exactly = 0) {
        get<com.plaid.client.request.PlaidApi>().identityGet(any())
      }
      verify(exactly = 0) {
        get<com.plaid.client.request.PlaidApi>().processorTokenCreate(any())
      }
      assertThat(publisher.events).hasSize(1)
      assertThat(publisher.events.first()).isEqualTo(
        Pair(connection.plaidItemId, mapOf("source" to "account-connected"))
      )
    }

  private fun createBasicPlaidBankAccount(
    connection: PlaidConnectionModel,
    accountMetadata: PlaidAccount,
  ) =
    PlaidBankAccountModel(
      businessGuid = connection.businessGuid,
      connectionGuid = connection.guid,
      plaidAccountId = accountMetadata.id,
      accountJson = jacksonObjectMapper().convertValue<JsonNode>(from = accountMetadata),
      currentBalance = null,
      availableBalance = null,
      creditLimit = null,
      currency = "USD", // Assuming USD
      accountName = accountMetadata.name,
      accountOfficialName = null,
      accountType = AccountType.fromValue(accountMetadata.type).name,
      accountSubtype = AccountSubtype.fromValue(accountMetadata.subtype).name,
      accountMask = accountMetadata.mask,
      accountOwnerFullName = null,
      plaidProcessorToken = null,
      institutionId = connection.institutionId,
      isActive = true, // Assume account is active
      achRoutingNumber = null,
      achAccountNumber = null,
      achWireRoutingNumber = null,
    )

  private suspend fun assertPlaidBankAccount(expectedRep: PlaidBankAccountRep.Complete) {
    assertThat(
      plaidClient.request(
        PlaidApi.GetBankAccountsByBusiness(
          businessGuid = businessGuid,
        )
      )
    ).isEqualTo(listOf(expectedRep))
  }
}
