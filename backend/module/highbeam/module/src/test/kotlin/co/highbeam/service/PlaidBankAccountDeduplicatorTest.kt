package co.highbeam.service

import co.highbeam.model.plaid.PlaidBankAccountModel
import co.highbeam.service.plaid.PlaidBankAccountDeduplicatorImpl
import co.highbeam.service.plaid.PlaidBankAccountService
import co.highbeam.store.plaid.PlaidBankAccountStore
import co.highbeam.testing.plaidAccountsGetResponse
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.plaid.client.model.AccountBase
import com.plaid.client.model.AccountSubtype
import com.plaid.client.model.AccountType
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class PlaidBankAccountDeduplicatorTest {
  private val plaidBankAccountService: PlaidBankAccountService = mockk(relaxed = true)
  private val plaidBankAccountStore: PlaidBankAccountStore = mockk()
  private val deduplicator = PlaidBankAccountDeduplicatorImpl(
    plaidBankAccountService = plaidBankAccountService,
    plaidBankAccountStore = plaidBankAccountStore,
  )
  private val businessGuid = UUID.randomUUID()
  private val connectionGuid = UUID.randomUUID()

  @BeforeEach
  fun setup() {
    clearAllMocks()
  }

  @Test
  fun `nothing deleted when no existing accounts are found`() = runBlocking {
    mockExistingAccounts()
    val plaidApiAccount = createPlaidApiAccount()
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertNoAccountsDeleted()
  }

  @Test
  fun `matching account ids don't conflict`() = runBlocking {
    val existingAccountModel = createPlaidBankAccountModel()
    mockExistingAccounts(existingAccountModel)
    val plaidApiAccount =
      createPlaidApiAccount(plaidAccountId = existingAccountModel.plaidAccountId)
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertNoAccountsDeleted()
  }

  @Test
  fun `mismatched account ids but differing fields don't conflict`() = runBlocking {
    val existingAccountModel = createPlaidBankAccountModel(
      plaidAccountId = "plaidAccountId",
      mask = "1234",
      name = "name",
      officialName = "officialName",
    )
    mockExistingAccounts(existingAccountModel)
    val plaidApiAccount = createPlaidApiAccount(
      plaidAccountId = "some other account id",
      mask = "5555",
      name = "other name",
      officialName = "other official name",
    )
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertNoAccountsDeleted()
  }

  @Test
  fun `mismatched account ids and matching persistent account ids conflict`() = runBlocking {
    val persistentAccountId = "persistentAccountId"
    val existingAccountModel = createPlaidBankAccountModel(
      plaidAccountId = "plaidAccountId",
      persistentAccountId = persistentAccountId,
    )
    mockExistingAccounts(existingAccountModel)
    val plaidApiAccount = createPlaidApiAccount(
      plaidAccountId = "some other account id",
      persistentAccountId = persistentAccountId,
    )
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertAccountDeleted(existingAccountModel)
  }

  @Test
  fun `mismatched account ids and matching mask + name conflict`() = runBlocking {
    val existingAccountModel = createPlaidBankAccountModel(
      plaidAccountId = "plaidAccountId",
      mask = "1234",
      name = "name",
    )
    mockExistingAccounts(existingAccountModel)
    val plaidApiAccount = createPlaidApiAccount(
      plaidAccountId = "some other account id",
      mask = "1234",
      name = "name",
    )
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertAccountDeleted(existingAccountModel)
  }

  @Test
  fun `mismatched account ids and matching mask + official name conflict`() = runBlocking {
    val existingAccountModel = createPlaidBankAccountModel(
      plaidAccountId = "plaidAccountId",
      mask = "1234",
      name = "name",
      officialName = "officialName",
    )
    mockExistingAccounts(existingAccountModel)
    val plaidApiAccount = createPlaidApiAccount(
      plaidAccountId = "some other account id",
      mask = "1234",
      name = "other name",
      officialName = "officialName",
    )
    deduplicator.removeConflictingAccounts(
      businessGuid = businessGuid,
      connectionGuid = connectionGuid,
      plaidApiAccounts = listOf(plaidApiAccount),
    )
    assertAccountDeleted(existingAccountModel)
  }

  private fun assertNoAccountsDeleted() {
    coVerify(exactly = 0) {
      plaidBankAccountService.hardDeletePlaidBankAccount(
        businessGuid = businessGuid,
        plaidAccountId = any(),
      )
    }
  }

  private fun assertAccountDeleted(deletedAccount: PlaidBankAccountModel) {
    coVerify(exactly = 1) {
      plaidBankAccountService.hardDeletePlaidBankAccount(
        businessGuid = businessGuid,
        plaidAccountId = deletedAccount.plaidAccountId,
        allowActiveAccountDeletion = true,
      )
    }
  }

  private fun mockExistingAccounts(vararg existingAccounts: PlaidBankAccountModel) {
    coEvery {
      plaidBankAccountStore.getByConnectionGuid(businessGuid, connectionGuid)
    } returns existingAccounts.toList()
  }

  private fun createPlaidApiAccount(
    plaidAccountId: String = "plaidAccountId",
    persistentAccountId: String? = null,
    mask: String? = "1234",
    name: String = "Test Name",
    officialName: String = "Test Official Name",
  ): AccountBase = plaidAccountsGetResponse(
    accountId = plaidAccountId,
    accountMask = mask,
    accountName = name,
    accountOfficialName = officialName,
    accountSubtype = AccountSubtype.CHECKING,
    accountType = AccountType.DEPOSITORY,
    persistentAccountId = persistentAccountId,
  ).accounts[0]

  private fun createPlaidBankAccountModel(
    plaidAccountId: String = "plaidAccountId",
    persistentAccountId: String? = null,
    mask: String? = "1234",
    name: String? = "Test Name",
    officialName: String? = "Test Official Name",
  ): PlaidBankAccountModel = PlaidBankAccountModel(
    businessGuid = businessGuid,
    connectionGuid = connectionGuid,
    plaidAccountId = plaidAccountId,
    accountJson = jacksonObjectMapper().valueToTree(
      mapOf("persistentAccountId" to persistentAccountId),
    ),
    accountOwnerFullName = null,
    currentBalance = null,
    availableBalance = null,
    creditLimit = null,
    currency = null,
    accountName = name,
    accountOfficialName = officialName,
    accountType = "DEPOSITORY",
    accountSubtype = "CHECKING",
    accountMask = mask,
    plaidProcessorToken = null,
    institutionId = "ins_56",
    achRoutingNumber = null,
    achAccountNumber = null,
    achWireRoutingNumber = null,
    isActive = true,
  )
}

