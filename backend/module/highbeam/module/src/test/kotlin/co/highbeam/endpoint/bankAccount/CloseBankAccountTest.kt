package co.highbeam.endpoint.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.exception.bankAccount.InvalidBankAccount
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccount.BankAccountRepFixture
import co.highbeam.server.Server
import co.highbeam.testing.BankAccountIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.rep.DepositAccountRep
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.util.UUID

internal class CloseBankAccountTest(
  server: Server<*>,
) : BankAccountIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @BeforeEach
  fun setup() {
    createBankAccountSetting(businessGuid)
  }

  @Test
  fun `bank account does not exist`() = integrationTest {
    val accountGuid = UUID.randomUUID()

    assertThat(bankAccountClient.request(BankAccountApi.Close(accountGuid))).isNull()
  }

  @Test
  fun `bank account exists`() = integrationTest {
    val accountRep = BankAccountRepFixture(this@CloseBankAccountTest)
      .complete(
        idSeed = 1,
        unitCoDepositAccountId = uuidGenerator[1].toString(),
        businessGuid = businessGuid,
        isPrimary = false,
      )

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(businessGuid = businessGuid, bankAccountGuid = accountRep.guid)
    mockUnitCoAccountClose(businessGuid = businessGuid, bankAccountGuid = accountRep.guid)

    bankAccountClient.request(
      BankAccountApi.Post(
        rep = BankAccountRepFixture(this@CloseBankAccountTest)
          .creator(businessGuid),
      ),
    )

    assertThat(bankAccountClient.request(BankAccountApi.Get(accountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)

    assertThat(bankAccountClient.request(BankAccountApi.Close(accountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.CLOSED)
  }

  @Test
  fun `cannot close Thread high yield account`() = integrationTest {
    val accountGuid = uuidGenerator[1]
    val depositProduct = DepositAccountRep.DepositProduct.HighYieldThread

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = accountGuid,
      depositProduct = depositProduct,
    )
    mockUnitCoAccountClose(businessGuid = businessGuid, bankAccountGuid = accountGuid)

    val bankAccountRep = bankAccountClient.request(
      BankAccountApi.PostInternal(
        BankAccountRep.CreatorInternal(
          businessGuid = businessGuid,
          isPrimary = false,
          name = "high yield",
          highbeamType = BankAccountRep.Type.HighYield,
          unitCoDepositProduct = depositProduct,
        )
      ),
    )

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)

    assertHighbeamException {
      bankAccountClient.request(BankAccountApi.Close(bankAccountRep.guid))!!
    }.isHighbeamException(InvalidBankAccount())

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)
  }

  @Test
  fun `can close BRB high yield account`() = integrationTest {
    val accountGuid = uuidGenerator[1]
    val depositProduct = DepositAccountRep.DepositProduct.HighYield

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = accountGuid,
      depositProduct = depositProduct,
    )
    mockUnitCoAccountClose(businessGuid = businessGuid, bankAccountGuid = accountGuid)

    val bankAccountRep = bankAccountClient.request(
      BankAccountApi.PostInternal(
        BankAccountRep.CreatorInternal(
          businessGuid = businessGuid,
          isPrimary = false,
          name = "high yield",
          highbeamType = BankAccountRep.Type.HighYield,
          unitCoDepositProduct = depositProduct,
        )
      ),
    )

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)

    assertThat(bankAccountClient.request(BankAccountApi.Close(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.CLOSED)
  }

  @Test
  fun `cannot close frozen account`() = integrationTest {
    val accountGuid = uuidGenerator[1]
    val depositProduct = DepositAccountRep.DepositProduct.CheckingThread

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = accountGuid,
      depositProduct = depositProduct,
      status = DepositAccountRep.Status.Frozen,
    )
    mockUnitCoAccountClose(businessGuid = businessGuid, bankAccountGuid = accountGuid)

    val bankAccountRep = bankAccountClient.request(
      BankAccountApi.PostInternal(
        BankAccountRep.CreatorInternal(
          businessGuid = businessGuid,
          isPrimary = false,
          name = "checking account",
          highbeamType = BankAccountRep.Type.DepositAccount,
          unitCoDepositProduct = depositProduct,
        )
      ),
    )

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.FROZEN)

    assertHighbeamException {
      bankAccountClient.request(BankAccountApi.Close(bankAccountRep.guid))!!
    }.isHighbeamException(InvalidBankAccount())

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.FROZEN)
  }

  @Test
  fun `cannot close primary account`() = integrationTest {
    val accountGuid = uuidGenerator[1]
    val depositProduct = DepositAccountRep.DepositProduct.CheckingThread

    mockBusiness(businessGuid)
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = accountGuid,
      depositProduct = depositProduct,
    )
    mockUnitCoAccountClose(businessGuid = businessGuid, bankAccountGuid = accountGuid)

    val bankAccountRep = bankAccountClient.request(
      BankAccountApi.PostInternal(
        BankAccountRep.CreatorInternal(
          businessGuid = businessGuid,
          isPrimary = true,
          name = "Primary",
          highbeamType = BankAccountRep.Type.DepositAccount,
          unitCoDepositProduct = depositProduct,
        )
      ),
    )

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)

    assertHighbeamException {
      bankAccountClient.request(BankAccountApi.Close(bankAccountRep.guid))!!
    }.isHighbeamException(InvalidBankAccount())

    assertThat(bankAccountClient.request(BankAccountApi.Get(bankAccountRep.guid))!!.status)
      .isEqualTo(BankAccountRep.Status.OPEN)
  }
}
