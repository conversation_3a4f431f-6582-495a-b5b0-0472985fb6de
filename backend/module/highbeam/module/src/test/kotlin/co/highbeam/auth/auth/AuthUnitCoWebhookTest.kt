package co.highbeam.auth.auth

import co.highbeam.testing.TEST_UNIT_CO_CONFIG
import io.ktor.http.Headers
import io.ktor.http.HeadersSingleImpl
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class AuthUnitCoWebhookTest {
  private val authUnitCoWebhook = AuthUnitCoWebhook.Provider(TEST_UNIT_CO_CONFIG)

  @Test
  fun `Empty headers`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = Headers.Empty
    val result = authUnitCoWebhook(body).authorize(null, headers)
    assertFalse(result)
  }

  @Test
  fun `Incorrect signature`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = HeadersSingleImpl("X-Unit-Signature", listOf("asdf"))
    val result = authUnitCoWebhook(body).authorize(null, headers)
    assertFalse(result)
  }

  @Test
  fun `Correct signature`() = runBlocking {
    val body = "{ \"description\": \"This is the raw body of the request\" }"
    val headers = HeadersSingleImpl("X-Unit-Signature", listOf("jKa+Zu0HUaUw6v1ggASH24zPUH4="))
    val result = authUnitCoWebhook(body).authorize(null, headers)
    assertTrue(result)
  }
}
