package co.highbeam.endpoint.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.exception.ForbiddenException
import co.highbeam.featureFlags.BusinessFlag
import co.highbeam.featureFlags.FakeFeatureFlagService
import co.highbeam.featureFlags.FeatureFlagService
import co.highbeam.model.bankAccount.BankAccountDataModel
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.money.Balance
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccount.BankAccountRepFixture
import co.highbeam.rep.bankAccountSetting.BankAccountSettingRep
import co.highbeam.rep.business.BusinessRep
import co.highbeam.server.Server
import co.highbeam.store.bankAccount.BankAccountStore
import co.highbeam.testing.BankAccountIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.client.UnitCoClient
import co.unit.rep.DepositAccountRep
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class PostBankAccountTest(
  server: Server<*>,
) : BankAccountIntegrationTest(server) {
  private val bankAccountStore: BankAccountStore = get()
  private val fakeFeatureFlagService = get<FeatureFlagService>() as FakeFeatureFlagService

  @Test
  fun `happy path, creates thread checking (Thread)`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()

      createBankAccountSetting(businessGuid = businessGuid)

      mockBusiness(businessGuid, unitCoCustomerId = unitCoCustomerId)

      val expectedNewAccount = BankAccountRepFixture(this@PostBankAccountTest)
        .complete(
          idSeed = 1,
          unitCoDepositAccountId = uuidGenerator[1].toString(),
          businessGuid = businessGuid,
          depositProduct = DepositAccountRep.DepositProduct.CheckingThread,
          isPrimary = false,
        )

      mockUnitCoAccountGet(
        businessGuid = businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        depositProduct = DepositAccountRep.DepositProduct.CheckingThread
      )

      assertThat(
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = BankAccountRepFixture(this@PostBankAccountTest)
              .creator(businessGuid),
          ),
        )
      ).isEqualTo(expectedNewAccount)

      verifyAccountRepCreated(DepositAccountRep.Creator(
        businessGuid = expectedNewAccount.businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        customerId = unitCoCustomerId,
        name = expectedNewAccount.name,
        depositProduct = DepositAccountRep.DepositProduct.CheckingThread,
      ))
    }

  @Test
  fun `happy path, creates checking account based on tier's default checking (Thread)`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()

      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.PromotionalApril,
      )

      mockBusiness(businessGuid, unitCoCustomerId = unitCoCustomerId)

      val expectedNewAccount = BankAccountRepFixture(this@PostBankAccountTest)
        .complete(
          idSeed = 1,
          unitCoDepositAccountId = uuidGenerator[1].toString(),
          businessGuid = businessGuid,
          depositProduct = DepositAccountRep.DepositProduct.CheckingVipLowThread,
          isPrimary = false,
        )

      mockUnitCoAccountGet(
        businessGuid = businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        depositProduct = DepositAccountRep.DepositProduct.CheckingVipLowThread
      )

      assertThat(
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = BankAccountRepFixture(this@PostBankAccountTest)
              .creator(businessGuid),
          ),
        )
      ).isEqualTo(expectedNewAccount)

      verifyAccountRepCreated(DepositAccountRep.Creator(
        businessGuid = expectedNewAccount.businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        customerId = unitCoCustomerId,
        name = expectedNewAccount.name,
        depositProduct = DepositAccountRep.DepositProduct.CheckingVipLowThread,
      ))
    }

  @Test
  fun `happy path, creates checking for Custom tier based on bank account setting (Thread)`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()

      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.Custom,
        checkingDepositProduct = DepositAccountRep.DepositProduct.CheckingVipThreadAtm,
        yieldDepositProduct = DepositAccountRep.DepositProduct.CheckingVipInterestMaxThreadAtm,
      )

      mockBusiness(businessGuid, unitCoCustomerId = unitCoCustomerId)

      val expectedNewAccount = BankAccountRepFixture(this@PostBankAccountTest)
        .complete(
          idSeed = 1,
          unitCoDepositAccountId = uuidGenerator[1].toString(),
          businessGuid = businessGuid,
          depositProduct = DepositAccountRep.DepositProduct.CheckingVipThreadAtm,
          isPrimary = false,
        )

      mockUnitCoAccountGet(
        businessGuid = businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        depositProduct = DepositAccountRep.DepositProduct.CheckingVipThreadAtm
      )

      assertThat(
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = BankAccountRepFixture(this@PostBankAccountTest)
              .creator(businessGuid),
          ),
        )
      ).isEqualTo(expectedNewAccount)

      verifyAccountRepCreated(DepositAccountRep.Creator(
        businessGuid = expectedNewAccount.businessGuid,
        bankAccountGuid = expectedNewAccount.guid,
        customerId = unitCoCustomerId,
        name = expectedNewAccount.name,
        depositProduct = DepositAccountRep.DepositProduct.CheckingVipThreadAtm,
      ))
    }

  @Test
  fun `does not create account if existing open accounts too large for tier - standard`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()
      createBankAccountSetting(businessGuid = businessGuid)
      mockBusiness(businessGuid, unitCoCustomerId = unitCoCustomerId)

      for (i in 1..10) {
        val unitCoDepositAccountID = UUID.randomUUID()
        val threadBankAccount: BankAccountModel.Creator = BankAccountModel.Creator(
          guid = UUID.randomUUID(),
          businessGuid = businessGuid,
          name = "Random Thread account $i",
          isPrimary = false,
          type = BankAccountRep.Type.DepositAccount,
          unitCoDepositProduct = DepositAccountRep.DepositProduct.CheckingThread,
          data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
        )
        bankAccountStore.create(
          threadBankAccount,
          unitCoDepositAccountID.toString(),
          "5678")
      }

      val bankAccountToCreate = BankAccountRep.Creator(
        businessGuid = businessGuid,
        name = "Extra Thread account",
      )
      assertHighbeamException {
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = bankAccountToCreate
          ),
        )
      }.isHighbeamException(ForbiddenException())
    }

  @Test
  fun `does not create account if existing open accounts too large for tier - high`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()
      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.High
      )
      mockBusiness(businessGuid, unitCoCustomerId = unitCoCustomerId)

      for (i in 1..100) {
        val unitCoDepositAccountID = UUID.randomUUID()
        val threadBankAccount: BankAccountModel.Creator = BankAccountModel.Creator(
          guid = UUID.randomUUID(),
          businessGuid = businessGuid,
          name = "Random Thread account $i",
          isPrimary = false,
          type = BankAccountRep.Type.DepositAccount,
          unitCoDepositProduct = DepositAccountRep.DepositProduct.CheckingThread,
          data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
        )
        bankAccountStore.create(
          threadBankAccount,
          unitCoDepositAccountID.toString(),
          "5678"
        )
      }

      val bankAccountToCreate = BankAccountRep.Creator(
        businessGuid = businessGuid,
        name = "Extra Thread account",
      )
      assertHighbeamException {
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = bankAccountToCreate
          ),
        )
      }.isHighbeamException(ForbiddenException())
    }

  @Test
  fun `does not create account if business is archived`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()
      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.High
      )
      mockBusiness(
        businessGuid = businessGuid,
        status = BusinessRep.Complete.Status.Archived,
        unitCoCustomerId = unitCoCustomerId
      )


      val bankAccountToCreate = BankAccountRep.Creator(
        businessGuid = businessGuid,
        name = " Account after archived state",
      )
      assertHighbeamException {
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = bankAccountToCreate
          ),
        )
      }.isHighbeamException(ForbiddenException())
    }

  @Test
  fun `does not create account if business is not allowed to create accounts`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()
      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.High
      )
      mockBusiness(
        businessGuid = businessGuid,
        unitCoCustomerId = unitCoCustomerId
      )


      val bankAccountToCreate = BankAccountRep.Creator(
        businessGuid = businessGuid,
        name = " Account while not allowed to create one",
      )

      fakeFeatureFlagService[BusinessFlag.PreventDepositAccountCreation] = true
      assertHighbeamException {
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = bankAccountToCreate
          ),
        )
      }.isHighbeamException(ForbiddenException())
    }

  @Test
  fun `does not create account if business is not allowed to create accounts - daca`() =
    integrationTest {
      val businessGuid = UUID.randomUUID()
      val unitCoCustomerId = UUID.randomUUID().toString()
      createBankAccountSetting(
        businessGuid = businessGuid,
        bankingLimitTier = BankAccountSettingRep.BankingLimitTier.High
      )
      mockBusiness(
        businessGuid = businessGuid,
        unitCoCustomerId = unitCoCustomerId
      )


      val bankAccountToCreate = BankAccountRep.Creator(
        businessGuid = businessGuid,
        name = " Account after archived state",
      )

      fakeFeatureFlagService[BusinessFlag.PreventDacaDepositAccountCreation] = true
      assertHighbeamException {
        bankAccountClient.request(
          BankAccountApi.Post(
            rep = bankAccountToCreate
          ),
        )
      }.isHighbeamException(ForbiddenException())
    }

  private fun verifyAccountRepCreated(depositAccountCreationRep: DepositAccountRep.Creator) {
    coVerify { get<UnitCoClient>().depositAccount.create(depositAccountCreationRep) }
  }
}
