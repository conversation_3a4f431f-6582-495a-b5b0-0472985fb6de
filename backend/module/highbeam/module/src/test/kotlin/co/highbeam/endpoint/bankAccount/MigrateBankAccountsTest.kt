package co.highbeam.endpoint.bankAccount

import co.highbeam.api.bankAccount.BankAccountApi
import co.highbeam.api.bankAccount.BankAccountSettingApi
import co.highbeam.capital.account.CapitalAccountClient
import co.highbeam.capital.account.FakeCapitalAccountClient
import co.highbeam.capital.account.rep.CapitalAccountControlsRep
import co.highbeam.capital.account.rep.CapitalAccountDetailsRep
import co.highbeam.capital.account.rep.CapitalAccountRep
import co.highbeam.capital.account.rep.CapitalLender
import co.highbeam.capital.repayment.rep.CapitalRepaymentOption
import co.highbeam.exception.bankAccount.PrimaryBankAccountAlreadyExists
import co.highbeam.model.bankAccount.BankAccountDataModel
import co.highbeam.model.bankAccount.BankAccountModel
import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.bankAccount.BankAccountRep
import co.highbeam.rep.bankAccountSetting.BankAccountSettingRep
import co.highbeam.server.Server
import co.highbeam.service.bankAccount.BankAccountService
import co.highbeam.testing.BankAccountIntegrationTest
import co.highbeam.testing.integration.isHighbeamException
import co.unit.rep.DepositAccountRep
import com.slack.client.SlackMessageClient
import io.mockk.coVerify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.UUID

internal class MigrateBankAccountsTest(
  server: Server<*>,
) : BankAccountIntegrationTest(server) {
  private val businessGuid = UUID.randomUUID()

  @BeforeEach
  fun setup() {
    mockBusiness(businessGuid)
    mockGetLineOfCredit(businessGuid)
  }

  @Test
  fun `primary bank is already on Thread`() = integrationTest {
    val bankAccount = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVipThread,
      isPrimary = true,
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount.guid,
      depositProduct = DepositAccountRep.DepositProduct.CheckingVipThread
    )

    assertHighbeamException {
      bankAccountClient.request(
        BankAccountApi.MigrateToThread(
          businessGuid = businessGuid
        )
      )
    }.isHighbeamException(PrimaryBankAccountAlreadyExists())
  }

  @Test
  fun `only copies over primary and high yield accounts`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = "Thread",
        businessGuid = businessGuid,
        copyAllAccounts = false,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name + " (Thread)", DepositAccountRep.DepositProduct.CheckingVipThread, false
      ),
      Triple(
        bankAccount2.name + " (Thread)", DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
    )
  }

  @Test
  fun `copies over all accounts to Thread`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = "Thread",
        businessGuid = businessGuid,
        copyAllAccounts = true,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name + " (Thread)", DepositAccountRep.DepositProduct.CheckingVipThread, false
      ),
      Triple(
        bankAccount2.name + " (Thread)", DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
      Triple(
        bankAccount3.name + " (Thread)", DepositAccountRep.DepositProduct.CheckingThread, false
      ),
    )
  }

  @Test
  fun `copies over all accounts to Thread - no postfix to new account names`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = null,
        businessGuid = businessGuid,
        copyAllAccounts = true,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, false
      ),
      Triple(
        bankAccount2.name, DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
      Triple(
        bankAccount3.name, DepositAccountRep.DepositProduct.CheckingThread, false
      ),
    )
  }

  @Test
  fun `changes primary accounts - when forceSwitchPrimary is passed`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = null,
        businessGuid = businessGuid,
        forceSwitchPrimary = true,
        copyAllAccounts = true,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, false),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, true
      ),
      Triple(
        bankAccount2.name, DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
      Triple(
        bankAccount3.name, DepositAccountRep.DepositProduct.CheckingThread, false
      ),
    )
  }

  @Test
  fun `changes primary accounts - when LOC does not exist`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    (get<CapitalAccountClient>() as FakeCapitalAccountClient).reset()

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = null,
        businessGuid = businessGuid,
        forceSwitchPrimary = false,
        copyAllAccounts = true,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, false),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, true
      ),
      Triple(
        bankAccount2.name, DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
      Triple(
        bankAccount3.name, DepositAccountRep.DepositProduct.CheckingThread, false
      ),
    )
  }

  @Test
  fun `does not change primary accounts - when LOC exists`() = integrationTest {
    val bankAccount1 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
      isPrimary = true,
    )
    val bankAccount2 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.HighYield,
      isPrimary = false,
      type = BankAccountRep.Type.HighYield,
      name = "High yieldy",
    )
    val bankAccount3 = createBankAccount(
      depositProduct = DepositAccountRep.DepositProduct.Checking,
      isPrimary = false,
      name = "Not primary account",
    )
    mockUnitCoAccountGet(
      businessGuid = businessGuid,
      bankAccountGuid = bankAccount1.guid,
      depositProduct = bankAccount1.depositProduct,
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

    mockGetLineOfCredit(businessGuid)

    bankAccountClient.request(
      BankAccountApi.MigrateToThread(
        accountNamePostfix = null,
        businessGuid = businessGuid,
        forceSwitchPrimary = false,
        copyAllAccounts = true,
      )
    )

    assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
      Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
    }).containsExactlyInAnyOrder(
      Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
      Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
      Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
      Triple(
        bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, false
      ),
      Triple(
        bankAccount2.name, DepositAccountRep.DepositProduct.HighYieldThread, false
      ),
      Triple(
        bankAccount3.name, DepositAccountRep.DepositProduct.CheckingThread, false
      ),
    )
  }

  @Test
  fun `copies over all accounts to Thread - does not copy over old LOC accounts`() =
    integrationTest {
      val bankAccount1 = createBankAccount(
        depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
        isPrimary = true,
      )
      val bankAccount2 = createBankAccount(
        depositProduct = DepositAccountRep.DepositProduct.HighYield,
        isPrimary = false,
        type = BankAccountRep.Type.HighYield,
        name = "High yieldy",
      )
      val bankAccount3 = createBankAccount(
        depositProduct = DepositAccountRep.DepositProduct.Checking,
        isPrimary = false,
        type = BankAccountRep.Type.LineOfCredit,
        name = "old line of credity",
      )
      mockUnitCoAccountGet(
        businessGuid = businessGuid,
        bankAccountGuid = bankAccount1.guid,
        depositProduct = bankAccount1.depositProduct,
      )

      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(3)

      bankAccountClient.request(
        BankAccountApi.MigrateToThread(
          accountNamePostfix = null,
          businessGuid = businessGuid,
        )
      )

      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
        Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
      }).containsExactlyInAnyOrder(
        Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
        Triple(bankAccount2.name, DepositAccountRep.DepositProduct.HighYield, false),
        Triple(bankAccount3.name, DepositAccountRep.DepositProduct.Checking, false),
        Triple(
          bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, false
        ),
        Triple(
          bankAccount2.name, DepositAccountRep.DepositProduct.HighYieldThread, false
        ),
      )
    }

  @Test
  fun `copies over all accounts to Thread - ensures a HighYield Thread account exists`() =
    integrationTest {
      val bankAccount1 = createBankAccount(
        depositProduct = DepositAccountRep.DepositProduct.CheckingVip,
        isPrimary = true,
      )
      mockUnitCoAccountGet(
        businessGuid = businessGuid,
        bankAccountGuid = bankAccount1.guid,
        depositProduct = bankAccount1.depositProduct,
      )

      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(1)

      bankAccountClient.request(
        BankAccountApi.MigrateToThread(
          accountNamePostfix = null,
          businessGuid = businessGuid,
        )
      )

      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
        Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
      }).containsExactlyInAnyOrder(
        Triple(bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVip, true),
        Triple(
          bankAccount1.name, DepositAccountRep.DepositProduct.CheckingVipThread, false
        ),
        Triple(
          "High yield", DepositAccountRep.DepositProduct.HighYieldThread, false
        ),
      )
    }

  @Test
  fun `creates default primary and high yield if customer has no accounts`() =
    integrationTest {
      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid))).hasSize(0)

      bankAccountClient.request(
        BankAccountApi.MigrateToThread(
          businessGuid = businessGuid,
        )
      )

      assertThat(get<BankAccountService>().getAllByBusinessGuids(listOf(businessGuid)).map {
        Triple(it.name, it.unitCoDepositProduct, it.isPrimary)
      }).containsExactlyInAnyOrder(
        Triple(
          "Primary", DepositAccountRep.DepositProduct.CheckingThread, true
        ),
        Triple(
          "High yield", DepositAccountRep.DepositProduct.HighYieldThread, false
        ),
      )

      assertBankingTeamMessageSent()
    }

  @Test
  fun `updates partner bank in bank account setting to Thread`() =
    integrationTest {
      bankAccountSettingClient.request(
        BankAccountSettingApi.Create(
          BankAccountSettingRep.Create(
            businessGuid = businessGuid,
            bankingPartner = DepositAccountRep.PartnerBank.BlueRidge,
            bankingLimitTier = BankAccountSettingRep.BankingLimitTier.Standard,
          )
        )
      )

      assertThat(
        checkNotNull(
          bankAccountSettingClient.request(
            BankAccountSettingApi.Get(
              businessGuid = businessGuid,
            )
          )
        ).bankingPartner
      ).isEqualTo(DepositAccountRep.PartnerBank.BlueRidge)

      bankAccountClient.request(
        BankAccountApi.MigrateToThread(
          businessGuid = businessGuid,
        )
      )

      assertThat(
        checkNotNull(
          bankAccountSettingClient.request(
            BankAccountSettingApi.Get(
              businessGuid = businessGuid,
            )
          )
        ).bankingPartner
      ).isEqualTo(DepositAccountRep.PartnerBank.Thread)
    }

  private suspend fun createBankAccount(
    depositProduct: DepositAccountRep.DepositProduct,
    isPrimary: Boolean = false,
    name: String = "Primary",
    type: BankAccountRep.Type = BankAccountRep.Type.DepositAccount,
  ): BankAccountRep.Complete {
    val bankAccount = get<BankAccountService>().create(
      BankAccountModel.Creator(
        guid = UUID.randomUUID(),
        businessGuid = businessGuid,
        name = name,
        isPrimary = isPrimary,
        type = type,
        unitCoDepositProduct = depositProduct,
        data = BankAccountDataModel(minimumRequiredBalance = Balance.ZERO),
      )
    )

    return BankAccountRep.Complete(
      guid = bankAccount.guid,
      unitCoDepositAccountId = bankAccount.unitCoDepositAccountId,
      businessGuid = bankAccount.businessGuid,
      name = bankAccount.name,
      status = bankAccount.status,
      isPrimary = bankAccount.isPrimary,
      availableBalance = Balance.ZERO,
      routingNumber = "this is a routing number",
      accountNumber = "this is an account number",
      type = "depositAccount",
      highbeamType = bankAccount.type,
      depositProduct = bankAccount.unitCoDepositProduct,
      minimumRequiredBalance = Balance.ZERO,
    )
  }

  private fun mockGetLineOfCredit(
    businessGuid: UUID,
  ) {
    (get<CapitalAccountClient>() as FakeCapitalAccountClient).add(
      lineOfCreditFixture().copy(
        businessGuid = businessGuid
      )
    )
  }

  private fun lineOfCreditFixture() = CapitalAccountRep(
    name = "LOC",
    type = CapitalAccountRep.Type.CashAccessOnly,
    guid = UUID.randomUUID(),
    businessGuid = UUID.randomUUID(),
    details = CapitalAccountDetailsRep(
      limit = Money(0),
      apr = BigDecimal.ZERO,
      repayment = CapitalAccountDetailsRep.Repayment(
        option = CapitalRepaymentOption.PayoutPercentage(BigDecimal.ZERO),
        bankAccountGuid = null,
      ),
      lineType = CapitalAccountDetailsRep.LineType.Revolving,
      targetRepaymentDays = 120,
      securedStatus = CapitalAccountDetailsRep.SecuredStatus.Unsecured,
    ),
    state = CapitalAccountRep.State.Active,
    controls = CapitalAccountControlsRep(
      drawdownEnabled = true,
    ),
    activatedAt = ZonedDateTime.now(clock),
    lender = CapitalLender.Highbeam,
    terminatedAt = null,
  )

  private fun assertBankingTeamMessageSent() {
    coVerify {
      get<SlackMessageClient>().sendMessage(
        key = null,
        webhookPath = "someSlackWebhookPath",
        body = mapOf(
          "businessGuid" to "$businessGuid",
          "subject" to "Thread migration completed",
          "message" to "Thread migration completed for businessGuid=$businessGuid. Please" +
            "ensure their primary account is set correctly."
        )
      )
    }
  }
}
