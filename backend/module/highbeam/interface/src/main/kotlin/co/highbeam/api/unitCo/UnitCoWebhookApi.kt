package co.highbeam.api.unitCo

import co.highbeam.rep.unitCo.UnitCoEventWrapperRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod
import java.util.UUID

object UnitCoWebhookApi {
  data class Post(val wrapper: UnitCoEventWrapperRep) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/unit-co-webhook",
    body = wrapper,
  )

  data class Replay(val webhookGuid: UUID) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/unit-co-webhook/$webhookGuid/replay",
  )
}
