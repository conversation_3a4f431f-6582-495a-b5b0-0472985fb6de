package co.highbeam.model.unitCoEvent

import co.highbeam.serialization.readValueNotNull
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.ZonedDateTime

@JsonDeserialize(using = UnitCoIgnoredEventModel.Deserializer::class)
data class UnitCoIgnoredEventModel(
  override val eventId: String,
  override val type: String,
  override val createdAt: ZonedDateTime,
) : UnitCoEventModel() {
  internal class Deserializer : StdDeserializer<UnitCoIgnoredEventModel>(
    UnitCoIgnoredEventModel::class.java,
  ) {
    override fun deserialize(
      p: Json<PERSON>ars<PERSON>,
      ctxt: DeserializationContext,
    ): UnitCoIgnoredEventModel {
      val tree = p.readValueAsTree<JsonNode>()
      val attributes = tree.get("attributes")

      return UnitCoIgnoredEventModel(
        eventId = tree.readValueNotNull(ctxt, "id"),
        type = tree.readValueNotNull(ctxt, "type"),
        createdAt = attributes.readValueNotNull(ctxt, "createdAt"),
      )
    }
  }
}
