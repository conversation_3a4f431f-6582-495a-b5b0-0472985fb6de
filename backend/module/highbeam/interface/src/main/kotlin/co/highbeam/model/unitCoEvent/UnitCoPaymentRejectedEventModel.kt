package co.highbeam.model.unitCoEvent

import co.highbeam.money.Money
import co.highbeam.serialization.readValue
import co.highbeam.serialization.readValueNotNull
import co.unit.rep.UnitCoPayment
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.ZonedDateTime
import java.util.UUID

@JsonDeserialize(using = UnitCoPaymentRejectedEventModel.Deserializer::class)
data class UnitCoPaymentRejectedEventModel(
  override val eventId: String,
  override val type: String,
  override val createdAt: ZonedDateTime,
  val accountId: String,
  val paymentId: String,
  val recurringPaymentId: String? = null,
  val payeeGuid: UUID?,
  val amount: Money,
  val reason: UnitCoPayment.Reason,
  val paymentType: String,
) : UnitCoEventModel() {
  companion object {
    const val TYPE = "payment.rejected"
  }

  internal class Deserializer : StdDeserializer<UnitCoPaymentRejectedEventModel>(
    UnitCoPaymentRejectedEventModel::class.java,
  ) {
    override fun deserialize(
      p: JsonParser,
      ctxt: DeserializationContext,
    ): UnitCoPaymentRejectedEventModel {
      val tree = p.readValueAsTree<JsonNode>()
      val attributes = tree.get("attributes")
      val relationships = tree.get("relationships")
      val tags = attributes.get("tags")

      val account = relationships.get("account").get("data")
      val payment = relationships.get("payment").get("data")
      val recurringPayment = relationships.get("recurringPayment")?.get("data")

      return UnitCoPaymentRejectedEventModel(
        eventId = tree.readValueNotNull(ctxt, "id"),
        type = TYPE,
        createdAt = attributes.readValueNotNull(ctxt, "createdAt"),
        accountId = account.readValueNotNull(ctxt, "id"),
        paymentId = payment.readValueNotNull(ctxt, "id"),
        recurringPaymentId = recurringPayment?.readValue(ctxt, "id"),
        payeeGuid = tags.readValue(ctxt, "recipientGuid"),
        amount = attributes.readValueNotNull(ctxt, "amount"),
        reason = attributes.readValueNotNull(ctxt, "reason"),
        paymentType = when (recurringPayment) {
          null -> "Default"
          else -> "Recurring"
        }
      )
    }
  }
}
