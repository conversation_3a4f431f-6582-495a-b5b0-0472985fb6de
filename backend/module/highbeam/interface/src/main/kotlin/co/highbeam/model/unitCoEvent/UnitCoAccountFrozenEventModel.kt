package co.highbeam.model.unitCoEvent

import co.highbeam.serialization.readValueNotNull
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.ZonedDateTime
import java.util.UUID

@JsonDeserialize(using = UnitCoAccountFrozenEventModel.Deserializer::class)
data class UnitCoAccountFrozenEventModel(
  override val eventId: String,
  override val type: String,
  override val createdAt: ZonedDateTime,
  val accountId: String,
  val businessGuid: UUID,
) : UnitCoEventModel() {
  companion object {
    const val TYPE = "account.frozen"
  }

  internal class Deserializer : StdDeserializer<UnitCoAccountFrozenEventModel>(
    UnitCoAccountFrozenEventModel::class.java,
  ) {
    override fun deserialize(
      p: JsonParser,
      ctxt: DeserializationContext,
    ): UnitCoAccountFrozenEventModel {
      val tree = p.readValueAsTree<JsonNode>()
      val attributes = tree.get("attributes")
      val tags = attributes.get("tags")
      val relationships = tree.get("relationships")
      val account = relationships.get("account").get("data")

      return UnitCoAccountFrozenEventModel(
        eventId = tree.readValueNotNull(ctxt, "id"),
        type = TYPE,
        createdAt = attributes.readValueNotNull(ctxt, "createdAt"),
        accountId = account.readValueNotNull(ctxt, "id"),
        businessGuid = tags.readValueNotNull(ctxt, "businessGuid"),
      )
    }
  }
}
