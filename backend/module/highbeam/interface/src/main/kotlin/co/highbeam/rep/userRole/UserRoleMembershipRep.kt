package co.highbeam.rep.userRole

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.util.UUID

data class UserRoleMembershipRep(
  val businessGuid: UUID,
  val userGuid: UUID,
  val userRoleGuid: UUID,
) : CompleteRep {
  data class Creator(
    val userRoleGuid: UUID,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }
}
