package co.highbeam.rep.bankAccount

import co.highbeam.money.Balance
import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.rep.UpdaterRep
import co.highbeam.serialization.readValueNotNull
import co.highbeam.validation.RepValidation
import co.highbeam.validation.Validator
import co.highbeam.validation.ifPresent
import co.unit.rep.DepositAccountRep
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import java.util.UUID

object BankAccountRep {
  enum class Status {
    OPEN, CLOSED, FROZEN;

    fun toDepositAccountRepStatus(): DepositAccountRep.Status {
      return when (this) {
        OPEN -> DepositAccountRep.Status.Open
        CLOSED -> DepositAccountRep.Status.Closed
        FROZEN -> DepositAccountRep.Status.Frozen
      }
    }

    fun equalTo(unitStatus: DepositAccountRep.Status): Boolean {
      return when (this) {
        OPEN -> unitStatus == DepositAccountRep.Status.Open
        CLOSED -> unitStatus == DepositAccountRep.Status.Closed
        FROZEN -> unitStatus == DepositAccountRep.Status.Frozen
      }
    }
  }

  fun DepositAccountRep.Status.toBankAccountRepStatus(): Status {
    return when (this) {
      DepositAccountRep.Status.Open -> Status.OPEN
      DepositAccountRep.Status.Closed -> Status.CLOSED
      DepositAccountRep.Status.Frozen -> Status.FROZEN
    }
  }

  fun DepositAccountRep.DepositProduct.toBankAccountRepType(): Type {
    return when (this) {
      DepositAccountRep.DepositProduct.Checking -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingHighPOS -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingRegularThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipThreadChecks -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip2Thread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip3Thread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip4Thread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.ChargeCardOperational -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipLowThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip2LowThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip3LowThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip4LowThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipMediumThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip2MediumThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip3MediumThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVip4MediumThread -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipThreadAtm -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipLowThreadAtm -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipMediumThreadAtm -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipInterestThreadAtm -> Type.DepositAccount
      DepositAccountRep.DepositProduct.CheckingVipInterestMaxThreadAtm -> Type.DepositAccount

      DepositAccountRep.DepositProduct.HighYield -> Type.HighYield
      DepositAccountRep.DepositProduct.SavingsHigh -> Type.HighYield
      DepositAccountRep.DepositProduct.HighYieldThread -> Type.HighYield
      DepositAccountRep.DepositProduct.ThreadSavingsHigh -> Type.HighYield
      DepositAccountRep.DepositProduct.HighYieldVipThread -> Type.HighYield
      DepositAccountRep.DepositProduct.HighYieldVipMaxThread -> Type.HighYield
      DepositAccountRep.DepositProduct.HighYieldVipMax2Thread -> Type.HighYield
    }
  }

  @JsonSerialize(using = Type.Serializer::class)
  @JsonDeserialize(using = Type.Deserializer::class)
  enum class Type(
    val includeInTotalBalance: Boolean,
    val includeInTransferMoney: Boolean,
    val includeInSendMoney: Boolean,
    val showDetailsToUser: Boolean,
    val supportsDebitCards: Boolean,
    val supportsExternalTransfers: Boolean,
    val supportedUnitCoDepositProducts: List<DepositAccountRep.DepositProduct>,
  ) {
    LockedDepositAccount(
      includeInTotalBalance = true,
      includeInTransferMoney = false,
      includeInSendMoney = false,
      showDetailsToUser = true,
      supportsDebitCards = false,
      supportsExternalTransfers = false,
      supportedUnitCoDepositProducts = listOf(
        DepositAccountRep.DepositProduct.Checking,
        DepositAccountRep.DepositProduct.CheckingVip,
        DepositAccountRep.DepositProduct.CheckingHighPOS,

        DepositAccountRep.DepositProduct.CheckingThread,
        DepositAccountRep.DepositProduct.CheckingRegularThread,
        DepositAccountRep.DepositProduct.CheckingVipThread,
        DepositAccountRep.DepositProduct.CheckingVip2Thread,
        DepositAccountRep.DepositProduct.CheckingVipLowThread,
        DepositAccountRep.DepositProduct.CheckingVip2LowThread,
        DepositAccountRep.DepositProduct.CheckingVipMediumThread,
        DepositAccountRep.DepositProduct.CheckingVip2MediumThread,

        DepositAccountRep.DepositProduct.CheckingVipThreadAtm,

        // Deprecated
        DepositAccountRep.DepositProduct.CheckingVipLowThreadAtm,
        DepositAccountRep.DepositProduct.CheckingVipMediumThreadAtm,
      ),
    ),
    DepositAccount(
      includeInTotalBalance = true,
      includeInTransferMoney = true,
      includeInSendMoney = true,
      showDetailsToUser = true,
      supportsDebitCards = true,
      supportsExternalTransfers = true,
      supportedUnitCoDepositProducts = listOf(
        DepositAccountRep.DepositProduct.Checking,
        DepositAccountRep.DepositProduct.CheckingVip,
        DepositAccountRep.DepositProduct.CheckingHighPOS,

        DepositAccountRep.DepositProduct.CheckingThread,
        DepositAccountRep.DepositProduct.CheckingRegularThread,
        DepositAccountRep.DepositProduct.CheckingVipThread,
        DepositAccountRep.DepositProduct.CheckingVip2Thread,
        DepositAccountRep.DepositProduct.CheckingVipLowThread,
        DepositAccountRep.DepositProduct.CheckingVip2LowThread,
        DepositAccountRep.DepositProduct.CheckingVipMediumThread,
        DepositAccountRep.DepositProduct.CheckingVip2MediumThread,

        DepositAccountRep.DepositProduct.CheckingVipThreadAtm,

        // Deprecated
        DepositAccountRep.DepositProduct.CheckingVipLowThreadAtm,
        DepositAccountRep.DepositProduct.CheckingVipMediumThreadAtm,
      ),
    ),
    HighYield(
      includeInTotalBalance = true,
      includeInTransferMoney = true,
      includeInSendMoney = false,
      showDetailsToUser = false,
      supportsDebitCards = false,
      supportsExternalTransfers = false,
      supportedUnitCoDepositProducts = listOf(
        DepositAccountRep.DepositProduct.HighYield,
        DepositAccountRep.DepositProduct.SavingsHigh,

        DepositAccountRep.DepositProduct.HighYieldThread,
        DepositAccountRep.DepositProduct.ThreadSavingsHigh,
        DepositAccountRep.DepositProduct.HighYieldVipThread,
        DepositAccountRep.DepositProduct.HighYieldVipMaxThread,
        DepositAccountRep.DepositProduct.HighYieldVipMax2Thread,

        // Deprecated
        DepositAccountRep.DepositProduct.CheckingVipInterestThreadAtm,
        DepositAccountRep.DepositProduct.CheckingVipInterestMaxThreadAtm,
      ),
    ),
    LineOfCredit(
      includeInTotalBalance = false,
      // TODO: Check with capital team if this should be true
      includeInTransferMoney = true,
      includeInSendMoney = false,
      showDetailsToUser = false,
      supportsDebitCards = false,
      supportsExternalTransfers = false,
      supportedUnitCoDepositProducts = listOf(
        DepositAccountRep.DepositProduct.Checking,
        DepositAccountRep.DepositProduct.CheckingVip,

        DepositAccountRep.DepositProduct.CheckingThread,
        DepositAccountRep.DepositProduct.CheckingVipThread,
      ),
    );

    internal class Serializer : StdSerializer<Type>(Type::class.java) {
      override fun serialize(value: Type, gen: JsonGenerator, provider: SerializerProvider) {
        gen.writeObject(
          mapOf(
            "name" to value.name,
            "includeInTotalBalance" to value.includeInTotalBalance,
            "includeInTransferMoney" to value.includeInTransferMoney,
            "includeInSendMoney" to value.includeInSendMoney,
            "showDetailsToUser" to value.showDetailsToUser,
            "supportsDebitCards" to value.supportsDebitCards,
            "supportsExternalTransfers" to value.supportsExternalTransfers,
          )
        )
      }
    }

    internal class Deserializer : StdDeserializer<Type>(Type::class.java) {
      override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Type =
        valueOf(p.readValueAsTree<JsonNode>().readValueNotNull(ctxt, "name"))
    }
  }

  data class Creator(
    val businessGuid: UUID,
    val name: String,
    val minimumRequiredBalance: Balance = Balance.ZERO,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Creator::name) { Validator.bankAccountName(this) }
    }
  }

  data class CreatorInternal(
    val businessGuid: UUID,
    val isPrimary: Boolean,
    val name: String,
    val highbeamType: Type,
    val unitCoDepositProduct: DepositAccountRep.DepositProduct,
    val minimumRequiredBalance: Balance = Balance.ZERO,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation {
      validate(CreatorInternal::name) { Validator.bankAccountName(this) }
    }
  }

  data class Complete(
    val guid: UUID,
    val unitCoDepositAccountId: String,
    val businessGuid: UUID,
    val unitCoCounterpartyId: String? = null,
    val name: String,
    val status: Status,
    val isPrimary: Boolean,
    val availableBalance: Balance,
    val routingNumber: String,
    val accountNumber: String,
    val type: String,
    val highbeamType: Type,
    // TODO: Remove this for any responses to the user facing client
    val depositProduct: DepositAccountRep.DepositProduct,
    val minimumRequiredBalance: Balance,
  ) : CompleteRep {
    val isDepositAccount: Boolean get() = highbeamType in setOf(Type.DepositAccount, Type.HighYield)
    val isLineOfCreditAccount: Boolean get() = highbeamType == Type.LineOfCredit

    val isThreadAccount: Boolean get() = depositProduct.bank == DepositAccountRep.PartnerBank.Thread

    val isBlueRidgeAccount: Boolean
      get() =
        depositProduct.bank == DepositAccountRep.PartnerBank.BlueRidge

    val nameWithMask: String = "$name •••• ${accountNumber.takeLast(4)}"
  }

  /*
   * A trimmed down version of the Complete Rep. Contains no sensitive information.
   * Efforts to make sure we are not sending PII data in API when not necessary.
   */
  data class Summary(
    val guid: UUID,
    val unitCoDepositAccountId: String,
    val unitCoDepositProduct: DepositAccountRep.DepositProduct,
    val businessGuid: UUID,
    val name: String,
    val status: Status,
    val isPrimary: Boolean,
    val highbeamType: Type,
  ) : CompleteRep {
    val isDepositAccount: Boolean get() = highbeamType in setOf(Type.DepositAccount, Type.HighYield)
    val isLineOfCreditAccount: Boolean get() = highbeamType == Type.LineOfCredit

    val isThreadAccount: Boolean
      get() =
        unitCoDepositProduct.bank == DepositAccountRep.PartnerBank.Thread

    val isBlueRidgeAccount: Boolean
      get() =
        unitCoDepositProduct.bank == DepositAccountRep.PartnerBank.BlueRidge
  }

  data class Updater(
    val name: String? = null,
    val isPrimary: Boolean? = null,
    val unitCoCounterpartyId: String? = null,
    val minimumRequiredBalance: Balance? = null,
  ) : UpdaterRep {
    override fun validate(): RepValidation = RepValidation {
      validate(Updater::name) { ifPresent { Validator.bankAccountName(this) } }
      validate(Updater::isPrimary) { ifPresent { this } }
    }
  }
}
