package co.highbeam.exception.plaid

import co.highbeam.exception.InternalServerErrorException
import com.plaid.client.model.ItemGetResponse
import retrofit2.Response

class PlaidItemGetError constructor(response: Response<ItemGetResponse>) :
  InternalServerErrorException(
    "Failed to retrieve Plaid Item with" +
      " status: ${response.code()}" +
      " and error ${response.errorBody()?.string()}",
  )
