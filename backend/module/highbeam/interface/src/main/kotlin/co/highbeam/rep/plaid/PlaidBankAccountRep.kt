package co.highbeam.rep.plaid

import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.rep.CompleteRep
import com.plaid.client.model.AccountSubtype
import com.plaid.client.model.AccountType
import java.util.UUID

/**
 * An account connected through Plaid.
 *
 * Despite the name, this can actually refer to any kind of Plaid account, not just
 * depository ("bank") accounts. Other examples include credit cards, money market accounts,
 * investment accounts, and loans. The name is kept the same since that is the name in the
 * database.
 */
object PlaidBankAccountRep {
  data class Complete(
    val businessGuid: UUID,
    val connectionGuid: UUID,
    val plaidAccountId: String,
    val accountOwnerFullName: String?,
    /**
     * from plaid's docs:
     *
     * > For credit-type accounts, a positive balance indicates the amount owed;
     * > a negative amount indicates the lender owing the account holder.
     * >
     * > For loan-type accounts, the current balance is the principal remaining on the loan ...
     * >
     * > For investment-type accounts the current balance is the total value of assets as
     * > presented by the institution.
     */
    val currentBalance: Balance?,

    /**
     * Note that this field should generally not be used for credit accounts. From Plaid's docs:
     *
     * > The amount of funds available to be withdrawn from the account,
     * > as determined by the financial institution.
     * >
     * > For credit-type accounts, the available balance typically equals the limit less
     * > the current balance, less any pending outflows plus any pending inflows.
     * >
     * > For depository-type accounts, the available balance typically equals the current
     * > balance less any pending outflows plus any pending inflows. For depository-type accounts,
     * > the available balance does not include the overdraft limit.
     * >
     * > For investment-type accounts, the available balance is the total cash available to
     * > withdraw as presented by the institution.
     * >
     * > Note that not all institutions calculate the available balance.
     * > In the event that available balance is unavailable, Plaid will return an available balance
     * > value of null.
     */
    val availableBalance: Balance?,

    /**
     * For credit accounts, this value may indicate the credit limit.
     */
    val creditLimit: Money?,
    val currency: String?,
    val accountName: String?,
    val accountOfficialName: String? = null,
    val subtype: AccountSubtype?,
    val accountMask: String?,
    val accountType: AccountType?,
    val plaidProcessorToken: String?,
    val institutionId: String?,
    val institutionName: String?,
    /**
     * Whether the account is still attached to the parent connection.
     *
     * This field acts as a sort of soft deletion flag. When an account is
     * removed from a connection through Link update or directly in a bank's
     * portal (for Chase only as of 10/2024), we set this value to false.
     * For data preservation purposes (for Capital monitoring in particular),
     * we don't hard delete accounts, but instead set them inactive this way
     * and encourage / require the user to reconnect them.
     *
     * This value can be true even when the parent connection is inactive.
     */
    val isActive: Boolean,
    /**
     * Whether the parent connection is healthy.
     */
    val isConnectionActive: Boolean,
    val achRoutingNumber: String?,
    val achAccountNumber: String?,
    val achWireRoutingNumber: String?,
  ) : CompleteRep {
    val isDepositAccount: Boolean get() = this.accountType == AccountType.DEPOSITORY
  }
}
