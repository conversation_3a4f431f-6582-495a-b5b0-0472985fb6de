package co.highbeam.rep.unitCo

import co.highbeam.money.Balance
import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import java.time.ZonedDateTime

data class UnitCoTransactionRep(
  val accountId: String,
  val accountName: String,
  val unitAccountType: String,
  val date: ZonedDateTime,
  val direction: MoneyDirection,
  val amount: Money,
  val balance: Balance,
  val id: String,
  val payeeName: String,
  val description: String,
  val transactionType: String,
)
