package co.highbeam.rep.plaid

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation
import java.util.UUID

object PlaidAccessTokenRep {
  data class Creator(
    val businessGuid: UUID,
    val publicToken: String,
    val metadata: PlaidLinkOnSuccessMetadata,
    val isNewConnection: Boolean,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  data class Complete(
    val itemId: String,
  ) : CompleteRep
}
