package co.highbeam.api.backendV2Email

import co.highbeam.rep.backendV2Email.ExampleApEmailRep
import co.highbeam.rep.backendV2Email.GenericEmailRep
import co.highbeam.restInterface.Endpoint
import io.ktor.http.HttpMethod

object BackendV2EmailApi {
  data class ExampleApEmail(
    val rep: ExampleApEmailRep,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/backend-v2-email/example-ap-email",
    body = rep
  )

  data class Email(
    val rep: GenericEmailRep,
  ) : Endpoint(
    httpMethod = HttpMethod.Post,
    path = "/backend-v2-email/email",
    body = rep
  )
}
