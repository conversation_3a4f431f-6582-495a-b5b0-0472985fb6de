package co.highbeam.email.template.backendV2Email

import co.highbeam.email.template.EmailTemplateButtonSection
import co.highbeam.email.template.EmailTemplateFooterSection
import co.highbeam.email.template.EmailTemplateHeaderSection
import co.highbeam.email.template.EmailTemplateSeparatorSection
import co.highbeam.email.template.EmailTemplateTableSection
import co.highbeam.email.template.EmailTemplateTextSection
import co.highbeam.email.template.EmailTemplateTitleSection
import co.highbeam.rep.backendV2Email.GenericEmailRep
import java.time.format.DateTimeFormatter
import java.util.Locale

class BillApprovalRequestedEmailTemplate(
  rep: GenericEmailRep,
  params: GenericEmailRep.GenericEmailParamsRep.BillApprovalRequested,
) : BackendV2EmailTemplate<GenericEmailRep>(rep) {

  override val subject: String = "${params.requesterName} requested your approval on a bill"
  val description =
    "${params.requesterName} has requested your approval on a bill " +
      "from ${params.billDetails.payeeName}."
  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateTitleSection(
      title = subject,
      subtitle = description,
    ),
    EmailTemplateSeparatorSection,
    EmailTemplateButtonSection(label = "Review bill", href = params.billLink),
    EmailTemplateTextSection(
      values = listOf(
        EmailTemplateTextSection.Value(
          value = "Bill details",
          fontWeight = EmailTemplateTextSection.Value.FontWeight.Bold,
        )
      ),
      dense = true,
      textAlign = EmailTemplateTextSection.TextAlign.Left,
    ),
    EmailTemplateTableSection(buildList {
      add(
        EmailTemplateTableSection.Row(
          "Due",
          params.billDetails.invoiceDueDate.format(
            DateTimeFormatter.ofPattern("MMM d, y", Locale.US)
          )
        )
      )
      add(EmailTemplateTableSection.Row("To", params.billDetails.payeeName))
      add(EmailTemplateTableSection.Row("Amount", params.billDetails.amountWithCurrency))
      add(EmailTemplateTableSection.Row("Invoice number", params.billDetails.invoiceNumber))
    }),
    EmailTemplateFooterSection(),
  )

  companion object {
    const val TEMPLATE_NAME = "BillApprovalRequestedEmailTemplate"
  }
}
