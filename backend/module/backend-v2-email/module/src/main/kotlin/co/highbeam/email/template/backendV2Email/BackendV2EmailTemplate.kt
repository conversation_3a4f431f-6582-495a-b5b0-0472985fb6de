package co.highbeam.email.template.backendV2Email

import co.highbeam.email.template.EmailTemplate
import co.highbeam.rep.backendV2Email.BackendV2EmailRep

abstract class BackendV2EmailTemplate<T : BackendV2EmailRep>(rep: T) : EmailTemplate() {
  override val bccs: List<Recipient> = rep.metadata.bccs
  override val ccs: List<Recipient> = rep.metadata.ccs
  override val sender: Sender = rep.metadata.sender
  override val replyTo: Sender? = rep.metadata.replyTo
  override val recipients: List<Recipient> = rep.metadata.recipients
}
