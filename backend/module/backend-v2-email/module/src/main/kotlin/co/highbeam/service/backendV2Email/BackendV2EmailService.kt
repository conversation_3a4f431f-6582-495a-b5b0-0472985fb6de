package co.highbeam.service.backendV2Email

import co.highbeam.email.EmailService
import co.highbeam.email.template.EmailTemplate
import co.highbeam.email.template.backendV2Email.BillApprovalRequestedEmailTemplate
import co.highbeam.email.template.backendV2Email.BillCommentAddedEmailTemplate
import co.highbeam.email.template.backendV2Email.BillFullyApprovedEmailTemplate
import co.highbeam.email.template.backendV2Email.ExampleApEmailTemplate
import co.highbeam.email.template.backendV2Email.capital.CapitalShareUpdatedFinancialsEmailTemplate
import co.highbeam.email.template.backendV2Email.card.CardFraudEmailTemplate
import co.highbeam.rep.backendV2Email.ExampleApEmailRep
import co.highbeam.rep.backendV2Email.GenericEmailRep
import com.google.inject.Inject

class BackendV2EmailService @Inject constructor(
  private val emailService: EmailService,
) {
  fun sendExampleApEmail(rep: ExampleApEmailRep) {
    emailService.async { sendEmail ->
      sendEmail(ExampleApEmailTemplate(rep))
    }
  }

  fun sendEmail(rep: GenericEmailRep) {
    emailService.async { sendEmail ->
      sendEmail(buildTemplate(rep))
    }
  }

  private fun buildTemplate(email: GenericEmailRep): EmailTemplate {
    return when (email.params.template) {
      CapitalShareUpdatedFinancialsEmailTemplate.TEMPLATE_NAME ->
        CapitalShareUpdatedFinancialsEmailTemplate(
          email,
          email.params as GenericEmailRep.GenericEmailParamsRep.CapitalShareUpdatedFinancials,
        )
      CardFraudEmailTemplate.TEMPLATE_NAME -> CardFraudEmailTemplate(
        email,
        email.params as GenericEmailRep.GenericEmailParamsRep.CardFraud
      )
      BillApprovalRequestedEmailTemplate.TEMPLATE_NAME -> BillApprovalRequestedEmailTemplate(
        email,
        email.params as GenericEmailRep.GenericEmailParamsRep.BillApprovalRequested,
      )
      BillCommentAddedEmailTemplate.TEMPLATE_NAME -> BillCommentAddedEmailTemplate(
        email,
        email.params as GenericEmailRep.GenericEmailParamsRep.BillCommentAdded,
      )
      BillFullyApprovedEmailTemplate.TEMPLATE_NAME -> BillFullyApprovedEmailTemplate(
        email,
        email.params as GenericEmailRep.GenericEmailParamsRep.BillFullyApproved,
      )
      else -> error("Unknown template: ${email.params.template}")
    }
  }
}
