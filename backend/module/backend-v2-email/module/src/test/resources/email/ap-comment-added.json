[{"background": true, "type": {"header": true}}, {"iconType": "Chatbox", "type": {"icon": true}}, {"title": "<PERSON> left a comment for you on a bill", "subtitle": "", "type": {"title": true}}, {"type": {"separator": true}}, {"values": [{"value": "<PERSON>:", "linkTo": null, "fontWeight": "bold", "fontStyle": "normal", "lineBreakAfter": false}], "dense": true, "textAlign": "center", "type": {"text": true}}, {"values": [{"value": "\"Please look at this bill\"", "linkTo": null, "fontWeight": "normal", "fontStyle": "italic", "lineBreakAfter": false}], "dense": true, "textAlign": "center", "type": {"text": true}}, {"type": {"separator": true}}, {"label": "View comment", "href": "https://highbeam.co/payments/bill/bill_12345", "type": {"button": true}}, {"values": [{"value": "Bill details", "linkTo": null, "fontWeight": "bold", "fontStyle": "normal", "lineBreakAfter": false}], "dense": true, "textAlign": "left", "type": {"text": true}}, {"rows": [{"key": "Due", "value": "Jan 1, 2021", "subtext": null}, {"key": "To", "value": "Test Payee", "subtext": null}, {"key": "Amount", "value": "$100.00", "subtext": null}, {"key": "Invoice number", "value": "12345", "subtext": null}], "type": {"table": true}}, {"background": true, "type": {"footer": true}}]