package co.highbeam.email.template.backendV2Email.capital

import co.highbeam.email.template.EmailTemplate
import co.highbeam.email.template.EmailTemplateTest
import co.highbeam.rep.backendV2Email.BackendV2EmailMetadataRep
import co.highbeam.rep.backendV2Email.GenericEmailRep
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import java.time.LocalDate
import java.time.Month

class CapitalShareUpdatedFinancialsEmailTemplateTest :
  EmailTemplateTest<CapitalShareUpdatedFinancialsEmailTemplate>() {
  override val testCases = listOf(
    TestCase(
      template = CapitalShareUpdatedFinancialsEmailTemplate(
        GenericEmailRep(
          metadata = BackendV2EmailMetadataRep(
            bccs = emptyList(),
            ccs = emptyList(),
            sender = EmailTemplate.Sender(
              emailAddress = "<EMAIL>",
              name = "Test Sender",
            ),
            replyTo = null,
            recipients = listOf(
              EmailTemplate.Recipient(
                emailAddress = "<EMAIL>",
                name = "Test Recipient",
              )
            ),
          ),
          params = getParams(),
        ),
        params = getParams(),
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/capital/share-updated-financials-email.json"),
      ),
    ),
  )

  private fun getParams() =
    GenericEmailRep.GenericEmailParamsRep.CapitalShareUpdatedFinancials(
      financialsThrough = Month.FEBRUARY,
      financialsDueAt = LocalDate.of(2021, 3, 1),
      appBaseUrl = "https://app.highbeam.co",
    )
}
