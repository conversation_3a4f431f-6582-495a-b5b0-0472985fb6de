package co.highbeam.auth.jwt

import co.highbeam.auth.permissions.Acl
import co.highbeam.permissions.platform.PlatformRole
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import java.util.UUID

data class Jwt(
  @JsonProperty(JwtClaims.acl)
  val acl: Acl? = null,
  @JsonProperty(JwtClaims.bankAccounts)
  val bankAccounts: Set<UUID>? = null,
  @JsonProperty(JwtClaims.businesses)
  val businesses: Map<UUID, JwtBusiness>? = null,
  @JsonProperty(JwtClaims.mfa)
  val mfa: JwtMfa? = null,
  @JsonProperty(JwtClaims.roles)
  val roles: Set<PlatformRole> = emptySet(),
  @JsonProperty(JwtClaims.user)
  val user: JwtUser? = null,

  // OIDC fields
  val aud: String? = null,
  val exp: Int? = null,
  val iat: Int? = null,
  val iss: String? = null,
  val sub: String? = null,
  val email: String? = null,
) {
  /**
   * Whether this JWT has the required fields for an OIDC token.
   */
  @JsonIgnore
  fun isOidc(): Boolean {
    return aud != null && exp != null && iat != null && iss != null && sub != null
  }
}
