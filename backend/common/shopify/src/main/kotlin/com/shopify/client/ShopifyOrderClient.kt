package com.shopify.client

import co.highbeam.protectedString.ProtectedString
import co.highbeam.util.time.inUTC
import com.google.inject.Inject
import com.shopify.rep.OrderRep
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime

class ShopifyOrderClient @Inject constructor(
  private val httpClient: ShopifyHttpClient,
) : ShopifyClient() {
  private val logger = KotlinLogging.logger {}

  suspend fun getSince(
    shopSubdomain: String,
    accessToken: ProtectedString,
    updatedSince: ZonedDateTime,
    updatedUntil: ZonedDateTime,
  ): Flow<List<OrderRep.Complete>> {
    logger.info {
      "Getting Shopify orders for shop $shopSubdomain" +
        " updated from $updatedSince to $updatedUntil."
    }
    data class Wrapper(val orders: List<OrderRep.Complete>)
    return paginatedRequest(
      request = { pageInfo ->
        httpClient.request(
          path = "/admin/api/$ADMIN_API_VERSION/orders.json",
          qp = buildMap {
            pageInfo.let {
              if (it != null) {
                put("page_info", listOf(it))
              } else {
                put("status", listOf("any"))
                put("updated_at_min", listOf(updatedSince.inUTC().toString()))
                put("updated_at_max", listOf(updatedUntil.inUTC().toString()))
              }
            }
            put("limit", listOf(MAX_PAGINATION_LIMIT))
          },
          builder = {
            setShopSubdomain(shopSubdomain)
            setAccessToken(accessToken)
          }
        )
      },
      getResults = {
        val results = it.readValue<Wrapper?>()?.orders
        if (results == null) {
          logger.warn { "Shopify order request for $shopSubdomain gave 404." }
          emptyList()
        } else {
          results
        }
      }
    )
  }
}
