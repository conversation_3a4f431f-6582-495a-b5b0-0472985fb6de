package com.shopify.client

import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

@Suppress("MaxLineLength")
internal class LinkHeaderTest {
  /**
   * https://shopify.dev/api/usage/pagination-rest
   */
  @Test
  fun `parse shopify link header`() {
    val linkHeader = LinkHeader.parse("<https://{shop}.myshopify.com/admin/api/{version}/products.json?page_info={page_info}&limit={limit}>; rel=\"next\", <https://{shop}.myshopify.com/admin/api/{version}/products.json?page_info={page_info}&limit={limit}>; rel=\"previous\"")
    assertEquals(mapOf(
      "next" to "https://{shop}.myshopify.com/admin/api/{version}/products.json?page_info={page_info}&limit={limit}",
      "previous" to "https://{shop}.myshopify.com/admin/api/{version}/products.json?page_info={page_info}&limit={limit}",
    ), linkHeader.toMap())
  }
}
