package co.highbeam.permissions.platform

import co.highbeam.permissions.Role

enum class PlatformRole(
  override val title: String,
  override val description: String,
) : Role {
  CLOUD_TASKS(
    title = "Cloud tasks",
    description = "Used to authenticate internal requests proxied through Cloud Tasks queues.",
  ),
  HIGHBEAM_SERVER(
    title = "Highbeam server",
    description = "Intended for server-to-server communication within Highbeam.",
  ),
  IDENTITY_PROVIDER(
    title = "Identity provider",
    description = "Allows access to endpoints that identity providers require.",
  ),
  SUPERBLOCKS(
    title = "Superblocks",
    description = "Authorizes Superblocks to access certain endpoints.",
  ),
  SUPERUSER(
    title = "Superuser",
    description = "Catch-all role that overrides endpoint authentication.",
  );
}
