package co.highbeam.serialization

import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JavaType
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

inline fun <reified T : Any> JsonNode.readValue(
  ctxt: DeserializationContext,
  fieldName: String,
): T? {
  if (!hasNonNull(fieldName)) return null
  return readValueNotNull(ctxt, fieldName)
}

inline fun <reified T : Any> JsonNode.readValueNotNull(
  ctxt: DeserializationContext,
  fieldName: String,
): T =
  readValueNotNull(ctxt, fieldName, ctxt.constructType(jacksonTypeRef<T>().type))

inline fun <reified T : Any> JsonNode.readValueNotNull(
  ctxt: DeserializationContext,
  fieldName: String,
  type: JavaType,
): T =
  ctxt.readTreeAsValue(get(fieldName), type)
