package co.highbeam.auth.auth

import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.jwt.JwtBusiness
import co.highbeam.auth.permissions.AclValue
import co.highbeam.auth.permissions.Permission
import co.highbeam.permissions.platform.PlatformRole
import io.ktor.http.Headers
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class AuthPermissionTest {
  private val authPermission: AuthPermission.Provider =
    AuthPermission.Provider()

  @Test
  fun `no token`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(null, Headers.Empty))
      .isFalse
  }

  @Test
  fun `null permissions`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `permission missing`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(requiredBusinessGuid to AclValue.from(Permission.PaymentApproval_Read)),
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `permission guid mismatch`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(
        UUID.randomUUID() to AclValue.from(Permission.Business_Read),
        UUID.randomUUID() to AclValue.from(Permission.Business_Read),
        requiredBusinessGuid to AclValue.from(Permission.PaymentApproval_Read),
      ),
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `permission guid match`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(
        UUID.randomUUID() to AclValue.from(
          Permission.Business_Read,
        ),
        requiredBusinessGuid to AclValue.from(
          Permission.Business_Read,
          Permission.PaymentApproval_Read,
        ),
      ),
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(jwt, Headers.Empty))
      .isTrue
  }

  @Test
  fun `null guid`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = mapOf(
        requiredBusinessGuid to AclValue.from(
          Permission.Business_Read,
          Permission.PaymentApproval_Read,
        ),
      ),
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { null }
      .authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `superuser override`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = setOf(PlatformRole.SUPERUSER),
      user = null,
    )
    assertThat(authPermission(Permission.Business_Read) { requiredBusinessGuid }
      .authorize(jwt, Headers.Empty))
      .isTrue
  }
}
