package co.highbeam.auth.auth

import co.highbeam.auth.jwt.Jwt
import co.highbeam.auth.jwt.JwtBusiness
import co.highbeam.permissions.platform.PlatformRole
import io.ktor.http.Headers
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.UUID

internal class AuthBusinessTest {
  private val authBusiness = AuthBusiness.Provider()

  @Test
  fun `No JWT`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    assertThat(authBusiness(requiredBusinessGuid).authorize(null, Headers.Empty))
      .isFalse
  }

  @Test
  fun `JWT has no businesses`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authBusiness(requiredBusinessGuid).authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `Business GUID mismatch`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = mapOf(
        UUID.randomUUID() to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authBusiness(requiredBusinessGuid).authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `Scoped JWT`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = listOf(),
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authBusiness(requiredBusinessGuid).authorize(jwt, Headers.Empty))
      .isFalse
  }

  @Test
  fun `Business GUID match (happy path)`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = mapOf(
        requiredBusinessGuid to JwtBusiness(
          name = "",
          scopes = null,
          businessMemberGuid = UUID.randomUUID(),
        ),
      ),
      mfa = null,
      roles = emptySet(),
      user = null,
    )
    assertThat(authBusiness(requiredBusinessGuid).authorize(jwt, Headers.Empty))
      .isTrue
  }

  @Test
  fun `JWT superuser override (happy path)`() = runBlocking<Unit> {
    val requiredBusinessGuid = UUID.randomUUID()
    val jwt = Jwt(
      acl = null,
      bankAccounts = null,
      businesses = null,
      mfa = null,
      roles = setOf(PlatformRole.SUPERUSER),
      user = null,
    )
    assertThat(authBusiness(requiredBusinessGuid).authorize(jwt, Headers.Empty))
      .isTrue
  }
}
