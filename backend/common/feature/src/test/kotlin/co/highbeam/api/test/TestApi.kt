package co.highbeam.api.test

import co.highbeam.restInterface.Endpoint

internal object TestApi {
  internal object Singleton : Endpoint("/singleton")

  internal data class Parameterized(
    val first: String,
    val second: String,
  ) : Endpoint("/firsts/$first/seconds/$second/thirds")

  internal data class RequiredQueryParamFoo(
    val foo: String,
  ) : Endpoint("/reqqp", qp = mapOf("foo" to listOf(foo)))

  internal data class RequiredQueryParamBar(
    val bar: String,
  ) : Endpoint("/reqqp", qp = mapOf("bar" to listOf(bar)))

  internal data class OptionalQueryParam(
    val optional: String?,
  ) : Endpoint("/optqp", qp = buildMap { optional?.let { put("optional", listOf(optional)) } })
}
