package co.highbeam.clock

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.DayOfWeek
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit
import kotlin.test.assertFailsWith

internal class FakeClockTest {
  private val clock: FakeClock = FakeClock()
  private val expectedDefaultInstant = Instant.parse("2007-12-03T10:15:30.789Z")
  private val expectedDefaultZone = ZoneId.of("America/New_York")

  @BeforeEach
  fun beforeEach() {
    clock.reset()
  }

  @Test
  fun defaults() {
    assertThat(clock.instant())
      .isEqualTo(expectedDefaultInstant)
    assertThat(clock.zone)
      .isEqualTo(expectedDefaultZone)

    assertThat(clock.date())
      .isEqualTo(
        ZonedDateTime.of(
          2007, 12, 3, 5, 15, 30, 789_000_000,
          expectedDefaultZone
        )
      )
  }

  @Test
  fun date() {
    assertThat(clock.date(UTC))
      .isEqualTo(
        ZonedDateTime.of(
          2007, 12, 3, 10, 15, 30, 789_000_000,
          UTC
        )
      )

    assertThat(clock.date(UTC))
      .isEqualTo(
        ZonedDateTime.of(
          2007, 12, 3, 2, 15, 30, 789_000_000,
          ZoneId.of("America/Los_Angeles")
        )
      )
  }

  @Test
  fun setNow() {
    clock.setNow(ZonedDateTime.of(
      2023, 12, 3, 5, 15, 30, 789_000_000,
      UTC,
    ))
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2023-12-03T05:15:30.789Z"))

    assertThat(clock.date())
      .isEqualTo(ZonedDateTime.of(
        2023, 12, 3, 5, 15, 30, 789_000_000,
        UTC,
      ))
  }

  @Test
  fun withZone() {
    val utcClock = clock.withZone(UTC)
    assertThat(utcClock.instant())
      .isEqualTo(expectedDefaultInstant)
    assertThat(utcClock.zone)
      .isEqualTo(UTC)
  }

  @Test
  fun addDays() {
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.789Z"))

    clock.add(1, TimeUnit.DAYS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-04T10:15:30.789Z"))

    clock.add(30, TimeUnit.DAYS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2008-01-03T10:15:30.789Z"))

    clock.add(-30, TimeUnit.DAYS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-04T10:15:30.789Z"))
  }

  @Test
  fun addHours() {
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.789Z"))

    clock.add(1, TimeUnit.HOURS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T11:15:30.789Z"))

    clock.add(30, TimeUnit.HOURS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-04T17:15:30.789Z"))

    clock.add(-30, TimeUnit.HOURS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T11:15:30.789Z"))
  }

  @Test
  fun addMinutes() {
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.789Z"))

    clock.add(1, TimeUnit.MINUTES)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:16:30.789Z"))

    clock.add(60, TimeUnit.MINUTES)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T11:16:30.789Z"))

    clock.add(-60, TimeUnit.MINUTES)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:16:30.789Z"))
  }

  @Test
  fun addSeconds() {
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.789Z"))

    clock.add(1, TimeUnit.SECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:31.789Z"))

    clock.add(60, TimeUnit.SECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:16:31.789Z"))

    clock.add(-60, TimeUnit.SECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:31.789Z"))
  }

  @Test
  fun addMilliseconds() {
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.789Z"))

    clock.add(1, TimeUnit.MILLISECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.790Z"))

    clock.add(1000, TimeUnit.MILLISECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:31.790Z"))

    clock.add(-1000, TimeUnit.MILLISECONDS)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-03T10:15:30.790Z"))
  }

  @Test
  fun addMicroseconds() {
    assertFailsWith<IllegalStateException> {
      clock.add(1, TimeUnit.MICROSECONDS)
    }
  }

  @Test
  fun addNanoseconds() {
    assertFailsWith<IllegalStateException> {
      clock.add(1, TimeUnit.NANOSECONDS)
    }
  }

  @Test
  fun nextWeek() {
    clock.next(DayOfWeek.MONDAY)
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-10T10:15:30.789Z"))
  }

  @Test
  fun nextDay() {
    clock.next(DayOfWeek.SUNDAY) // Start on Sunday
    assertThat(clock.instant())
      .isEqualTo(Instant.parse("2007-12-09T10:15:30.789Z"))
    DayOfWeek.values().forEachIndexed { i, day ->
      clock.next(day)
      assertThat(clock.instant())
        .isEqualTo(Instant.parse("2007-12-${getDate(i)}T10:15:30.789Z"))
    }
  }

  private fun getDate(i: Int) = (10 + i).toString().padStart(2, '0')
}
