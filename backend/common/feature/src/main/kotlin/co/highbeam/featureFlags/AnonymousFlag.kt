package co.highbeam.featureFlags

/**
 * [AnonymousFlag]s are a type of [Flag] that can be accessed WITHOUT a business guid.
 *
 * NOTE: ONLY USE THIS CLASS FOR FLAGS THAT MEET THE ABOVE CRITERION.
 * It does not make sense for most flags. Most flags should use [BusinessFlag].
 */
sealed class AnonymousFlag<T>(
  override val featureKey: String,
  override val defaultValue: T,
) : Flag<T> {
  object CurrencyCloudDelayedMessage : AnonymousFlag<Boolean>(
    featureKey = "international-wire-delayed-message",
    defaultValue = false,
  )

  object CurrencyCloudWebhookPubsub : AnonymousFlag<Boolean>(
    featureKey = "currency-cloud-webhook-pubsub",
    defaultValue = false,
  )

  object UnitCoReceivedPaymentMarkedForReturn : AnonymousFlag<Boolean>(
    featureKey = "unit-co-received-payment-marked-for-return",
    defaultValue = false,
  )
}
