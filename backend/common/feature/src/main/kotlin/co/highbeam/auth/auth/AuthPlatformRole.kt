package co.highbeam.auth.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.jwt.Jwt
import co.highbeam.permissions.platform.PlatformRole
import com.google.inject.Inject
import io.ktor.http.Headers

class AuthPlatformRole private constructor(private val role: PlatformRole) : Auth() {
  class Provider @Inject constructor() {
    operator fun invoke(role: PlatformRole): AuthPlatformRole = AuthPlatformRole(role)
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    jwt ?: return false
    return role in jwt.roles
  }
}
