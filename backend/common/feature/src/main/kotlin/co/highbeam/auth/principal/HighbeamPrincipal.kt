package co.highbeam.auth.principal

import co.highbeam.auth.jwt.Jwt
import com.fasterxml.jackson.annotation.JsonValue
import io.ktor.server.auth.Principal

/**
 * IMPORTANT: The JWT contains sensitive data and must be accessed with caution. A function must
 * explicitly opt in to [JwtAccess] to read the [jwt] field.
 */
@RequiresOptIn
annotation class JwtAccess

/**
 * Uses [Jwt] even if it is not sourced from a JWT, since this class contains all relevant metadata
 * needed for authorization.
 */
data class HighbeamPrincipal(
  @JsonValue
  val jwt: Jwt?,
  internal val insecure: Boolean = false, // ONLY for integration testing.
) : Principal
