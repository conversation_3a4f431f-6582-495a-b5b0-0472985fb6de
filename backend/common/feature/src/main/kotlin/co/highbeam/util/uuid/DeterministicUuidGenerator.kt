package co.highbeam.util.uuid

import java.util.UUID

/**
 * This is a deterministic way of generating UUIDs that's useful for tests.
 */
class DeterministicUuidGenerator : UuidGenerator {
  private var seed: Int = 0

  fun reset() {
    seed = 0
  }

  override fun generate(): UUID {
    val result = this[seed]
    seed++
    return result
  }

  operator fun get(i: Int): UUID =
    UUID.fromString("00000000-0000-0000-abcd-${i.toString().padStart(12, '0')}")
}
