package co.highbeam.auth.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.jwt.Jwt
import com.google.inject.Inject
import io.ktor.http.Headers

/**
 * Verify that the JWT is an OIDC token with a given audience and email.
 *
 * Although the `email` field is optional in OIDC, we require it here
 * since it is always present in our current use-cases.
 */
open class AuthOidc protected constructor(
  private val oidcAudience: String,
  private val oidcEmail: String,
) : Auth() {

  class Provider @Inject constructor() {
    operator fun invoke(oidcAudience: String, oidcEmail: String) =
      AuthOidc(oidcAudience, oidcEmail)
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    if (jwt == null) {
      return false
    }
    if (!jwt.isOidc()) {
      return false
    }
    if (oidcEmail != jwt.email) {
      return false
    }
    if (oidcAudience != jwt.aud) {
      return false
    }
    return true
  }
}
