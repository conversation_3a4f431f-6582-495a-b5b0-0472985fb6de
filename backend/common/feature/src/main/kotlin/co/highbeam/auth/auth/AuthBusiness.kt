package co.highbeam.auth.auth

import co.highbeam.auth.Auth
import co.highbeam.auth.jwt.Jwt
import com.google.inject.Inject
import io.ktor.http.Headers
import java.util.UUID

class AuthBusiness private constructor(private val businessGuid: UUID) : Auth() {
  class Provider @Inject constructor() {
    operator fun invoke(businessGuid: UUID): AuthBusiness = AuthBusiness(businessGuid)
  }

  override suspend fun authorizeJwt(jwt: Jwt?, headers: Headers): Boolean {
    jwt ?: return false
    val businesses = jwt.businesses ?: return false
    val business = businesses[businessGuid] ?: return false
    val scopes = business.scopes
    return scopes == null
  }
}
