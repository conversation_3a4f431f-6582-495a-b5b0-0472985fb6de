package co.highbeam.restInterface

import io.ktor.server.application.ApplicationCall
import io.ktor.server.routing.HttpAcceptRouteSelector
import io.ktor.server.routing.HttpMethodRouteSelector
import io.ktor.server.routing.ParameterRouteSelector
import io.ktor.server.routing.Route
import io.ktor.server.routing.createRouteFromPath
import io.ktor.util.KtorDsl
import io.ktor.util.pipeline.PipelineInterceptor

/**
 * Builds a Ktor route to match specified [endpointTemplate]. This should be used to easily wire up
 * endpoints to a Ktor application.
 */
@KtorDsl
fun Route.route(
  endpointTemplate: EndpointTemplate<*>,
  body: PipelineInterceptor<Unit, ApplicationCall>,
) {
  val pathRoute = createRouteFromPath(endpointTemplate.pathTemplate)
    .createChild(HttpAcceptRouteSelector(endpointTemplate.contentType))
    .createChild(HttpMethodRouteSelector(endpointTemplate.httpMethod))
  val fullRoute = endpointTemplate.requiredQueryParams.fold(pathRoute) { route, queryParam ->
    route.createChild(ParameterRouteSelector(queryParam))
  }
  fullRoute.handle(body)
}
