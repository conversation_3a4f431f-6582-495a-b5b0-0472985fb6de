package co.highbeam.typeConversion.typeConverter

import org.junit.jupiter.api.Test
import java.time.Duration
import kotlin.test.assertEquals
import kotlin.test.assertFails

internal class DurationTypeConverterTest {
  @Test
  fun parseString() {
    assertEquals(
      expected = Duration.ofMinutes(15),
      actual = DurationTypeConverter.parseString("900000"),
    )

    assertEquals(
      expected = Duration.ofMillis(-321),
      actual = DurationTypeConverter.parseString("-321"),
    )

    assertFails("Decimals are not permitted.") {
      DurationTypeConverter.parseString("99.95")
    }
  }

  @Test
  fun parse() {
    assertEquals(
      expected = Duration.ofMinutes(15),
      actual = DurationTypeConverter.parse(900_000),
    )

    assertEquals(
      expected = Duration.ofMillis(-321),
      actual = DurationTypeConverter.parse(-321),
    )
  }

  @Test
  fun writeString() {
    assertEquals(
      expected = "900000",
      actual = DurationTypeConverter.writeString(Duration.ofMinutes(15)),
    )
    assertEquals(
      expected = "-321",
      actual = DurationTypeConverter.writeString(Duration.ofMillis(-321)),
    )
  }

  @Test
  fun write() {
    assertEquals(
      expected = 900_000,
      actual = DurationTypeConverter.write(Duration.ofMinutes(15)),
    )
    assertEquals(
      expected = -321,
      actual = DurationTypeConverter.write(Duration.ofMillis(-321)),
    )
  }
}
