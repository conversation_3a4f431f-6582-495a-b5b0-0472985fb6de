package co.highbeam.typeConversion.typeConverter

import co.highbeam.typeConversion.StringTypeConverter
import co.highbeam.util.time.inUTC
import java.time.ZonedDateTime

object ZonedDateTimeTypeConverter : StringTypeConverter<ZonedDateTime>(ZonedDateTime::class) {
  override fun parse(value: String): ZonedDateTime = ZonedDateTime.parse(value).inUTC()

  override fun write(value: ZonedDateTime): String = value.inUTC().toString()
}
