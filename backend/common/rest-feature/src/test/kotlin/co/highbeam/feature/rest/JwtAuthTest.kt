package co.highbeam.feature.rest

import co.highbeam.auth.jwt.JwtClaims
import co.highbeam.config.JwtMechanismConfig
import co.highbeam.protectedString.ProtectedString
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import io.ktor.http.HttpStatusCode
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

internal class JwtAuthTest : AuthTest() {
  private val highbeamIssuer: String = "https://highbeam.co/"
  private val unknownIssuer: String = "https://unknown.com/"
  private val hmac256Secret: ProtectedString = ProtectedString("HMAC256 secret")

  private val authHeaderSettings = AuthHeaderSettings(
    issuer = highbeamIssuer,
    algorithm = Algorithm.HMAC256(hmac256Secret.value),
  )

  @Test
  fun `happy path`() {
    test(authHeaderSettings, expectation = Expectation.Jwt(emptySet()))
  }

  @Test
  fun `incorrect secret`() {
    test(
      authHeaderSettings.copy(algorithm = Algorithm.HMAC256("incorrect secret")),
      expectation = Expectation.InvalidJwt
    )
  }

  @Test
  fun `unknown issuer`() {
    test(
      authHeaderSettings.copy(issuer = unknownIssuer),
      expectation = Expectation.UnknownIssuer
    )
  }

  @Test
  fun `invalid JWT`() {
    test("Bearer jwt.jwt.jwt.jwt.jwt") { statusCode, responseBody ->
      assertEquals(HttpStatusCode.Forbidden, statusCode)
      assertEquals("Invalid JWT.", responseBody)
    }
  }

  @Test
  fun `Token without roles field is allowed`() {
    val jwt = JWT.create().withIssuer(highbeamIssuer).sign(Algorithm.HMAC256(hmac256Secret.value))
    test("Bearer $jwt") { statusCode, responseBody ->
      assertEquals(HttpStatusCode.OK, statusCode)
    }
  }

  private data class AuthHeaderSettings(
    val issuer: String?,
    val algorithm: Algorithm,
  )

  private fun test(authHeaderSettings: AuthHeaderSettings, expectation: Expectation) {
    val jwt = JWT.create().apply {
      authHeaderSettings.issuer?.let { issuer -> withIssuer(issuer) }
      withClaim(JwtClaims.roles, emptyList<String>())
    }.sign(authHeaderSettings.algorithm)
    val authorizationHeader = "Bearer $jwt"
    test(authorizationHeader, expectation)
  }

  override fun JwtAuthConfigBuilder.configureAuth() {
    val mechanisms = listOf(
      JwtMechanismConfig.Static(
        issuer = highbeamIssuer,
        leeway = 0,
        algorithm = JwtMechanismConfig.Static.Algorithm.Hmac256,
        secret = hmac256Secret,
      ),
    )
    addVerifier(
      scheme = JwtAuthVerifier.scheme,
      verifier = JwtAuthVerifier(mechanisms, objectMapper),
    )
  }
}
