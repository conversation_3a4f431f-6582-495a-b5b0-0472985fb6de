package co.highbeam.feature.rest

import io.ktor.server.auth.AuthenticationConfig

/**
 * This is used internally by Ktor, but never exposed to the end user.
 */
private const val AUTH_KEY: String = "HIGHBEAM_AUTH_KEY"

internal fun AuthenticationConfig.auth(block: JwtAuthConfigBuilder.() -> Unit) {
  val config = JwtAuthConfigBuilder().apply(block)
  val provider = AuthProvider(config.verifiers, authKey = AUTH_KEY)
  register(provider)
}
