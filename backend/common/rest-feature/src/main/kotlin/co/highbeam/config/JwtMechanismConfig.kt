package co.highbeam.config

import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.auth0.jwt.algorithms.Algorithm as JwtAlgorithm

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  include = JsonTypeInfo.As.PROPERTY,
  property = "source",
)
@JsonSubTypes(
  JsonSubTypes.Type(JwtMechanismConfig.Jwk::class, name = "Jwk"),
  JsonSubTypes.Type(JwtMechanismConfig.Static::class, name = "Static"),
)
sealed class JwtMechanismConfig {
  abstract val issuer: String?
  abstract val leeway: Long

  data class Jwk(
    override val issuer: String,
    override val leeway: Long = 20,
    val url: String,
  ) : JwtMechanismConfig()

  data class Static(
    override val issuer: String,
    override val leeway: Long = 20,
    val algorithm: Algorithm,
    @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
    val secret: ProtectedString,
  ) : JwtMechanismConfig() {
    enum class Algorithm { Hmac256 }

    fun createAlgorithm(): JwtAlgorithm = when (algorithm) {
      Algorithm.Hmac256 -> JwtAlgorithm.HMAC256(secret.value)
    }
  }
}
