package co.highbeam.feature.rest

import co.highbeam.restInterface.OBJECT_MAPPER_ATTRIBUTE_KEY
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.ContentType
import io.ktor.serialization.jackson.JacksonConverter
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation

internal fun Application.configureContentNegotiation(objectMapper: ObjectMapper) {
  // We only use <PERSON><PERSON>'s built-in content negotiation handling through the JacksonConverter
  // implementation for sending, not for receiving. This is because it maps the body's input stream
  // directly to the desired type. While this is efficient, but doesn't allow us to see the raw
  // original text, which is required in circumstances such as with encrypted bodies or signed
  // requests.
  attributes.put(OBJECT_MAPPER_ATTRIBUTE_KEY, objectMapper)
  install(ContentNegotiation) {
    register(
      contentType = ContentType.Application.Json,
      converter = JacksonConverter(objectMapper),
    )
  }
}
