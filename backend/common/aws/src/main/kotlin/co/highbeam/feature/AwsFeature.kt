package co.highbeam.feature

import co.highbeam.aws.CustomCredentialsProvider
import co.highbeam.config.AwsConfig
import com.amazonaws.auth.BasicAWSCredentials

class AwsFeature(private val config: AwsConfig) : Feature() {
  override fun bind() {
    initCredentialsProvider(config.credentials)
  }

  private fun initCredentialsProvider(credentials: AwsConfig.Credentials?) {
    if (credentials == null) return
    val awsCredentials = BasicAWSCredentials(
      credentials.accessKeyId,
      credentials.secretAccessKey.value,
    )
    CustomCredentialsProvider.instance.init(awsCredentials)
  }
}
