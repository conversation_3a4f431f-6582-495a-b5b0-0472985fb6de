package highbeam.task

import co.highbeam.restInterface.Endpoint
import mu.KLogger
import mu.KotlinLogging

object NoopTaskCreator : TaskCreator() {
  private val logger: KLogger = KotlinLogging.logger {}

  override suspend fun <T : Endpoint> create(endpoint: T, queueName: String) {
    logger.info { "Creating (no-op) task on queue $queueName: $endpoint." }
  }

  override fun start(): Unit = Unit

  override fun stop(): Unit = Unit
}
