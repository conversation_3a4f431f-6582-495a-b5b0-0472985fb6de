package co.highbeam.testing.integration

import co.highbeam.client.HttpClient
import co.highbeam.client.exception.HighbeamHttpClientException
import co.highbeam.clock.FakeClock
import co.highbeam.exception.HighbeamException
import co.highbeam.feature.typeLiteral
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.server.Server
import co.highbeam.util.uuid.DeterministicUuidGenerator
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.inject.Injector
import com.google.inject.Key
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.assertj.core.api.AbstractObjectAssert
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.extension.ExtendWith
import kotlin.test.assertEquals
import kotlin.test.fail

@ExtendWith(TestApplicationEngineParameterResolver::class)
abstract class AbstractIntegrationTest(server: Server<*>) {
  private val logger = KotlinLogging.logger {}

  val injector: Injector = server.injector

  val uuidGenerator: DeterministicUuidGenerator = server.uuidGenerator
  val clock: FakeClock = server.clock

  val objectMapper: ObjectMapper = HighbeamObjectMapper.json {
    prettyPrint(true)
    allowUnknownProperties(true)
    useTypeConverters(injector.getInstance(Key.get(typeLiteral())))
  }.build()
  val httpClient: HttpClient by lazy {
    // TODO: Move HTTP interaction to the REST feature testing library.
    IntegrationTestHttpClient(server.engine.client, objectMapper)
  }

  inline fun <reified T : Any> get(): T = get(Key.get(typeLiteral()))

  inline fun <reified T : Any> get(key: Key<T>): T = injector.getInstance(key)

  fun integrationTest(block: suspend CoroutineScope.() -> Unit) {
    runBlocking(block = block)
  }

  fun test(block: suspend CoroutineScope.() -> Unit) {
    runBlocking(block = block)
  }

  @Deprecated("Use AssertJ.")
  fun setup(description: String, block: suspend () -> Unit): Unit = runBlocking {
    logger.debug(description)
    block()
  }

  @Deprecated("Use AssertJ.")
  fun <T> test(expectResult: T, block: suspend () -> T): Unit =
    test(expectResult, { it }, block)

  @Deprecated("Use AssertJ.")
  fun <T> test(expectResult: T, transform: (T) -> T, block: suspend () -> T) {
    val expect = { actual: T -> assertEquals(transform(expectResult), transform(actual)) }
    test(expect, block)
  }

  @Deprecated("Use AssertJ.")
  fun <T> test(expect: (T) -> Unit, block: suspend () -> T): Unit = runBlocking {
    val actual = block()
    expect(actual)
  }

  @Deprecated("Use AssertJ.")
  fun test(
    expectError: HighbeamException,
    block: suspend () -> Any?,
  ): Unit = runBlocking {
    try {
      val actual = block()
      fail("Expected exception. Instead got $actual.")
    } catch (e: HighbeamHttpClientException) {
      assertEquals(expectError.properties, objectMapper.readValue(e.errorMessage))
    }
  }

  fun assertHighbeamException(
    shouldRaiseThrowable: suspend CoroutineScope.() -> Unit,
  ): AbstractObjectAssert<*, Map<String, Any>> =
    assertThatThrownBy { runBlocking(block = shouldRaiseThrowable) }
      .isInstanceOf(HighbeamHttpClientException::class.java)
      .extracting { objectMapper.readValue((it as HighbeamHttpClientException).errorMessage) }
}

@JvmName("isHighbeamExceptionThrowable")
fun AbstractObjectAssert<*, out Throwable>.isHighbeamException(
  exception: HighbeamException,
) {
  isInstanceOf(HighbeamException::class.java)
    .extracting { (it as HighbeamException).properties }
    .isHighbeamException(exception)
}

@JvmName("isHighbeamExceptionMap")
fun AbstractObjectAssert<*, Map<String, Any>>.isHighbeamException(
  exception: HighbeamException,
): AbstractObjectAssert<*, *> =
  isEqualTo(exception.properties)
