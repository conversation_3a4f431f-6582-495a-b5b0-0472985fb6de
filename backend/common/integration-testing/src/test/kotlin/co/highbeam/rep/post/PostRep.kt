package co.highbeam.rep.post

import co.highbeam.rep.CompleteRep
import co.highbeam.rep.CreatorRep
import co.highbeam.validation.RepValidation

internal object PostRep {
  internal data class Creator(
    val userId: Int,
    val title: String,
    val body: String,
  ) : CreatorRep {
    override fun validate(): RepValidation = RepValidation.none()
  }

  internal data class Complete(
    val userId: Int,
    val id: Int,
    val title: String,
    val body: String,
  ) : CompleteRep
}
