package co.highbeam.optional

import com.fasterxml.jackson.module.kotlin.MissingKotlinParameterException
import com.fasterxml.jackson.module.kotlin.readValue
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import java.util.Optional

internal class NullOptionalTest : OptionalTest() {

  @Test
  override fun `equals method`() {
    assertThat(OptionalWrapper.Nullable(null))
      .isEqualTo(OptionalWrapper.Nullable(null))
    assertThat(OptionalWrapper.Nullable(null))
      .isNotEqualTo(OptionalWrapper.Nullable(Optional.empty()))
    assertThat(OptionalWrapper.Nullable(null))
      .isNotEqualTo(OptionalWrapper.Nullable(Optional.of("")))
    assertThat(OptionalWrapper.Nullable(null))
      .isNotEqualTo(OptionalWrapper.Nullable(Optional.of("42")))
  }

  @Test
  override fun `toString method`() {
    assertThat(OptionalWrapper.Nullable(null).toString())
      .isEqualTo("Nullable(optional=null)")
  }

  @Test
  override fun serialize() {
    assertThat(objectMapper.readValue<OptionalWrapper.Nullable>("{}"))
      .isEqualTo(OptionalWrapper.Nullable(null))
    assertThatThrownBy {
      objectMapper.readValue<OptionalWrapper.NotNullable>("{}")
    }.isInstanceOf(MissingKotlinParameterException::class.java)
  }

  @Test
  override fun deserialize() {
    assertThat(objectMapper.writeValueAsString(OptionalWrapper.Nullable(null)))
      .isEqualTo("{}")
  }
}
