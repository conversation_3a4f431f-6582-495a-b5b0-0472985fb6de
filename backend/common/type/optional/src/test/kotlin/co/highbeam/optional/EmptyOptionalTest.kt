package co.highbeam.optional

import com.fasterxml.jackson.module.kotlin.readValue
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.util.Optional

internal class EmptyOptionalTest : OptionalTest() {
  @Test
  override fun `equals method`() {
    assertThat(OptionalWrapper.Nullable(Optional.empty()))
      .isEqualTo(OptionalWrapper.Nullable(Optional.empty()))
    assertThat(OptionalWrapper.Nullable(Optional.empty()))
      .isNotEqualTo(OptionalWrapper.Nullable(null))
    assertThat(OptionalWrapper.Nullable(Optional.empty()))
      .isNotEqualTo(OptionalWrapper.Nullable(Optional.of("")))
    assertThat(OptionalWrapper.Nullable(Optional.empty()))
      .isNotEqualTo(OptionalWrapper.Nullable(Optional.of("42")))

    assertThat(OptionalWrapper.NotNullable(Optional.empty()))
      .isEqualTo(OptionalWrapper.NotNullable(Optional.empty()))
    assertThat(OptionalWrapper.NotNullable(Optional.empty()))
      .isNotEqualTo(OptionalWrapper.NotNullable(Optional.of("")))
    assertThat(OptionalWrapper.NotNullable(Optional.empty()))
      .isNotEqualTo(OptionalWrapper.NotNullable(Optional.of("42")))
  }

  @Test
  override fun `toString method`() {
    assertThat(OptionalWrapper.Nullable(Optional.empty()).toString())
      .isEqualTo("Nullable(optional=Optional.empty)")

    assertThat(OptionalWrapper.NotNullable(Optional.empty()).toString())
      .isEqualTo("NotNullable(optional=Optional.empty)")
  }

  @Test
  override fun serialize() {
    assertThat(objectMapper.readValue<OptionalWrapper.Nullable>("{\"optional\":null}"))
      .isEqualTo(OptionalWrapper.Nullable(Optional.empty()))

    assertThat(objectMapper.readValue<OptionalWrapper.NotNullable>("{\"optional\":null}"))
      .isEqualTo(OptionalWrapper.NotNullable(Optional.empty()))
  }

  @Test
  override fun deserialize() {
    assertThat(objectMapper.writeValueAsString(OptionalWrapper.Nullable(Optional.empty())))
      .isEqualTo("{\"optional\":null}")
    assertThat(objectMapper.writeValueAsString(OptionalWrapper.NotNullable(Optional.empty())))
      .isEqualTo("{\"optional\":null}")
  }
}
