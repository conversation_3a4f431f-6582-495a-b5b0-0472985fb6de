package co.highbeam.slug

import co.highbeam.util.uuid.asByteArray
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue
import java.math.BigInteger
import java.util.UUID

private const val BASE: String = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz1234567890"
private val BASE_LENGTH: BigInteger = BASE.length.toBigInteger()

// Slugs are hard-coded to 20 characters. This can be changed in the future, but we must be
// careful not to exceed the entropy of a UUID if this is increased.
private const val SLUG_LENGTH: Int = 20

data class Slug @JsonCreator constructor(private val slug: String) {
  constructor(guid: UUID) : this(generateSlug(guid))

  @JsonValue
  override fun toString(): String = slug
}

private fun generateSlug(guid: UUID): String {
  var int = BigInteger(1, guid.asByteArray())
  val result = StringBuilder()
  repeat(SLUG_LENGTH) {
    result.append(BASE[int.remainder(BASE_LENGTH).toInt()])
    int /= BASE_LENGTH
  }
  return result.toString()
}
