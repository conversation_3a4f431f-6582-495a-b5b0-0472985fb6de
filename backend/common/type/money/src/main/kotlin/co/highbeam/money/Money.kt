package co.highbeam.money

import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Currency
import kotlin.math.abs
import kotlin.math.min

/**
 * [Money] is a scalar value. Negative amounts are not allowed. See the README for more info.
 */
data class Money(val rawCents: Long) : Comparable<Money> {
  init {
    require(rawCents >= 0)
  }

  companion object {
    val ZERO = Money(0)

    fun min(a: Money, b: Money): Money {
      return fromCents(min(a.rawCents, b.rawCents))
    }

    fun fromCents(cents: Long): Money = Money(cents)

    fun fromCents(cents: BigDecimal): Money =
      Money(cents.setScale(0, RoundingMode.HALF_EVEN).toLong())

    fun fromDollars(dollars: Long): Money = Money(dollars * 100)

    fun fromDollars(dollars: BigDecimal): Money =
      Money((dollars.setScale(2, RoundingMode.HALF_EVEN) * BigDecimal.valueOf(100)).toLong())

    fun fromDollarsAndCents(dollars: Long, cents: Long): Money {
      require(dollars >= 0)
      require(cents >= 0)
      return Money(dollars * 100 + cents)
    }

    fun fromDollarsAndCents(dollars: Double) =
      Money(
        BigDecimal.valueOf(dollars * 100)
          .setScale(0, RoundingMode.HALF_EVEN).toLong()
      )

    fun fromString(value: String): Money {
      val split = value.split('.')
      require(split.size in 1..2)
      return fromDollarsAndCents(split[0].toLong(), split.getOrElse(1) { "0" }.toLong())
    }

    fun fromBalance(balance: Balance): Money = Money(abs(balance.rawCents))
  }

  override fun compareTo(other: Money): Int = rawCents.compareTo(other.rawCents)

  override fun toString() = formatString(withDollarSign = true, withComma = false)

  fun formatString(withDollarSign: Boolean, withComma: Boolean): String =
    formatCentsAsDollarAmount(rawCents, withDollarSign, withComma)

  fun toDollarsAndCents(currency: Currency = Currency.getInstance("USD")): Double {
    val scale = currency.defaultFractionDigits
    return (rawCents / 100.0).toBigDecimal().setScale(scale, RoundingMode.HALF_EVEN).toDouble()
  }

  operator fun plus(otherMoney: Money) = Money(rawCents + otherMoney.rawCents)

  operator fun minus(otherMoney: Money) = Money(rawCents - otherMoney.rawCents)

  operator fun compareTo(other: Balance): Int = rawCents.compareTo(other.rawCents)

  operator fun times(multiplier: BigDecimal): Money {
    require(multiplier >= BigDecimal.ZERO) { "Multiplier must be non-negative" }
    return Money((BigDecimal(rawCents) * multiplier).setScale(0, RoundingMode.HALF_EVEN).toLong())
  }

  operator fun times(multiplier: Long): Money {
    require(multiplier >= 0) { "Multiplier must be non-negative" }
    return Money(rawCents * multiplier)
  }
}
