package kotlin.collections

import co.highbeam.money.Balance
import co.highbeam.money.Money

@OptIn(kotlin.experimental.ExperimentalTypeInference::class)
@OverloadResolutionByLambdaReturnType
@JvmName("sumOfMoney")
inline fun <T> Iterable<T>.sumOf(selector: (T) -> Money): Money =
  Money(sumOf { selector(it).rawCents })

@OptIn(kotlin.experimental.ExperimentalTypeInference::class)
@OverloadResolutionByLambdaReturnType
@JvmName("sumOfBalance")
inline fun <T> Iterable<T>.sumOf(selector: (T) -> Balance): Balance =
  Balance(sumOf { selector(it).rawCents })
