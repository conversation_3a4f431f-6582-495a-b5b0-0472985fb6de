package co.highbeam.protectedString

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

internal class ProtectedStringTest {
  @Suppress("ReplaceAssertBooleanWithAssertEquality", "SENSELESS_COMPARISON")
  @Test
  fun `equals method`() {
    assertFalse(ProtectedString("1") == null)
    assertTrue(ProtectedString("1") == ProtectedString("1"))
    assertFalse(ProtectedString("1") == ProtectedString("2"))
  }

  @Test
  fun `hashCode method`() {
    assertEquals("1".hashCode(), ProtectedString("1").hashCode())
  }

  @Test
  fun `toString method`() {
    assertEquals("REDACTED", ProtectedString("1").toString())
  }
}
