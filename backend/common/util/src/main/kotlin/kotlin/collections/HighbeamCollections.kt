package kotlin.collections

/**
 * Adapted from [single] and [singleOrNull].
 */
fun <T> Iterable<T>.singleNullOrThrow(predicate: (T) -> Boolean): T? {
  when (val filteredList = this.filter { predicate(it) }) {
    is List -> when (filteredList.size) {
      0 -> return null
      1 -> return filteredList[0]
    }
    else -> {
      val iterator = filteredList.iterator()
      if (!iterator.hasNext()) return null
      val single = iterator.next()
      if (!iterator.hasNext()) return single
    }
  }
  throw IllegalArgumentException("Collection has more than one element.")
}

fun <T> Iterable<T>.singleNullOrThrow(): T? = singleNullOrThrow { true }
