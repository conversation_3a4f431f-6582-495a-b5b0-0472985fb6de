package co.highbeam.event.publisher

import co.highbeam.event.admin.GcpEventAdminClient
import co.highbeam.event.admin.TopicConfig
import co.highbeam.serialization.HighbeamObjectMapper
import com.google.cloud.pubsub.v1.Publisher
import com.google.protobuf.ByteString
import com.google.pubsub.v1.PubsubMessage
import com.google.pubsub.v1.TopicName
import mu.KotlinLogging
import java.util.concurrent.TimeUnit

class GcpPubSubEventPublisher<T : Any>(
  private val gcpEventAdminClient: GcpEventAdminClient,
  private val topic: TopicName,
  private val async: Boolean,
) : EventPublisher<T> {
  private val logger = KotlinLogging.logger {}

  private val objectMapper = HighbeamObjectMapper.json().build()

  private val publisher = Publisher.newBuilder(topic).build()

  override fun publishEvent(event: T, attributes: Map<String, String>) {
    gcpEventAdminClient.ensureTopicExists(
      project = topic.project,
      config = TopicConfig(name = topic.topic),
    )

    val data = ByteString.copyFrom(objectMapper.writeValueAsBytes(event))
    val pubsubMessage = PubsubMessage.newBuilder()
      .setData(data)
      .putAllAttributes(attributes)
      .build()
    val messageIdFuture = publisher.publish(pubsubMessage)
    if (!async) {
      messageIdFuture.get()
    }
  }

  override fun close() {
    logger.info { "Shutting down pubsub publisher for topic: '${topic.topic}'" }
    publisher.shutdown()
    publisher.awaitTermination(5, TimeUnit.SECONDS)
  }
}
