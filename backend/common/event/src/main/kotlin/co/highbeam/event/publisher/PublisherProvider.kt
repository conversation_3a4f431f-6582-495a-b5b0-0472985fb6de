package co.highbeam.event.publisher

import co.highbeam.event.admin.TopicConfig
import com.google.inject.Binder
import com.google.inject.Provider
import com.google.inject.TypeLiteral
import com.google.inject.name.Names
import kotlin.reflect.KClass

abstract class PublisherProvider<T : Any> : Provider<T> {
  abstract class Companion<T : Any>(
    protected val topic: TopicConfig,
    private val typeLiteral: TypeLiteral<EventPublisher<T>>,
    private val provider: KClass<out PublisherProvider<EventPublisher<T>>>,
  ) {
    fun bind(binder: Binder, publishers: MutableSet<TypeLiteral<out EventPublisher<*>>>) {
      binder.bind(typeLiteral)
        .annotatedWith(Names.named(topic.name))
        .toProvider(provider.java)
        .asEagerSingleton()
      publishers.add(typeLiteral)
    }
  }
}
