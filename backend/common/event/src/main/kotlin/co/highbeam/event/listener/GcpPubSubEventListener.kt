package co.highbeam.event.listener

import co.highbeam.serialization.HighbeamObjectMapper
import com.google.api.gax.batching.FlowControlSettings
import com.google.cloud.pubsub.v1.MessageReceiver
import com.google.cloud.pubsub.v1.Subscriber
import com.google.pubsub.v1.SubscriptionName
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import java.io.Closeable

class GcpPubSubEventListener<T : Any>(
  maxOutstandingElements: Long,
  private val subscriptionName: SubscriptionName,
  private val clazz: Class<T>,
  private val listener: EventListener<T>,
) : Closeable {
  private val logger = KotlinLogging.logger {}
  private val objectMapper = HighbeamObjectMapper.json().build()

  @SuppressWarnings("TooGenericExceptionCaught")
  private val receiver = MessageReceiver { message, consumer ->
    val event = try {
      objectMapper.readValue(message.data.toStringUtf8(), clazz)
    } catch (e: Exception) {
      logger.error(e) { "Failed to deserialize pubsub event for subscription: $subscriptionName" }
      consumer.nack()
      return@MessageReceiver
    }
    /**
     * TODO: For now, we're using blocking calls for event listeners.
     *  Performance could be improved by introducing a separate CoroutineContext
     *  and an associated ThreadPool.
     */
    runBlocking {
      listener(event, GcpPubSubAckReply(consumer))
    }
  }

  private val subscriber =
    Subscriber
      .newBuilder(subscriptionName.toString(), receiver)
      .setFlowControlSettings(
        FlowControlSettings.newBuilder()
          .setMaxOutstandingElementCount(maxOutstandingElements)
          .build()
      )
      .build()

  init {
    // starts a default of 5 threads per channel
    subscriber.startAsync().awaitRunning()
  }

  override fun close() {
    subscriber.stopAsync()
  }
}
