package co.highbeam.event.messaging

import co.highbeam.event.GcpEmulatorTest
import co.highbeam.event.admin.SubscriptionConfig
import co.highbeam.event.admin.TopicConfig
import co.highbeam.event.exception.DuplicateSubscriptionException
import com.fasterxml.jackson.annotation.JsonProperty
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@Tag("docker")
@Disabled // TODO: We don't want to run this everytime. Move to exclude tag approach
class GcpPubSubMessagingTest : GcpEmulatorTest() {

  data class TestEvent(
    @JsonProperty("id")
    val id: Int,
    @JsonProperty("content")
    val content: String,
  )

  @AfterEach
  fun tearDown() {
    eventListenerFactory.close()
  }

  @Test
  fun `singlePublisher - singleConsumer`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }
    val publisher = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    publisher.publishEvent(TestEvent(1, "test"))
    publisher.publishEvent(TestEvent(2, "test2"))
    publisher.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 2)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 3)).isEqualTo("test3")

    publisher.close()
  }

  @Test
  fun `multiPublisher - singleConsumer`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }
    val publisher1 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    val publisher2 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    publisher1.publishEvent(TestEvent(1, "test"))
    publisher2.publishEvent(TestEvent(2, "test2"))
    publisher1.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 2)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 3)).isEqualTo("test3")

    publisher1.close()
    publisher2.close()
  }

  @Test
  fun `singlePublisher - singleConsumer - sync`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }
    val publisher = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
      async = false,
    )
    publisher.publishEvent(TestEvent(1, "test"))
    publisher.publishEvent(TestEvent(2, "test2"))
    publisher.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 2)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 3)).isEqualTo("test3")

    publisher.close()
  }

  @Test
  fun `singlePublisher - multiConsumer`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }

    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer2"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id + 100] = event.content
      ackReply.ack()
    }

    val publisher = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
      async = false,
    )
    publisher.publishEvent(TestEvent(1, "test"))
    publisher.publishEvent(TestEvent(2, "test2"))
    publisher.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 2)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 3)).isEqualTo("test3")

    assertThat(awaitEvent(listenerMessage, 101)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 102)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 103)).isEqualTo("test3")

    publisher.close()
  }

  @Test
  fun `singlePublisher - duplicateConsumer`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }

    assertThrows<DuplicateSubscriptionException> {
      eventListenerFactory.startAsync(
        topicConfig = TopicConfig("test-event"),
        subscriptionConfig = SubscriptionConfig("consumer"),
        clazz = TestEvent::class.java,
      ) { event, ackReply ->
        listenerMessage[event.id + 100] = event.content
        ackReply.ack()
      }
    }
  }

  @Test
  fun `multiPublisher - multiConsumer`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }

    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer2"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id + 100] = event.content
      ackReply.ack()
    }

    val publisher1 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    val publisher2 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    publisher1.publishEvent(TestEvent(1, "test"))
    publisher2.publishEvent(TestEvent(2, "test2"))
    publisher1.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 2)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 3)).isEqualTo("test3")

    assertThat(awaitEvent(listenerMessage, 101)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 102)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 103)).isEqualTo("test3")

    publisher1.close()
    publisher2.close()
  }

  @Test
  fun `singlePublisher - singleConsumer - multiTopic`() {
    val listenerMessage = mutableMapOf<Int, String>()
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id] = event.content
      ackReply.ack()
    }
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event-1"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      listenerMessage[event.id + 100] = event.content
      ackReply.ack()
    }
    val publisher1 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    val publisher2 = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event-1"),
    )
    publisher1.publishEvent(TestEvent(1, "test"))
    publisher2.publishEvent(TestEvent(2, "test2"))
    publisher2.publishEvent(TestEvent(3, "test3"))

    assertThat(awaitEvent(listenerMessage, 1)).isEqualTo("test")
    assertThat(awaitEvent(listenerMessage, 102)).isEqualTo("test2")
    assertThat(awaitEvent(listenerMessage, 103)).isEqualTo("test3")

    publisher1.close()
    publisher2.close()
  }

  @Test
  fun `singlePublisher - singleConsumer - retry`() {
    val listenerMessage = mutableMapOf<Int, String>()
    var deliveryCount = 0

    // Fail the message until third redelivery
    eventListenerFactory.startAsync(
      topicConfig = TopicConfig("test-event"),
      subscriptionConfig = SubscriptionConfig("consumer"),
      clazz = TestEvent::class.java,
    ) { event, ackReply ->
      deliveryCount += 1
      listenerMessage[event.id + deliveryCount * 100] = event.content
      if (deliveryCount == 3) ackReply.ack() else ackReply.nack(RuntimeException())
    }
    val publisher = eventPublisherFactory.buildPublisher<TestEvent>(
      topicConfig = TopicConfig("test-event"),
    )
    publisher.publishEvent(TestEvent(1, "test"))

    assertThat(awaitEvent(listenerMessage, 301)).isEqualTo("test")

    publisher.close()
  }

  private fun awaitEvent(listenerMessage: Map<Int, String>, id: Int): String? {
    var maxRetryCount = 200
    while (!listenerMessage.containsKey(id) && maxRetryCount-- > 0) {
      Thread.sleep(50)
    }
    if (maxRetryCount == 0) {
      return null
    }
    return listenerMessage[id]
  }
}
