package co.highbeam.event.publisher

import co.highbeam.feature.typeLiteral
import co.highbeam.testing.integration.AbstractIntegrationTest
import com.google.inject.Key
import com.google.inject.name.Names
import mu.KotlinLogging

class FakePubSubEventPublisher<T : Any> : EventPublisher<T> {
  val events: MutableList<Pair<T, Map<String, String>>> = mutableListOf()
  private val logger = KotlinLogging.logger {}

  override fun publishEvent(event: T, attributes: Map<String, String>) {
    logger.info { "Publishing event: $event with attributes ${attributes}." }
    events.add(Pair(event, attributes))
  }

  override fun close() = Unit

  fun reset() {
    events.clear()
  }
}

/**
 * Convenience function for tests.
 */
inline fun <reified T : Any> AbstractIntegrationTest.getPublisher(name: String) =
  get(Key.get(typeLiteral<EventPublisher<T>>(), Names.named(name))) as FakePubSubEventPublisher<T>
