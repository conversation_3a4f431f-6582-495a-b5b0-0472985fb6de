package co.highbeam.event

import co.highbeam.event.listener.EventListenerFactory
import co.highbeam.event.listener.FakeEventListenerFactory
import co.highbeam.event.publisher.EventPublisherFactory
import co.highbeam.event.publisher.FakeEventPublisherFactory
import co.highbeam.feature.event.EventFeature

object FakeEventFeature : EventFeature() {
  override fun bind() {
    bind(EventListenerFactory::class.java).toInstance(FakeEventListenerFactory)
    bind(EventPublisherFactory::class.java).toInstance(FakeEventPublisherFactory)
  }

  fun reset() {
    FakeEventPublisherFactory.reset()
  }
}
