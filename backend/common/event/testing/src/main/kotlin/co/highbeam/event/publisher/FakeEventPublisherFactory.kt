package co.highbeam.event.publisher

import co.highbeam.event.admin.TopicConfig

object FakeEventPublisherFactory : EventPublisherFactory() {
  private val publishers = mutableListOf<FakePubSubEventPublisher<out Any>>()

  override fun <T : Any> buildPublisher(
    topicConfig: TopicConfig,
    async: Boolean,
  ): EventPublisher<T> {
    val publisher = FakePubSubEventPublisher<T>()
    publishers.add(publisher)
    return publisher
  }

  fun reset() {
    publishers.forEach { it.reset() }
  }
}
