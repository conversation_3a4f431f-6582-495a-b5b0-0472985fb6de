package co.highbeam.email.template

import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources

internal class UserInvitationEmailTemplateTest
  : EmailTemplateTest<UserInvitationEmailTemplate>() {

  override val testCases = listOf(
    TestCase(
      template = UserInvitationEmailTemplate(
        recipient = EmailTemplate.Recipient(emailAddress = "<EMAIL>", "<PERSON>"),
        businessName = "Acme Co",
        recipientFirstName = "Jeff",
        senderName = "<PERSON><PERSON><PERSON>",
        userRoleName = "Unknown",
        ctaLink = "https://app.highbeam.co/invitation/foo-bar",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/user-invitation.0.json"),
      ),
    ),
    TestCase(
      template = UserInvitationEmailTemplate(
        recipient = EmailTemplate.Recipient(emailAddress = "<EMAIL>", "<PERSON>"),
        businessName = "Acme Co",
        recipientFirstName = "Jeff",
        senderName = "<PERSON><PERSON><PERSON>",
        userRoleName = "Admin",
        ctaLink = "https://app.highbeam.co/invitation/foo-bar",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/user-invitation.1.json"),
      ),
    ),
    TestCase(
      template = UserInvitationEmailTemplate(
        recipient = EmailTemplate.Recipient(emailAddress = "<EMAIL>", "Jeff Hudson"),
        businessName = "Acme Co",
        recipientFirstName = "Jeff",
        senderName = "Gautam Gupta",
        userRoleName = "Bookkeeper",
        ctaLink = "https://app.highbeam.co/invitation/foo-bar",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/user-invitation.2.json"),
      ),
    ),
  )
}
