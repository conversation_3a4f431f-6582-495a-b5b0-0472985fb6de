package co.highbeam.email.template

import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources

internal class ApplicationApprovedEmailTemplateTest
  : EmailTemplateTest<ApplicationApprovedEmailTemplate>() {
  @Suppress("MaxLineLength")
  override val testCases = listOf(
    TestCase(
      template = ApplicationApprovedEmailTemplate(
        recipient = EmailTemplate.Recipient(
          emailAddress = "<EMAIL>",
          name = null
        ),
        businessName = "Highbeam Inc.",
        actionUrl = "https://app.highbeam.co",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/application-approved.json"),
      ),
    ),
  )
}
