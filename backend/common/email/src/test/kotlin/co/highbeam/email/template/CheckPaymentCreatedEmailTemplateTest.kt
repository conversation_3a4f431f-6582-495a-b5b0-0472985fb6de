package co.highbeam.email.template

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import java.time.LocalDate
import java.time.ZonedDateTime

internal class CheckPaymentCreatedEmailTemplateTest
  : EmailTemplateTest<CheckPaymentCreatedEmailTemplate>() {
  override val testCases = listOf(
    TestCase(
      template = CheckPaymentCreatedEmailTemplate(
        recipients = listOf(
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", name = "<PERSON><PERSON><PERSON>"),
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", name = "<PERSON><PERSON>"),
        ),
        accountWithMask = "Primary Account **** 1234",
        amount = Money.fromDollarsAndCents(20.00),
        description = "Check payment to vendor",
        cancellationDeadline = ZonedDateTime.parse("2023-03-24T15:50:00.000Z"),
        paymentCreatedAt = LocalDate.parse("2023-03-22"),
        transactionsUrl = "https://highbeam.co/transactions",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/check-payment.0.json"),
      ),
    ),
  )
}
