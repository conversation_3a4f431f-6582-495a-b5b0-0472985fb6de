package co.highbeam.email.template

import co.highbeam.money.Balance
import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import java.time.LocalDate

internal class ChargeCardPaymentInsufficientBalanceTemplateTest
  : EmailTemplateTest<ChargeCardPaymentInsufficientBalanceTemplate>() {

  override val testCases = listOf(
    TestCase(
      template = ChargeCardPaymentInsufficientBalanceTemplate(
        recipients = listOf(
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", "<PERSON>")
        ),
        capitalUrl = "https://app.highbeam.co/capital",
        outstandingBalance = Money(1234_56),
        highbeamBankAccountName = "Highbeam Primary ---- 1234",
        highbeamBankAccountBalance = Balance(4321_00),
        repaymentDate = LocalDate.of(2023, 10, 15),
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/charge-card-insufficient-balance.json"),
      ),
    ),
  )
}
