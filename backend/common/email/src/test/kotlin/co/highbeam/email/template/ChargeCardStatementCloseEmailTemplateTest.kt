package co.highbeam.email.template

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import java.time.LocalDate

internal class ChargeCardStatementCloseEmailTemplateTest
  : EmailTemplateTest<ChargeCardStatementCloseEmailTemplate>() {

  override val testCases = listOf(
    TestCase(
      template = ChargeCardStatementCloseEmailTemplate(
        recipients = listOf(
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", "<PERSON>")
        ),
        capitalAccountName = "Flex Card",
        capitalUrl = "https://app.highbeam.co/capital/12345",
        closingBalance = Money(1234_56),
        dueDate = LocalDate.of(2023, 8, 5),
        highbeamBankAccountName = "Highbeam Primary ---- 1234",
        periodStartDate = LocalDate.of(2023, 7, 1),
        periodEndDate = LocalDate.of(2023, 7, 31)
      ),
      templateData = objectMapper.readValue(
        Resources.getResource(
          "email/charge-card-statement-close-with-outstanding-balance.json"
        ),
      ),
    ),
    TestCase(
      template = ChargeCardStatementCloseEmailTemplate(
        recipients = listOf(
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", "Jeff Hudson")
        ),
        capitalAccountName = "Flex Card",
        capitalUrl = "https://app.highbeam.co/capital/12345",
        closingBalance = Money(0),
        dueDate = LocalDate.of(2023, 8, 5),
        highbeamBankAccountName = "Highbeam Primary ---- 1234",
        periodStartDate = LocalDate.of(2023, 7, 1),
        periodEndDate = LocalDate.of(2023, 7, 31)
      ),
      templateData = objectMapper.readValue(
        Resources.getResource(
          "email/charge-card-statement-close-with-zero-balance-owing.json"
        ),
      ),
    ),
  )
}
