package co.highbeam.email.template

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.common.io.Resources
import java.time.LocalDate

internal class ChargeCardAutomaticPaymentCompleteEmailTemplateTest
  : EmailTemplateTest<ChargeCardAutomaticPaymentCompleteEmailTemplate>() {

  override val testCases = listOf(
    TestCase(
      template = ChargeCardAutomaticPaymentCompleteEmailTemplate(
        recipients = listOf(
          EmailTemplate.Recipient(emailAddress = "<EMAIL>", "<PERSON>"),
        ),
        capitalUrl = "https://app.highbeam.co/capital",
        closingBalance = Money(1234_56),
        highbeamBankAccountName = "Highbeam Primary ---- 1234",
        periodStartDate = LocalDate.of(2023, 7, 1),
        periodEndDate = LocalDate.of(2023, 7, 31),
        capitalAccountName = "Flex Card",
      ),
      templateData = objectMapper.readValue(
        Resources.getResource("email/charge-card-automatic-payment.json"),
      ),
    ),
  )
}
