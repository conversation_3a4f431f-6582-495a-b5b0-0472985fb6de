package co.highbeam.email.template

import co.highbeam.money.Money

data class DepositInterestEmailTemplate(
  override val recipients: List<Recipient>,
  val amountThisMonth: Money,
  val amountYearToDate: Money,
  val actionUrl: String,
) : EmailTemplate() {
  override val sender: Sender = Sender.HIGHBEAM

  override val subject: String = run {
    val formattedAmount = amountThisMonth.formatString(withDollarSign = true, withComma = true)
    return@run "You earned $formattedAmount via Highbeam high yield!"
  }

  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateSpacingSection,
    EmailTemplateIconSection(EmailTemplateIconSection.Icon.Money),
    EmailTemplateSectionHeaderSection("You earned interest last month!"),
    EmailTemplateTextSection(
      values = listOf(
        EmailTemplateTextSection.Value(
          value = "Nice job putting your extra cash to work.",
        ),
      ),
    ),
    EmailTemplateTableSection(
      rows = listOf(
        EmailTemplateTableSection.Row(
          key = "Last month",
          value = amountThisMonth.formatString(withDollarSign = true, withComma = true),
        ),
        EmailTemplateTableSection.Row(
          key = "Year-to-date",
          value = amountYearToDate.formatString(withDollarSign = true, withComma = true),
        ),
        EmailTemplateTableSection.Row(
          key = "Deposited to",
          value = "High yield",
        ),
      ),
    ),
    EmailTemplateButtonSection("Go to Highbeam", actionUrl),
    EmailTemplateFooterSection(),
  )
}
