package co.highbeam.email.template

data class ApplicationApprovedEmailTemplate(
  val recipient: Recipient,
  val businessName: String,
  val actionUrl: String,
) : EmailTemplate() {
  override val sender = Sender.HIGHBEAM

  override val recipients = listOf(recipient)

  override val subject = "Congrats! We’ve verified your business"

  override val sections = listOfNotNull(
    EmailTemplateHeaderSection(),
    EmailTemplateIconSection(EmailTemplateIconSection.Icon.Confetti),
    EmailTemplateTitleSection(
      title = "Congrats! $businessName has been verified on Highbeam",
      subtitle = "You're ready to get started with Highbeam banking. " +
        "It's easy and takes under 30 minutes."
    ),
    EmailTemplateButtonSection("Go to Highbeam", actionUrl),
    EmailTemplateSectionHeaderSection(
      value = "Up next - Onboard to banking",
      subtitle = "Follow these steps to onboard to Highbeam banking",
    ),
    EmailTemplateTextSection(
      listOf(
        EmailTemplateTextSection.Value(
          value = "Est time ~20 minutes",
          fontWeight = EmailTemplateTextSection.Value.FontWeight.Bold
        ),
      ),
      dense = true
    ),
    EmailTemplateImageSection(EmailTemplateImageSection.Image.OnboardingChecklist),
    EmailTemplateFooterSection(),
  )
}
