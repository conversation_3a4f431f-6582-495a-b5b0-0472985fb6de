package co.highbeam.email.template

import co.highbeam.money.Money
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale

data class ScheduledPaymentCanceledEmailTemplate(
  override val recipients: List<Recipient>,
  val accountLast4Digits: String,
  val accountName: String,
  val amount: Money,
  val counterpartyName: String,
  val scheduledPaymentDate: LocalDate?,
) : EmailTemplate() {
  override val sender = Sender.HIGHBEAM

  override val subject = "Your scheduled payment to $counterpartyName has been canceled"

  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateIconSection(EmailTemplateIconSection.Icon.Cross),
    EmailTemplateTitleSection(
      title = subject,
      subtitle = "",
    ),
    EmailTemplateSeparatorSection,
    EmailTemplateTableSection(
      listOfNotNull(
        EmailTemplateTableSection.Row("Amount", formatAmount(amount)),
        EmailTemplateTableSection.Row("To", counterpartyName),
        EmailTemplateTableSection.Row(
          "From",
          "$accountName •••• $accountLast4Digits"
        ),
        scheduledPaymentDate?.let {
          EmailTemplateTableSection.Row(
            key = "Scheduled for",
            value = it.format(DateTimeFormatter.ofPattern("MMM d, y", Locale.US))
          )
        },
      ),
    ),
    EmailTemplateFooterSection()
  )

  private fun formatAmount(amount: Money): String =
    amount.formatString(withDollarSign = true, withComma = true)
}
