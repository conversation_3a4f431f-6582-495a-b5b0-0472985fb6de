package co.highbeam.config

import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

data class EmailConfig(
  val enabled: Boolean,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val sendgridApiKey: ProtectedString?,
  val sendgridTemplateId: String?,
) {
  init {
    if (enabled) {
      requireNotNull(sendgridApiKey)
      requireNotNull(sendgridTemplateId)
    }
  }
}
