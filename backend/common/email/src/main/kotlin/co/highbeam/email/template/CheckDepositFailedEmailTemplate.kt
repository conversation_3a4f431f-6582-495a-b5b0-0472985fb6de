package co.highbeam.email.template

import co.highbeam.money.Money

data class CheckDepositFailedEmailTemplate(
  override val recipients: List<Recipient>,
  val amount: Money,
  val counterparty: String?,
  val failureReason: String,
) : EmailTemplate() {
  override val sender = Sender.HIGHBEAM

  override val subject = "Your check deposit ${counterparty.let{ "from $counterparty " }}" +
    "has failed"

  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateTitleSection(
      title = "Check deposit failed.",
      subtitle = "Your recent check deposit has failed.",
    ),
    EmailTemplateSeparatorSection,
    EmailTemplateSectionHeaderSection("Here are the details of this check deposit."),
    EmailTemplateTableSection(listOf(
      EmailTemplateTableSection.Row("Amount", amount.formatString(
        withDollarSign = true,
        withComma = true)
      ),
      EmailTemplateTableSection.Row("From", counterparty ?: "Unknown"),
      EmailTemplateTableSection.Row("Reason", failureReason),
    )),
    EmailTemplateFooterSection(),
  )
}
