package co.highbeam.email.template

import co.highbeam.money.Money
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale

data class ChargeCardAutomaticPaymentCompleteEmailTemplate(
  override val recipients: List<Recipient>,
  val capitalUrl: String,
  val closingBalance: Money,
  val highbeamBankAccountName: String,
  val periodStartDate: LocalDate,
  val periodEndDate: LocalDate,
  val capitalAccountName: String,
) : EmailTemplate() {
  override val sender = Sender.HIGHBEAM

  override val subject = "Auto payment for $capitalAccountName completed"

  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateSpacingSection,
    EmailTemplateIconSection(EmailTemplateIconSection.Icon.Card),
    EmailTemplateTitleSection(
      title = "Auto payment completed",
      subtitle = "Your outstanding $capitalAccountName balance has been paid."
    ),
    EmailTemplateTableSection(
      rows = listOf(
        EmailTemplateTableSection.Row(
          key = "Amount paid",
          value = closingBalance.formatString(withDollarSign = true, withComma = true),
        ),
        EmailTemplateTableSection.Row(
          key = "Statement period",
          value = "${formatMonthDay(periodStartDate)} to " +
            "${formatMonthDay(periodEndDate)}, ${getYear(periodEndDate)}",
        ),
        EmailTemplateTableSection.Row(
          key = "From",
          value = highbeamBankAccountName,
        ),
      ),
    ),
    EmailTemplateButtonSection("View details", capitalUrl),
    EmailTemplateFooterSection(),
  )

  private fun formatMonthDay(date: LocalDate): String {
    return date.format(DateTimeFormatter.ofPattern("MMM d", Locale.US))
  }

  private fun getYear(date: LocalDate): String {
    return date.format(DateTimeFormatter.ofPattern("yyyy", Locale.US))
  }
}
