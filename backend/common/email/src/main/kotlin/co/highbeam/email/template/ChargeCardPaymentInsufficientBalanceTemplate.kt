package co.highbeam.email.template

import co.highbeam.money.Balance
import co.highbeam.money.Money
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale

data class ChargeCardPaymentInsufficientBalanceTemplate(
  override val recipients: List<Recipient>,
  val capitalUrl: String,
  val outstandingBalance: Money,
  val highbeamBankAccountName: String,
  val highbeamBankAccountBalance: Balance,
  val repaymentDate: LocalDate,
) : EmailTemplate() {
  private val statementMonth = getStatementMonth(repaymentDate)

  override val sender = Sender.HIGHBEAM

  override val subject = "Your Highbeam Card auto pay account has an insufficient balance"

  override val sections = listOf(
    EmailTemplateHeaderSection(),
    EmailTemplateSpacingSection,
    EmailTemplateIconSection(EmailTemplateIconSection.Icon.Info),
    EmailTemplateTitleSection(
      title = subject,
      subtitle = "Please add funds or change your auto pay account. " +
        "If the balance is insufficient on the due date," +
        " it will convert into a draw down."
    ),
    EmailTemplateTableSection(
      rows = listOf(
        EmailTemplateTableSection.Row(
          key = "Outstanding $statementMonth balance",
          value = outstandingBalance.formatString(withDollarSign = true, withComma = true),
        ),
        EmailTemplateTableSection.Row(
          key = "Due date",
          value = formatFullDate(repaymentDate),
        ),
        EmailTemplateTableSection.Row(
          key = "Auto-pay from",
          value = highbeamBankAccountName,
          subtext = "Balance: ${
            highbeamBankAccountBalance.formatString(
              withDollarSign = true,
              withComma = true
            )
          }",
        ),
      ),
    ),

    EmailTemplateButtonSection("Change auto pay account", capitalUrl),
    EmailTemplateFooterSection(),
  )

  private fun formatFullDate(date: LocalDate): String {
    return date.format(DateTimeFormatter.ofPattern("MMM d, yyyy", Locale.US))
  }

  private fun getStatementMonth(date: LocalDate): String {
    val previousMonth = date.minusMonths(1)
    return previousMonth.format(DateTimeFormatter.ofPattern("MMMM", Locale.US))
  }
}
