package co.highbeam.client.backendV2.insights

import co.highbeam.api.backendV2.insights.InsightsSyncTaskApi
import co.highbeam.client.backendV2.BackendV2HttpClient
import com.google.inject.Inject

class InsightsSyncTaskClient @Inject constructor(
  private val httpClient: BackendV2HttpClient,
) {
  suspend fun request(endpoint: InsightsSyncTaskApi.SyncSingleTransaction): Unit =
    httpClient.request(endpoint).readValue()
}
