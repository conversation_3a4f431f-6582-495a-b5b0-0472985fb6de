package co.highbeam.feature.backendV2

import co.highbeam.client.backendV2.BackendV2HttpClient
import co.highbeam.config.backendV2.BackendV2Config
import co.highbeam.feature.Feature

class BackendV2Feature(private val config: BackendV2Config) : Feature() {
  override fun bind() {
    bind(BackendV2Config::class.java).toInstance(config)
    bind(BackendV2HttpClient::class.java).asEagerSingleton()
  }
}
