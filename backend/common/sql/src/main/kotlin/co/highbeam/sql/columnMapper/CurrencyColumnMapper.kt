package co.highbeam.sql.columnMapper

import org.jdbi.v3.core.mapper.ColumnMapper
import org.jdbi.v3.core.statement.StatementContext
import java.sql.ResultSet
import java.util.Currency

internal class CurrencyColumnMapper : ColumnMapper<Currency> {
  override fun map(r: ResultSet, columnNumber: Int, ctx: StatementContext): Currency? {
    val string = r.getString(columnNumber)
    return string?.let { Currency.getInstance(it) }
  }
}
