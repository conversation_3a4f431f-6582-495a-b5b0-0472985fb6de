package co.highbeam.feature.sql

import co.highbeam.config.SqlDatabaseConfig
import co.highbeam.metrics.Metrics
import com.google.inject.Inject
import com.google.inject.Provider
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource

internal class DataSourceProvider @Inject constructor(
  private val config: SqlDatabaseConfig,
  private val metrics: Metrics,
) : Provider<HikariDataSource> {
  override fun get(): HikariDataSource =
    createDataSource(config) {
      metricRegistry = metrics.meterRegistry
    }
}

fun createDataSource(
  config: SqlDatabaseConfig,
  configure: HikariConfig.() -> Unit = {},
): HikariDataSource {
  val hikariConfig = HikariConfig().apply {
    config.jdbcUrl.let { jdbcUrl = it }
    config.defaultSchema?.let { schema = it }
    config.username.let { username = it }
    config.password?.let { password = it.value }
    config.connectionTimeout?.let { connectionTimeout = it }
    config.minimumIdle?.let { minimumIdle = it }
    config.maximumPoolSize?.let { maximumPoolSize = it }
    config.properties.forEach { addDataSourceProperty(it.key, it.value) }

    configure()
  }
  return HikariDataSource(hikariConfig)
}
