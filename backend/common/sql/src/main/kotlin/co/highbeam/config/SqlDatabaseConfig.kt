package co.highbeam.config

import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

/**
 * This class encapsulates the configuration for the connection to a SQL database.
 */
data class SqlDatabaseConfig(
  @JsonDeserialize(using = ConfigStringDeserializer::class)
  val jdbcUrl: String,
  val defaultSchema: String? = null,
  @JsonDeserialize(using = ConfigStringDeserializer::class)
  val username: String,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val password: ProtectedString?,
  val runMigrations: Boolean,
  val connectionTimeout: Long? = null,
  val minimumIdle: Int? = null,
  val maximumPoolSize: Int? = null,
  val properties: Map<String, String> = emptyMap(),
)
