package co.highbeam.sql.argumentFactory

import co.highbeam.money.Balance
import org.jdbi.v3.core.argument.AbstractArgumentFactory
import org.jdbi.v3.core.argument.Argument
import org.jdbi.v3.core.config.ConfigRegistry
import java.sql.Types

internal class BalanceArgumentFactory : AbstractArgumentFactory<Balance>(Types.BIGINT) {
  override fun build(value: Balance, config: ConfigRegistry): Argument =
    Argument { position, statement, _ ->
      statement.setLong(position, value.rawCents)
    }
}
