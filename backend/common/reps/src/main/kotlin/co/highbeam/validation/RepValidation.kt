package co.highbeam.validation

import co.highbeam.rep.ValidatableRep
import kotlin.reflect.KProperty1

/**
 * Validation class for [ValidatableRep]s.
 */
class RepValidation(validation: Builder.() -> Unit) {
  companion object {
    fun none() = RepValidation {}
  }

  class Builder internal constructor() {
    private val validations: MutableList<ValueValidation<*>> = mutableListOf()

    fun <R : ValidatableRep, T : Any?> R.validate(
      property: KProperty1<R, T>,
      validator: T.() -> <PERSON><PERSON><PERSON>,
    ) {
      val value = property.get(this)
      val validation = ValueValidation(property.name, value, value.validator())
      validations.add(validation)
    }

    fun <R : ValidatableRep> R.validate(validator: R.() -> <PERSON><PERSON><PERSON>) {
      val validation = ValueValidation(null, this, validator())
      validations.add(validation)
    }

    @JvmName("validateRep")
    fun <R : ValidatableRep, T : ValidatableRep> R.validate(property: KProperty1<R, T>) {
      validateIfPresent(property)
    }

    @JvmName("validateRepIfPresent")
    fun <R : ValidatableRep, T : ValidatableRep> R.validateIfPresent(property: KProperty1<R, T?>) {
      val value = property.get(this) ?: return
      val subValidations = value.validate().validations
      validations.addAll(subValidations.map { it.withNamePrefix("${property.name}.") })
    }

    @JvmName("validateReps")
    fun <R : ValidatableRep, T : List<ValidatableRep>> R.validate(
      property: KProperty1<R, T>,
    ) {
      validateIfPresent(property)
    }

    @JvmName("validateRepsIfPresent")
    fun <R : ValidatableRep, T : List<ValidatableRep>> R.validateIfPresent(
      property: KProperty1<R, T?>,
    ) {
      val value = property.get(this) ?: return
      value.forEachIndexed { i, rep ->
        val subValidations = rep.validate().validations
        validations.addAll(subValidations.map { it.withNamePrefix("${property.name}[$i].") })
      }
    }

    internal fun build(): List<ValueValidation<*>> = validations
  }

  private val validations: List<ValueValidation<*>> = Builder()
    .apply { validation() }.build()

  val isValid: Boolean = validations.all { it.isValid }

  val invalidPropertyNames: List<String> by lazy {
    validations.filter { !it.isValid }.mapNotNull { it.name }
      .also { check(it.isNotEmpty()) }
  }
}
