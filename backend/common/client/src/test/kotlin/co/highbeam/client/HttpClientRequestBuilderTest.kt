package co.highbeam.client

import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import kotlin.test.assertFails

internal class HttpClientRequestBuilderTest {
  @Test
  fun default() {
    val requestBuilder = requestBuilder(ContentType.Application.Json)
    assertThat(requestBuilder.httpMethod).isEqualTo(HttpMethod.Get)
    assertThat(requestBuilder.href).isEqualTo("/foo/bar?baz=qux")
    assertThat(requestBuilder.url).isEqualTo("http://localhost:3000/foo/bar?baz=qux")
    assertThat(requestBuilder.headers).isEqualTo(mapOf("Accept" to "application/json"))
    assertThat(requestBuilder.contentType).isEqualTo(ContentType.Application.Json)
  }

  @Test
  fun `additional query params by builder`() {
    val requestBuilder = requestBuilder(ContentType.Application.Json)
    requestBuilder.addQueryParam("extra1", "value1")
    requestBuilder.addQueryParam("extra2", "value2")
    assertThat(requestBuilder.href)
      .isEqualTo("/foo/bar?baz=qux&extra1=value1&extra2=value2")
    assertThat(requestBuilder.url)
      .isEqualTo("http://localhost:3000/foo/bar?baz=qux&extra1=value1&extra2=value2")
  }

  @Test
  fun `different accept header passed in`() {
    val requestBuilder = requestBuilder(ContentType.Text.CSV)
    assertThat(requestBuilder.headers).isEqualTo(mapOf("Accept" to "text/csv"))
  }

  @Test
  fun `additional headers by builder`() {
    val requestBuilder = requestBuilder(ContentType.Application.Json)
    requestBuilder.putHeader(HttpHeaders.Age, "12345")
    requestBuilder.putHeader(HttpHeaders.If, "condition")
    assertThat(requestBuilder.headers).isEqualTo(mapOf(
      "Accept" to "application/json",
      "Age" to "12345",
      "If" to "condition",
    ))
  }

  @Test
  fun `overriding header by builder`() {
    val requestBuilder = requestBuilder(ContentType.Application.Json)
    requestBuilder.putHeader(HttpHeaders.Age, "12345")
    requestBuilder.putHeader(HttpHeaders.Age, "23456")
    requestBuilder.putHeader(HttpHeaders.Accept, "text/plain")
    assertFails {
      requestBuilder.putHeader(HttpHeaders.ContentType, ContentType.Application.Json.toString())
    }
    assertThat(requestBuilder.headers).isEqualTo(mapOf(
      "Accept" to "text/plain",
      "Age" to "23456",
    ))
  }

  private fun requestBuilder(contentType: ContentType) = HighbeamHttpClientRequestBuilder(
    httpMethod = HttpMethod.Get,
    baseUrl = "http://localhost:3000",
    path = "/foo/bar",
    qp = mapOf("baz" to listOf("qux")),
    accept = contentType,
  )
}
