package co.highbeam.client

import co.highbeam.client.exception.HighbeamHttpClientException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import io.ktor.http.isSuccess
import mu.KotlinLogging

abstract class HttpResponse(val objectMapper: ObjectMapper) {
  private val logger = KotlinLogging.logger {}

  abstract val statusCode: HttpStatusCode

  abstract val headers: Headers

  suspend inline fun <reified T> readValue(): T {
    val text = readText() ?: return null as T
    if (text.isEmpty()) return Unit as T
    return objectMapper.readValue(text)
  }

  suspend inline fun readValueBytes(): ByteArray? {
    return readBytes()
  }

  suspend fun readBytes(): ByteArray? {
    if (statusCode == HttpStatusCode.NotFound) {
      logger.debug { "Status code was $statusCode. Returning null." }
      return null
    }

    val responseBody = readResponseBodyBytes()
    if (statusCode.isSuccess()) {
      logger.debug { "Status code was $statusCode with response body $responseBody. Returning." }
      return responseBody
    }

    logger.debug { "Status code was $statusCode with response body $responseBody. Throwing." }
    throw HighbeamHttpClientException(statusCode, responseBody.decodeToString())
  }

  suspend fun readText(): String? {
    if (statusCode == HttpStatusCode.NotFound) {
      logger.debug { "Status code was $statusCode. Returning null." }
      return null
    }

    val responseBody = readResponseBody()
    if (statusCode.isSuccess()) {
      logger.debug { "Status code was $statusCode with response body $responseBody. Returning." }
      return responseBody
    }

    logger.debug { "Status code was $statusCode with response body $responseBody. Throwing." }
    throw HighbeamHttpClientException(statusCode, responseBody)
  }

  abstract suspend fun readResponseBody(): String

  abstract suspend fun readResponseBodyBytes(): ByteArray
}
