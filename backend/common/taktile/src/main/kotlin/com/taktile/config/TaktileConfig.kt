package com.taktile.config

import co.highbeam.config.ConfigStringDeserializer
import co.highbeam.config.ProtectedConfigStringDeserializer
import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.taktile.client.TaktileHttpClient

/**
 * This class uses renamed delegates in order to fail slowly when API key/secret is not provided.
 * This is useful for local development.
 */
data class TaktileConfig(
  val environment: TaktileHttpClient.Environment,
  @JsonDeserialize(using = ProtectedConfigStringDeserializer::class)
  val apiKey: ProtectedString,
  @JsonDeserialize(using = ConfigStringDeserializer::class)
  val clientId: String,
  @JsonDeserialize(using = ConfigStringDeserializer::class)
  val flowSlug: String,
  @JsonDeserialize(using = ConfigStringDeserializer::class)
  val flowVersion: String,
)
