package com.taktile.client

import co.highbeam.metrics.Metrics
import com.google.inject.Inject
import com.google.inject.Provider
import com.taktile.config.TaktileConfig

class TaktileProvider @Inject constructor(
  private val metrics: Metrics,
  private val taktileConfig: TaktileConfig,
) : Provider<TaktileClient> {
  override fun get(): TaktileClient {
    val httpClient = TaktileHttpClient(
      environment = taktileConfig.environment,
      metrics = metrics,
    )
    return TaktileClient(
      httpClient = httpClient,
      taktileApiKey = taktileConfig.apiKey,
      taktileClientId = taktileConfig.clientId,
      environment = taktileConfig.environment,
    )
  }
}
