@file:Suppress("MatchingDeclarationName")

package co.highbeam.config

import co.highbeam.util.testing.TestingOnly
import com.google.cloud.secretmanager.v1.ListSecretVersionsRequest
import com.google.cloud.secretmanager.v1.SecretManagerServiceClient
import com.google.cloud.secretmanager.v1.SecretName
import com.google.cloud.secretmanager.v1.SecretVersion
import com.google.cloud.secretmanager.v1.SecretVersionName

@RequiresOptIn(
  message = "These methods should only be used in testing and adhoc code. "
    + "Production code should fetch secrets though config instead.",
)
annotation class DirectSecretAccess


@Suppress("FunctionName")
fun DEFAULT_GET_ENV(name: String): String? = System.getenv(name)

/**
 * The [getEnv] getter defines how environment variables should be fetched for this entire library.
 * [DEFAULT_GET_ENV] should always be used in production code. Using this as a delegate function,
 * however, allows for proper testing.
 */
internal var getEnv: (name: String) -> String? = ::DEFAULT_GET_ENV
  @TestingOnly set


@DirectSecretAccess
@Suppress("FunctionName")
fun DEFAULT_GET_GCP_SECRET(
  projectId: String,
  secretId: String,
  versionId: String,
): String =
  SecretManagerServiceClient.create().use { client ->
    val accessResponse = if (versionId == "latest") {
      val latestActiveVersionId = getLatestActiveVersionId(projectId, secretId, client)
      client.accessSecretVersion(latestActiveVersionId.name)
    } else {
      client.accessSecretVersion(SecretVersionName.of(projectId, secretId, versionId))
    }
    return@use accessResponse.payload.data.toStringUtf8()
  }

private fun getLatestActiveVersionId(
  projectId: String,
  secretId: String,
  client: SecretManagerServiceClient
): SecretVersion {
  val parent = SecretName.of(projectId, secretId).toString()
  val latestVersion = client.listSecretVersions(
    ListSecretVersionsRequest.newBuilder()
      .setParent(parent)
      .build()
  ).iterateAll()
    .filter { it.state == SecretVersion.State.ENABLED }
    .maxByOrNull { it.createTime.seconds }
  check(latestVersion != null) { "No enabled version found for $parent" }
  return latestVersion
}

/**
 * The [getGcpSecret] getter defines how GCP secrets should be fetched for this entire library.
 * [DEFAULT_GET_GCP_SECRET] should always be used in production code. Using this as a delegate
 * function, however, allows for proper testing.
 */
@OptIn(DirectSecretAccess::class)
internal var getGcpSecret: (
  projectId: String,
  secretId: String,
  versionId: String,
) -> String = ::DEFAULT_GET_GCP_SECRET
  @TestingOnly set

@Suppress("FunctionName")
fun DEFAULT_RUN_COMMAND(command: String): String? {
  val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", command))
  return process.inputReader().readLines().singleNullOrThrow()
}

/**
 * The [runCommand] getter defines how shell commands should be run for this entire library.
 * [DEFAULT_RUN_COMMAND] should always be used in production code.
 */
internal val runCommand: (command: String) -> String? = ::DEFAULT_RUN_COMMAND
