package co.unit.rep

import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

internal class UnitCoCreditAccountRepTest : RepTest() {
  @Test
  fun `Freeze - reason as Fraud - serialize`() {
    val unitCreditAccountId = UUID.randomUUID().toString()

    val rep = UnitCoCreditAccountRep.Freeze(
      unitCoCreditAccountId = unitCreditAccountId,
      reason = UnitCoCreditAccountRep.FrozenReason.Fraud,
    )

    val expected = """
      {
        "attributes" : {
          "reason" : "Fraud"
        },
        "type" : "creditAccountFreeze"
      }
    """.trimIndent()

    val actual = objectMapper.writeValueAsString(rep)
    assertEquals(expected, actual)
  }

  @Test
  fun `Freeze - reason as Other - serialize`() {
    val unitCreditAccountId = UUID.randomUUID().toString()

    val rep = UnitCoCreditAccountRep.Freeze(
      unitCoCreditAccountId = unitCreditAccountId,
      reason = UnitCoCreditAccountRep.FrozenReason.Other("Capital account frozen."),
    )

    val expected = """
      {
        "attributes" : {
          "reason" : "Other",
          "reasonText" : "Capital account frozen."
        },
        "type" : "creditAccountFreeze"
      }
    """.trimIndent()

    val actual = objectMapper.writeValueAsString(rep)
    assertEquals(expected, actual)
  }
}
