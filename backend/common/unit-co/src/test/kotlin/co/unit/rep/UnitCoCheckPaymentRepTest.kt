package co.unit.rep

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import java.time.ZonedDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

internal class UnitCoCheckPaymentRepTest : RepTest() {
  @Test
  fun `complete - deserialize`() {
    val json = """
      {
        "type": "checkPayment",
        "id": "3",
        "attributes": {
          "createdAt": "2023-02-21T11:31:03.704Z",
          "updatedAt": "2023-02-21T11:31:03.704Z",
          "amount": 10000,
          "sendAt": "2023-09-10T12:50:00.704Z",
          "description": "Check Payment | 0322",
          "status": "Processed",
          "deliveryStatus": "Delivered",
          "trackedAt": "2023-02-23T11:31:03.704Z",
          "postalCode": "94303",
          "onUsAuxiliary": "0322",
          "onUs": "************/",
          "counterparty": {
            "name": "<PERSON>",
            "address": {
              "street": "5230 Newell Rd",
              "city": "Palo Alto",
              "state": "CA",
              "postalCode": "94303"
            }
          },
          "counterpartyRoutingNumber": "*********",
          "returnCutoffTime": "2023-03-23T15:50:00.000Z",
          "additionalVerificationStatus": "Required"
        },
        "relationships": {
          "account": {
            "data": {
              "type": "account",
              "id": "75002"
            }
          },
          "customer": {
            "data": {
              "type": "customer",
              "id": "100425"
            }
          },
          "customers": {
            "data": [
              {
                "type": "customer",
                "id": "10001"
              }
            ]
          },
          "transaction": {
            "data": {
              "type": "transaction",
              "id": "123423"
            }
          }
        }
      }
    """.trimIndent()

    val expected = UnitCoCheckPaymentRep.Complete(
      id = "3",
      createdAt = ZonedDateTime.parse("2023-02-21T11:31:03.704Z"),
      updatedAt = ZonedDateTime.parse("2023-02-21T11:31:03.704Z"),
      amount = Money.fromCents(10000),
      description = "Check Payment | 0322",
      accountId = "75002",
      customerId = "100425",
      counterparty = "John Doe",
      returnCutoffTime = ZonedDateTime.parse("2023-03-23T15:50:00.000Z"),
      status = "Processed",
      returnReason = null,
    )

    val actual = objectMapper.readValue<UnitCoCheckPaymentRep.Complete>(json)
    assertEquals(expected, actual)
  }
}
