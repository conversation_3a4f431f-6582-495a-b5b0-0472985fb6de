package co.unit.rep

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

internal class UnitCoWirePaymentRepTest : RepTest() {
  @Test
  fun `creator - serialize`() {
    val rep = UnitCoWirePaymentRep.Creator(
      amount = Money(100),
      inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
        routingNumber = "*********",
        accountNumber = "**********",
        name = "Shubham",
        address = AddressRep(
          street = "123 West 23rd Street",
          street2 = "Opposite shake shack",
          state = "NY",
          postalCode = "10036",
          country = "US",
          city = "New York",
        ),
      ),
      description = "Payment to Shubham",
      idempotencyKey = UUID.randomUUID(),
      payeeGuid = UUID.randomUUID(),
      fromAccountType = "depositAccount",
      fromAccountId = "884612",
    )

    val expected = """
      {
        "attributes" : {
          "amount" : 100,
          "counterparty" : {
            "accountNumber" : "**********",
            "address" : {
              "city" : "New York",
              "country" : "US",
              "postalCode" : "10036",
              "state" : "NY",
              "street" : "123 West 23rd Street",
              "street2" : "Opposite shake shack"
            },
            "name" : "Shubham",
            "routingNumber" : "*********"
          },
          "description" : "Payment to Shubham",
          "idempotencyKey" : "${rep.idempotencyKey}",
          "tags" : {
            "recipientGuid" : "${rep.payeeGuid}"
          }
        },
        "relationships" : {
          "account" : {
            "data" : {
              "id" : "884612",
              "type" : "depositAccount"
            }
          }
        },
        "type" : "wirePayment"
      }
    """.trimIndent()

    val actual = objectMapper.writeValueAsString(rep)
    assertEquals(expected, actual)
  }

  @Test
  fun `complete - deserialize`() {
    val payeeGuid = UUID.randomUUID()

    val json = """
     {
      "type": "wirePayment",
      "id": "1135",
      "attributes": {
        "createdAt": "2021-08-30T12:19:18.639Z",
        "amount": 200,
        "direction": "Credit",
        "description": "Wire payment",
        "counterparty": {
          "name": "April Oniel",
          "routingNumber": "*********",
          "accountNumber": "**********",
          "address": {
            "street": "20 Ingram St",
            "street2": "Opposite shake shack",
            "city": "Forest Hills",
            "state": "CA",
            "postalCode": "11375",
            "country": "US"
          }
        },
        "status": "Sent",
         "tags": {
           "recipientGuid": "$payeeGuid"
         }
      },
      "relationships": {
        "account": {
          "data": {
            "type": "account",
            "id": "10000"
          }
        },
        "customer": {
          "data": {
            "type": "customer",
            "id": "10000"
          }
        },
        "customers": {
          "data": [
            {
              "type": "customer",
              "id": "10000"
            }
          ]
        },
        "transaction": {
          "data": {
            "type": "transaction",
            "id": "4"
          }
        }
      }
    }
    """.trimIndent()

    val expected = UnitCoWirePaymentRep.Complete(
      id = "1135",
      createdAt = ZonedDateTime.parse("2021-08-30T12:19:18.639Z"),
      amount = Money(200),
      description = "Wire payment",
      inlineCounterparty = UnitCoWirePaymentRep.InlineCounterparty(
        routingNumber = "*********",
        accountNumber = "**********",
        name = "April Oniel",
        address = AddressRep(
          street = "20 Ingram St",
          street2 = "Opposite shake shack",
          state = "CA",
          postalCode = "11375",
          country = "US",
          city = "Forest Hills",
        ),
      ),
      payeeGuid = payeeGuid,
      status = UnitCoWirePaymentRep.Status.Sent,
      reason = null
    )

    val actual = objectMapper.readValue<UnitCoWirePaymentRep.Complete>(json)
    assertEquals(expected, actual)
  }
}
