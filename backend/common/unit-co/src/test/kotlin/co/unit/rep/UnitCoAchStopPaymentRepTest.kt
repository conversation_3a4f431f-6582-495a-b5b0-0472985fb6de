package co.unit.rep

import co.highbeam.money.Money
import com.fasterxml.jackson.module.kotlin.readValue
import org.assertj.core.api.Assertions.assertThat
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test

internal class UnitCoAchStopPaymentRepTest : RepTest() {
  @Test
  fun `creator - serialize`() {
    val idempotencyKey = UUID.randomUUID()

    val rep = UnitCoAchStopPaymentRep.Creator(
      unitAccountId = "884612",
      minAmount = Money(100),
      originatorName = emptyList(),
      expiration = null,
      isMultiUse = false,
      description = "Stop all payments",
      idempotencyKey = idempotencyKey,
    )

    assertThat(rep.validate().isValid).isTrue

    assertThat(objectMapper.writeValueAsString(rep)).isEqualTo(
      """
{
  "attributes" : {
    "direction" : "Debit",
    "idempotencyKey" : "$idempotencyKey",
    "minAmount" : 100
  },
  "relationships" : {
    "account" : {
      "data" : {
        "id" : "884612",
        "type" : "depositAccount"
      }
    }
  },
  "type" : "achStopPayment"
}
      """.trimIndent()
    )
  }

  @Test
  fun `complete - deserialize`() {
    val json = """
      {
        "type": "achStopPayment",
        "id": "13235",
        "attributes": {
          "createdAt": "2023-02-05T18:32:34.682Z",
          "updatedAt": "2023-02-05T18:32:34.682Z",
          "minAmount": 21000,
          "direction": "Debit",
          "isMultiUse": true,
          "expiration": "2025-02-05T18:32:34.682Z",
          "status": "Active",
          "description": "Test",
          "tags": {
            "test": "test"
          }
        },
        "relationships": {
          "account": {
            "data": {
              "type": "account",
              "id": "123456"
            }
          },
          "customer": {
            "data": {
              "type": "customer",
              "id": "95032"
            }
          },
          "customers": {
            "data": [
              {
                "type": "customer",
                "id": "95032"
              }
            ]
          }
        }
      }
    """.trimIndent()



    assertThat(objectMapper.readValue<UnitCoAchStopPaymentRep.Complete>(json))
      .isEqualTo(
        UnitCoAchStopPaymentRep.Complete(
          id = "13235",
          createdAt = ZonedDateTime.of(2023, 2, 5, 18, 32, 34, *********, ZoneOffset.UTC),
          updatedAt = ZonedDateTime.of(2023, 2, 5, 18, 32, 34, *********, ZoneOffset.UTC),
          minAmount = Money(21000),
          originatorName = emptyList(),
          expiration = ZonedDateTime.of(2025, 2, 5, 18, 32, 34, *********, ZoneOffset.UTC),
          isMultiUse = true,
          description = "Test",
          disableReason = null,
          unitAccountId = "123456",
          unitCustomerId = "95032"
        )
      )
  }
}
