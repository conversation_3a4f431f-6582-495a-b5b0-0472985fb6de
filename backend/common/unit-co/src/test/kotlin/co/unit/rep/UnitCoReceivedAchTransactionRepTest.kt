package co.unit.rep

import co.highbeam.money.Balance
import com.fasterxml.jackson.module.kotlin.readValue
import java.time.ZoneOffset
import java.time.ZonedDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

internal class UnitCoReceivedAchTransactionRepTest : RepTest() {
  @Test
  fun `complete - deserialize`() {
    val json = """
      {
        "type": "receivedAchTransaction",
        "id": "*********",
        "attributes": {
            "createdAt": "2023-11-16T08:59:56.684Z",
            "amount": 535335,
            "direction": "Credit",
            "balance": 99999,
            "description": "TRANSFER",
            "summary": "Shopify  |  TRANSFER",
            "companyName": "Shopify",
            "counterpartyRoutingNumber": "*********",
            "traceNumber": "***************",
            "secCode": "CCD"
        },
        "relationships": {
            "account": {
                "data": {
                    "type": "account",
                    "id": "222222"
                }
            },
            "customer": {
                "data": {
                    "type": "customer",
                    "id": "1"
                }
            },
            "customers": {
                "data": [
                    {
                        "type": "customer",
                        "id": "1"
                    }
                ]
            },
            "org": {
                "data": {
                    "type": "org",
                    "id": "65"
                }
            }
        }
      }
    """.trimIndent()

    val expected = UnitCoReceivedAchTransactionRep(
      id = "*********",
      type = "receivedAchTransaction",
      createdAt = ZonedDateTime.of(2023, 11, 16, 8, 59, 56, 684_000_000, ZoneOffset.UTC),
      businessGuid = null,
      bankAccountId = "222222",
      bankAccountGuid = null,
      generalPaymentMetadataGuid = null,
      transactionGuid = null,
      amount = Balance.fromCents(5353_35),
      balance = Balance.fromCents(999_99),
      summary = "Shopify  |  TRANSFER",
      companyName = "Shopify",
      counterpartyRoutingNumber = "*********",
      json = objectMapper.readValue(json),
      transactionType = null,
    )

    val actual = objectMapper.readValue<UnitCoTransactionRep>(json)
    assertEquals(expected, actual)
  }
}
