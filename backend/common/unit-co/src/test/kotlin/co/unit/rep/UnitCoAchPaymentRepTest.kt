package co.unit.rep

import co.highbeam.money.Money
import co.highbeam.money.MoneyDirection
import com.fasterxml.jackson.module.kotlin.readValue
import org.assertj.core.api.Assertions.assertThat
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test

internal class UnitCoAchPaymentRepTest : RepTest() {
  @Test
  fun `creator - serialize`() {
    val rep = UnitCoAchPaymentRep.Creator(
      amount = Money(100),
      direction = MoneyDirection.Credit,
      counterparty = UnitCoAchPaymentRep.InlineCounterparty(
        routingNumber = "*********",
        accountNumber = "**********",
        accountType = UnitCoAchPaymentRep.AccountType.Checking,
        name = "<PERSON>bha<PERSON>",
      ),
      description = "Payment to",
      addenda = " Shubham",
      idempotencyKey = UUID.randomUUID(),
      payeeGuid = UUID.randomUUID(),
      fromAccountType = "depositAccount",
      fromAccountId = "884612",
      payeeEmail = "<EMAIL>"
    )

    assertThat(rep.validate().isValid).isTrue

    assertThat(objectMapper.writeValueAsString(rep)).isEqualTo(
      """
      {
        "attributes" : {
          "addenda" : " Shubham",
          "amount" : 100,
          "counterparty" : {
            "accountNumber" : "**********",
            "accountType" : "Checking",
            "name" : "Shubham",
            "routingNumber" : "*********"
          },
          "description" : "Payment to",
          "direction" : "Credit",
          "idempotencyKey" : "${rep.idempotencyKey}",
          "sameDay" : false,
          "tags" : {
            "payeeEmail" : "${rep.payeeEmail}",
            "recipientGuid" : "${rep.payeeGuid}"
          }
        },
        "relationships" : {
          "account" : {
            "data" : {
              "id" : "884612",
              "type" : "depositAccount"
            }
          }
        },
        "type" : "achPayment"
      }
    """.trimIndent()
    )
  }

  @Test
  fun `complete - deserialize`() {
    val payeeGuid = UUID.randomUUID()

    val json = """
      {
        "type": "achPayment",
        "id": "154",
        "attributes": {
          "createdAt": "2022-07-14T19:34:38.462Z",
          "amount": 100,
          "direction": "Credit",
          "description": "Payment to",
          "addenda": " Shubham",
          "counterparty": {
            "name": "Shubham",
            "routingNumber": "*********",
            "accountNumber": "**********",
            "accountType": "Checking"
          },
          "status": "Sent",
          "tags": {
            "recipientGuid": "$payeeGuid"
          }
        },
        "relationships": {
          "account": {
            "data": {
              "type": "account",
              "id": "602675"
            }
          },
          "customer": {
            "data": {
              "type": "customer",
              "id": "431742"
            }
          },
          "customers": {
            "data": [
              {
                "type": "customer",
                "id": "431742"
              }
            ]
          },
          "transaction": {
            "data": {
              "type": "transaction",
              "id": "1532146"
            }
          },
          "org": {
            "data": {
              "type": "org",
              "id": "553"
            }
          }
        }
      }
    """.trimIndent()

    assertThat(objectMapper.readValue<UnitCoAchPaymentRep.Complete>(json))
      .isEqualTo(
        UnitCoAchPaymentRep.Complete(
          id = "154",
          createdAt = ZonedDateTime.parse("2022-07-14T19:34:38.462Z"),
          amount = Money(100),
          direction = MoneyDirection.Credit,
          description = "Payment to",
          addenda = "Shubham", // Space gets removed (debt/bug). Resolved when we use Unit.co SDK.
          counterparty = UnitCoAchPaymentRep.InlineCounterparty(
            routingNumber = "*********",
            accountNumber = "**********",
            accountType = UnitCoAchPaymentRep.AccountType.Checking,
            name = "Shubham",
          ),
          payeeGuid = payeeGuid,
          status = UnitCoAchPaymentRep.Status.Sent,
          reason = null,
        )
      )
  }
}
