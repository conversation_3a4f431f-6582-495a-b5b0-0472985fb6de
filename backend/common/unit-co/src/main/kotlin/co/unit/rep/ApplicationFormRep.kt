package co.unit.rep

import java.util.UUID

object ApplicationFormRep {
  data class Creation(
    val applicantDetails: ApplicantDetails,
    val tags: Tags,
    val allowedApplicationTypes: List<String>
  ) {
    data class ApplicantDetails(
      val name: String?,
      val phone: PhoneRep?,
      val contact: BusinessContactRep?,
    ) {
      val industry: String = "OnlineRetailer"
      val entityType: String = "LLC"
      val businessVertical: String = "RetailTrade"
    }

    data class Tags(val businessGuid: UUID)

    internal fun toRep(): UnitCreationRep<Creation> = UnitCreationRep(
      type = "applicationForm",
      attributes = this,
    )
  }

  data class Complete(
    val stage: String,
    val url: String,
    val tags: Tags,
  ) {
    data class Tags(val businessGuid: UUID)
  }
}
