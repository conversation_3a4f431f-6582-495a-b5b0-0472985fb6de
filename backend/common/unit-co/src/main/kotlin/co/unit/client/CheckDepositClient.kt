package co.unit.client

import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoCheckDepositRep
import io.ktor.http.HttpMethod
import mu.KotlinLogging

class CheckDepositClient internal constructor(private val request: UnitCoRequest) {
  private val logger = KotlinLogging.logger {}

  suspend fun get(
    checkDepositId: String,
  ): UnitCoCheckDepositRep.Complete? {
    logger.info { "Getting Unit check deposit account with ID $checkDepositId." }
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/check-deposits/$checkDepositId",
    )
    return request.readValue<DataWrapper<UnitCoCheckDepositRep.Complete>?>()?.data
  }
}
