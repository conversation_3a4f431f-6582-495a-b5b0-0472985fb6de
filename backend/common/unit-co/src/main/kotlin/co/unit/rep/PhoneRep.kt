package co.unit.rep

import co.highbeam.util.phone.parsePhoneNumber

/**
 * https://docs.unit.co/types/#phone
 */
data class PhoneRep(
  val countryCode: String,
  val number: String,
) {
  companion object {
    fun fromString(phoneNumber: String): PhoneRep {
      val (countryCode, number) = parsePhoneNumber(phoneNumber)
      return PhoneRep(countryCode = countryCode, number = number)
    }
  }

  fun toE164() = "+$countryCode $number"
}
