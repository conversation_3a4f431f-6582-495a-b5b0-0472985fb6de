package co.unit.client

import co.unit.rep.DataWrapper
import co.unit.rep.DepositAccountRep
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.util.UUID

class DepositAccountClient internal constructor(
  private val request: UnitCoRequest,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun create(
    creator: DepositAccountRep.Creator,
  ): DepositAccountRep.Complete {
    logger.info { "Creating Unit deposit account: $creator." }
    val request = request.request(
      httpMethod = HttpMethod.Post,
      path = "/accounts",
      body = DataWrapper(creator),
    )
    return request.readValue<DataWrapper<DepositAccountRep.Complete>>().data
  }

  suspend fun get(
    accountId: String,
  ): DepositAccountRep.Complete? {
    logger.info { "Getting Unit deposit account with ID $accountId." }
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/accounts/$accountId",
    )
    return request.readValue<DataWrapper<DepositAccountRep.Complete>?>()?.data
  }

  suspend fun getByBusiness(
    businessGuid: UUID,
  ): List<DepositAccountRep.Complete> {
    logger.info { "Getting Unit deposit accounts for business $businessGuid." }
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/accounts",
      qp = buildMap {
        put("filter[tags]", listOf("{\"businessGuid\":\"$businessGuid\"}"))
        put("filter[type]", listOf("deposit"))
      },
    )
    return request.readValue<DataWrapper<List<DepositAccountRep.Complete>>>().data
  }

  suspend fun getAll(
    status: DepositAccountRep.Status = DepositAccountRep.Status.Open,
  ): Flow<List<DepositAccountRep.Complete>> {
    logger.info { "Getting all Unit deposit accounts with $status." }

    return request.paginatedRequest(
      request = { paginationParams ->
        val request = request.request(
          httpMethod = HttpMethod.Get,
          path = "/accounts",
          qp = paginationParams + buildMap {
            put("filter[type]", listOf("deposit"))
            put("filter[status]", listOf(status.toString()))
          },
        )
        return@paginatedRequest request.readValue()
      },
      getResult = { it.data },
    )
  }

  suspend fun update(
    accountId: String,
    updater: DepositAccountRep.Updater,
  ): DepositAccountRep.Complete {
    logger.info { "Updating Unit deposit account with ID $accountId and $updater" }
    val request = request.request(
      httpMethod = HttpMethod.Patch,
      path = "/accounts/$accountId",
      body = DataWrapper(updater),
    )
    return request.readValue<DataWrapper<DepositAccountRep.Complete>>().data
  }

  suspend fun close(
    accountId: String,
    closeRep: DepositAccountRep.Close,
  ): DepositAccountRep.Complete {
    logger.info { "Closing Unit deposit account with ID $accountId." }
    val request = request.request(
      httpMethod = HttpMethod.Post,
      path = "/accounts/$accountId/close",
      body = DataWrapper(closeRep),
    )
    return request.readValue<DataWrapper<DepositAccountRep.Complete>>().data
  }
}
