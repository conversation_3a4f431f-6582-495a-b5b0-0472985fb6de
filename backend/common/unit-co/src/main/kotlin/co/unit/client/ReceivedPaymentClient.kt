package co.unit.client

import co.unit.rep.DataWrapper
import co.unit.rep.UnitAchReceivedPaymentRep
import com.fasterxml.jackson.databind.ObjectMapper
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class ReceivedPaymentClient internal constructor(
  private val request: UnitCoRequest,
  private val objectMapper: ObjectMapper,
) {
  private val logger = KotlinLogging.logger {}

  fun list(
    customerId: String?,
    accountId: String?,
    from: ZonedDateTime? = null,
    to: ZonedDateTime? = null,
    tags: Map<String, String>? = null,
    statuses: List<String>? = null,
  ): Flow<List<UnitAchReceivedPaymentRep>> {
    return request.paginatedRequest(
      request = { paginationParams ->
        val request = request.request(
          httpMethod = HttpMethod.Get,
          path = "/received-payments",
          qp = paginationParams + buildMap {
            customerId?.let { put("filter[customerId]", listOf(customerId)) }
            accountId?.let { put("filter[accountId]", listOf(accountId)) }
            from?.let {
              put(
                "filter[since]",
                listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
              )
            }
            to?.let {
              put(
                "filter[until]",
                listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
              )
            }
            tags?.let { put("filter[tags]", listOf(objectMapper.writeValueAsString(it))) }
            statuses?.let {
              it.forEachIndexed { idx, status ->
                put("filter[status][${idx}]", listOf(status))
              }
            }

          },
        )
        return@paginatedRequest request.readValue()
      },
      getResult = { it.data }
    )
  }

  suspend fun getReceivedPayment(
    receivedPaymentId: String,
  ): UnitAchReceivedPaymentRep? {
    logger.info { "Getting UnitCo receivedPaymentId=$receivedPaymentId." }

    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/received-payments/$receivedPaymentId",
    )
    return request.readValue<DataWrapper<UnitAchReceivedPaymentRep>?>()?.data
  }

  suspend fun reprocess(
    receivedPaymentId: String,
  ): UnitAchReceivedPaymentRep {
    logger.info { "Reprocessing UnitCo receivedPaymentId=$receivedPaymentId." }

    val request = request.request(
      httpMethod = HttpMethod.Post,
      path = "/received-payments/$receivedPaymentId/reprocess",
    )

    return request.readValue<DataWrapper<UnitAchReceivedPaymentRep>>().data
  }
}
