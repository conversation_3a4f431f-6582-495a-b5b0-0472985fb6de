package co.unit.client

import co.highbeam.money.Balance
import co.unit.rep.DataWrapper
import co.unit.rep.PaginatedDataWrapper
import co.unit.rep.TransactionAccountType
import co.unit.rep.UnitCoTransactionRep
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import io.ktor.http.HttpMethod
import kotlinx.coroutines.flow.Flow
import mu.KotlinLogging
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class TransactionClient internal constructor(
  private val request: UnitCoRequest,
  private val objectMapper: ObjectMapper,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun get(
    accountId: String,
    transactionId: String,
  ): UnitCoTransactionRep? {
    logger.info {
      "Getting Unit transaction with ID $transactionId" +
        " from deposit account with ID $accountId."
    }
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/accounts/$accountId/transactions/$transactionId",
    )
    return request.readValue<DataWrapper<UnitCoTransactionRep>?>()?.data
  }

  /**
   * Used for paginated requests to /transactions since it makes paginated requests using flows.
   */
  suspend fun list(
    customerId: String?,
    accountId: String?,
    query: String?,
    from: ZonedDateTime?,
    to: ZonedDateTime?,
    tags: Map<String, String>? = null,
    types: List<String>? = null,
    fromAmount: Balance? = null,
    toAmount: Balance? = null,
    accountType: TransactionAccountType? = null,
    cardId: String? = null,
    sort: String? = null,
    accountIds: List<String>? = null,
  ): Flow<JsonNode> {
    return request.paginatedRequest(
      request = { paginationParams ->
        val request = request.request(
          httpMethod = HttpMethod.Get,
          path = "/transactions",
          qp = paginationParams + buildMap {
            customerId?.let { put("filter[customerId]", listOf(customerId)) }
            cardId?.let { put("filter[cardId]", listOf(cardId)) }
            accountId?.let { put("filter[accountId]", listOf(accountId)) }
            accountType?.let { put("filter[accountType]", listOf(it.value)) }
            query?.let { put("filter[query]", listOf(it)) }
            from?.let {
              put(
                "filter[since]",
                listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
              )
            }
            to?.let {
              put(
                "filter[until]",
                listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
              )
            }
            tags?.let { put("filter[tags]", listOf(objectMapper.writeValueAsString(it))) }
            types?.let {
              it.forEachIndexed { idx, type ->
                put("filter[type][${idx}]", listOf(type))
              }
            }
            accountIds?.let {
              it.forEachIndexed { idx, account ->
                put("filter[accountIds][${idx}]", listOf(account))
              }
            }
            fromAmount?.let {
              put(
                "filter[fromAmount]",
                listOf(fromAmount.rawCents.toString())
              )
            }
            toAmount?.let {
              put(
                "filter[toAmount]",
                listOf(toAmount.rawCents.toString())
              )
            }
            sort?.let {
              put(
                "sort",
                listOf(sort)
              )
            }
            put("include", listOf("account"))
          },
        )
        request.readValue()
      },
      getResult = { objectMapper.convertValue(it) },
      isLastPage = { wrapper, limit ->
        objectMapper.convertValue<List<JsonNode>>(wrapper.data).size < limit
      },
    )
  }

  /**
   * Used for non-paginated requests to /transactions. For paginated requests, use
   * [TransactionClient.list].
   */
  suspend fun list(
    accountId: String?,
    customerId: String? = null,
    direction: String? = null,
    from: ZonedDateTime?,
    to: ZonedDateTime?,
    limit: Long?,
    query: String? = null,
    sort: String? = null,
    type: String? = null,
  ): List<UnitCoTransactionRep> {
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/transactions",
      qp = buildMap {
        accountId?.let { put("filter[accountId]", listOf(accountId)) }
        customerId?.let { put("filter[customerId]", listOf(customerId)) }
        from?.let {
          put(
            "filter[since]",
            listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
          )
        }
        to?.let {
          put(
            "filter[until]",
            listOf(it.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
          )
        }
        sort?.let {
          put(
            "sort",
            listOf(sort)
          )
        }
        put("include", listOf("account"))
        limit?.let {
          put(
            "page[limit]",
            listOf(limit.toString())
          )
        }
        direction?.let {
          put(
            "filter[direction][]",
            listOf(direction)
          )
        }
        query?.let {
          put(
            "filter[query]",
            listOf(query)
          )
        }
        type?.let {
          put(
            "filter[type][]",
            listOf(type)
          )
        }
      },
    )
    return request.readValue<PaginatedDataWrapper<List<UnitCoTransactionRep>>>().data
  }

  suspend fun update(
    accountId: String,
    transactionId: String,
    update: UnitCoTransactionRep.Updater,
  ): UnitCoTransactionRep? {
    logger.info {
      "Updating Unit transaction with ID $transactionId" +
        " from deposit account with ID $accountId: $update."
    }
    val request = request.request(
      httpMethod = HttpMethod.Patch,
      path = "/accounts/$accountId/transactions/$transactionId",
      body = DataWrapper(update),
    )
    return request.readValue<DataWrapper<UnitCoTransactionRep>?>()?.data
  }
}
