package co.unit.client

import co.unit.rep.DataWrapper
import co.unit.rep.UnitCoAchRepaymentRep
import io.ktor.http.HttpMethod
import mu.KotlinLogging

class RepaymentClient internal constructor(
  private val request: UnitCoRequest,
) {
  private val logger = KotlinLogging.logger {}

  suspend fun createAch(
    creator: UnitCoAchRepaymentRep.Creator,
  ): UnitCoAchRepaymentRep.Complete {
    logger.info { "Creating Unit ACH repayment: $creator." }
    val request = request.request(
      httpMethod = HttpMethod.Post,
      path = "/repayments",
      body = DataWrapper(creator),
    )
    return request.readValue<DataWrapper<UnitCoAchRepaymentRep.Complete>>().data
  }

  suspend fun list(
    creditAccountId: String,
    pageLimit: Int? = null,
    pageOffset: Int? = null,
  ): List<UnitCoAchRepaymentRep.Complete> {
    logger.info { "Fetching repayments for creditAccount=$creditAccountId." }
    val request = request.request(
      httpMethod = HttpMethod.Get,
      path = "/repayments",
      qp = buildMap {
        put("filter[creditAccountId]", listOf(creditAccountId))
        pageLimit?.let { put("page[limit]", listOf(it.toString())) }
        pageOffset?.let { put("page[offset]", listOf(it.toString())) }
      }
    )
    return request.readValue<DataWrapper<List<UnitCoAchRepaymentRep.Complete>>>().data
  }
}
