package com.rutter.rep

import co.highbeam.money.Balance
import co.highbeam.serialization.readValueNotNull
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer

object RutterBalanceRep {
  @JsonDeserialize(using = Complete.Deserializer::class)
  data class Complete(
    val currency: String,
    val amount: Balance,
    val json: JsonNode,
  ) {
    internal class Deserializer : StdDeserializer<Complete>(Complete::class.java) {
      override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Complete {
        val tree = p.readValueAsTree<JsonNode>()
        return Complete(
          currency = tree.readValueNotNull(ctxt, "iso_currency_code"),
          amount = tree.readValueNotNull(ctxt, "amount"),
          json = tree,
        )
      }
    }
  }
}
