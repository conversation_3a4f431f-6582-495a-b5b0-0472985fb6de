package com.rutter.rep

import co.highbeam.money.Balance
import co.highbeam.serialization.readValueNotNull
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import java.time.ZonedDateTime

object RutterTransactionRep {
  enum class Type {
    Sale,
    Refund,
    Fee,
    Void,
    Payout,
    Other;
  }

  enum class Status {
    Success,
    Failure,
    Pending,
    Canceled,
    Refunded,
    Other;
  }

  @JsonDeserialize(using = Complete.Deserializer::class)
  data class Complete(
    val rutterGuid: String,
    val type: String,
    val currency: String,
    val amount: Balance,
    val status: String,
    val initiatedAt: ZonedDateTime,
    val json: JsonNode,
  ) {
    internal class Deserializer : StdDeserializer<Complete>(Complete::class.java) {
      override fun deserialize(p: J<PERSON><PERSON>ars<PERSON>, ctxt: DeserializationContext): Complete {
        val tree = p.readValueAsTree<JsonNode>()
        return Complete(
          rutterGuid = tree.readValueNotNull(ctxt, "id"),
          type = tree.readValueNotNull(ctxt, "type"),
          currency = tree.readValueNotNull(ctxt, "iso_currency_code"),
          amount = tree.readValueNotNull(ctxt, "amount"),
          status = tree.readValueNotNull(ctxt, "status"),
          initiatedAt = tree.readValueNotNull(ctxt, "created_at"),
          json = tree,
        )
      }
    }
  }
}
