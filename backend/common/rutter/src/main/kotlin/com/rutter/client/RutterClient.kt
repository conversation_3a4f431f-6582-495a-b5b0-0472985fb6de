package com.rutter.client

import co.highbeam.client.HighbeamHttpClientRequestBuilder
import co.highbeam.client.HttpResponse
import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.databind.JsonNode
import io.ktor.http.HttpHeaders
import io.ktor.util.encodeBase64
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

const val RUTTER_API_SECRET = "RUTTER_API_SECRET"
const val RUTTER_CLIENT_ID = "RUTTER_CLIENT_ID"

abstract class RutterClient {
  protected fun HighbeamHttpClientRequestBuilder.setAuthToken(
    accessToken: ProtectedString,
    clientId: String,
    apiSecret: ProtectedString
  ) {
    putHeader(
      key = HttpHeaders.Authorization,
      value = "Basic " + (clientId + ":" + apiSecret.value).encodeBase64()
    )
    addQueryParam("access_token", accessToken.value)
  }

  protected fun <T : Any> paginatedRequest(
    request: suspend (nextCursor: String?) -> HttpResponse,
    getResults: suspend (response: HttpResponse) -> List<T>,
  ): Flow<List<T>> =
    flow {
      var nextCursor: String? = null
      while (true) {
        val response = request(nextCursor)
        emit(getResults(response))
        nextCursor = response.nextCursor() ?: break
      }
    }

  private suspend fun HttpResponse.nextCursor(): String? {
    val resp = readValue<JsonNode>()
    if (resp.get("next_cursor") == null || resp.get("next_cursor").toString() == "null") {
      return null
    }
    return resp.get("next_cursor").toString()
  }
}
