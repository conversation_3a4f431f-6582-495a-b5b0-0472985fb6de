package co.highbeam.hashing

import com.google.common.hash.HashCode
import com.google.common.hash.HashFunction
import org.apache.commons.codec.binary.Hex
import java.nio.charset.Charset
import java.util.Base64

fun HashFunction.hashString(input: CharSequence): HashCode =
  hashString(input, Charset.defaultCharset())

fun HashCode.base64Encoded(): String =
  Base64.getEncoder().encodeToString(asBytes())

fun HashCode.asHex(): String =
  Hex.encodeHexString(asBytes())
