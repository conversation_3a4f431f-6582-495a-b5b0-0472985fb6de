package co.highbeam.testing

import co.highbeam.config.ConfigLoader
import co.highbeam.config.ServerConfig
import co.highbeam.server.JobRunnerServer
import co.highbeam.server.Server
import co.highbeam.testing.integration.AbstractIntegrationTest
import co.highbeam.testing.integration.AbstractIntegrationTestExtension
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.extension.ExtensionContext

@ExtendWith(JobRunnerServerIntegrationTest.Extension::class)
internal abstract class JobRunnerServerIntegrationTest(
  server: Server<*>,
) : AbstractIntegrationTest(server) {
  internal class Extension : AbstractIntegrationTestExtension() {
    private companion object {
      val sharedState = SharedState()
      val config: ServerConfig = ConfigLoader.load("test")
    }

    override fun beforeAll(context: ExtensionContext) {
      sharedState.ensureStarted(context) { JobRunnerServer(config) }
    }

    override fun stop() {
      sharedState.stop()
    }
  }
}
