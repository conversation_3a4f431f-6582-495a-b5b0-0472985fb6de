package co.highbeam.feature.clients

import co.highbeam.client.BackendHttpClient
import co.highbeam.client.HttpClient
import co.highbeam.config.Hosts
import co.highbeam.feature.rest.INTERNAL_TOKEN
import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.Inject
import com.google.inject.Provider
import com.google.inject.name.Named

internal class PaymentHttpClientProvider @Inject constructor(
  private val hosts: Hosts,
  private val metrics: Metrics,
  private val objectMapper: ObjectMapper,
  @Named(INTERNAL_TOKEN) private val internalToken: ProtectedString,
) : Provider<HttpClient> {
  override fun get(): HttpClient = BackendHttpClient(
    baseUrl = hosts.backend,
    metrics = metrics,
    objectMapper = objectMapper,

    /**
     * Matches the values in UnitCoHttpClient
     */
    connectTimeoutMillis = 30_000,
    requestTimeoutMillis = 180_000,

    token = internalToken,
  )
}
