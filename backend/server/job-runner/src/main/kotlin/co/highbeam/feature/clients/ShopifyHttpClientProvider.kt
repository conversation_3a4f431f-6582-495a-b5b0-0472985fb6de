package co.highbeam.feature.clients

import co.highbeam.metrics.Metrics
import com.google.inject.Inject
import com.google.inject.Provider
import com.shopify.client.ShopifyHttpClient

internal class ShopifyHttpClientProvider @Inject constructor(
  private val metrics: Metrics,
) : Provider<ShopifyHttpClient> {
  /**
   * We use Shopify prod env for all Highbeam envs since Highbeam doesn't
   * perform any write operations. So prod accounts will not be affected
   * by actions in other envs.
   */
  override fun get(): ShopifyHttpClient = ShopifyHttpClient(
    baseUrl = "https://{shopSubdomain}.myshopify.com",
    metrics = metrics,
  )
}
