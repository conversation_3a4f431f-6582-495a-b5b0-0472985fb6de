package co.highbeam.feature.clients

import co.highbeam.config.NotionConfig
import co.highbeam.metrics.Metrics
import co.highbeam.notion.NotionHttpClient
import com.google.inject.Inject
import com.google.inject.Provider

internal class NotionHttpClientProvider @Inject constructor(
  private val notionConfig: NotionConfig,
  private val metrics: Metrics,
) : Provider<NotionHttpClient> {
  override fun get(): NotionHttpClient = NotionHttpClient(
    baseUrl = notionConfig.baseUrl,
    metrics = metrics,
    notionVersion = notionConfig.notionVersion,
    secret = notionConfig.secret,
  )
}
