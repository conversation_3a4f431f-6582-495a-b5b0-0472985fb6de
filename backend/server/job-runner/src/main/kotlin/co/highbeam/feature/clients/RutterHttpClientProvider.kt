package co.highbeam.feature.clients

import co.highbeam.config.RutterConfig
import co.highbeam.metrics.Metrics
import com.google.inject.Inject
import com.google.inject.Provider
import com.rutter.client.RutterHttpClient

internal class RutterHttpClientProvider @Inject constructor(
  private val rutterConfig: RutterConfig,
  private val metrics: Metrics,
) : Provider<RutterHttpClient> {
  override fun get(): RutterHttpClient = RutterHttpClient(
    baseUrl = rutterConfig.baseUrl,
    version = rutterConfig.version,
    metrics = metrics,
  )
}
