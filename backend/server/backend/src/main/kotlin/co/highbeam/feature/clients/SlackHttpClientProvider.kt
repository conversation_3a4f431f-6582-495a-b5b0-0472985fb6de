package co.highbeam.feature.clients

import co.highbeam.metrics.Metrics
import com.google.inject.Inject
import com.google.inject.Provider
import com.slack.client.SlackHttpClient

internal class SlackHttpClientProvider @Inject constructor(
  private val metrics: Metrics,
) : Provider<SlackHttpClient> {
  override fun get(): SlackHttpClient = SlackHttpClient(
    baseUrl = "https://hooks.slack.com/",
    metrics = metrics,
  )
}
