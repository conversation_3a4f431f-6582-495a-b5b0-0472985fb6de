package co.highbeam.feature.clients

import co.highbeam.businessMetrics.feature.METRICS_FEATURE
import co.highbeam.capital.account.feature.CAPITAL_ACCOUNT_FEATURE
import co.highbeam.capital.chargeCard.feature.CHARGE_CARD_FEATURE
import co.highbeam.capital.repayment.feature.CAPITAL_REPAYMENT_FEATURE
import co.highbeam.capital.repaymentschedule.feature.REPAYMENT_SCHEDULE_FEATURE
import co.highbeam.capital.transaction.feature.CAPITAL_TRANSACTION_FEATURE
import co.highbeam.client.HttpClient
import co.highbeam.config.Auth0Config
import co.highbeam.config.Hosts
import co.highbeam.config.NotionConfig
import co.highbeam.config.UnitCoConfig
import co.highbeam.feature.Feature
import co.highbeam.feature.auth.AUTH_FEATURE
import co.highbeam.feature.backendV2Email.BACKEND_V2_EMAIL_FEATURE
import co.highbeam.feature.bankAccounts.BANK_ACCOUNTS_FEATURE
import co.highbeam.feature.bankAccounts.PLAID_BANK_ACCOUNTS_FEATURE
import co.highbeam.feature.business.BUSINESS_DETAILS_FEATURE
import co.highbeam.feature.business.BUSINESS_FEATURE
import co.highbeam.feature.connect.CONNECT_FEATURE
import co.highbeam.feature.credit.CREDIT_FEATURE
import co.highbeam.feature.evaluations.EVALUATIONS_FEATURE
import co.highbeam.feature.healthCheck.HEALTH_CHECK_FEATURE
import co.highbeam.feature.job.JOB_FEATURE
import co.highbeam.feature.onboarding.ONBOARDING_FEATURE
import co.highbeam.feature.paymentV2.PAYMENT_FEATURE
import co.highbeam.feature.transfer.TRANSFER_FEATURE
import co.highbeam.feature.treasury.TREASURY_FEATURE
import co.highbeam.feature.users.USER_FEATURE
import co.highbeam.notion.NotionHttpClient
import co.unit.client.UnitCoClient
import com.auth0.client.Auth0ManagementHttpClient
import com.auth0.client.Auth0OauthHttpClient
import com.currencycloud.client.CurrencyCloudClient
import com.google.inject.name.Names
import com.rutter.client.RutterHttpClient
import com.shopify.client.ShopifyHttpClient
import com.slack.client.SlackHttpClient
import com.plaid.client.request.PlaidApi as InternalPlaidApi

internal class ClientsFeature(
  private val auth0Config: Auth0Config,
  private val hosts: Hosts,
  private val notionConfig: NotionConfig,
  private val unitCoConfig: UnitCoConfig,
) : Feature() {
  override fun bind() {
    bindAuth0Clients()
    bindCurrencyCloudClient()
    bindHighbeamClients()
    bindNotionClient()
    bindPlaidClient()
    bindRutterClient()
    bindShopifyClient()
    bindSlackClient()
    bindUnitCoClient()
  }

  private fun bindAuth0Clients() {
    bind(Auth0Config::class.java).toInstance(auth0Config)

    bind(Auth0OauthHttpClient::class.java)
      .toProvider(Auth0OauthHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(Auth0ManagementHttpClient::class.java)
      .toProvider(Auth0ManagementHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindCurrencyCloudClient() {
    bind(CurrencyCloudClient::class.java)
      .toProvider(CurrencyCloudClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindHighbeamClients() {
    bind(Hosts::class.java).toInstance(hosts)

    bind(HttpClient::class.java).annotatedWith(Names.named(AUTH_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(BACKEND_V2_EMAIL_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(BANK_ACCOUNTS_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(BUSINESS_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(BUSINESS_DETAILS_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CONNECT_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CHARGE_CARD_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CREDIT_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CAPITAL_ACCOUNT_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(EVALUATIONS_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(HEALTH_CHECK_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(JOB_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(METRICS_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(ONBOARDING_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(PAYMENT_FEATURE))
      .toProvider(PaymentHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(PLAID_BANK_ACCOUNTS_FEATURE))
      .toProvider(PlaidHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CAPITAL_TRANSACTION_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(CAPITAL_REPAYMENT_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(REPAYMENT_SCHEDULE_FEATURE))
      .toProvider(CapitalHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(TRANSFER_FEATURE))
      .toProvider(PaymentHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(TREASURY_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
    bind(HttpClient::class.java).annotatedWith(Names.named(USER_FEATURE))
      .toProvider(BackendHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindNotionClient() {
    bind(NotionConfig::class.java).toInstance(notionConfig)

    bind(NotionHttpClient::class.java)
      .toProvider(NotionHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindPlaidClient() {
    bind(InternalPlaidApi::class.java)
      .toProvider(PlaidApiProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindRutterClient() {
    bind(RutterHttpClient::class.java)
      .toProvider(RutterHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindShopifyClient() {
    bind(ShopifyHttpClient::class.java)
      .toProvider(ShopifyHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindSlackClient() {
    bind(SlackHttpClient::class.java)
      .toProvider(SlackHttpClientProvider::class.java)
      .asEagerSingleton()
  }

  private fun bindUnitCoClient() {
    bind(UnitCoConfig::class.java).toInstance(unitCoConfig)

    bind(UnitCoClient::class.java)
      .toProvider(UnitCoClientProvider::class.java)
      .asEagerSingleton()
  }
}
