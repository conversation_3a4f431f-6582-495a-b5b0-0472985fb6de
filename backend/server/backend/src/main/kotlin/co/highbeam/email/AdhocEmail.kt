@file:Suppress("UnusedPrivateMember")

package co.highbeam.email

import co.highbeam.config.EmailConfig
import co.highbeam.email.template.EmailTemplate
import co.highbeam.protectedString.ProtectedString
import co.highbeam.sql.store.handle
import org.jdbi.v3.core.Jdbi
import org.jdbi.v3.core.kotlin.KotlinPlugin
import org.jdbi.v3.postgres.PostgresPlugin

/**
 * This function helps send adhoc emails locally.
 */
fun main() {
  val jdbi = Jdbi.create(
    System.getenv("HIGHBEAM_PRODUCTION_POSTGRES_JDBC_URL"),
    System.getenv("HIGHBEAM_PRODUCTION_POSTGRES_USERNAME"),
    run {
      val process = Runtime.getRuntime().exec(arrayOf("sh", "-c", "gcloud auth print-access-token"))
      return@run process.inputReader().readLines().singleNullOrThrow()
    }
  ).apply {
    installPlugin(KotlinPlugin())
    installPlugin(PostgresPlugin())
  }

  val emailService = EmailService(
    config = EmailConfig(
      enabled = true,
      sendgridApiKey = ProtectedString(System.getenv("SENDGRID_API_KEY")),
      sendgridTemplateId = "d-d05050e75dd341618653d7428a8bb148"
    ),
    jdbi = jdbi,
    // Replace with instance of Metrics.
    metrics = TODO()
  )

  val allUsers = jdbi.handle { handle ->
    val query = handle.createQuery("""
      select email_address, first_name
      from users.user;
    """.trimIndent())
    return@handle query.mapToMap().list()
  }

  val users = allUsers.filter { user ->
    TODO("Your filter here.")
  }

  println(users)

  val recipients = users.map { user ->
    EmailTemplate.Recipient(user["email_address"] as String, name = user["first_name"] as String?)
  }

  recipients.forEach { recipient ->
    println("Sending email to $recipient.")
    @Suppress("TooGenericExceptionCaught")
    try {
      emailService.sendEmail(recipient)
      Thread.sleep(100) // Slows it down so we don't hit any rate limits.
      println("Sent email to $recipient.")
    } catch (_: Exception) {
      println("Failed to send to $recipient.")
    }
  }
}

private fun EmailService.sendEmail(recipient: EmailTemplate.Recipient) {
  sync(key = null) { sendEmail ->
    sendEmail(TODO("Your email here."))
  }
}
