package co.highbeam.feature.clients

import co.highbeam.config.CurrencyCloudConfig
import com.currencycloud.client.CurrencyCloudClient
import com.google.inject.Inject
import com.google.inject.Provider

internal class CurrencyCloudClientProvider @Inject constructor(
  private val currencyCloudConfig: CurrencyCloudConfig
) : Provider<CurrencyCloudClient> {
  override fun get() = CurrencyCloudClient(
    currencyCloudConfig.environment.currencyCloudEnvironment,
    currencyCloudConfig.loginId,
    currencyCloudConfig.apiKey.value
  )
}
