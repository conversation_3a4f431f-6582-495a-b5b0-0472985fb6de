@file:Suppress("NestedBlockDepth")

package co.highbeam.adhoc

import co.highbeam.api.business.BusinessApi
import co.highbeam.client.BackendHttpClient
import co.highbeam.client.HighbeamHttpClientRequestBuilder
import co.highbeam.client.HttpClient
import co.highbeam.client.business.BusinessClient
import co.highbeam.config.DEFAULT_GET_ENV
import co.highbeam.config.DEFAULT_GET_GCP_SECRET
import co.highbeam.config.DEFAULT_RUN_COMMAND
import co.highbeam.config.DirectSecretAccess
import co.highbeam.config.SqlDatabaseConfig
import co.highbeam.feature.business.BUSINESS_FEATURE
import co.highbeam.feature.sql.SqlFeature
import co.highbeam.metrics.Metrics
import co.highbeam.protectedString.ProtectedString
import co.highbeam.rep.business.BusinessRep
import co.highbeam.serialization.HighbeamObjectMapper
import co.highbeam.typeConversion.typeConverter.DEFAULT_TYPE_CONVERTERS
import co.unit.client.UnitCoHttpClient
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.inject.AbstractModule
import com.google.inject.Guice
import com.google.inject.Injector
import com.google.inject.name.Names
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.micrometer.core.instrument.logging.LoggingMeterRegistry
import kotlinx.coroutines.runBlocking
import java.time.Clock
import java.util.UUID

@OptIn(DirectSecretAccess::class)
private object PlaygroundModule : AbstractModule() {
  private val metrics = Metrics(LoggingMeterRegistry())
  override fun configure() {
    bindClients()
    bindClock()
    bindMetrics()
    bindObjectMapper()
  }

  private fun bindClients() {
    val backendHttpClient: HttpClient = BackendHttpClient(
      baseUrl = "https://api.highbeam.co",
      metrics = metrics,
      objectMapper = HighbeamObjectMapper.json {
        allowUnknownProperties(true)
        useTypeConverters(DEFAULT_TYPE_CONVERTERS)
      }.build(),
      token = DEFAULT_GET_GCP_SECRET(
        projectId = "highbeam-production",
        secretId = "internal-token",
        versionId = "latest",
      ).let { ProtectedString(it) },
    )
    bind(HttpClient::class.java).annotatedWith(Names.named(BUSINESS_FEATURE))
      .toInstance(backendHttpClient)
  }

  private fun bindClock() {
    bind(Clock::class.java).toInstance(Clock.systemUTC())
  }

  private fun bindMetrics() {
    bind(Metrics::class.java).toInstance(metrics)
  }

  private fun bindObjectMapper() {
    bind(ObjectMapper::class.java).toInstance(
      HighbeamObjectMapper.json {
        allowUnknownProperties(true)
        useTypeConverters(DEFAULT_TYPE_CONVERTERS)
      }.build(),
    )
  }
}

enum class RunType(
  val onlyUpdateCardAddressesIfTheyMatchCustomerAddress: Boolean,
) {
  All(onlyUpdateCardAddressesIfTheyMatchCustomerAddress = false),
  Matching(onlyUpdateCardAddressesIfTheyMatchCustomerAddress = true),
}

private data class Address(
  val street: String,
  val street2: String?,
  val city: String,
  val state: String,
  val postalCode: String,
  val country: String,
)

@OptIn(DirectSecretAccess::class)
@Suppress("TooGenericExceptionCaught")
private class UpdateAddress {
  private val injector: Injector = Guice.createInjector(
    PlaygroundModule,
    SqlFeature(
      config = SqlDatabaseConfig(
        jdbcUrl = DEFAULT_GET_ENV("HIGHBEAM_PRODUCTION_POSTGRES_JDBC_URL")
          .let { checkNotNull(it) },
        username = DEFAULT_GET_ENV("HIGHBEAM_PRODUCTION_POSTGRES_USERNAME")
          .let { checkNotNull(it) },
        password = DEFAULT_RUN_COMMAND("gcloud auth print-access-token")
          .let { ProtectedString(checkNotNull(it)) },
        runMigrations = false,
      ),
    ),
  )

  private val metrics = Metrics(LoggingMeterRegistry())
  private val businessClient: BusinessClient =
    injector.getInstance(BusinessClient::class.java)
  private val unit: UnitCoHttpClient =
    UnitCoHttpClient(UnitCoHttpClient.Environment.Production, metrics)

  suspend fun run() {
    printPreamble()
    val business = getBusiness()
    val customerId = checkNotNull(business.unitCoCustomerId)
    val customer = getCustomer(customerId)
    val newAddress = getNewAddress()
    val runType = getRunType()
    val previousAddress = getAddressFromCustomer(customer)
    val cards = getCards(customerId)
    var success = true
    cards.forEach { card ->
      try {
        val cardAddress = getAddressFromCard(card)
        if (runType.onlyUpdateCardAddressesIfTheyMatchCustomerAddress) {
          if (cardAddress != previousAddress) return@forEach
        }
        updateCardAddress(card["id"].textValue(), card["type"].textValue(), newAddress)
      } catch (e: Exception) {
        println("Card update failed: $e")
        success = false
      }
    }
    if (success) updateCustomerAddress(customerId, newAddress)
  }

  private fun printPreamble() {
    println("This script will update the address for a business.")
    println("It updates it in the following locations:")
    println("  - On the Unit customer.")
    println("  - On each of the Unit cards whose address matches the address on the Unit customer.")
    println("")
  }

  private suspend fun getBusiness(): BusinessRep.Complete {
    val businessGuid = UUID.fromString(read("business UUID"))
    val business = checkNotNull(businessClient.request(BusinessApi.Get(businessGuid)))
    println("Business: $business")
    confirm()
    return business
  }

  private suspend fun getCustomer(customerId: String): JsonNode {
    val response = unit.request(
      httpMethod = HttpMethod.Get,
      path = "/customers/$customerId",
      builder = { useUnitCoOrganizationToken() }
    ).readValue<JsonNode>()
    return response["data"]
  }

  private fun getRunType(): RunType {
    return read("runType").let { RunType.valueOf(requireNotNull(it)) }
  }

  private fun getNewAddress(): Address {
    val address = Address(
      street = read("street").let { requireNotNull(it) },
      street2 = read("street2").let { if (it.isNotBlank()) it else null },
      city = read("city").let { requireNotNull(it) },
      state = read("state").let { requireNotNull(it) },
      postalCode = read("postalCode").let { requireNotNull(it) },
      country = read("country").let { requireNotNull(it) },
    )
    println("Address: $address")
    confirm()
    return address
  }

  private fun getAddressFromCustomer(customer: JsonNode): Address {
    return Address(
      street = customer["attributes"]["address"]["street"].textValue(),
      street2 = customer["attributes"]["address"]["street2"]?.textValue(),
      city = customer["attributes"]["address"]["city"].textValue(),
      state = customer["attributes"]["address"]["state"].textValue(),
      postalCode = customer["attributes"]["address"]["postalCode"].textValue(),
      country = customer["attributes"]["address"]["country"].textValue(),
    )
  }

  private suspend fun updateCustomerAddress(
    customerId: String,
    address: Address,
  ): JsonNode {
    val response = unit.request(
      httpMethod = HttpMethod.Patch,
      path = "/customers/$customerId",
      body = mapOf(
        "data" to mapOf(
          "type" to "businessCustomer",
          "attributes" to mapOf(
            "address" to mapOf(
              "street" to address.street,
              "street2" to address.street2,
              "city" to address.city,
              "state" to address.state,
              "postalCode" to address.postalCode,
              "country" to address.country,
            ),
          ),
        ),
      ),
      builder = { useUnitCoOrganizationToken() }
    ).readValue<JsonNode>()
    return response["data"]
  }

  private suspend fun getCards(customerId: String): JsonNode {
    val response = unit.request(
      httpMethod = HttpMethod.Get,
      path = "/cards",
      qp = mapOf(
        "page[limit]" to listOf("1000"),
        "filter[customerId]" to listOf(customerId),
        "filter[status][0]" to listOf("Inactive"),
        "filter[status][1]" to listOf("Active"),
        "filter[status][2]" to listOf("Frozen"),
        "filter[status][3]" to listOf("SuspectedFraud"),
      ),
      builder = { useUnitCoOrganizationToken() }
    ).readValue<JsonNode>()
    check(response["meta"] == null || response["meta"]["pagination"]["total"].intValue() <= 1000)
    return response["data"]
  }

  private fun getAddressFromCard(card: JsonNode): Address {
    return Address(
      street = card["attributes"]["address"]["street"].textValue(),
      street2 = card["attributes"]["address"]["street2"]?.textValue(),
      city = card["attributes"]["address"]["city"].textValue(),
      state = card["attributes"]["address"]["state"].textValue(),
      postalCode = card["attributes"]["address"]["postalCode"].textValue(),
      country = card["attributes"]["address"]["country"].textValue(),
    )
  }

  private suspend fun updateCardAddress(
    cardId: String,
    cardType: String,
    address: Address,
  ): JsonNode {
    val response = unit.request(
      httpMethod = HttpMethod.Patch,
      path = "/cards/$cardId",
      body = mapOf(
        "data" to mapOf(
          "type" to cardType,
          "attributes" to mapOf(
            "address" to mapOf(
              "street" to address.street,
              "street2" to address.street2,
              "city" to address.city,
              "state" to address.state,
              "postalCode" to address.postalCode,
              "country" to address.country,
            ),
          ),
        ),
      ),
      builder = { useUnitCoOrganizationToken() }
    ).readValue<JsonNode>()
    return response["data"]
  }

  private fun read(name: String): String {
    println("Enter $name:")
    return readln()
  }

  private fun confirm() {
    println("Does this look right? (Enter \"yes\")")
    val response = readln().trim().lowercase()
    require(response == "yes") { "Must enter \"yes\"" }
  }

  private fun HighbeamHttpClientRequestBuilder.useUnitCoOrganizationToken() {
    val token = DEFAULT_GET_GCP_SECRET(
      projectId = "highbeam-production",
      secretId = "unit-co-secret",
      versionId = "latest",
    )
    putHeader(HttpHeaders.Authorization, "Bearer $token")
  }
}

fun main() {
  with(UpdateAddress()) {
    runBlocking {
      run()
    }
  }
}
