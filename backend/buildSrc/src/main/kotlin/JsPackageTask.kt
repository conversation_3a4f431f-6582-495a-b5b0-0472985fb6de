import org.gradle.api.Project
import org.gradle.api.Task

/**
 * Copies the outputs of the Kotlin/JS compiler to a common folder for use by JS code.
 */
class JsPackageTask : HighbeamGradleTask() {
  override val name: String = "jsPackage"

  override val dependencies: List<String> = listOf("jsPublicPackageJson")

  override val isDependencyOf: List<String> = listOf("assemble")

  override fun Task.run(target: Project) {
    val buildDir = target.buildDir
    val rootBuildDir = target.rootProject.buildDir
    // The combinedName convention is based on observed Kotlin/JS compiler output directories.
    val combinedName = "${target.rootProject.name}-${target.name}"
    val sourceDir = "$rootBuildDir/js/packages/$combinedName/kotlin"
    val packageJsonFile = "$buildDir/tmp/jsPublicPackageJson/package.json"
    val outputDir = "$buildDir/js-package"

    inputs.dir(sourceDir)
    inputs.file(packageJsonFile)
    outputs.dir(outputDir)

    doLast {
      target.copy {
        from(sourceDir, packageJsonFile)
        into(outputDir)
      }
    }
  }
}
