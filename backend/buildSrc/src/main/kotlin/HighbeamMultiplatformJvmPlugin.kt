import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * For multiplatform Gradle modules that support JVM.
 * Must be applied after [HighbeamMultiplatformPlugin].
 */
class HighbeamMultiplatformJvmPlugin : Plugin<Project> {
  override fun apply(target: Project) {
    target.kotlin {
      jvm()
    }
    JvmConfigurator(MultiplatformDependencyInstaller({ jvmMain }, { jvmTest })).apply(target)
  }
}
