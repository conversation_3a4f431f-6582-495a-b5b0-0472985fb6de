import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * For multiplatform Gradle modules that support JS.
 * Must be applied after [HighbeamMultiplatformPlugin].
 */
class HighbeamMultiplatformJsPlugin : Plugin<Project> {
  override fun apply(target: Project) {
    target.kotlin {
      js(IR) {
        browser {
          binaries.executable()
        }
      }
    }
    JsConfigurator.apply(target)
  }
}
