import org.gradle.api.Plugin
import org.gradle.api.Project

/**
 * For JVM application servers.
 * Must be applied after [HighbeamJvmPlugin].
 */
class HighbeamJvmServerPlugin : Plugin<Project> {
  override fun apply(target: Project) {
    target.configureApplication()
    target.configureShadowJar()
  }

  private fun Project.configureApplication() {
    pluginManager.apply(Plugins.application)
    group = "co.highbeam.server"
    version = "1-SNAPSHOT"
    application {
      mainClass.set("co.highbeam.server.ServerImplKt")
    }
  }

  private fun Project.configureShadowJar() {
    pluginManager.apply(Plugins.shadow)
    shadowJar {
      isZip64 = true
      archiveFileName.set("server.jar")
      mergeServiceFiles()
      manifest {
        attributes(mapOf("MainClass" to project.application.mainClass.get()))
      }
    }
  }
}
