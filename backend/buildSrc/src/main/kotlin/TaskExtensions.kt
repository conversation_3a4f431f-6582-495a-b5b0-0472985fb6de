import com.github.jengelman.gradle.plugins.shadow.tasks.ShadowJar
import org.gradle.api.Project
import org.gradle.jvm.tasks.Jar
import org.gradle.kotlin.dsl.withType
import org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompile
import org.jetbrains.kotlin.gradle.dsl.KotlinCompile
import org.jetbrains.kotlin.gradle.dsl.KotlinJsCompile
import org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile

internal fun Project.jar(configuration: Jar.() -> Unit) {
  tasks.withType(configuration)
}

fun Project.kotlinCommonCompile(configuration: KotlinCommonCompile.() -> Unit) {
  tasks.withType(configuration)
}

fun Project.kotlinCompile(configuration: KotlinCompile<*>.() -> Unit) {
  tasks.withType(configuration)
}

fun Project.kotlinJsCompile(configuration: KotlinJsCompile.() -> Unit) {
  tasks.withType(configuration)
}

fun Project.kotlinJvmCompile(configuration: KotlinJvmCompile.() -> Unit) {
  tasks.withType(configuration)
}

internal fun Project.shadowJar(configuration: ShadowJar.() -> Unit) {
  tasks.withType(configuration)
}
