import org.gradle.api.Project
import org.gradle.api.Task

abstract class HighbeamGradleTask {
  abstract val name: String

  open val dependencies: List<String>? = null

  open val isDependencyOf: List<String>? = null

  fun apply(target: Project) {
    target.kotlin {
      target.task(name) {
        dependencies?.let { taskNames -> dependsOn(*taskNames.toTypedArray()) }
        run(project)
        isDependencyOf?.let {
          it.forEach { taskName ->
            project.tasks.named(taskName).get().dependsOn(name)
          }
        }
      }
    }
  }

  protected abstract fun Task.run(target: Project)
}
