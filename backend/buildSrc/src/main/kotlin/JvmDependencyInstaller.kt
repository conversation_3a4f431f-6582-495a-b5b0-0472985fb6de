import org.gradle.api.Project
import org.gradle.kotlin.dsl.dependencies
import org.gradle.kotlin.dsl.kotlin

internal object JvmDependencyInstaller : DependencyInstaller() {
  override fun main(target: Project, configure: HighbeamDependencyHandler.() -> Unit) {
    target.dependencies {
      object : HighbeamDependencyHandler(::kotlin) {
        override fun add(dependencyNotation: Any, configurationName: String) {
          <EMAIL>(configurationName, dependencyNotation)
        }
      }.configure()
    }
  }

  override fun test(target: Project, configure: HighbeamDependencyHandler.() -> Unit) {
    target.dependencies {
      object : HighbeamDependencyHandler(::kotlin) {
        override fun add(dependencyNotation: Any, configurationName: String) {
          when (configurationName) {
            "implementation" -> <EMAIL>("implementation", dependencyNotation)
            else -> error("Unsupported configuration name: $configurationName.")
          }
        }
      }.configure()
    }
  }
}
