import io.gitlab.arturbosch.detekt.extensions.DetektExtension
import org.gradle.api.Project
import org.gradle.api.plugins.JavaApplication
import org.gradle.api.plugins.JavaPluginExtension
import org.gradle.kotlin.dsl.getByName
import org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension

internal val Project.application: JavaApplication
  get() = project.extensions.getByName<JavaApplication>("application")

internal fun Project.application(action: JavaApplication.() -> Unit) {
  project.extensions.configure("application", action)
}

internal fun Project.detekt(action: DetektExtension.() -> Unit) {
  project.extensions.configure("detekt", action)
}

internal fun Project.java(action: JavaPluginExtension.() -> Unit) {
  project.extensions.configure("java", action)
}

internal val Project.kotlin
  get() = extensions.getByName<KotlinMultiplatformExtension>("kotlin")

internal fun Project.kotlin(action: KotlinMultiplatformExtension.() -> Unit) {
  project.extensions.configure("kotlin", action)
}
