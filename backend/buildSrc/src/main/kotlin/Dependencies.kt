object Dependencies {
  object Apache {
    val commonsCodec = "commons-codec:commons-codec"
      .version(Versions.apacheCommonsCodec)
    val commonsCsv = "org.apache.commons:commons-csv"
      .version(Versions.apacheCommonsCsv)
    val hadoopAws = "org.apache.hadoop:hadoop-aws"
      .version(Versions.hadoop)
    val hadoopCommon = "org.apache.hadoop:hadoop-common"
      .version(Versions.hadoop)
    val hadoopMapreduceClientCore = "org.apache.hadoop:hadoop-mapreduce-client-core"
      .version(Versions.hadoop)
    val parquetAvro = "org.apache.parquet:parquet-avro"
      .version(Versions.parquetAvro)
  }

  object Aws {
    val sdk = "com.amazonaws:aws-java-sdk"
      .version(Versions.aws)
  }

  object CurrencyCloud {
    val currencyCloud: String = "com.currencycloud.currencycloud-java:currencycloud-java"
      .version(Versions.currencyCloud)
  }

  object Email {
    val sendgrid: String = "com.sendgrid:sendgrid-java"
      .version(Versions.sendgrid)
  }

  object Gcp {
    val cloudTasks: String = "com.google.cloud:google-cloud-tasks"
      .version(Versions.gcpCloudTasks)
    val pubSub: String = "com.google.cloud:google-cloud-pubsub"
      .version(Versions.gcpPubSub)
    val secretManager: String = "com.google.cloud:google-cloud-secretmanager"
      .version(Versions.gcpSecretManager)
    val storage: String = "com.google.cloud:google-cloud-storage"
      .version(Versions.gcpStorage)
  }

  object Geantyref {
    val geantyref: String = "io.leangen.geantyref:geantyref"
      .version(Versions.geantyref)
  }

  object Google {
    val apiClient: String = "com.google.api-client:google-api-client"
      .version(Versions.googleApiClient)
    val guava: String = "com.google.guava:guava"
      .version(Versions.guava)
    val guice: String = "com.google.inject:guice"
      .version(Versions.guice)
    val oauthClient: String = "com.google.oauth-client:google-oauth-client-jetty"
      .version(Versions.googleOauthClient)
    val sheets: String = "com.google.apis:google-api-services-sheets"
      .version(Versions.googleSheets)
  }

  object Jackson {
    val annotations: String = "com.fasterxml.jackson.core:jackson-annotations"
      .version(Versions.jackson)
    val databind: String = "com.fasterxml.jackson.core:jackson-databind"
      .version(Versions.jackson)
    val dataformatYaml: String = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml"
      .version(Versions.jackson)
    val datatypeJdk8: String = "com.fasterxml.jackson.datatype:jackson-datatype-jdk8"
      .version(Versions.jackson)
    val datatypeJsr310: String = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"
      .version(Versions.jackson)
    val moduleKotlin: String = "com.fasterxml.jackson.module:jackson-module-kotlin"
      .version(Versions.jackson)
  }

  object Jwt {
    val auth0JavaJwt: String = "com.auth0:java-jwt"
      .version(Versions.auth0JavaJwt)
    val auth0JwksRsa: String = "com.auth0:jwks-rsa"
      .version(Versions.auth0JwksRsa)
  }

  object Kotlinx {
    val coroutinesCore: String = "org.jetbrains.kotlinx:kotlinx-coroutines-core"
      .version(Versions.kotlinx)
  }

  object Ktor {
    val clientCio: String = "io.ktor:ktor-client-cio"
      .version(Versions.ktor)
    val httpJvm: String = "io.ktor:ktor-http-jvm"
      .version(Versions.ktor)
    val serializationJackson: String = "io.ktor:ktor-serialization-jackson"
      .version(Versions.ktor)
    val serverAuth: String = "io.ktor:ktor-server-auth"
      .version(Versions.ktor)
    val serverAutoHeadResponse: String = "io.ktor:ktor-server-auto-head-response"
      .version(Versions.ktor)
    val serverCallId: String = "io.ktor:ktor-server-call-id"
      .version(Versions.ktor)
    val serverCallLogging: String = "io.ktor:ktor-server-call-logging"
      .version(Versions.ktor)
    val serverCompression: String = "io.ktor:ktor-server-compression"
      .version(Versions.ktor)
    val serverContentNegotiation: String = "io.ktor:ktor-server-content-negotiation"
      .version(Versions.ktor)
    val serverCore: String = "io.ktor:ktor-server-core"
      .version(Versions.ktor)
    val serverCors: String = "io.ktor:ktor-server-cors"
      .version(Versions.ktor)
    val serverDataConversion: String = "io.ktor:ktor-server-data-conversion"
      .version(Versions.ktor)
    val serverDefaultHeaders: String = "io.ktor:ktor-server-default-headers"
      .version(Versions.ktor)
    val serverForwardedHeader: String = "io.ktor:ktor-server-forwarded-header"
      .version(Versions.ktor)
    val serverHostCommon: String = "io.ktor:ktor-server-host-common"
      .version(Versions.ktor)
    val serverHsts: String = "io.ktor:ktor-server-hsts"
      .version(Versions.ktor)
    val serverHtmlBuilder: String = "io.ktor:ktor-server-html-builder"
      .version(Versions.ktor)
    val serverMetricsMicrometer: String = "io.ktor:ktor-server-metrics-micrometer"
      .version(Versions.ktor)
    val serverMustache: String = "io.ktor:ktor-server-mustache"
      .version(Versions.ktor)
    val serverNetty: String = "io.ktor:ktor-server-netty"
      .version(Versions.ktor)
    val serverStatusPages: String = "io.ktor:ktor-server-status-pages"
      .version(Versions.ktor)
    val serverTestHost: String = "io.ktor:ktor-server-test-host"
      .version(Versions.ktor)
  }

  object LaunchDarkly {
    val launchDarkly: String = "com.launchdarkly:launchdarkly-java-server-sdk"
      .version(Versions.launchDarkly)
  }

  object Logging {
    /**
     * DO NOT REMOVE THIS DEPENDENCY.
     * It is not used as part of the compilation process,
     * but required to make SLF4J compatible with Google Cloud Logging at runtime.
     */
    val janino: String = "org.codehaus.janino:janino"
      .version(Versions.janino)
    val kotlinLogging: String = "io.github.microutils:kotlin-logging-jvm"
      .version(Versions.kotlinLogging)
    val logbackClassic: String = "ch.qos.logback:logback-classic"
      .version(Versions.logback)
    val logbackEncoder: String = "net.logstash.logback:logstash-logback-encoder"
      .version(Versions.logbackEncoder)
    val slf4j: String = "org.slf4j:slf4j-api"
      .version(Versions.slf4j)
  }

  object Monitoring {
    val micrometerCore: String = "io.micrometer:micrometer-core"
      .version(Versions.micrometer)
    val micrometerRegistryStackdriver: String = "io.micrometer:micrometer-registry-stackdriver"
      .version(Versions.micrometer)
    val micrometerRegistryDatadog: String = "io.micrometer:micrometer-registry-datadog"
      .version(Versions.micrometer)
  }

  object Nimbusds {
    val joseJwt: String = "com.nimbusds:nimbus-jose-jwt"
      .version(Versions.nimbusds)
  }

  object Pdf {
    val pdf: String = "com.openhtmltopdf:openhtmltopdf-pdfbox"
      .version(Versions.pdf)
  }

  object Plaid {
    val plaid: String = "com.plaid:plaid-java"
      .version(Versions.plaid)
  }

  object Quartz {
    val quartz: String = "org.quartz-scheduler:quartz"
      .version(Versions.quartz)
  }

  object Sentry {
    val logback: String = "io.sentry:sentry-logback"
      .version(Versions.sentry)
    val sentry: String = "io.sentry:sentry"
      .version(Versions.sentry)
  }

  object Sql {
    val flyway: String = "org.flywaydb:flyway-core"
      .version(Versions.flyway)
    val hikari: String = "com.zaxxer:HikariCP"
      .version(Versions.hikari)
    val json: String = "org.jdbi:jdbi3-jackson2"
      .version(Versions.jdbi3)
    val jdbi3Kotlin: String = "org.jdbi:jdbi3-kotlin"
      .version(Versions.jdbi3)
    val jdbi3Postgres: String = "org.jdbi:jdbi3-postgres"
      .version(Versions.jdbi3)
    val postgres: String = "org.postgresql:postgresql"
      .version(Versions.postgres)
  }

  object Testing {
    val assertJ: String = "org.assertj:assertj-core"
      .version(Versions.assertJ)
    val junitApi: String = "org.junit.jupiter:junit-jupiter-api"
      .version(Versions.junit)
    val junitEngine: String = "org.junit.jupiter:junit-jupiter-engine"
      .version(Versions.junit)
    val junitParams: String = "org.junit.jupiter:junit-jupiter-params"
      .version(Versions.junit)
    val mockK: String = "io.mockk:mockk-jvm"
      .version(Versions.mockK)
    val testContainers: String = "org.testcontainers:testcontainers"
      .version(Versions.testContainers)
    val testContainerGcloud: String = "org.testcontainers:gcloud"
      .version(Versions.testContainers)
  }

  object Math {
    val deCampoXirr: String = "org.decampo:xirr"
      .version(Versions.deCampoXirr)

    val kotlinStatistics: String = "org.nield:kotlin-statistics"
      .version(Versions.kotlinStatistics)
  }

  private fun String.version(version: String): String = "$this:$version"
}
