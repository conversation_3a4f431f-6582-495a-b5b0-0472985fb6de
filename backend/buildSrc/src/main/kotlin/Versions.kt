object Versions {
  /**
   * https://mvnrepository.com/artifact/commons-codec/commons-codec
   */
  const val apacheCommonsCodec = "1.16.0"

  /**
   * https://mvnrepository.com/artifact/org.apache.commons/commons-csv
   */
  const val apacheCommonsCsv = "1.10.0"

  /**
   * https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk
   */
  const val aws = "1.12.515"

  /**
   * https://github.com/CurrencyCloud/currencycloud-java
   */
  const val currencyCloud = "5.12.0"

  /**
   * https://github.com/assertj/assertj-core/tags
   */
  const val assertJ = "3.24.2"

  /**
   * https://github.com/auth0/java-jwt/releases
   */
  const val auth0JavaJwt = "4.3.0"

  /**
   * https://github.com/auth0/jwks-rsa-java/releases
   */
  const val auth0JwksRsa = "0.22.0"

  /**
   * https://github.com/flyway/flyway/releases
   */
  const val flyway = "9.21.1"

  /**
   * https://mvnrepository.com/artifact/com.google.cloud/google-cloud-tasks
   */
  const val gcpCloudTasks: String = "2.43.0"

  /**
   * https://mvnrepository.com/artifact/com.google.cloud/google-cloud-pubsub
   */
  const val gcpPubSub = "1.123.7"

  /**
   * https://mvnrepository.com/artifact/com.google.cloud/google-cloud-secretmanager
   */
  const val gcpSecretManager = "2.13.0"

  /**
   * https://mvnrepository.com/artifact/com.google.cloud/google-cloud-storage
   */
  const val gcpStorage = "2.20.2"

  /**
   * https://github.com/leangen/geantyref
   */
  const val geantyref = "2.0.0"

  /**
   * https://github.com/googleapis/google-api-java-client/releases
   */
  const val googleApiClient = "2.2.0"

  /**
   * https://github.com/googleapis/google-oauth-java-client/releases
   */
  const val googleOauthClient = "1.34.1"

  /**
   * https://googleapis.dev/java/google-api-services-sheets/latest/
   */
  const val googleSheets = "v4-rev20220927-2.0.0"

  /**
   * https://github.com/google/guava/releases
   */
  const val guava = "32.1.2-jre"

  /**
   * https://github.com/google/guice/releases
   */
  const val guice = "5.1.0"

  /**
   * https://mvnrepository.com/artifact/org.apache.hadoop/hadoop-common
   */
  const val hadoop = "3.3.5"

  /**
   * https://github.com/brettwooldridge/HikariCP/tags
   */
  const val hikari = "5.0.1"

  /**
   * https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-core
   */
  const val jackson = "2.14.2"

  /**
   * https://github.com/janino-compiler/janino/releases
   */
  const val janino = "3.1.9"

  /**
   * https://github.com/jdbi/jdbi/releases
   */
  const val jdbi3 = "3.39.1"

  /**
   * https://github.com/junit-team/junit5/releases
   */
  const val junit = "5.10.0"

  /**
   * https://github.com/Kotlin/kotlinx.coroutines/releases
   */
  const val kotlinx = "1.6.4"

  /**
   * https://github.com/MicroUtils/kotlin-logging/releases
   */
  const val kotlinLogging = "3.0.5"

  /**
   * https://github.com/ktorio/ktor/releases
   */
  const val ktor = "2.2.4"

  /**
   * https://github.com/launchdarkly/java-server-sdk/releases
   */
  const val launchDarkly = "7.3.0"

  /**
   * https://github.com/qos-ch/logback/tags
   */
  const val logback = "1.4.6"

  /**
   * https://github.com/logfellow/logstash-logback-encoder/tags
   */
  const val logbackEncoder = "7.3"

  /**
   * https://github.com/micrometer-metrics/micrometer/releases
   */
  const val micrometer = "1.10.5"

  /**
   * https://bitbucket.org/connect2id/nimbus-jose-jwt/src/master/
   */
  const val nimbusds = "9.31"

  /**
   * https://github.com/mockk/mockk/releases
   */
  const val mockK = "1.13.5"

  /**
   * https://mvnrepository.com/artifact/org.apache.parquet/parquet-avro
   */
  const val parquetAvro = "1.13.1"

  /**
   * https://github.com/danfickle/openhtmltopdf/tags
   */
  const val pdf = "1.0.10"

  /**
   * https://github.com/plaid/plaid-java/tags
   */
  const val plaid = "27.0.0"

  /**
   * https://github.com/pgjdbc/pgjdbc/tags
   */
  const val postgres = "42.6.0"

  /**
   * https://github.com/quartz-scheduler/quartz/releases
   *
   * IMPORTANT: When updating Quartz, take a look at
   * https://github.com/quartz-scheduler/quartz/blob/v2.3.2/quartz-core/src/main/resources/org/quartz/impl/jdbcjobstore/tables_postgres.sql
   * to see if the schema has changed at all. If it has, create a corresponding migration.
   */
  const val quartz = "2.3.2"

  /**
   * https://github.com/sendgrid/sendgrid-java/releases
   */
  const val sendgrid = "4.9.3"

  /**
   * https://github.com/getsentry/sentry-java/releases
   */
  const val sentry = "6.16.0"

  /**
   * https://github.com/qos-ch/slf4j/tags
   */
  const val slf4j = "2.0.7"

  /**
   * https://github.com/testcontainers/testcontainers-java/releases
   */
  const val testContainers = "1.18.3"

  /**
   * https://github.com/RayDeCampo/java-xirr/releases
   */
  const val deCampoXirr = "1.2"

  /**
   * https://mvnrepository.com/artifact/org.nield/kotlin-statistics
   */
  const val kotlinStatistics = "1.2.1"
}
