import org.gradle.api.JavaVersion
import org.gradle.api.Project
import org.gradle.jvm.toolchain.JavaLanguageVersion

internal class JvmConfigurator(private val dependencyInstaller: DependencyInstaller) {
  fun apply(target: Project) {
    target.kotlinCompile {
      kotlinOptions.freeCompilerArgs += "-opt-in=kotlin.ExperimentalStdlibApi"
    }

    target.java {
      toolchain {
        languageVersion.set(JavaLanguageVersion.of(JavaVersion.VERSION_17.toString()))
      }
    }

    dependencyInstaller.main(target) {
      add(kotlin("reflect"))
      add(Dependencies.Kotlinx.coroutinesCore)
      add(Dependencies.Logging.janino)
      add(Dependencies.Logging.kotlinLogging)
      add(Dependencies.Logging.logbackClassic)
      add(Dependencies.Logging.logbackEncoder)
      add(Dependencies.Logging.slf4j)
    }
    dependencyInstaller.test(target) {
      add(kotlin("test-junit5"))
      add(Dependencies.Testing.junitEngine)
      add(Dependencies.Testing.assertJ)
    }

    target.jar {
      // Archives (JARs) are named using the fully qualified project path in order to avoid
      // collisions when multiple JARs are combined to form an application.
      archiveBaseName.set(project.path.drop(1).replace(':', '-'))
    }
  }
}
