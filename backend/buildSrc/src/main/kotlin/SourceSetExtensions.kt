import org.gradle.api.Project

val Project.commonMain
  get() = kotlin.sourceSets.getByName("commonMain")

val Project.commonTest
  get() = kotlin.sourceSets.getByName("commonTest")

val Project.jsMain
  get() = kotlin.sourceSets.getByName("jsMain")

val Project.jsTest
  get() = kotlin.sourceSets.getByName("jsTest")

val Project.jvmMain
  get() = kotlin.sourceSets.getByName("jvmMain")

val Project.jvmTest
  get() = kotlin.sourceSets.getByName("jvmTest")
