import org.gradle.api.Project
import org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet

internal class MultiplatformDependencyInstaller(
  private val mainSourceSet: Project.() -> KotlinSourceSet,
  private val testSourceSet: Project.() -> KotlinSourceSet,
) : DependencyInstaller() {
  override fun main(target: Project, configure: HighbeamDependencyHandler.() -> Unit) {
    target.mainSourceSet().dependencies {
      object : HighbeamDependencyHandler(::kotlin) {
        override fun add(dependencyNotation: Any, configurationName: String) {
          when (configurationName) {
            "implementation" -> <EMAIL>(dependencyNotation)
            else -> error("Unsupported configuration name: $configurationName.")
          }
        }
      }.configure()
    }
  }

  override fun test(target: Project, configure: HighbeamDependencyHandler.() -> Unit) {
    target.testSourceSet().dependencies {
      object : HighbeamDependencyHandler(::kotlin) {
        override fun add(dependencyNotation: Any, configurationName: String) {
          when (configurationName) {
            "implementation" -> <EMAIL>(dependencyNotation)
            else -> error("Unsupported configuration name: $configurationName.")
          }
        }
      }.configure()
    }
  }
}
